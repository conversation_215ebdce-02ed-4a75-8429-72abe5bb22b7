package server

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"time"

	"github.com/gorilla/mux"
	_ "github.com/lib/pq"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	"gopkg.in/yaml.v2"

	configlib "github.com/temui-sisva/go-lib/config"
	contextlib "github.com/temui-sisva/go-lib/context"
	"github.com/temui-sisva/go-lib/cryptography"
	"github.com/temui-sisva/go-lib/cryptography/encryption"
	"github.com/temui-sisva/go-lib/graceful"
	tenantconfigurationclient "github.com/temui-sisva/go-lib/grpcclient/tenantconfiguration"
	tenantconfigurationclientservice "github.com/temui-sisva/go-lib/grpcclient/tenantconfiguration/service"
	tenantuserclient "github.com/temui-sisva/go-lib/grpcclient/tenantuser"
	tenantuserclientservice "github.com/temui-sisva/go-lib/grpcclient/tenantuser/service"
	pglib "github.com/temui-sisva/go-lib/postgresql"
	prometheuslib "github.com/temui-sisva/go-lib/prometheus"
	secretlocalfile "github.com/temui-sisva/go-lib/secret/client/localfile"
	"github.com/temui-sisva/report/cmd/report-api-http/config"
	"github.com/temui-sisva/report/internal/detail"
	detailservice "github.com/temui-sisva/report/internal/detail/service"
	detailpgstore "github.com/temui-sisva/report/internal/detail/store/postgresql"
	"github.com/temui-sisva/report/internal/manager"
	managerhttphandler "github.com/temui-sisva/report/internal/manager/handler/http"
	managerservice "github.com/temui-sisva/report/internal/manager/service"
	managerpgstore "github.com/temui-sisva/report/internal/manager/store/postgresql"
	"github.com/temui-sisva/report/internal/scoring"
	scoringhttphandler "github.com/temui-sisva/report/internal/scoring/handler/http"
	scoringservice "github.com/temui-sisva/report/internal/scoring/service"
	scoringpgstore "github.com/temui-sisva/report/internal/scoring/store/postgresql"
)

// Following constants are the possible exit code returned
// when running a server.
const (
	CodeSuccess = iota
	CodeBadConfig
	CodeFailServeHTTP
)

// Option contains available options to run the server.
type Option struct {
	SecretPath string
	ConfigTest bool
}

// Run creates a server with the given Option and starts the
// server.
//
// Run returns a status code suitable for os.Exit() argument.
func Run(opt Option) int {
	s, err := new(opt)
	if err != nil {
		return CodeBadConfig
	}

	// do not start server for config testing
	if opt.ConfigTest {
		return CodeSuccess
	}

	return s.start()
}

// server is the long-runnning application.
type server struct {
	srv      *http.Server
	handlers []handler
	config   config.Config
}

// handler provides mechanism to start HTTP handler. All HTTP
// handlers must implements this interface.
type handler interface {
	Start(multiplexer *mux.Router) error
}

// new creates and returns a new server.
func new(opt Option) (*server, error) {
	s := &server{
		srv: &http.Server{
			ReadTimeout:  10 * time.Second,
			WriteTimeout: 10 * time.Second,
		},
	}

	// initialize secrets
	var secrets map[string]string
	{
		client := secretlocalfile.New()
		bytes, err := client.Fetch(context.Background(), opt.SecretPath)
		if err != nil {
			log.Printf("[report-api-http] failed to fetch secrets: %s\n", err.Error())
			return nil, fmt.Errorf("failed to fetch secrets: %s", err.Error())
		}

		err = yaml.Unmarshal(bytes, &secrets)
		if err != nil {
			log.Printf("[report-api-http] failed to unmarshal secrets: %s\n", err.Error())
			return nil, fmt.Errorf("failed to unmarshal secrets: %s", err.Error())
		}
	}

	// initialize config
	{
		configPath, err := getConfigFilePath()
		if err != nil {
			log.Printf("[report-api-http] failed to get config filepath: %s\n", err.Error())
			return nil, fmt.Errorf("failed to get config filepath: %s", err.Error())
		}

		var cfg config.Config
		err = configlib.ReadFile(configPath, &cfg, configlib.WithStrictParsing(), configlib.WithSecrets(secrets))
		if err != nil {
			log.Printf("[report-api-http] failed to read config: %s\n", err.Error())
			return nil, fmt.Errorf("failed to read config: %s", err.Error())
		}
		s.config = cfg
	}

	// end of config testing
	if opt.ConfigTest {
		return s, nil
	}

	// initialize tenant configuration client service
	var tenantConfigurationClientSvc tenantconfigurationclient.Service
	{
		options := make([]tenantconfigurationclientservice.Option, 0)
		options = append(options, tenantconfigurationclientservice.WithGRPCConfig(tenantconfigurationclientservice.GRPCConfig{
			ConnectionTimeout: time.Duration(s.config.TenantConfigurationClient.ConnectionTimeout),
			RetryInterval:     time.Duration(s.config.TenantConfigurationClient.RetryInterval),
		}))

		for scope, cfg := range s.config.TenantConfigurationClient.ScopeSettings {
			options = append(options, tenantconfigurationclientservice.WithScopeSetting(scope, tenantconfigurationclientservice.ScopeSetting{
				BreakerErrorThreshold:   cfg.BreakerErrorThreshold,
				BreakerSuccessThreshold: cfg.BreakerSuccessThreshold,
				BreakerTimeout:          time.Duration(cfg.BreakerTimeout),
			}))
		}

		var err error
		tenantConfigurationClientSvc, err = tenantconfigurationclientservice.New(s.config.TenantConfigurationClient.Address, options...)
		if err != nil {
			log.Printf("[report-api-http] failed to initialize tenant configuration client service: %s\n", err.Error())
			return nil, fmt.Errorf("failed to initialize tenant configuration client service: %s", err.Error())
		}
	}

	// initialize encryption service
	var encryptionSvc cryptography.Encryption
	{
		var err error
		encryptionSvc, err = encryption.New([]byte(s.config.Encryption.Key))
		if err != nil {
			log.Printf("[report-api-http] failed to initialize encryption service: %s\n", err.Error())
			return nil, fmt.Errorf("failed to initialize encryption service: %s", err.Error())
		}
	}

	// initilize postgresql client manager
	var pgClientManager *pglib.ClientManager
	{
		ctx := contextlib.SetSource(context.Background(), "report.cmd.report-api-http")
		configs, err := tenantConfigurationClientSvc.GetSchoolConfigurationsByKey(ctx, tenantconfigurationclient.SchoolConfigurationKeyCommonPG)
		if err != nil {
			log.Printf("[report-api-http] failed to get common pg configurations: %s\n", err.Error())
			return nil, fmt.Errorf("failed to get common pg configurations: %s", err.Error())
		}

		var options []pglib.Option
		for _, config := range configs {
			connStrBytes, err := encryptionSvc.Decrypt([]byte(config.Value))
			if err != nil {
				log.Printf("[report-api-http] failed to decrypt common pg connection string: %s\n", err.Error())
				return nil, fmt.Errorf("failed to decrypt common pg connection string: %s", err.Error())
			}

			options = append(options, pglib.WithClientConfig(fmt.Sprintf("common.%s", config.SchoolID), pglib.ClientConfig{
				ConnectionString:  string(connStrBytes),
				ConnectionTimeout: time.Duration(s.config.PostgreSQL.ConnectionTimeout),
			}))
		}

		pgClientManager, err = pglib.NewClientManager(options...)
		if err != nil {
			log.Printf("[report-api-http] failed to initialize postgresql client manager: %s\n", err.Error())
			return nil, fmt.Errorf("failed to initialize postgresql client manager: %s", err.Error())
		}
	}

	// initialize tenant configuration client service
	var tenantUserClientSvc tenantuserclient.Service
	{
		options := make([]tenantuserclientservice.Option, 0)
		options = append(options, tenantuserclientservice.WithGRPCConfig(tenantuserclientservice.GRPCConfig{
			ConnectionTimeout: time.Duration(s.config.TenantUserClient.ConnectionTimeout),
			RetryInterval:     time.Duration(s.config.TenantUserClient.RetryInterval),
		}))

		for scope, cfg := range s.config.TenantUserClient.ScopeSettings {
			options = append(options, tenantuserclientservice.WithScopeSetting(scope, tenantuserclientservice.ScopeSetting{
				BreakerErrorThreshold:   cfg.BreakerErrorThreshold,
				BreakerSuccessThreshold: cfg.BreakerSuccessThreshold,
				BreakerTimeout:          time.Duration(cfg.BreakerTimeout),
			}))
		}

		var err error
		tenantUserClientSvc, err = tenantuserclientservice.New(s.config.TenantUserClient.Address, options...)
		if err != nil {
			log.Printf("[report-api-http] failed to initialize tenant user client service: %s\n", err.Error())
			return nil, fmt.Errorf("failed to initialize tenant user client service: %s", err.Error())
		}
	}

	// initialize detail service
	var detailSvc detail.Service
	{
		pgStore := detailpgstore.New(pgClientManager, func(s string) string { return fmt.Sprintf("common.%s", s) })
		detailSvc = detailservice.New(pgStore)
	}

	// initialize manager service
	var managerSvc manager.Service
	{
		pgStore := managerpgstore.New(pgClientManager, func(s string) string { return fmt.Sprintf("common.%s", s) })
		managerSvc = managerservice.New(pgStore)
	}

	// initialize manager HTTP handler
	{
		var options []managerhttphandler.Option
		for scopeName, cfg := range s.config.Manager.Handler.HTTP {
			options = append(options, managerhttphandler.WithScopeSetting(scopeName, managerhttphandler.ScopeSetting{
				Timeout: time.Duration(cfg.Timeout),
			}))
		}

		identities := []managerhttphandler.HandlerIdentity{
			managerhttphandler.HandlerTemplates,
			managerhttphandler.HandlerTemplate,
			managerhttphandler.HandlerReports,
			managerhttphandler.HandlerReport,
		}

		for _, identity := range identities {
			options = append(options, managerhttphandler.WithHandler(identity))
		}

		managerHTTP, err := managerhttphandler.New(managerSvc, tenantUserClientSvc, options...)
		if err != nil {
			log.Printf("[report-api-http] failed to initialize manager http handlers: %s\n", err.Error())
			return nil, fmt.Errorf("failed to initialize manager http handlers: %s", err.Error())
		}

		s.handlers = append(s.handlers, managerHTTP)
	}

	// initialize scoring service
	var scoringSvc scoring.Service
	{
		pgStore := scoringpgstore.New(pgClientManager, func(s string) string { return fmt.Sprintf("common.%s", s) })
		scoringSvc = scoringservice.New(pgStore, detailSvc)
	}

	// initialize scoring HTTP handler
	{
		var options []scoringhttphandler.Option
		for scopeName, cfg := range s.config.Scoring.Handler.HTTP {
			options = append(options, scoringhttphandler.WithScopeSetting(scopeName, scoringhttphandler.ScopeSetting{
				Timeout: time.Duration(cfg.Timeout),
			}))
		}

		identities := []scoringhttphandler.HandlerIdentity{
			scoringhttphandler.HandlerStudentScores,
		}

		for _, identity := range identities {
			options = append(options, scoringhttphandler.WithHandler(identity))
		}

		scoringHTTP, err := scoringhttphandler.New(scoringSvc, tenantUserClientSvc, options...)
		if err != nil {
			log.Printf("[report-api-http] failed to initialize scoring http handlers: %s\n", err.Error())
			return nil, fmt.Errorf("failed to initialize scoring http handlers: %s", err.Error())
		}

		s.handlers = append(s.handlers, scoringHTTP)
	}

	return s, nil
}

// start starts the given server.
func (s *server) start() int {
	log.Println("[report-api-http] starting server...")

	// create multiplexer object
	rootMux := mux.NewRouter()
	appMux := rootMux.PathPrefix("/report").Subrouter()

	// use middlewares to app mux only
	appMux.Use(prometheuslib.GetHTTPHandlerMiddleware("report-api-http"))

	// starts handlers
	for _, h := range s.handlers {
		if err := h.Start(appMux); err != nil {
			log.Printf("[report-api-http] failed to start handler: %s\n", err.Error())
			return CodeFailServeHTTP
		}
	}

	// handle prometheus pull endpoint
	rootMux.Handle("/metrics", promhttp.Handler())

	// assign multiplexer as server handler
	s.srv.Handler = rootMux

	// serve using graceful mechanism
	address := fmt.Sprintf(":%d", s.config.Server.Port)
	err := graceful.ServeHTTP(s.srv, address, 0)
	if err != nil {
		log.Printf("[report-api-http] failed to start server: %s\n", err.Error())
		return CodeFailServeHTTP
	}

	return CodeSuccess
}
