package config

import configlib "github.com/temui-sisva/go-lib/config"

type TenantUserClient struct {
	Address           string                                  `yaml:"address"`
	ConnectionTimeout configlib.Duration                      `yaml:"connection_timeout"`
	RetryInterval     configlib.Duration                      `yaml:"retry_interval"`
	ScopeSettings     map[string]TenantUserClientScopeSetting `yaml:"scope_settings"`
}

type TenantUserClientScopeSetting struct {
	BreakerErrorThreshold   int                `yaml:"breaker_error_threshold"`
	BreakerSuccessThreshold int                `yaml:"breaker_success_threshold"`
	BreakerTimeout          configlib.Duration `yaml:"breaker_timeout"`
}