package config

import configlib "github.com/temui-sisva/go-lib/config"

type Config struct {
	Server                    Server                    `yaml:"server"`
	TenantConfigurationClient TenantConfigurationClient `yaml:"tenant_configuration_client"`
	Encryption                Encryption                `yaml:"encryption"`
	PostgreSQL                PostgreSQL                `yaml:"postgresql"`
	TenantUserClient          TenantUserClient          `yaml:"tenant_user_client"`
	Manager                   Manager                   `yaml:"manager"`
	Scoring                   Scoring                   `yaml:"scoring"`
}

type Server struct {
	Port int `yaml:"port"`
}

type Encryption struct {
	Key string `yaml:"key"`
}

type PostgreSQL struct {
	ConnectionTimeout configlib.Duration `yaml:"connection_timeout"`
}

type Handler struct {
	HTTP map[string]HTTPHandler `yaml:"http"`
}

type HTTPHandler struct {
	Timeout configlib.Duration `yaml:"timeout"`
}
