package postgresql

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"

	"github.com/google/uuid"
	"github.com/jmoiron/sqlx"

	"github.com/temui-sisva/report/internal/model"
	"github.com/temui-sisva/report/internal/scoring"
	"github.com/temui-sisva/report/internal/tool/ptr"
)

func (sc *storeClient) SetStudentScores(ctx context.Context, studentScores []*model.StudentScore) error {
	var argsKVs []map[string]interface{}
	for _, studentScore := range studentScores {
		var scoreJSON *string
		if studentScore.Scores != nil {
			v, err := json.Marshal(studentScore.Scores)
			if err != nil {
				return err
			}
			scoreJSON = ptr.StrPtr(string(v))
		}

		argsKVs = append(argsKVs, map[string]interface{}{
			"student_id":  studentScore.StudentID,
			"report_id":   studentScore.ReportID,
			"class_id":    studentScore.ClassID,
			"score_json":  scoreJSON,
			"create_by":   studentScore.CreateBy,
			"create_time": studentScore.CreateTime,
			"update_by":   studentScore.UpdateBy,
			"update_time": studentScore.UpdateTime,
		})
	}

	query, args, err := sqlx.Named(querySetStudentScores, argsKVs)
	if err != nil {
		return err
	}
	query, args, err = sqlx.In(query, args...)
	if err != nil {
		return err
	}
	query = sc.q.Rebind(query)

	_, err = sc.q.Exec(query, args...)
	if err != nil {
		return err
	}

	return nil
}

func (sc *storeClient) GetStudentScores(ctx context.Context, filter scoring.GetStudentScoresFilter) ([]*model.StudentScore, error) {
	argsKV := make(map[string]interface{})
	addJoins := make([]string, 0)
	addConditions := make([]string, 0)

	if filter.StudentID != uuid.Nil {
		addConditions = append(addConditions, "srcs.student_id = :student_id")
		argsKV["student_id"] = filter.StudentID
	}

	if filter.ReportID > 0 {
		addConditions = append(addConditions, "srcs.report_id = :report_id")
		argsKV["report_id"] = filter.ReportID
	}

	if filter.ClassID > 0 {
		addConditions = append(addConditions, "srcs.class_id = :class_id")
		argsKV["class_id"] = filter.ClassID
	}

	addJoin := strings.Join(addJoins, " ")
	addCondition := strings.Join(addConditions, " AND ")
	if len(addConditions) > 0 {
		addCondition = fmt.Sprintf("WHERE %s", addCondition)
	}

	query := fmt.Sprintf(queryGetStudentScores, fmt.Sprintf("%s %s", addJoin, addCondition))

	query, args, err := sqlx.Named(query, argsKV)
	if err != nil {
		return nil, err
	}
	query, args, err = sqlx.In(query, args...)
	if err != nil {
		return nil, err
	}
	query = sc.q.Rebind(query)

	rows, err := sc.q.Queryx(query, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	res := make([]studentScoreDB, 0)
	for rows.Next() {
		var s studentScoreDB
		err = rows.StructScan(&s)
		if err != nil {
			return nil, err
		}

		res = append(res, s)
	}

	if err := rows.Err(); err != nil {
		return nil, err
	}

	studentScores := make([]*model.StudentScore, 0)
	for _, v := range res {
		studentScore, err := v.format()
		if err != nil {
			return nil, err
		}

		studentScores = append(studentScores, studentScore)
	}

	return studentScores, nil
}

func (sc *storeClient) DeleteStudentScores(ctx context.Context, studentScore *model.StudentScore) error {
	argsKV := make(map[string]interface{})
	addConditions := make([]string, 0)

	if studentScore.GetReportID() > 0 {
		addConditions = append(addConditions, "report_id = :report_id")
		argsKV["report_id"] = studentScore.ReportID
	}

	if studentScore.GetStudentID() != uuid.Nil {
		addConditions = append(addConditions, "student_id = :student_id")
		argsKV["student_id"] = studentScore.StudentID
	}

	if studentScore.GetClassID() > 0 {
		addConditions = append(addConditions, "class_id = :class_id")
		argsKV["class_id"] = studentScore.ClassID
	}

	addCondition := strings.Join(addConditions, " AND ")
	if len(addConditions) > 0 {
		addCondition = fmt.Sprintf("WHERE %s", addCondition)
	}

	query := fmt.Sprintf(queryDeleteStudentScores, addCondition)

	query, args, err := sqlx.Named(query, argsKV)
	if err != nil {
		return err
	}
	query, args, err = sqlx.In(query, args...)
	if err != nil {
		return err
	}
	query = sc.q.Rebind(query)

	_, err = sc.q.Exec(query, args...)
	if err != nil {
		return err
	}

	return nil
}
