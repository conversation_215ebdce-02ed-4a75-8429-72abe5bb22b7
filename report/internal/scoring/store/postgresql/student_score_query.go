package postgresql

const querySetStudentScores = `
	INSERT INTO
		student_report_class_score 
	(
		student_id,
		report_id,
		class_id,
		score_json,
		create_by,
		create_time
	) VALUES (
		:student_id,
		:report_id,
		:class_id,
		:score_json,
		:create_by,
		:create_time
	) ON CONFLICT (
		student_id,
		report_id,
		class_id
	) DO UPDATE SET
		score_json = EXCLUDED.score_json,
		update_by = EXCLUDED.update_by,
		update_time = EXCLUDED.update_time
`

const queryGetStudentScores = `
	SELECT 
		srcs.student_id,
		srcs.report_id,
		srcs.class_id,
		srcs.score_json,
		srcs.create_by,
		srcs.create_time,
		srcs.update_by,
		srcs.create_time
	FROM
		student_report_class_score srcs
		%s
`

const queryDeleteStudentScores = `
	DELETE FROM
		student_report_class_score 
	WHERE
		%s
`
