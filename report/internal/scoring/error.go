package scoring

import "errors"

var (
	ErrEmptyScore        error = errors.New("EMPTY_SCORE")
	ErrInvalidClassID    error = errors.New("INVALID_CLASS_ID")
	ErrInvalidReportID   error = errors.New("INVALID_REPORT_ID")
	ErrInvalidSchoolID   error = errors.New("INVALID_SCHOOL_ID")
	ErrInvalidScoreName  error = errors.New("INVALID_SCORE_NAME")
	ErrInvalidScoreValue error = errors.New("INVALID_SCORE_VALUE")
	ErrInvalidUserID     error = errors.New("INVALID_USER_ID")
	ErrTooManyReportID   error = errors.New("TOO_MANY_REPORT_ID")
	ErrTooManyRequest    error = errors.New("TOO_MANY_REQUEST")
)
