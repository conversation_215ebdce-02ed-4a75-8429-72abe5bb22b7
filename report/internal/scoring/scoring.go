package scoring

import (
	"context"

	"github.com/google/uuid"

	"github.com/temui-sisva/report/internal/model"
)

type Service interface {
	SetStudentScores(ctx context.Context, studentScores []*model.StudentScore) error
	GetStudentScores(ctx context.Context, filter GetStudentScoresFilter, opt GetStudentScoreOption) ([]*model.StudentScore, error)
	DeleteStudentScores(ctx context.Context, studentScore *model.StudentScore) error
}

type GetStudentScoresFilter struct {
	StudentID uuid.UUID
	ReportID  int64
	ClassID   int64
}

type GetStudentScoreOption struct {
	WithStudentDetail bool
	WithClassDetail   bool
}
