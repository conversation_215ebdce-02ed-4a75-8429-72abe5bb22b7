package service

import (
	"context"

	"github.com/google/uuid"

	contextlib "github.com/temui-sisva/go-lib/context"
	"github.com/temui-sisva/report/internal/model"
	"github.com/temui-sisva/report/internal/scoring"
)

var maxSetStudentScoresLength = 100

func (s *service) SetStudentScores(ctx context.Context, studentScores []*model.StudentScore) error {
	if len(studentScores) == 0 {
		return nil
	}
	if len(studentScores) > maxSetStudentScoresLength {
		return scoring.ErrTooManyRequest
	}

	// must have unique report_id and class_id
	reportID := studentScores[0].GetReportID()
	for _, studentScore := range studentScores {
		if studentScore.GetReportID() != reportID {
			return scoring.ErrTooManyReportID
		}
	}

	tNow := s.timeNow()

	for _, studentScore := range studentScores {
		studentScore.UpdateBy = studentScore.CreateBy
		studentScore.CreateTime = &tNow
		studentScore.UpdateTime = &tNow

		err := validateStudentScore(studentScore)
		if err != nil {
			return err
		}
	}

	schoolID, ok := contextlib.GetSchoolID(ctx)
	if !ok || schoolID == "" {
		return scoring.ErrInvalidSchoolID
	}

	sc, err := s.pgStore.NewClient(schoolID, false)
	if err != nil {
		return err
	}

	err = sc.SetStudentScores(ctx, studentScores)
	if err != nil {
		return err
	}

	return nil
}

func (s *service) GetStudentScores(ctx context.Context, filter scoring.GetStudentScoresFilter, opt scoring.GetStudentScoreOption) ([]*model.StudentScore, error) {
	// report ID is mandatory in filter
	if filter.ReportID <= 0 {
		return nil, scoring.ErrInvalidReportID
	}

	schoolID, ok := contextlib.GetSchoolID(ctx)
	if !ok || schoolID == "" {
		return nil, scoring.ErrInvalidSchoolID
	}

	sc, err := s.pgStore.NewClient(schoolID, false)
	if err != nil {
		return nil, err
	}

	studentScores, err := sc.GetStudentScores(ctx, filter)
	if err != nil {
		return nil, err
	}

	if opt.WithStudentDetail {
		studentIDs := []uuid.UUID{}
		if filter.StudentID != uuid.Nil {
			studentIDs = append(studentIDs, filter.StudentID)
		} else {
			for _, studentScore := range studentScores {
				if studentScore == nil {
					continue
				}

				studentIDs = append(studentIDs, studentScore.GetStudentID())
			}
		}

		studentDetails, err := s.detail.GetStudentDetailsByIDs(ctx, studentIDs)
		if err != nil {
			return nil, err
		}

		for _, studentScore := range studentScores {
			if studentScore == nil {
				continue
			}

			if d, ok := studentDetails[studentScore.GetStudentID()]; ok {
				studentScore.StudentDetail = d
			}
		}
	}

	if opt.WithClassDetail {
		classIDs := []int64{}
		if filter.ClassID > 0 {
			classIDs = append(classIDs, filter.ClassID)
		} else {
			for _, studentScore := range studentScores {
				if studentScore == nil {
					continue
				}

				classIDs = append(classIDs, studentScore.GetClassID())
			}
		}

		classDetails, err := s.detail.GetClassDetailsByIDs(ctx, classIDs)
		if err != nil {
			return nil, err
		}

		for _, studentScore := range studentScores {
			if studentScore == nil {
				continue
			}

			if d, ok := classDetails[studentScore.GetClassID()]; ok {
				studentScore.ClassDetail = d
			}
		}
	}

	return studentScores, nil
}

func (s *service) DeleteStudentScores(ctx context.Context, studentScore *model.StudentScore) error {
	// report ID is mandatory
	if studentScore.GetReportID() <= 0 {
		return scoring.ErrInvalidReportID
	}

	if studentScore.StudentID != nil || studentScore.GetStudentID() == uuid.Nil {
		return scoring.ErrInvalidUserID
	}
	if studentScore.ClassID != nil || studentScore.GetClassID() <= 0 {
		return scoring.ErrInvalidClassID
	}

	schoolID, ok := contextlib.GetSchoolID(ctx)
	if !ok || schoolID == "" {
		return scoring.ErrInvalidSchoolID
	}

	sc, err := s.pgStore.NewClient(schoolID, false)
	if err != nil {
		return err
	}

	err = sc.DeleteStudentScores(ctx, studentScore)
	if err != nil {
		return err
	}

	return nil
}

func validateStudentScore(studentScore *model.StudentScore) error {
	if studentScore.GetStudentID() == uuid.Nil {
		return scoring.ErrInvalidUserID
	}

	if studentScore.GetReportID() <= 0 {
		return scoring.ErrInvalidReportID
	}

	if studentScore.GetClassID() <= 0 {
		return scoring.ErrInvalidClassID
	}

	if len(studentScore.GetScores()) == 0 {
		return scoring.ErrEmptyScore
	}

	for _, score := range studentScore.GetScores() {
		err := validateScore(score)
		if err != nil {
			return err
		}
	}

	return nil
}

func validateScore(score *model.Score) error {
	if score.GetName() == "" {
		return scoring.ErrInvalidScoreName
	}

	if score.GetValueNum() <= 0 || score.GetValueText() == "" {
		return scoring.ErrInvalidScoreValue
	}

	return nil
}
