package service

import (
	"context"

	"github.com/temui-sisva/report/internal/model"
	"github.com/temui-sisva/report/internal/scoring"
)

type PGStore interface {
	NewClient(schoolID string, useTx bool) (PGStoreClient, error)
}

type PGStoreClient interface {
	Commit() error
	Rollback() error

	SetStudentScores(ctx context.Context, studentScores []*model.StudentScore) error
	GetStudentScores(ctx context.Context, filter scoring.GetStudentScoresFilter) ([]*model.StudentScore, error)
	DeleteStudentScores(ctx context.Context, studentScore *model.StudentScore) error
}
