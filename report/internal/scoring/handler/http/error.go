package http

import (
	"errors"

	"github.com/temui-sisva/report/internal/detail"
	"github.com/temui-sisva/report/internal/scoring"
)

// Followings are the known errors from User HTTP handlers.
var (
	errBadRequest         = errors.New("BAD_REQUEST")
	errInternalServer     = errors.New("INTERNAL_SERVER_ERROR")
	errMethodNotAllowed   = errors.New("METHOD_NOT_ALLOWED")
	errRequestTimeout     = errors.New("REQUEST_TIMEOUT")
	errSourceNotProvided  = errors.New("SOURCE_NOT_PROVIDED")
	errUnauthorizedAccess = errors.New("UNAUTHORIZED_ACCESS")
	errInvalidToken       = errors.New("INVALID_TOKEN")
)

var (
	// badRequestErr maps doamaint error that categorize as
	// bad request error.
	//
	// Internal server error-related should not be mapped
	// here, and the handler should just return `errInternal`
	// as the error instead
	badRequestErr = map[error]struct{}{
		scoring.ErrEmptyScore:        {},
		scoring.ErrInvalidClassID:    {},
		scoring.ErrInvalidReportID:   {},
		scoring.ErrInvalidSchoolID:   {},
		scoring.ErrInvalidScoreName:  {},
		scoring.ErrInvalidScoreValue: {},
		scoring.ErrInvalidUserID:     {},
		scoring.ErrTooManyReportID:   {},
		scoring.ErrTooManyRequest:    {},
		detail.ErrInvalidSchoolID:    {},
	}
)
