package http

import (
	"net/url"
	"strconv"

	"github.com/google/uuid"
	"github.com/temui-sisva/report/internal/scoring"
)

func parseGetStudentScoresFilter(v url.Values) (scoring.GetStudentScoresFilter, error) {
	filter := scoring.GetStudentScoresFilter{}

	if text := v.Get("student_id"); text != "" {
		studentID, err := uuid.Parse(text)
		if err != nil {
			return scoring.GetStudentScoresFilter{}, scoring.ErrInvalidUserID
		}

		filter.StudentID = studentID
	}

	if text := v.Get("report_id"); text != "" {
		reportID, err := strconv.ParseInt(text, 10, 64)
		if err != nil {
			return scoring.GetStudentScoresFilter{}, scoring.ErrInvalidReportID
		}

		filter.ReportID = reportID
	}

	if text := v.Get("class_id"); text != "" {
		classID, err := strconv.ParseInt(text, 10, 64)
		if err != nil {
			return scoring.GetStudentScoresFilter{}, scoring.ErrInvalidClassID
		}

		filter.ClassID = classID
	}

	return filter, nil
}

func parseGetStudentScoreOption(v url.Values) scoring.GetStudentScoreOption {
	opt := scoring.GetStudentScoreOption{}

	if text := v.Get("with_student_detail"); text != "" {
		v, _ := strconv.ParseBool(text)
		opt.WithStudentDetail = v
	}

	if text := v.Get("with_class_detail"); text != "" {
		v, _ := strconv.ParseBool(text)
		opt.WithClassDetail = v
	}

	return opt
}
