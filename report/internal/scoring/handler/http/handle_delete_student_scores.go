package http

import (
	"context"
	"encoding/json"
	"io"
	"log"
	"net/http"

	contextlib "github.com/temui-sisva/go-lib/context"
	httplib "github.com/temui-sisva/go-lib/http"
	"github.com/temui-sisva/report/internal/model"
	"github.com/temui-sisva/report/internal/scoring"
)

func (h *studentScoresHandler) handleDeleteStudentScores(w http.ResponseWriter, r *http.Request) {
	// add timeout to context
	timeout := h.scopeSettings[ScopeDeleteStudentScores].Timeout
	ctx, cancel := context.WithTimeout(r.Context(), timeout)
	defer cancel()

	var (
		err        error           // stores error in this handler
		source     string          // stores request source
		resBody    []byte          // stores response body to write
		statusCode = http.StatusOK // stores response status code
	)

	// write response
	defer func() {
		// error
		if err != nil {
			log.Printf("[Scoring HTTP][handleDeleteStudentScores] Failed to set student scores. Source: %s, Err: %s\n", source, err.Error())
			httplib.WriteErrorResponse(w, statusCode, []string{err.Error()})
			return
		}
		// success
		httplib.WriteResponse(w, resBody, statusCode, httplib.JSONContentTypeDecorator)
	}()

	// prepare channels for main go routine
	resChan := make(chan *model.StudentScore, 1)
	errChan := make(chan error, 1)

	go func() {
		source, err = httplib.GetSourceFromHeader(r)
		if err != nil {
			statusCode = http.StatusBadRequest
			errChan <- errSourceNotProvided
			return
		}
		ctx = contextlib.SetSource(ctx, source)

		schoolID, err := httplib.GetSchoolIDFromHeader(r)
		if err != nil {
			statusCode = http.StatusBadRequest
			errChan <- scoring.ErrInvalidSchoolID
			return
		}
		ctx = contextlib.SetSchoolID(ctx, schoolID)

		reqUserID, err := httplib.GetUserIDFromHeader(r)
		if err != nil {
			statusCode = http.StatusBadRequest
			errChan <- scoring.ErrInvalidUserID
			return
		}
		ctx = contextlib.SetUserID(ctx, reqUserID)

		token, err := httplib.GetBearerTokenFromHeader(r)
		if err != nil {
			statusCode = http.StatusBadRequest
			errChan <- errInvalidToken
			return
		}

		body, err := io.ReadAll(r.Body)
		if err != nil {
			statusCode = http.StatusBadRequest
			errChan <- errBadRequest
			return
		}

		studentScore := &model.StudentScore{}
		err = json.Unmarshal(body, studentScore)
		if err != nil {
			statusCode = http.StatusBadRequest
			errChan <- errBadRequest
			return
		}

		err = checkAccessToken(ctx, h.tenantUserClient, token, reqUserID, schoolID, "handleDeleteStudentScores")
		if err != nil {
			statusCode = http.StatusUnauthorized
			errChan <- err
			return
		}

		// TODO: add authorization flow with roles

		err = h.scoring.DeleteStudentScores(ctx, studentScore)
		if err != nil {
			parsedErr := errInternalServer
			statusCode = http.StatusInternalServerError
			if _, ok := badRequestErr[err]; ok {
				parsedErr = err
				statusCode = http.StatusBadRequest
			}

			// log the actual error if its internal error
			if statusCode == http.StatusInternalServerError {
				log.Printf("[Scoring HTTP][handleDeleteStudentScores] Internal error from DeleteStudentScores, Err: %s\n", err.Error())
			}

			errChan <- parsedErr
			return
		}

		resChan <- studentScore
	}()

	// wait and handle main go routine
	select {
	case <-ctx.Done():
		statusCode = http.StatusGatewayTimeout
		err = errRequestTimeout
	case err = <-errChan:
	case studentScore := <-resChan:
		resBody, err = json.Marshal(httplib.ResponseEnvelope{
			Data: studentScore,
		})
	}
}
