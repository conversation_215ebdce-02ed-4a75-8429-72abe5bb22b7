package http

import (
	"net/http"

	tenantuserclient "github.com/temui-sisva/go-lib/grpcclient/tenantuser"
	httplib "github.com/temui-sisva/go-lib/http"
	"github.com/temui-sisva/report/internal/scoring"
)

type studentScoresHandler struct {
	scoring          scoring.Service
	tenantUserClient tenantuserclient.Service
	scopeSettings    map[Scope]ScopeSetting
}

func (h *studentScoresHandler) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	switch r.Method {
	case http.MethodPut:
		h.handleSetStudentScores(w, r)
	case http.MethodGet:
		h.handleGetStudentScores(w, r)
	case http.MethodDelete:
		h.handleDeleteStudentScores(w, r)
	default:
		httplib.WriteErrorResponse(w, http.StatusMethodNotAllowed, []string{errMethodNotAllowed.Error()})
	}
}
