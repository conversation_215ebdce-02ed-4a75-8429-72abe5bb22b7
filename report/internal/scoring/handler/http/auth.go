package http

import (
	"context"
	"log"

	tenantuserclient "github.com/temui-sisva/go-lib/grpcclient/tenantuser"
	"github.com/temui-sisva/report/internal/scoring"
)

func checkAccessToken(ctx context.Context, client tenantuserclient.Service, token, userID, schoolID, name string) error {
	tokenData, err := client.ValidateToken(ctx, token)
	if err != nil {
		log.Printf("[Scoring HTTP][%s] Unauthorized error from ValidateToken. Err: %s\n", name, err.Error())
		return errUnauthorizedAccess
	}

	if userID != tokenData.UserID {
		return scoring.ErrInvalidUserID
	}

	if schoolID != tokenData.SchoolID {
		return scoring.ErrInvalidSchoolID
	}

	return nil
}
