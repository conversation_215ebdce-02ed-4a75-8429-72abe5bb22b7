package model

import (
	"time"

	"github.com/google/uuid"
)

type Template struct {
	ID                 *int64         `json:"id,omitempty" db:"id"`
	Name               *string        `json:"name,omitempty" db:"name"`
	PDFGeneratorFnText *string        `json:"pdf_generator_fn_text,omitempty" db:"pdf_generator_fn_text"`
	Placeholders       []*Placeholder `json:"placeholders,omitempty"`
	RefURL             *string        `json:"ref_url,omitempty" db:"ref_url"`
	Description        *string        `json:"description,omitempty" db:"description"`

	// metadata
	CreateBy   *uuid.UUID `json:"-" db:"create_by"`
	CreateTime *time.Time `json:"-" db:"create_time"`
	UpdateBy   *uuid.UUID `json:"-" db:"update_by"`
	UpdateTime *time.Time `json:"-" db:"update_time"`
}

func (t *Template) GetID() int64 {
	if t != nil && t.ID != nil {
		return *t.ID
	}
	return 0
}

func (t *Template) GetName() string {
	if t != nil && t.Name != nil {
		return *t.Name
	}
	return ""
}

func (t *Template) GetPDFGeneratorFnText() string {
	if t != nil && t.PDFGeneratorFnText != nil {
		return *t.PDFGeneratorFnText
	}
	return ""
}

func (t *Template) GetPlaceholders() []*Placeholder {
	if t != nil && t.Placeholders != nil {
		return t.Placeholders
	}
	return nil
}

func (t *Template) GetRefURL() string {
	if t != nil && t.RefURL != nil {
		return *t.RefURL
	}
	return ""
}

func (t *Template) GetDescription() string {
	if t != nil && t.Description != nil {
		return *t.Description
	}
	return ""
}

func (t *Template) GetCreateBy() uuid.UUID {
	if t != nil && t.CreateBy != nil {
		return *t.CreateBy
	}
	return uuid.Nil
}

func (t *Template) GetCreateTime() time.Time {
	if t != nil && t.CreateTime != nil {
		return *t.CreateTime
	}
	return time.Time{}
}

func (t *Template) GetUpdateBy() uuid.UUID {
	if t != nil && t.UpdateBy != nil {
		return *t.UpdateBy
	}
	return uuid.Nil
}

func (t *Template) GetUpdateTime() time.Time {
	if t != nil && t.UpdateTime != nil {
		return *t.UpdateTime
	}
	return time.Time{}
}
