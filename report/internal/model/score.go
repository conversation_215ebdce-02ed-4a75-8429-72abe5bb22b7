package model

type Score struct {
	Name      *string  `json:"name,omitempty"`
	ValueNum  *float64 `json:"value_num,omitempty"`
	ValueText *string  `json:"value_text,omitempty"`
}

func (s *Score) GetName() string {
	if s != nil && s.Name != nil {
		return *s.Name
	}
	return ""
}

func (s *Score) GetValueNum() float64 {
	if s != nil && s.ValueNum != nil {
		return *s.ValueNum
	}
	return 0
}

func (s *Score) GetValueText() string {
	if s != nil && s.ValueText != nil {
		return *s.ValueText
	}
	return ""
}
