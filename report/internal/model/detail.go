package model

import "github.com/google/uuid"

type PeriodDetail struct {
	Name *string `json:"name,omitempty"`
}

func (d *PeriodDetail) GetName() string {
	if d != nil && d.Name != nil {
		return *d.Name
	}
	return ""
}

type StudentDetail struct {
	ID   *uuid.UUID `json:"id,omitempty" db:"id"`
	Name *string    `json:"name,omitempty" db:"name"`
	NIK  *string    `json:"nik,omitempty" db:"nik"`
}

func (d *StudentDetail) GetID() uuid.UUID {
	if d != nil && d.ID != nil {
		return *d.ID
	}
	return uuid.Nil
}

func (d *StudentDetail) GetName() string {
	if d != nil && d.Name != nil {
		return *d.Name
	}
	return ""
}

func (d *StudentDetail) GetNIK() string {
	if d != nil && d.NIK != nil {
		return *d.NIK
	}
	return ""
}

type StudentGroupDetail struct {
	ID                  *int64  `json:"id,omitempty" db:"id"`
	Name                *string `json:"name,omitempty" db:"name"`
	StudyProgramName    *string `json:"study_program_name,omitempty" db:"study_program_name"`
	Grade               *string `json:"grade,omitempty" db:"grade"`
	HomeroomTeacherName *string `json:"homeroom_teacher_name,omitempty" db:"homeroom_teacher_name"`
	HomeroomTeacherNIK  *string `json:"homeroom_teacher_nik,omitempty" db:"homeroom_teacher_nik"`
}

func (d *StudentGroupDetail) GetID() int64 {
	if d != nil && d.ID != nil {
		return *d.ID
	}
	return 0
}

func (d *StudentGroupDetail) GetName() string {
	if d != nil && d.Name != nil {
		return *d.Name
	}
	return ""
}

func (d *StudentGroupDetail) GetStudyProgramName() string {
	if d != nil && d.StudyProgramName != nil {
		return *d.StudyProgramName
	}
	return ""
}

func (d *StudentGroupDetail) GetHomeroomTeacherName() string {
	if d != nil && d.HomeroomTeacherName != nil {
		return *d.HomeroomTeacherName
	}
	return ""
}

func (d *StudentGroupDetail) GetHomeroomTeacherNIK() string {
	if d != nil && d.HomeroomTeacherNIK != nil {
		return *d.HomeroomTeacherNIK
	}
	return ""
}

type ClassDetail struct {
	ID          *int64  `json:"id,omitempty" db:"id"`
	SubjectName *string `json:"subject_name,omitempty" db:"subject_name"`
	TeacherName *string `json:"teacher_name,omitempty" db:"teacher_name"`
}

func (d *ClassDetail) GetID() int64 {
	if d != nil && d.ID != nil {
		return *d.ID
	}
	return 0
}

func (d *ClassDetail) GetSubjectName() string {
	if d != nil && d.SubjectName != nil {
		return *d.SubjectName
	}
	return ""
}

func (d *ClassDetail) GetTeacherName() string {
	if d != nil && d.TeacherName != nil {
		return *d.TeacherName
	}
	return ""
}
