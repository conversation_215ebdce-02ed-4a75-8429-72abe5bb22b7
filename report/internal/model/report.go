package model

import (
	"time"

	"github.com/google/uuid"
)

type ReportStatus int

const (
	ReportStatusUnknown   ReportStatus = 0
	ReportStatusPublished ReportStatus = 1
	ReportStatusDraft     ReportStatus = 2
	ReportStatusFilling   ReportStatus = 3
)

var (
	ReportStatusList = map[ReportStatus]struct{}{
		ReportStatusPublished: {},
		ReportStatusDraft:     {},
		ReportStatusFilling:   {},
	}

	reportStatusName = map[ReportStatus]string{
		ReportStatusPublished: "published",
		ReportStatusDraft:     "draft",
		ReportStatusFilling:   "filling",
	}
)

func (rs ReportStatus) Value() int {
	return int(rs)
}

func (rs ReportStatus) String() string {
	return reportStatusName[rs]
}

type Report struct {
	ID             *int64        `json:"id,omitempty" db:"id"`
	Name           *string       `json:"name,omitempty" db:"name"`
	TemplateID     *int64        `json:"template_id,omitempty" db:"template_id"`
	PeriodID       *int64        `json:"period_id,omitempty" db:"period_id"`
	StudyProgramID *int64        `json:"study_program_id,omitempty" db:"study_program_id"`
	Grade          *string       `json:"grade,omitempty" db:"grade"`
	Status         *ReportStatus `json:"-" db:"status"`

	// Detail
	PeriodDetail *PeriodDetail `json:"period_detail"`

	// metadata
	CreateBy   *uuid.UUID `json:"-" db:"create_by"`
	CreateTime *time.Time `json:"-" db:"create_time"`
	UpdateBy   *uuid.UUID `json:"-" db:"update_by"`
	UpdateTime *time.Time `json:"-" db:"update_time"`
}

func (r *Report) GetID() int64 {
	if r != nil && r.ID != nil {
		return *r.ID
	}
	return 0
}

func (r *Report) GetName() string {
	if r != nil && r.Name != nil {
		return *r.Name
	}
	return ""
}

func (r *Report) GetTemplateID() int64 {
	if r != nil && r.TemplateID != nil {
		return *r.TemplateID
	}
	return 0
}

func (r *Report) GetPeriodID() int64 {
	if r != nil && r.PeriodID != nil {
		return *r.PeriodID
	}
	return 0
}

func (r *Report) GetStudyProgramID() int64 {
	if r != nil && r.StudyProgramID != nil {
		return *r.StudyProgramID
	}
	return 0
}

func (r *Report) GetGrade() string {
	if r != nil && r.Grade != nil {
		return *r.Grade
	}
	return ""
}

func (r *Report) GetStatus() ReportStatus {
	if r != nil && r.Status != nil {
		return *r.Status
	}
	return ReportStatusUnknown
}

func (r *Report) GetPeriodDetail() *PeriodDetail {
	if r != nil {
		return r.PeriodDetail
	}
	return nil
}

func (r *Report) GetCreateBy() uuid.UUID {
	if r != nil && r.CreateBy != nil {
		return *r.CreateBy
	}
	return uuid.Nil
}

func (r *Report) GetCreateTime() time.Time {
	if r != nil && r.CreateTime != nil {
		return *r.CreateTime
	}
	return time.Time{}
}

func (r *Report) GetUpdateBy() uuid.UUID {
	if r != nil && r.UpdateBy != nil {
		return *r.UpdateBy
	}
	return uuid.Nil
}

func (r *Report) GetUpdateTime() time.Time {
	if r != nil && r.UpdateTime != nil {
		return *r.UpdateTime
	}
	return time.Time{}
}
