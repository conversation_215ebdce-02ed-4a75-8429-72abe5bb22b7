package model

import (
	"time"

	"github.com/google/uuid"
)

type StudentScore struct {
	StudentID *uuid.UUID `json:"student_id,omitempty" db:"student_id"`
	ReportID  *int64     `json:"report_id,omitempty" db:"report_id"`
	ClassID   *int64     `json:"class_id,omitempty" db:"class_id"`
	Scores    []*Score   `json:"scores,omitempty"`

	// detail
	StudentDetail *StudentDetail `json:"student_detail,omitempty"`
	ClassDetail   *ClassDetail   `json:"class_detail,omitempty"`

	// metadata
	CreateBy   *uuid.UUID `json:"-" db:"create_by"`
	CreateTime *time.Time `json:"-" db:"create_time"`
	UpdateBy   *uuid.UUID `json:"-" db:"update_by"`
	UpdateTime *time.Time `json:"-" db:"update_time"`
}

func (ss *StudentScore) GetStudentID() uuid.UUID {
	if ss != nil && ss.StudentID != nil {
		return *ss.StudentID
	}
	return uuid.Nil
}

func (ss *StudentScore) GetReportID() int64 {
	if ss != nil && ss.ReportID != nil {
		return *ss.ReportID
	}
	return 0
}

func (ss *StudentScore) GetClassID() int64 {
	if ss != nil && ss.ClassID != nil {
		return *ss.ClassID
	}
	return 0
}

func (ss *StudentScore) GetScores() []*Score {
	if ss != nil && ss.Scores != nil {
		return ss.Scores
	}
	return nil
}

func (ss *StudentScore) GetStudentDetail() *StudentDetail {
	if ss != nil {
		return ss.StudentDetail
	}
	return nil
}

func (ss *StudentScore) GetClassDetail() *ClassDetail {
	if ss != nil {
		return ss.ClassDetail
	}
	return nil
}

func (ss *StudentScore) GetCreateBy() uuid.UUID {
	if ss != nil && ss.CreateBy != nil {
		return *ss.CreateBy
	}
	return uuid.Nil
}

func (ss *StudentScore) GetCreateTime() time.Time {
	if ss != nil && ss.CreateTime != nil {
		return *ss.CreateTime
	}
	return time.Time{}
}

func (ss *StudentScore) GetUpdateBy() uuid.UUID {
	if ss != nil && ss.UpdateBy != nil {
		return *ss.UpdateBy
	}
	return uuid.Nil
}

func (ss *StudentScore) GetUpdateTime() time.Time {
	if ss != nil && ss.UpdateTime != nil {
		return *ss.UpdateTime
	}
	return time.Time{}
}
