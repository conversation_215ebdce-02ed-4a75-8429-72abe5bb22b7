package postgresql

import (
	"context"

	"github.com/google/uuid"

	"github.com/temui-sisva/report/internal/model"
)

func (sc *storeClient) GetStudentDetailsByIDs(ctx context.Context, ids []uuid.UUID) (map[uuid.UUID]*model.StudentDetail, error) {
	rows, err := sc.q.Queryx(queryGetStudentDetails, ids)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	res := make(map[uuid.UUID]*model.StudentDetail)
	for rows.Next() {
		d := &model.StudentDetail{}
		err = rows.StructScan(d)
		if err != nil {
			return nil, err
		}

		res[d.GetID()] = d
	}

	if err := rows.Err(); err != nil {
		return nil, err
	}

	return res, nil
}

func (sc *storeClient) GetClassDetailsByIDs(ctx context.Context, ids []int64) (map[int64]*model.ClassDetail, error) {
	rows, err := sc.q.Queryx(queryGetClassDetails, ids)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	res := make(map[int64]*model.ClassDetail)
	for rows.Next() {
		d := &model.ClassDetail{}
		err = rows.StructScan(d)
		if err != nil {
			return nil, err
		}

		res[d.GetID()] = d
	}

	if err := rows.Err(); err != nil {
		return nil, err
	}

	return res, nil
}
