package postgresql

import (
	"errors"

	"github.com/jmoiron/sqlx"

	pglib "github.com/temui-sisva/go-lib/postgresql"
	"github.com/temui-sisva/report/internal/detail/service"
)

var (
	errInvalidCommit   = errors.New("cannot do commit on non-transactional querier")
	errInvalidRollback = errors.New("cannot do rollback on non-transactional querier")
)

type store struct {
	cm   *pglib.ClientManager
	cKey func(string) string
}

type storeClient struct {
	q pglib.Querier
}

func New(cm *pglib.ClientManager, cKey func(string) string) *store {
	s := &store{
		cm:   cm,
		cKey: cKey,
	}

	return s
}

func (s *store) NewClient(schoolID string, useTx bool) (service.PGStoreClient, error) {
	// get database for the given school ID
	db, err := s.cm.GetDatabase(s.cKey(schoolID))
	if err != nil {
		return nil, err
	}

	// determine what object should be use as querier
	var q pglib.Querier
	q = db
	if useTx {
		q, err = db.Beginx()
		if err != nil {
			return nil, err
		}
	}

	return &storeClient{
		q: q,
	}, nil
}

func (sc *storeClient) Commit() error {
	if tx, ok := sc.q.(*sqlx.Tx); ok {
		return tx.Commit()
	}
	return errInvalidCommit
}

func (sc *storeClient) Rollback() error {
	if tx, ok := sc.q.(*sqlx.Tx); ok {
		return tx.Rollback()
	}
	return errInvalidRollback
}
