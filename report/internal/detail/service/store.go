package service

import (
	"context"

	"github.com/google/uuid"

	"github.com/temui-sisva/report/internal/model"
)

type PGStore interface {
	NewClient(schoolID string, useTx bool) (PGStoreClient, error)
}

type PGStoreClient interface {
	Commit() error
	Rollback() error

	GetStudentDetailsByIDs(ctx context.Context, ids []uuid.UUID) (map[uuid.UUID]*model.StudentDetail, error)
	GetClassDetailsByIDs(ctx context.Context, ids []int64) (map[int64]*model.ClassDetail, error)
}
