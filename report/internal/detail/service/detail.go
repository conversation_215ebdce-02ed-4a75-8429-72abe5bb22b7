package service

import (
	"context"

	"github.com/google/uuid"

	contextlib "github.com/temui-sisva/go-lib/context"
	"github.com/temui-sisva/report/internal/detail"
	"github.com/temui-sisva/report/internal/model"
)

func (s *service) GetStudentDetailsByIDs(ctx context.Context, ids []uuid.UUID) (map[uuid.UUID]*model.StudentDetail, error) {
	if len(ids) == 0 {
		return nil, nil
	}

	mUniqueIDs := make(map[uuid.UUID]struct{})
	for _, id := range ids {
		if _, ok := mUniqueIDs[id]; ok {
			continue
		}
		mUniqueIDs[id] = struct{}{}
	}

	uniqueIDs := make([]uuid.UUID, 0)
	for id := range mUniqueIDs {
		uniqueIDs = append(uniqueIDs, id)
	}

	schoolID, ok := contextlib.GetSchoolID(ctx)
	if !ok || schoolID == "" {
		return nil, detail.ErrInvalidSchoolID
	}

	sc, err := s.pgStore.NewClient(schoolID, false)
	if err != nil {
		return nil, err
	}

	res, err := sc.GetStudentDetailsByIDs(ctx, uniqueIDs)
	if err != nil {
		return nil, err
	}

	return res, nil
}

func (s *service) GetClassDetailsByIDs(ctx context.Context, ids []int64) (map[int64]*model.ClassDetail, error) {
	if len(ids) == 0 {
		return nil, nil
	}

	mUniqueIDs := make(map[int64]struct{})
	for _, id := range ids {
		if _, ok := mUniqueIDs[id]; ok {
			continue
		}
		mUniqueIDs[id] = struct{}{}
	}

	uniqueIDs := make([]int64, 0)
	for id := range mUniqueIDs {
		uniqueIDs = append(uniqueIDs, id)
	}

	schoolID, ok := contextlib.GetSchoolID(ctx)
	if !ok || schoolID == "" {
		return nil, detail.ErrInvalidSchoolID
	}

	sc, err := s.pgStore.NewClient(schoolID, false)
	if err != nil {
		return nil, err
	}

	res, err := sc.GetClassDetailsByIDs(ctx, uniqueIDs)
	if err != nil {
		return nil, err
	}

	return res, nil
}
