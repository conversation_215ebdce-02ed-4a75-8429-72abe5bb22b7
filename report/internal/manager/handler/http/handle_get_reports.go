package http

import (
	"context"
	"encoding/json"
	"log"
	"net/http"

	contextlib "github.com/temui-sisva/go-lib/context"
	httplib "github.com/temui-sisva/go-lib/http"
	"github.com/temui-sisva/report/internal/manager"
)

func (h *reportsHandler) handleGetReports(w http.ResponseWriter, r *http.Request) {
	// add timeout to context
	timeout := h.scopeSettings[ScopeGetReports].Timeout
	ctx, cancel := context.WithTimeout(r.Context(), timeout)
	defer cancel()

	var (
		err        error           // stores error in this handler
		source     string          // stores request source
		resBody    []byte          // stores response body to write
		statusCode = http.StatusOK // stores response status code
	)

	// write response
	defer func() {
		// error
		if err != nil {
			log.Printf("[Manager HTTP][handleGetReports] Failed to get reports. Source: %s, Err: %s\n", source, err.Error())
			httplib.WriteErrorResponse(w, statusCode, []string{err.Error()})
			return
		}
		// success
		httplib.WriteResponse(w, resBody, statusCode, httplib.JSONContentTypeDecorator)
	}()

	// prepare channels for main go routine
	resChan := make(chan []*reportHTTP, 1)
	errChan := make(chan error, 1)

	go func() {
		source, err = httplib.GetSourceFromHeader(r)
		if err != nil {
			statusCode = http.StatusBadRequest
			errChan <- errSourceNotProvided
			return
		}
		ctx = contextlib.SetSource(ctx, source)

		schoolID, err := httplib.GetSchoolIDFromHeader(r)
		if err != nil {
			statusCode = http.StatusBadRequest
			errChan <- manager.ErrInvalidSchoolID
			return
		}
		ctx = contextlib.SetSchoolID(ctx, schoolID)

		reqUserID, err := httplib.GetUserIDFromHeader(r)
		if err != nil {
			statusCode = http.StatusBadRequest
			errChan <- manager.ErrInvalidUserID
			return
		}
		ctx = contextlib.SetUserID(ctx, reqUserID)

		token, err := httplib.GetBearerTokenFromHeader(r)
		if err != nil {
			statusCode = http.StatusBadRequest
			errChan <- errInvalidToken
			return
		}

		err = checkAccessToken(ctx, h.tenantUserClient, token, reqUserID, schoolID, "handleGetReports")
		if err != nil {
			statusCode = http.StatusUnauthorized
			errChan <- err
			return
		}

		// TODO: add authorization flow with roles

		opt := parseGetReportOption(r.URL.Query())
		reports, err := h.manager.GetReports(ctx, opt)
		if err != nil {
			parsedErr := errInternalServer
			statusCode = http.StatusInternalServerError
			if _, ok := badRequestErr[err]; ok {
				parsedErr = err
				statusCode = http.StatusBadRequest
			}

			// log the actual error if its internal error
			if statusCode == http.StatusInternalServerError {
				log.Printf("[Manager HTTP][handleGetReports] Internal error from GetReports. Err: %s\n", err.Error())
			}

			errChan <- parsedErr
			return
		}

		var res []*reportHTTP
		for _, v := range reports {
			e := formatReport(v)
			if e != nil {
				res = append(res, e)
			}
		}

		resChan <- res
	}()

	// wait and handle main go routine
	select {
	case <-ctx.Done():
		statusCode = http.StatusGatewayTimeout
		err = errRequestTimeout
	case err = <-errChan:
	case res := <-resChan:
		resBody, err = json.Marshal(httplib.ResponseEnvelope{
			Data: res,
		})
	}
}
