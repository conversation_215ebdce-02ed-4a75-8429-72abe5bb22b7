package http

import (
	"net/url"
	"strconv"

	"github.com/google/uuid"

	"github.com/temui-sisva/report/internal/manager"
	"github.com/temui-sisva/report/internal/model"
	"github.com/temui-sisva/report/internal/tool/ptr"
)

type templateHTTP struct {
	// from request body
	model.Template

	// from header (add manually)
	CreateByText *string `json:"-"`
	UpdateByText *string `json:"-"`
}

func formatTemplate(t *model.Template) *templateHTTP {
	if t == nil {
		return nil
	}

	return &templateHTTP{
		Template: *t,
	}
}

func (t *templateHTTP) parseTemplate() (*model.Template, error) {
	if t == nil {
		return nil, nil
	}

	template := t.Template

	if t.CreateByText != nil {
		createBy, err := uuid.Parse(*t.CreateByText)
		if err != nil {
			return nil, manager.ErrInvalidUserID
		}

		template.CreateBy = &createBy
	}

	if t.UpdateByText != nil {
		updateBy, err := uuid.Parse(*t.UpdateByText)
		if err != nil {
			return nil, manager.ErrInvalidUserID
		}

		template.UpdateBy = &updateBy
	}

	return &template, nil
}

type reportHTTP struct {
	// from request body
	model.Report
	StatusText *string `json:"status"`

	// from header (add manually)
	CreateByText *string `json:"-"`
	UpdateByText *string `json:"-"`
}

func formatReport(r *model.Report) *reportHTTP {
	if r == nil {
		return nil
	}

	return &reportHTTP{
		Report:     *r,
		StatusText: ptr.StrPtr(r.Status.String()),
	}
}

func (r *reportHTTP) parseReport() (*model.Report, error) {
	if r == nil {
		return nil, nil
	}

	report := r.Report

	if r.StatusText != nil {
		reportStatus, err := parseReportStatus(*r.StatusText)
		if err != nil {
			return nil, manager.ErrInvalidReportStatus
		}

		report.Status = &reportStatus
	}

	if r.CreateByText != nil {
		createBy, err := uuid.Parse(*r.CreateByText)
		if err != nil {
			return nil, manager.ErrInvalidUserID
		}

		report.CreateBy = &createBy
	}

	if r.UpdateByText != nil {
		updateBy, err := uuid.Parse(*r.UpdateByText)
		if err != nil {
			return nil, manager.ErrInvalidUserID
		}

		report.UpdateBy = &updateBy
	}

	return &report, nil
}

func parseReportStatus(req string) (model.ReportStatus, error) {
	switch req {
	case model.ReportStatusPublished.String():
		return model.ReportStatusPublished, nil
	case model.ReportStatusDraft.String():
		return model.ReportStatusDraft, nil
	case model.ReportStatusFilling.String():
		return model.ReportStatusFilling, nil
	}

	return model.ReportStatusUnknown, manager.ErrInvalidReportStatus
}

func parseGetReportOption(v url.Values) manager.GetReportOption {
	opt := manager.GetReportOption{}

	if text := v.Get("with_period_detail"); text != "" {
		v, _ := strconv.ParseBool(text)
		opt.WithPeriodDetail = v
	}

	return opt
}
