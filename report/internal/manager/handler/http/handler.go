package http

import (
	"log"
	"net/http"
	"strconv"

	"github.com/gorilla/mux"

	tenantuserclient "github.com/temui-sisva/go-lib/grpcclient/tenantuser"
	httplib "github.com/temui-sisva/go-lib/http"
	"github.com/temui-sisva/report/internal/manager"
)

type templatesHandler struct {
	manager          manager.Service
	tenantUserClient tenantuserclient.Service
	scopeSettings    map[Scope]ScopeSetting
}

func (h *templatesHandler) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	switch r.Method {
	case http.MethodPost:
		h.handleCreateTemplate(w, r)
	case http.MethodGet:
		h.handleGetTemplates(w, r)
	default:
		httplib.WriteErrorResponse(w, http.StatusMethodNotAllowed, []string{errMethodNotAllowed.Error()})
	}
}

type templateHandler struct {
	manager          manager.Service
	tenantUserClient tenantuserclient.Service
	scopeSettings    map[Scope]ScopeSetting
}

func (h *templateHandler) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)

	templateID, err := strconv.ParseInt(vars["id"], 10, 64)
	if err != nil {
		log.Printf("[Manager HTTP][templateHandler] Failed to parse template ID. ID: %s. Err: %s\n", vars["id"], err.Error())
		httplib.WriteErrorResponse(w, http.StatusBadRequest, []string{manager.ErrInvalidTemplateID.Error()})
		return
	}

	switch r.Method {
	case http.MethodGet:
		h.handleGetTemplateByID(w, r, templateID)
	case http.MethodPatch:
		h.handleUpdateTemplate(w, r, templateID)
	case http.MethodDelete:
		h.handleDeleteTemplateByID(w, r, templateID)
	default:
		httplib.WriteErrorResponse(w, http.StatusMethodNotAllowed, []string{errMethodNotAllowed.Error()})
	}
}

type reportsHandler struct {
	manager          manager.Service
	tenantUserClient tenantuserclient.Service
	scopeSettings    map[Scope]ScopeSetting
}

func (h *reportsHandler) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	switch r.Method {
	case http.MethodPost:
		h.handleCreateReport(w, r)
	case http.MethodGet:
		h.handleGetReports(w, r)
	default:
		httplib.WriteErrorResponse(w, http.StatusMethodNotAllowed, []string{errMethodNotAllowed.Error()})
	}
}

type reportHandler struct {
	manager          manager.Service
	tenantUserClient tenantuserclient.Service
	scopeSettings    map[Scope]ScopeSetting
}

func (h *reportHandler) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)

	reportID, err := strconv.ParseInt(vars["id"], 10, 64)
	if err != nil {
		log.Printf("[Manager HTTP][reportHandler] Failed to parse report ID. ID: %s. Err: %s\n", vars["id"], err.Error())
		httplib.WriteErrorResponse(w, http.StatusBadRequest, []string{manager.ErrInvalidReportID.Error()})
		return
	}

	switch r.Method {
	case http.MethodGet:
		h.handleGetReportByID(w, r, reportID)
	case http.MethodPatch:
		h.handleUpdateReport(w, r, reportID)
	case http.MethodDelete:
		h.handleDeleteReportByID(w, r, reportID)
	default:
		httplib.WriteErrorResponse(w, http.StatusMethodNotAllowed, []string{errMethodNotAllowed.Error()})
	}
}
