package http

import (
	"context"
	"log"

	tenantuserclient "github.com/temui-sisva/go-lib/grpcclient/tenantuser"
	"github.com/temui-sisva/report/internal/manager"
)

func checkAccessToken(ctx context.Context, client tenantuserclient.Service, token, userID, schoolID, name string) error {
	tokenData, err := client.ValidateToken(ctx, token)
	if err != nil {
		log.Printf("[Manager HTTP][%s] Unauthorized error from ValidateToken. Err: %s\n", name, err.Error())
		return errUnauthorizedAccess
	}

	if userID != tokenData.UserID {
		return manager.ErrInvalidUserID
	}

	if schoolID != tokenData.SchoolID {
		return manager.ErrInvalidSchoolID
	}

	return nil
}
