package http

import (
	"context"
	"encoding/json"
	"log"
	"net/http"

	contextlib "github.com/temui-sisva/go-lib/context"
	httplib "github.com/temui-sisva/go-lib/http"
	"github.com/temui-sisva/report/internal/manager"
)

func (h *templateHandler) handleGetTemplateByID(w http.ResponseWriter, r *http.Request, templateID int64) {
	// add timeout to context
	timeout := h.scopeSettings[ScopeGetTemplateByID].Timeout
	ctx, cancel := context.WithTimeout(r.Context(), timeout)
	defer cancel()

	var (
		err        error           // stores error in this handler
		source     string          // stores request source
		resBody    []byte          // stores response body to write
		statusCode = http.StatusOK // stores response status code
	)

	// write response
	defer func() {
		// error
		if err != nil {
			log.Printf("[Manager HTTP][handleGetTemplateByID] Failed to get template. Template ID: %d, Source: %s, Err: %s\n", templateID, source, err.Error())
			httplib.WriteErrorResponse(w, statusCode, []string{err.Error()})
			return
		}
		// success
		httplib.WriteResponse(w, resBody, statusCode, httplib.JSONContentTypeDecorator)
	}()

	// prepare channels for main go routine
	resChan := make(chan *templateHTTP, 1)
	errChan := make(chan error, 1)

	go func() {
		source, err = httplib.GetSourceFromHeader(r)
		if err != nil {
			statusCode = http.StatusBadRequest
			errChan <- errSourceNotProvided
			return
		}
		ctx = contextlib.SetSource(ctx, source)

		schoolID, err := httplib.GetSchoolIDFromHeader(r)
		if err != nil {
			statusCode = http.StatusBadRequest
			errChan <- manager.ErrInvalidSchoolID
			return
		}
		ctx = contextlib.SetSchoolID(ctx, schoolID)

		reqUserID, err := httplib.GetUserIDFromHeader(r)
		if err != nil {
			statusCode = http.StatusBadRequest
			errChan <- manager.ErrInvalidUserID
			return
		}
		ctx = contextlib.SetUserID(ctx, reqUserID)

		token, err := httplib.GetBearerTokenFromHeader(r)
		if err != nil {
			statusCode = http.StatusBadRequest
			errChan <- errInvalidToken
			return
		}

		err = checkAccessToken(ctx, h.tenantUserClient, token, reqUserID, schoolID, "handleGetTemplateByID")
		if err != nil {
			statusCode = http.StatusUnauthorized
			errChan <- err
			return
		}

		// TODO: add authorization flow with roles

		template, err := h.manager.GetTemplateByID(ctx, templateID)
		if err != nil {
			parsedErr := errInternalServer
			statusCode = http.StatusInternalServerError
			if _, ok := badRequestErr[err]; ok {
				parsedErr = err
				statusCode = http.StatusBadRequest
			}

			// log the actual error if its internal error
			if statusCode == http.StatusInternalServerError {
				log.Printf("[Manager HTTP][handleGetTemplateByID] Internal error from GetTemplateByID. Template ID: %d, Err: %s\n", templateID, err.Error())
			}

			errChan <- parsedErr
			return
		}

		resChan <- formatTemplate(template)
	}()

	// wait and handle main go routine
	select {
	case <-ctx.Done():
		statusCode = http.StatusGatewayTimeout
		err = errRequestTimeout
	case err = <-errChan:
	case res := <-resChan:
		resBody, err = json.Marshal(httplib.ResponseEnvelope{
			Data: res,
		})
	}
}
