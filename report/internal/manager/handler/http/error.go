package http

import (
	"errors"

	"github.com/temui-sisva/report/internal/detail"
	"github.com/temui-sisva/report/internal/manager"
)

// Followings are the known errors from User HTTP handlers.
var (
	errBadRequest         = errors.New("BAD_REQUEST")
	errInternalServer     = errors.New("INTERNAL_SERVER_ERROR")
	errMethodNotAllowed   = errors.New("METHOD_NOT_ALLOWED")
	errRequestTimeout     = errors.New("REQUEST_TIMEOUT")
	errSourceNotProvided  = errors.New("SOURCE_NOT_PROVIDED")
	errUnauthorizedAccess = errors.New("UNAUTHORIZED_ACCESS")
	errInvalidToken       = errors.New("INVALID_TOKEN")
)

var (
	// badRequestErr maps doamaint error that categorize as
	// bad request error.
	//
	// Internal server error-related should not be mapped
	// here, and the handler should just return `errInternal`
	// as the error instead
	badRequestErr = map[error]struct{}{
		manager.ErrDataNotFound:          {},
		manager.ErrEmptyRequest:          {},
		manager.ErrInvalidGrade:          {},
		manager.ErrInvalidPeriodID:       {},
		manager.ErrInvalidReportID:       {},
		manager.ErrInvalidReportName:     {},
		manager.ErrInvalidReportStatus:   {},
		manager.ErrInvalidTemplateID:     {},
		manager.ErrInvalidTemplateName:   {},
		manager.ErrInvalidSchoolID:       {},
		manager.ErrInvalidStudyProgramID: {},
		manager.ErrInvalidUserID:         {},
		detail.ErrInvalidSchoolID:        {},
	}
)
