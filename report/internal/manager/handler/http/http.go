package http

import (
	"errors"
	"net/http"
	"time"

	"github.com/gorilla/mux"

	tenantuserclient "github.com/temui-sisva/go-lib/grpcclient/tenantuser"
	"github.com/temui-sisva/report/internal/manager"
)

var (
	errUnknownScope  = errors.New("unknown scope name")
	errUnknownConfig = errors.New("unknown config name")
)

type Handler struct {
	handlers         []*handler
	manager          manager.Service
	tenantUserClient tenantuserclient.Service
	scopeSettings    map[Scope]ScopeSetting
}

// handler is the HTTP handler wrapper.
type handler struct {
	h        http.Handler
	identity HandlerIdentity
}

type HandlerIdentity struct {
	Name string
	URL  string
}

var (
	HandlerTemplates = HandlerIdentity{
		Name: "templates",
		URL:  "/v1/templates",
	}

	HandlerTemplate = HandlerIdentity{
		Name: "template",
		URL:  "/v1/templates/{id}",
	}

	HandlerReports = HandlerIdentity{
		Name: "reports",
		URL:  "/v1/reports",
	}

	HandlerReport = HandlerIdentity{
		Name: "report",
		URL:  "/v1/reports/{id}",
	}
)

// Scope is a shared settings identifier.
//
// Registering a new Scope is done by adding a new Scope
// value and new entry in ScopeName and ScopeValue.
type Scope int

const (
	_ Scope = iota
	ScopeCreateTemplate
	ScopeGetTemplateByID
	ScopeGetTemplates
	ScopeUpdateTemplate
	ScopeDeleteTemplateByID
	ScopeCreateReport
	ScopeGetReportByID
	ScopeGetReports
	ScopeUpdateReport
	ScopeDeleteReportByID
)

var (
	ScopeName = map[Scope]string{
		ScopeCreateTemplate:     "CreateTemplate",
		ScopeGetTemplateByID:    "GetTemplateByID",
		ScopeGetTemplates:       "GetTemplates",
		ScopeUpdateTemplate:     "UpdateTemplate",
		ScopeDeleteTemplateByID: "DeleteTemplateByID",
		ScopeCreateReport:       "CreateReport",
		ScopeGetReportByID:      "GetReportByID",
		ScopeGetReports:         "GetReports",
		ScopeUpdateReport:       "UpdateReport",
		ScopeDeleteReportByID:   "DeleteReportByID",
	}

	ScopeValue = map[string]Scope{
		ScopeName[ScopeCreateTemplate]:     ScopeCreateTemplate,
		ScopeName[ScopeGetTemplateByID]:    ScopeGetTemplateByID,
		ScopeName[ScopeGetTemplates]:       ScopeGetTemplates,
		ScopeName[ScopeUpdateTemplate]:     ScopeUpdateTemplate,
		ScopeName[ScopeDeleteTemplateByID]: ScopeDeleteTemplateByID,
		ScopeName[ScopeCreateReport]:       ScopeCreateReport,
		ScopeName[ScopeGetReportByID]:      ScopeGetReportByID,
		ScopeName[ScopeGetReports]:         ScopeGetReports,
		ScopeName[ScopeUpdateReport]:       ScopeUpdateReport,
		ScopeName[ScopeDeleteReportByID]:   ScopeDeleteReportByID,
	}
)

// ScopeSetting is the available configurations of a Scope.
type ScopeSetting struct {
	Timeout time.Duration
}

// Followings are default values for ScopeSetting fields.
const (
	defaultTimeout = 5000 * time.Millisecond
)

func getDefaultScopeSettings() map[Scope]ScopeSetting {
	defaultSettings := make(map[Scope]ScopeSetting)
	for _, scope := range ScopeValue {
		defaultSettings[scope] = ScopeSetting{
			Timeout: defaultTimeout,
		}
	}
	return defaultSettings
}

// Option controls the behavior of Handler.
type Option func(*Handler) error

// WithHandler returns Option to add HTTP handler.
func WithHandler(identity HandlerIdentity) Option {
	return Option(func(h *Handler) error {
		if h.handlers == nil {
			h.handlers = []*handler{}
		}

		hndl, err := h.createHTTPHandler(identity.Name)
		if err != nil {
			return err
		}

		h.handlers = append(h.handlers, &handler{
			identity: identity,
			h:        hndl,
		})

		return nil
	})
}

// WithScopeSetting returns Option to set scope setting for
// a specific scope name.
func WithScopeSetting(scopeName string, scopeSetting ScopeSetting) Option {
	return Option(func(h *Handler) error {
		scope, ok := ScopeValue[scopeName]
		if !ok {
			return errUnknownScope
		}

		// validate setting
		if scopeSetting.Timeout <= 0 {
			scopeSetting.Timeout = defaultTimeout
		}

		h.scopeSettings[scope] = scopeSetting
		return nil
	})
}

// New creates a new Handler.
//
// For the given Option, WithScopeSetting() should come first
// before WithHandler()
func New(manager manager.Service, tenantUserClient tenantuserclient.Service, options ...Option) (*Handler, error) {
	h := &Handler{
		handlers:         make([]*handler, 0),
		manager:          manager,
		tenantUserClient: tenantUserClient,
		scopeSettings:    getDefaultScopeSettings(),
	}

	// apply options
	for _, opt := range options {
		err := opt(h)
		if err != nil {
			return nil, err
		}
	}

	return h, nil
}

// createHTTPHandler creates a new HTTP handler that
// implements http.Handler.
func (h *Handler) createHTTPHandler(configName string) (http.Handler, error) {
	var httpHandler http.Handler
	switch configName {
	case HandlerTemplates.Name:
		httpHandler = &templatesHandler{
			manager:          h.manager,
			tenantUserClient: h.tenantUserClient,
			scopeSettings:    h.scopeSettings,
		}
	case HandlerTemplate.Name:
		httpHandler = &templateHandler{
			manager:          h.manager,
			tenantUserClient: h.tenantUserClient,
			scopeSettings:    h.scopeSettings,
		}
	case HandlerReports.Name:
		httpHandler = &reportsHandler{
			manager:          h.manager,
			tenantUserClient: h.tenantUserClient,
			scopeSettings:    h.scopeSettings,
		}
	case HandlerReport.Name:
		httpHandler = &reportHandler{
			manager:          h.manager,
			tenantUserClient: h.tenantUserClient,
			scopeSettings:    h.scopeSettings,
		}
	default:
		return httpHandler, errUnknownConfig
	}

	return httpHandler, nil
}

// Start starts all HTTP handlers.
func (h *Handler) Start(multiplexer *mux.Router) error {
	for _, handler := range h.handlers {
		multiplexer.Handle(handler.identity.URL, handler.h)
	}
	return nil
}
