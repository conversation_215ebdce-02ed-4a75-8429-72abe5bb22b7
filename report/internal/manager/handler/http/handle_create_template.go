package http

import (
	"context"
	"encoding/json"
	"io"
	"log"
	"net/http"

	contextlib "github.com/temui-sisva/go-lib/context"
	httplib "github.com/temui-sisva/go-lib/http"
	"github.com/temui-sisva/report/internal/manager"
)

func (h *templatesHandler) handleCreateTemplate(w http.ResponseWriter, r *http.Request) {
	// add timeout to context
	timeout := h.scopeSettings[ScopeCreateTemplate].Timeout
	ctx, cancel := context.WithTimeout(r.Context(), timeout)
	defer cancel()

	var (
		err        error           // stores error in this handler
		source     string          // stores request source
		resBody    []byte          // stores response body to write
		statusCode = http.StatusOK // stores response status code
	)

	// write response
	defer func() {
		// error
		if err != nil {
			log.Printf("[Manager HTTP][handleCreateTemplate] Failed to create template. Source: %s, Err: %s\n", source, err.Error())
			httplib.WriteErrorResponse(w, statusCode, []string{err.Error()})
			return
		}
		// success
		httplib.WriteResponse(w, resBody, statusCode, httplib.JSONContentTypeDecorator)
	}()

	// prepare channels for main go routine
	resChan := make(chan int64, 1)
	errChan := make(chan error, 1)

	go func() {
		source, err = httplib.GetSourceFromHeader(r)
		if err != nil {
			statusCode = http.StatusBadRequest
			errChan <- errSourceNotProvided
			return
		}
		ctx = contextlib.SetSource(ctx, source)

		schoolID, err := httplib.GetSchoolIDFromHeader(r)
		if err != nil {
			statusCode = http.StatusBadRequest
			errChan <- manager.ErrInvalidSchoolID
			return
		}
		ctx = contextlib.SetSchoolID(ctx, schoolID)

		reqUserID, err := httplib.GetUserIDFromHeader(r)
		if err != nil {
			statusCode = http.StatusBadRequest
			errChan <- manager.ErrInvalidUserID
			return
		}
		ctx = contextlib.SetUserID(ctx, reqUserID)

		token, err := httplib.GetBearerTokenFromHeader(r)
		if err != nil {
			statusCode = http.StatusBadRequest
			errChan <- errInvalidToken
			return
		}

		body, err := io.ReadAll(r.Body)
		if err != nil {
			statusCode = http.StatusBadRequest
			errChan <- errBadRequest
			return
		}

		request := templateHTTP{}
		err = json.Unmarshal(body, &request)
		if err != nil {
			statusCode = http.StatusBadRequest
			errChan <- errBadRequest
			return
		}

		err = checkAccessToken(ctx, h.tenantUserClient, token, reqUserID, schoolID, "handleCreateTemplate")
		if err != nil {
			statusCode = http.StatusUnauthorized
			errChan <- err
			return
		}

		// TODO: add authorization flow with roles

		request.CreateByText = &reqUserID
		template, err := request.parseTemplate()
		if err != nil {
			statusCode = http.StatusUnauthorized
			errChan <- err
			return
		}

		templateID, err := h.manager.CreateTemplate(ctx, template)
		if err != nil {
			parsedErr := errInternalServer
			statusCode = http.StatusInternalServerError
			if _, ok := badRequestErr[err]; ok {
				parsedErr = err
				statusCode = http.StatusBadRequest
			}

			// log the actual error if its internal error
			if statusCode == http.StatusInternalServerError {
				log.Printf("[Manager HTTP][handleCreateTemplate] Internal error from CreateTemplate. Err: %s\n", err.Error())
			}

			errChan <- parsedErr
			return
		}

		resChan <- templateID
	}()

	// wait and handle main go routine
	select {
	case <-ctx.Done():
		statusCode = http.StatusGatewayTimeout
		err = errRequestTimeout
	case err = <-errChan:
	case templateID := <-resChan:
		resBody, err = json.Marshal(httplib.ResponseEnvelope{
			Data: templateID,
		})
	}
}
