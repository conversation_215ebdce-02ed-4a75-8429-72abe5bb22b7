package service

import (
	"context"

	"github.com/google/uuid"

	contextlib "github.com/temui-sisva/go-lib/context"
	"github.com/temui-sisva/report/internal/manager"
	"github.com/temui-sisva/report/internal/model"
)

const maxReportNameLength = 255

func (s *service) CreateReport(ctx context.Context, report *model.Report) (int64, error) {
	if report == nil {
		return 0, manager.ErrEmptyRequest
	}

	schoolID, ok := contextlib.GetSchoolID(ctx)
	if !ok || schoolID == "" {
		return 0, manager.ErrInvalidSchoolID
	}

	// set metadata
	tNow := s.timeNow()
	report.CreateTime = &tNow
	report.UpdateBy = report.CreateBy
	report.UpdateTime = report.CreateTime

	// default value
	status := model.ReportStatusDraft
	report.Status = &status

	err := validateReport(report)
	if err != nil {
		return 0, err
	}

	sc, err := s.pgStore.NewClient(schoolID, false)
	if err != nil {
		return 0, err
	}

	id, err := sc.CreateReport(ctx, report)
	if err != nil {
		return 0, err
	}

	return id, nil
}

func (s *service) GetReportByID(ctx context.Context, id int64, opt manager.GetReportOption) (*model.Report, error) {
	if id <= 0 {
		return nil, manager.ErrInvalidReportID
	}

	schoolID, ok := contextlib.GetSchoolID(ctx)
	if !ok || schoolID == "" {
		return nil, manager.ErrInvalidSchoolID
	}

	sc, err := s.pgStore.NewClient(schoolID, false)
	if err != nil {
		return nil, err
	}

	report, err := sc.GetReportByID(ctx, id, opt)
	if err != nil {
		return nil, err
	}

	return report, nil
}

func (s *service) GetReports(ctx context.Context, opt manager.GetReportOption) ([]*model.Report, error) {
	schoolID, ok := contextlib.GetSchoolID(ctx)
	if !ok || schoolID == "" {
		return nil, manager.ErrInvalidSchoolID
	}

	sc, err := s.pgStore.NewClient(schoolID, false)
	if err != nil {
		return nil, err
	}

	reports, err := sc.GetReports(ctx, opt)
	if err != nil {
		return nil, err
	}

	return reports, nil
}

func (s *service) UpdateReport(ctx context.Context, report *model.Report) error {
	if report == nil {
		return manager.ErrEmptyRequest
	}

	schoolID, ok := contextlib.GetSchoolID(ctx)
	if !ok || schoolID == "" {
		return manager.ErrInvalidSchoolID
	}

	sc, err := s.pgStore.NewClient(schoolID, false)
	if err != nil {
		return err
	}

	initial, err := sc.GetReportByID(ctx, report.GetID(), manager.GetReportOption{
		WithPeriodDetail: false,
	})
	if err != nil {
		return err
	}

	if initial == nil {
		return manager.ErrDataNotFound
	}

	err = s.validateReportWithInitialData(report, initial)
	if err != nil {
		return err
	}

	// set metadata
	tNow := s.timeNow()
	report.UpdateTime = &tNow

	err = validateReport(report)
	if err != nil {
		return err
	}

	err = sc.UpdateReport(ctx, report)
	if err != nil {
		return err
	}

	return nil
}

func (s *service) DeleteReportByID(ctx context.Context, id int64) error {
	if id <= 0 {
		return manager.ErrInvalidReportID
	}

	schoolID, ok := contextlib.GetSchoolID(ctx)
	if !ok || schoolID == "" {
		return manager.ErrInvalidSchoolID
	}

	sc, err := s.pgStore.NewClient(schoolID, false)
	if err != nil {
		return err
	}

	report, err := sc.GetReportByID(ctx, id, manager.GetReportOption{
		WithPeriodDetail: false,
	})
	if err != nil {
		return err
	}

	// TODO: add delete validations
	_ = report

	err = sc.DeleteReportByID(ctx, id)
	if err != nil {
		return err
	}

	return nil
}

func (s *service) validateReportWithInitialData(req *model.Report, initial *model.Report) error {
	// make sure create metadata not changed
	req.CreateBy = initial.CreateBy
	req.CreateTime = initial.CreateTime

	// TODO: Add more validations

	return nil
}

func validateReport(report *model.Report) error {
	if report.GetName() == "" || len(report.GetName()) > maxReportNameLength {
		return manager.ErrInvalidReportName
	}

	if report.GetTemplateID() <= 0 {
		return manager.ErrInvalidTemplateID
	}

	if report.GetPeriodID() <= 0 {
		return manager.ErrInvalidPeriodID
	}

	if report.StudyProgramID != nil && report.GetStudyProgramID() <= 0 {
		return manager.ErrInvalidStudyProgramID
	}

	if report.Grade != nil && report.GetGrade() == "" {
		return manager.ErrInvalidGrade
	}

	if _, valid := model.ReportStatusList[report.GetStatus()]; !valid {
		return manager.ErrInvalidReportStatus
	}

	if report.GetCreateBy() == uuid.Nil || report.GetUpdateBy() == uuid.Nil {
		return manager.ErrInvalidUserID
	}

	return nil
}
