package service

import (
	"context"

	contextlib "github.com/temui-sisva/go-lib/context"
	"github.com/temui-sisva/report/internal/manager"
	"github.com/temui-sisva/report/internal/model"
)

const maxTemplateNameLength = 255

func (s *service) CreateTemplate(ctx context.Context, template *model.Template) (int64, error) {
	if template == nil {
		return 0, manager.ErrEmptyRequest
	}

	schoolID, ok := contextlib.GetSchoolID(ctx)
	if !ok || schoolID == "" {
		return 0, manager.ErrInvalidSchoolID
	}

	// set metadata
	tNow := s.timeNow()
	template.CreateTime = &tNow
	template.UpdateBy = template.CreateBy
	template.UpdateTime = template.CreateTime

	err := validateTemplate(template)
	if err != nil {
		return 0, err
	}

	sc, err := s.pgStore.NewClient(schoolID, false)
	if err != nil {
		return 0, err
	}

	id, err := sc.CreateTemplate(ctx, template)
	if err != nil {
		return 0, err
	}

	return id, nil
}

func (s *service) GetTemplateByID(ctx context.Context, id int64) (*model.Template, error) {
	if id <= 0 {
		return nil, manager.ErrInvalidTemplateID
	}

	schoolID, ok := contextlib.GetSchoolID(ctx)
	if !ok || schoolID == "" {
		return nil, manager.ErrInvalidSchoolID
	}

	sc, err := s.pgStore.NewClient(schoolID, false)
	if err != nil {
		return nil, err
	}

	template, err := sc.GetTemplateByID(ctx, id)
	if err != nil {
		return nil, err
	}

	return template, nil
}

func (s *service) GetTemplates(ctx context.Context) ([]*model.Template, error) {
	schoolID, ok := contextlib.GetSchoolID(ctx)
	if !ok || schoolID == "" {
		return nil, manager.ErrInvalidSchoolID
	}

	sc, err := s.pgStore.NewClient(schoolID, false)
	if err != nil {
		return nil, err
	}

	templates, err := sc.GetTemplates(ctx)
	if err != nil {
		return nil, err
	}

	return templates, nil
}

func (s *service) UpdateTemplate(ctx context.Context, template *model.Template) error {
	if template == nil {
		return manager.ErrEmptyRequest
	}

	schoolID, ok := contextlib.GetSchoolID(ctx)
	if !ok || schoolID == "" {
		return manager.ErrInvalidSchoolID
	}

	sc, err := s.pgStore.NewClient(schoolID, false)
	if err != nil {
		return err
	}

	// set metadata
	tNow := s.timeNow()
	template.UpdateTime = &tNow

	err = validateTemplate(template)
	if err != nil {
		return err
	}

	err = sc.UpdateTemplate(ctx, template)
	if err != nil {
		return err
	}

	return nil
}

func (s *service) DeleteTemplateByID(ctx context.Context, id int64) error {
	if id <= 0 {
		return manager.ErrInvalidTemplateID
	}

	schoolID, ok := contextlib.GetSchoolID(ctx)
	if !ok || schoolID == "" {
		return manager.ErrInvalidSchoolID
	}

	sc, err := s.pgStore.NewClient(schoolID, false)
	if err != nil {
		return err
	}

	err = sc.DeleteTemplateByID(ctx, id)
	if err != nil {
		return err
	}

	return nil
}

func validateTemplate(template *model.Template) error {
	if template.GetName() == "" || len(template.GetName()) > maxTemplateNameLength {
		return manager.ErrInvalidTemplateName
	}

	return nil
}
