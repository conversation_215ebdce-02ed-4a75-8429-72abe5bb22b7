package manager

import "errors"

var (
	ErrDataNotFound          error = errors.New("DATA_NOT_FOUND")
	ErrEmptyRequest          error = errors.New("EMPTY_REQUEST")
	ErrInvalidGrade          error = errors.New("INVALID_GRADE")
	ErrInvalidPeriodID       error = errors.New("INVALID_PERIOD_ID")
	ErrInvalidReportID       error = errors.New("INVALID_REPORT_ID")
	ErrInvalidReportName     error = errors.New("INVALID_REPORT_NAME")
	ErrInvalidReportStatus   error = errors.New("INVALID_REPORT_STATUS")
	ErrInvalidTemplateID     error = errors.New("INVALID_TEMPLATE_ID")
	ErrInvalidTemplateName   error = errors.New("INVALID_TEMPLATE_NAME")
	ErrInvalidSchoolID       error = errors.New("INVALID_SCHOOL_ID")
	ErrInvalidStudyProgramID error = errors.New("INVALID_STUDY_PROGRAM_ID")
	ErrInvalidUserID         error = errors.New("INVALID_USER_ID")
)
