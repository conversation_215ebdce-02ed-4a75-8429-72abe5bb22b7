package postgresql

const queryCreateTemplate = `
	INSERT INTO
		report_template 
	(
		name,
		pdf_generator_fn_text,
		placeholder_json,
		ref_url,
		description,
		create_by,
		create_time
	) VALUES (
		:name,
		:pdf_generator_fn_text,
		:placeholder_json,
		:ref_url,
		:description,
		:create_by,
		:create_time
	) RETURNING 
		id
`

const queryGetTemplates = `
	SELECT 
		rt.id,
		rt.name,
		rt.pdf_generator_fn_text,
		rt.placeholder_json,
		rt.ref_url,
		rt.description,
		rt.create_by,
		rt.create_time,
		rt.update_by,
		rt.update_time
	FROM
		report_template rt
		%s
`

const queryUpdateTemplate = `
	UPDATE
		report_template 
	SET
		name = :name,
		pdf_generator_fn_text = :pdf_generator_fn_text,
		placeholder_json = :placeholder_json,
		ref_url = :ref_url,
		description = :description,
		update_by = :update_by,
		update_time = :update_time
	WHERE
		id = :id
`

const queryDeleteTemplateByID = `
	DELETE FROM
		report_template
	WHERE
		id = $1
`
