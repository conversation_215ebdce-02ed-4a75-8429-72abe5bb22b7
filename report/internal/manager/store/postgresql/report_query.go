package postgresql

const queryCreateReport = `
	INSERT INTO
		report 
	(
		name,
		template_id,
		period_id,
		study_program_id,
		grade,
		status,
		create_by,
		create_time
	) VALUES (
		:name,
		:template_id,
		:period_id,
		:study_program_id,
		:grade,
		:status,
		:create_by,
		:create_time
	) RETURNING 
		id
`

const queryGetReports = `
	SELECT 
		r.id,
		r.name,
		r.template_id,
		r.period_id,
		r.study_program_id,
		r.grade,
		r.status,
		%s
		r.create_by,
		r.create_time,
		r.update_by,
		r.update_time
	FROM
		report r
		%s
		%s
`

const queryUpdateReport = `
	UPDATE
		report 
	SET
		name = :name,
		template_id = :template_id,
		period_id = :period_id,
		study_program_id = :study_program_id,
		grade = :grade,
		status = :status,
		update_by = :update_by,
		update_time = :update_time
	WHERE
		id = :id
`

const queryDeleteReportByID = `
	DELETE FROM
		report
	WHERE
		id = $1
`
