package postgresql

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"

	"github.com/jmoiron/sqlx"

	"github.com/temui-sisva/report/internal/manager"
	"github.com/temui-sisva/report/internal/model"
	"github.com/temui-sisva/report/internal/tool/ptr"
)

func (sc *storeClient) CreateTemplate(ctx context.Context, template *model.Template) (int64, error) {
	var placeholderJSON *string
	if len(template.Placeholders) > 0 {
		v, err := json.Marshal(template.Placeholders)
		if err != nil {
			return 0, err
		}

		placeholderJSON = ptr.StrPtr(string(v))
	}

	argsKV := map[string]interface{}{
		"name":                  template.Name,
		"pdf_generator_fn_text": template.PDFGeneratorFnText,
		"placeholder_json":      placeholderJSON,
		"ref_url":               template.RefURL,
		"description":           template.Description,
		"create_by":             template.CreateBy,
		"create_time":           template.CreateTime,
	}

	query, args, err := sqlx.Named(queryCreateTemplate, argsKV)
	if err != nil {
		return 0, err
	}
	query, args, err = sqlx.In(query, args...)
	if err != nil {
		return 0, err
	}
	query = sc.q.Rebind(query)

	var templateID int64
	err = sc.q.QueryRowx(query, args...).Scan(&templateID)
	if err != nil {
		return 0, err
	}

	return templateID, nil
}

func (sc *storeClient) GetTemplateByID(ctx context.Context, id int64) (*model.Template, error) {
	query := fmt.Sprintf(queryGetTemplates, "WHERE rt.id = $1")

	res := templateDB{}
	err := sc.q.QueryRowx(query, id).StructScan(&res)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, manager.ErrDataNotFound
		}
		return nil, err
	}

	template, err := res.format()
	if err != nil {
		return nil, err
	}

	return template, nil
}

func (sc *storeClient) GetTemplates(ctx context.Context) ([]*model.Template, error) {
	query := fmt.Sprintf(queryGetTemplates, "")

	rows, err := sc.q.Queryx(query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	res := make([]*model.Template, 0)
	for rows.Next() {
		var t templateDB
		err = rows.StructScan(&t)
		if err != nil {
			return nil, err
		}

		template, err := t.format()
		if err != nil {
			return nil, err
		}

		res = append(res, template)
	}

	if err := rows.Err(); err != nil {
		return nil, err
	}

	return res, nil
}

func (sc *storeClient) UpdateTemplate(ctx context.Context, template *model.Template) error {
	var placeholderJSON *string
	if len(template.Placeholders) > 0 {
		v, err := json.Marshal(template.Placeholders)
		if err != nil {
			return err
		}

		placeholderJSON = ptr.StrPtr(string(v))
	}

	argsKV := map[string]interface{}{
		"id":                    template.ID,
		"name":                  template.Name,
		"pdf_generator_fn_text": template.PDFGeneratorFnText,
		"placeholder_json":      placeholderJSON,
		"ref_url":               template.RefURL,
		"description":           template.Description,
		"update_by":             template.UpdateBy,
		"update_time":           template.UpdateTime,
	}

	query, args, err := sqlx.Named(queryUpdateTemplate, argsKV)
	if err != nil {
		return err
	}
	query, args, err = sqlx.In(query, args...)
	if err != nil {
		return err
	}
	query = sc.q.Rebind(query)

	_, err = sc.q.Exec(query, args...)
	if err != nil {
		return err
	}

	return nil
}

func (sc *storeClient) DeleteTemplateByID(ctx context.Context, id int64) error {
	_, err := sc.q.Exec(queryDeleteTemplateByID, id)
	if err != nil {
		return err
	}

	return nil
}
