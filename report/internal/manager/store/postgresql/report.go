package postgresql

import (
	"context"
	"database/sql"
	"fmt"
	"strings"

	"github.com/jmoiron/sqlx"

	"github.com/temui-sisva/report/internal/manager"
	"github.com/temui-sisva/report/internal/model"
)

func (sc *storeClient) CreateReport(ctx context.Context, report *model.Report) (int64, error) {
	argsKV := map[string]interface{}{
		"name":             report.Name,
		"template_id":      report.TemplateID,
		"report_id":        report.TemplateID,
		"period_id":        report.PeriodID,
		"study_program_id": report.StudyProgramID,
		"grade":            report.Grade,
		"status":           report.Status,
		"create_by":        report.CreateBy,
		"create_time":      report.CreateTime,
	}

	query, args, err := sqlx.Named(queryCreateReport, argsKV)
	if err != nil {
		return 0, err
	}
	query, args, err = sqlx.In(query, args...)
	if err != nil {
		return 0, err
	}
	query = sc.q.Rebind(query)

	var reportID int64
	err = sc.q.QueryRowx(query, args...).Scan(&reportID)
	if err != nil {
		return 0, err
	}

	return reportID, nil
}

func (sc *storeClient) GetReportByID(ctx context.Context, id int64, opt manager.GetReportOption) (*model.Report, error) {
	query := constructGetReportsQuery(opt, "WHERE r.id = $1")

	r := reportDB{}
	err := sc.q.QueryRowx(query, id).StructScan(&r)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, manager.ErrDataNotFound
		}
		return nil, err
	}

	return r.format(), nil
}

func (sc *storeClient) GetReports(ctx context.Context, opt manager.GetReportOption) ([]*model.Report, error) {
	query := constructGetReportsQuery(opt, "")

	rows, err := sc.q.Queryx(query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	res := make([]*model.Report, 0)
	for rows.Next() {
		r := reportDB{}
		err = rows.StructScan(&r)
		if err != nil {
			return nil, err
		}

		res = append(res, r.format())
	}

	if err := rows.Err(); err != nil {
		return nil, err
	}

	return res, nil
}

func (sc *storeClient) UpdateReport(ctx context.Context, report *model.Report) error {
	argsKV := map[string]interface{}{
		"id":               report.ID,
		"name":             report.Name,
		"template_id":      report.TemplateID,
		"report_id":        report.TemplateID,
		"period_id":        report.PeriodID,
		"study_program_id": report.StudyProgramID,
		"grade":            report.Grade,
		"status":           report.Status,
		"update_by":        report.UpdateBy,
		"update_time":      report.UpdateTime,
	}

	query, args, err := sqlx.Named(queryUpdateReport, argsKV)
	if err != nil {
		return err
	}
	query, args, err = sqlx.In(query, args...)
	if err != nil {
		return err
	}
	query = sc.q.Rebind(query)

	_, err = sc.q.Exec(query, args...)
	if err != nil {
		return err
	}

	return nil
}

func (sc *storeClient) DeleteReportByID(ctx context.Context, id int64) error {
	_, err := sc.q.Exec(queryDeleteReportByID, id)
	if err != nil {
		return err
	}

	return nil
}

func constructGetReportsQuery(opt manager.GetReportOption, whereClause string) string {
	addSelects := make([]string, 0)
	addJoins := make([]string, 0)

	if opt.WithPeriodDetail {
		addSelects = append(addSelects, "p.name as period_name")
		addJoins = append(addJoins, "INNER JOIN period p ON r.period_id = p.id")
	}

	// construct query
	addSelect := strings.Join(addSelects, ",")
	if addSelect != "" {
		addSelect = fmt.Sprintf("%s,", addSelect)
	}
	addJoin := strings.Join(addJoins, " ")
	query := fmt.Sprintf(queryGetReports, addSelect, addJoin, whereClause)

	return query
}
