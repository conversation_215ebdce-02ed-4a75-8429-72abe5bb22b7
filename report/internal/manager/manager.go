package manager

import (
	"context"

	"github.com/temui-sisva/report/internal/model"
)

type Service interface {
	CreateTemplate(ctx context.Context, template *model.Template) (int64, error)
	GetTemplateByID(ctx context.Context, id int64) (*model.Template, error)
	GetTemplates(ctx context.Context) ([]*model.Template, error)
	UpdateTemplate(ctx context.Context, template *model.Template) error
	DeleteTemplateByID(ctx context.Context, id int64) error

	CreateReport(ctx context.Context, report *model.Report) (int64, error)
	GetReportByID(ctx context.Context, id int64, opt GetReportOption) (*model.Report, error)
	GetReports(ctx context.Context, opt GetReportOption) ([]*model.Report, error)
	UpdateReport(ctx context.Context, report *model.Report) error
	DeleteReportByID(ctx context.Context, id int64) error
}

type GetReportOption struct {
	WithPeriodDetail bool
}
