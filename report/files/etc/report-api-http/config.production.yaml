server:
  port: 9071

tenant_configuration_client:
  address: "127.0.0.1:9002"
  connection_timeout: 2s
  retry_interval: 5s
  scope_settings:
    "GetSchoolConfigurationsByKey":
      breaker_error_threshold: 20
      breaker_success_threshold: 10
      breaker_timeout: 15s

encryption:
  key: ${encryption_key}

postgresql:
  connection_timeout: 15s

tenant_user_client:
  address: "127.0.0.1:9002"
  connection_timeout: 2s
  retry_interval: 5s
  scope_settings:
    "ValidateToken":
      breaker_error_threshold: 20
      breaker_success_threshold: 10
      breaker_timeout: 15s

manager:
  handler:
    http:
      "CreateTemplate":
        timeout: 2s
      "GetTemplateByID":
        timeout: 1s
      "GetTemplates":
        timeout: 5s
      "UpdateTemplate":
        timeout: 3s
      "DeleteTemplateByID":
        timeout: 3s
      "CreateReport":
        timeout: 2s
      "GetReportByID":
        timeout: 1s
      "GetReports":
        timeout: 5s
      "UpdateReport":
        timeout: 3s
      "DeleteReportByID":
        timeout: 3s

scoring:
  handler:
    http:
      "SetStudentScores":
        timeout: 5s
      "GetStudentScores":
        timeout: 5s
      "DeleteStudentScores":
        timeout: 5s
