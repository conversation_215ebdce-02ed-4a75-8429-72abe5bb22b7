name: Deploy Staging

on:
  push:
    branches: ["main"]
  workflow_dispatch:

jobs:
  deploy-http:
    runs-on: [self-hosted, api-server, staging]
    steps:
      - uses: actions/checkout@v3
      - name: Move files
        run: sudo cp -R files/etc/report-api-http /etc
      - name: Install binary
        run: /usr/local/go/bin/go install ./cmd/report-api-http
      - name: Move binary
        run: sudo mv ~/go/bin/report-api-http /usr/local/go/bin/report-api-http
      - name: Restart service
        run: sudo service report-api-http restart
