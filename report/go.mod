module github.com/temui-sisva/report

go 1.19

require (
	github.com/google/uuid v1.3.0
	github.com/gorilla/mux v1.8.1
	github.com/jmoiron/sqlx v1.4.0
	github.com/lib/pq v1.10.9
	github.com/prometheus/client_golang v1.18.0
	github.com/temui-sisva/go-lib/config v0.0.0-20250113164615-0e991db501ac
	github.com/temui-sisva/go-lib/context v0.0.0-20250113164615-0e991db501ac
	github.com/temui-sisva/go-lib/cryptography v0.0.0-20250113164615-0e991db501ac
	github.com/temui-sisva/go-lib/environment v0.0.0-20250113164615-0e991db501ac
	github.com/temui-sisva/go-lib/graceful v0.0.0-20250113164615-0e991db501ac
	github.com/temui-sisva/go-lib/grpcclient/tenantconfiguration v0.0.0-20250113164615-0e991db501ac
	github.com/temui-sisva/go-lib/grpcclient/tenantuser v0.0.0-20250113164615-0e991db501ac
	github.com/temui-sisva/go-lib/http v0.0.0-20230322194103-23d1a61aa188
	github.com/temui-sisva/go-lib/postgresql v0.0.0-20250113164615-0e991db501ac
	github.com/temui-sisva/go-lib/prometheus v0.0.0-20250113164615-0e991db501ac
	github.com/temui-sisva/go-lib/secret v0.0.0-20250113164615-0e991db501ac
	gopkg.in/yaml.v2 v2.4.0
)

require (
	github.com/beorn7/perks v1.0.1 // indirect
	github.com/cespare/xxhash/v2 v2.3.0 // indirect
	github.com/eapache/go-resiliency v1.3.0 // indirect
	github.com/golang/mock v1.6.0 // indirect
	github.com/golang/protobuf v1.5.3 // indirect
	github.com/kr/text v0.2.0 // indirect
	github.com/matttproud/golang_protobuf_extensions/v2 v2.0.0 // indirect
	github.com/prometheus/client_model v0.5.0 // indirect
	github.com/prometheus/common v0.45.0 // indirect
	github.com/prometheus/procfs v0.12.0 // indirect
	github.com/temui-sisva/go-lib/grpc v0.0.0-20230322194103-23d1a61aa188 // indirect
	github.com/temui-sisva/grpc/common v0.0.0-20230216101249-c1df561dba3f // indirect
	github.com/temui-sisva/grpc/tenantconfiguration v0.0.0-20230218163901-85474745669a // indirect
	github.com/temui-sisva/grpc/tenantuser v0.0.0-20230312075501-78d02d612dfa // indirect
	golang.org/x/net v0.26.0 // indirect
	golang.org/x/sys v0.22.0 // indirect
	golang.org/x/text v0.16.0 // indirect
	google.golang.org/genproto v0.0.0-20230110181048-76db0878b65f // indirect
	google.golang.org/grpc v1.54.0 // indirect
	google.golang.org/protobuf v1.34.2 // indirect
)
