# Sisva

Ini adalah monorepo typescript untuk sisva yang disetup menggunakan [Turborepo](https://turbo.build/).

## Apps dan Packages

- `administration`: React app untuk module Administration
- `classroom`: React app untuk module Classroom
- `guardian`: React app untuk module Guardian
- `cbt`: React app untuk module CBT
- `@sisva/ui`: Shared React component untuk aplikasi sisva
- `@sisva/eslint-config`: Shared eslint config
- `@sisva/typescript-config`: Shared typescript config
- `@sisva/api`: Axios instance untuk fetching data
- `@sisva/hooks`: React hooks untuk fetching data dan utility lainnya
- `@sisva/types`: Typescript types, schema, dan constant
- `@sisva/providers`: React component yang berupa providers
- `@sisva/utils`: Utility function

## Typescript, Eslint dan Prettier

Tolong setup Typescript, <PERSON><PERSON>lint dan Prettier di IDE kalian, enable format dan fix lint error on save.

**Note untuk Typescript**:

- `tsconfig.json` di setiap apps dan package punya strict enabled
- **Dilarang** menggunakan `any`, `//@ts-ignore` atau `//@ts-exprect-error` tanpa persetujuan lead dev
- Wajib pakai Typescript, dilarang ngoding di file `.js` dan `.jsx`

## PNPM

Repo ini menggunakan pnpm. Jangan pakai npm.

## Library yang wajib digunakan

- [Tanstack Router](https://tanstack.com/router/latest). Router untuk React
- [reack-hook-form](https://www.react-hook-form.com/) dan [valibot](https://valibot.dev/) saat membuat form
- [Tanstack Query](https://tanstack.com/query/latest) untuk fetching data
- [Mantine Hooks](https://mantine.dev/hooks/use-debounced-callback/) dan [ahooks](https://ahooks.js.org/). Hindari penggunakan `useEffect` sebisa mungkin
- [fast-sort](https://github.com/snovakovic/fast-sort) dan [fuzzysort](https://github.com/farzher/fuzzysort) untuk sorting dan fuzzy search
- [dayjs](https://day.js.org/) untuk parsing dan formatting tanggal

## Script

- `pnpm run dev`: Run dev server
- `pnpm run typecheck`: Check typescript error
- `pnpm run build`: Build and bundle for production
- `pnpm run lint:fix`: Fix lint error
- `pnpm run format`: Format with prettier

Lebih lengkapnya bisa dicek di `package.json`

## SVG

Jika ada SVG yang ingin diubah jadi JSX, bisa convert pakai [SVGR](https://react-svgr.com/playground/?dimensions=false&exportType=named&prettierConfig=%7B%0A%20%20%22singleQuote%22%3A%20false%2C%0A%20%20%22trailingComma%22%3A%20%22es5%22%2C%0A%20%20%22semi%22%3A%20true%2C%0A%20%20%22tabWidth%22%3A%202%2C%0A%20%20%22printWidth%22%3A%2080%2C%0A%20%20%22overrides%22%3A%20%5B%0A%20%20%20%20%7B%0A%20%20%20%20%20%20%22files%22%3A%20%22src%2Fapi%2F%2A.ts%22%2C%0A%20%20%20%20%20%20%22options%22%3A%20%7B%0A%20%20%20%20%20%20%20%20%22printWidth%22%3A%20140%0A%20%20%20%20%20%20%7D%0A%20%20%20%20%7D%0A%20%20%5D%0A%7D%0A&svgo=false&typescript=true) dulu.
