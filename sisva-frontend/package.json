{"name": "sisva", "private": true, "scripts": {"build": "turbo run build", "build:@sisva/api": "turbo run build --filter=@sisva/api", "build:@sisva/hooks": "turbo run build --filter=@sisva/hooks", "build:@sisva/providers": "turbo run build --filter=@sisva/providers", "build:@sisva/ui": "turbo run build --filter=@sisva/ui", "build:@sisva/utils": "turbo run build --filter=@sisva/utils", "build:@sisva/types": "turbo run build --filter=@sisva/types", "dev": "turbo run dev --concurrency 12", "dev:@sisva/api": "turbo run dev --filter=@sisva/api", "dev:@sisva/hooks": "turbo run dev --filter=@sisva/hooks", "dev:@sisva/providers": "turbo run dev --filter=@sisva/providers", "dev:@sisva/ui": "turbo run dev --filter=@sisva/ui", "dev:@sisva/utils": "turbo run dev --filter=@sisva/utils", "dev:@sisva/types": "turbo run dev --filter=@sisva/types", "lint": "turbo run lint", "lint:fix": "turbo run lint:fix", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "typecheck": "turbo run typecheck", "knip": "knip"}, "devDependencies": {"knip": "^5.45.0", "prettier": "^3.5.2", "turbo": "^2.4.4", "typescript": "5.7.3"}, "packageManager": "pnpm@10.5.2", "engines": {"node": ">=22.12.0"}}