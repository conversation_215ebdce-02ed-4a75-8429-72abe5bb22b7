{
  "extends": "@sisva/typescript-config/tsconfig.base.json",
  "compilerOptions": {
    "checkJs": false,

    // --- strictness ---
    "strict": true,
    "strictNullChecks": true,
    "strictBindCallApply": true,
    "strictBuiltinIteratorReturn": true,
    "strictPropertyInitialization": true,
    "noImplicitThis": true,
    "useUnknownInCatchVariables": true,

    "noImplicitAny": true,
    "strictFunctionTypes": true,
    "noUncheckedIndexedAccess": true,
    // --- strictness ---

    "noEmit": true,
    "outDir": "dist",
    "paths": {
      "#/*": ["./src/*"]
    }
  },
  "include": ["src", "vite.config.ts"],
  "exclude": ["node_modules", "dist"]
}
