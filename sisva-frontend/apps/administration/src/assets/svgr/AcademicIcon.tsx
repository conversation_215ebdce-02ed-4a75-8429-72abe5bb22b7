import { SvgIcon } from "@mui/material";

export const AcademicIcon = () => {
  return (
    <SvgIcon fontSize="inherit" viewBox="0 0 20 20">
      <g clipPath="url(#clip0_490_10103)">
        <path d="M19.6234 6.92642C19.5681 6.90244 18.2214 6.31901 10.2479 2.86371C10.0893 2.79495 9.90959 2.79495 9.75098 2.86371L0.375491 6.92642C-0.127583 7.14397 -0.122739 7.85807 0.375491 8.07336C2.19684 8.8625 5.17438 10.1529 9.75098 12.1361C9.90865 12.2044 10.0883 12.2053 10.2479 12.1361C10.6825 11.9477 16.6041 9.38167 16.8748 9.26443V15.9203C16.8748 16.257 17.1336 16.5465 17.4699 16.5622C17.8289 16.5789 18.1249 16.2931 18.1249 15.9378V9.03774C18.1249 8.93954 18.0521 8.8566 17.9547 8.84406C16.7815 8.69288 11.2489 7.97878 9.91935 7.80733C9.5348 7.75928 9.29518 7.36988 9.39628 7.0233C9.47558 6.73059 9.75348 6.54414 10.035 6.56351C10.063 6.56515 18.9702 7.71459 18.9639 7.71377C19.1303 7.73517 19.2671 7.84174 19.3328 7.98624C19.3787 8.08726 19.4957 8.13355 19.5962 8.08644C19.7643 8.00761 19.9102 7.90386 19.9765 7.66904C20.0619 7.36476 19.9076 7.04932 19.6234 6.92642Z" />
        <path d="M9.2537 13.283L4.0221 11.016C3.89311 10.9601 3.74911 11.0547 3.74911 11.1952V13.4377C3.74911 15.5405 6.49457 17.1879 9.99944 17.1879C13.5043 17.1879 16.2498 15.5405 16.2498 13.4377V11.1952C16.2498 11.0547 16.1058 10.9602 15.9768 11.016L10.7448 13.283C10.2698 13.4895 9.7292 13.4893 9.2537 13.283Z" />
      </g>
      <defs>
        <clipPath id="clip0_490_10103">
          <rect width="20" height="20" fill="white" />
        </clipPath>
      </defs>
    </SvgIcon>
  );
};
