import { DeleteForeverRounded } from "@mui/icons-material";
import { IconButton } from "@mui/material";

export default function DeleteIcon({ onClick }: { onClick?: () => void }) {
  return (
    <IconButton
      sx={{
        borderRadius: 2,
        backgroundColor: "error.main",
        "&:hover": {
          backgroundColor: "error.dark",
        },
      }}
      onClick={onClick}
    >
      <DeleteForeverRounded sx={{ color: "white", fontSize: 18 }} />
    </IconButton>
  );
}
