import { BorderColorRounded } from "@mui/icons-material";
import { IconButton } from "@mui/material";

export default function EditIcon({ onClick }: { onClick?: () => void }) {
  return (
    <IconButton
      onClick={onClick}
      sx={{
        borderRadius: 2,
        backgroundColor: "grey.300",
        "&:hover": {
          backgroundColor: "grey.400",
        },
      }}
    >
      <BorderColorRounded sx={{ color: "grey.600", fontSize: 18 }} />
    </IconButton>
  );
}
