import { alpha, Box, Stack } from "@mui/material";
import { useTheme } from "@mui/material/styles";
import type { ReactNode } from "react";

export default function GroupHeader({ children }: { children: ReactNode }) {
  const theme = useTheme();
  return (
    <Stack
      sx={{
        bgcolor: alpha(theme.palette.primary.main, 0.3),
        py: 1.25,
        px: 1.5,
        fontWeight: 600,
        position: "relative",
        overflow: "hidden",
        borderRadius: 2,
      }}
    >
      <Box
        sx={{
          width: 8,
          height: 25,
          bgcolor: "grey.900",
          position: "absolute",
          left: -4,
          borderRadius: 1,
          top: "50%",
          transform: "translateY(-50%)",
        }}
      ></Box>
      {children}
    </Stack>
  );
}
