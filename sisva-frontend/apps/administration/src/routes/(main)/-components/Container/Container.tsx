import { Box } from "@mui/material";
import { useToggle } from "ahooks";
import type { ReactNode } from "react";

import Header from "./Header";
import Navigation from "./Navigation";

export function Container({ children }: { children: ReactNode }) {
  const [open, { toggle }] = useToggle();
  return (
    <Box sx={{ height: "100vh", width: "100%" }}>
      <Header onMenuClick={toggle} />
      <Navigation
        openMobileNav={open}
        onMobileNavClose={toggle}
        onNavLinkClick={toggle}
      />
      <Box
        sx={{
          height: "100%",
          width: "100%",
          pl: { xs: 0, lg: "280px" },
          pt: "70px",
          backgroundColor: "base.base20",
          overflow: "auto",
        }}
      >
        {children}
      </Box>
    </Box>
  );
}
