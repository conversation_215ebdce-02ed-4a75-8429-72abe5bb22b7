import { Menu } from "@mui/icons-material";
import { <PERSON>, Button, Paper, Stack, Typography } from "@mui/material";
import { useSchool } from "@sisva/providers";
import { Image } from "@sisva/ui";
import { Link } from "@tanstack/react-router";

import UserMenu from "./UserMenu";

export default function Header({ onMenuClick }: { onMenuClick: () => void }) {
  const school = useSchool();
  const activePathname = "";

  function renderTitle() {
    const nav =
      activePathname.split("/").slice(-1)[0] !== "dashboard"
        ? activePathname.split("/").slice(-2, -1) +
          "/" +
          activePathname.split("/").slice(-1)
        : activePathname.split("/").slice(-1)[0];

    const titleList = [
      {
        title: "Beranda",
        slug: "dashboard",
      },
      {
        title: "<PERSON><PERSON>",
        slug: "school/profile",
      },
      {
        title: "<PERSON><PERSON><PERSON>",
        slug: "staff/profile",
      },
      {
        title: "<PERSON><PERSON><PERSON><PERSON>",
        slug: "staff/attendance",
      },
      {
        title: "Program Studi",
        slug: "academic/study-program",
      },
      {
        title: "Kurikulum",
        slug: "academic/curriculum",
      },
      {
        title: "Periode",
        slug: "academic/period",
      },
      {
        title: "Guru",
        slug: "academic/teacher",
      },
      {
        title: "Kelas",
        slug: "academic/class",
      },
      {
        title: "Jadwal Pelajaran",
        slug: "academic/schedule",
      },
      { title: "Ekstrakurikuler", slug: "academic/extracurricular" },
      {
        title: "Daftar Siswa",
        slug: "student/profile",
      },
      {
        title: "Kehadiran Siswa",
        slug: "student/attendance",
      },
      {
        title: "Alumni",
        slug: "student/alumni",
      },

      {
        title: "Template Rapot",
        slug: "report/template",
      },
      { title: "Buat Rapot", slug: "report/create" },
      {
        title: "Edit Rapot",
        slug: "report/edit",
      },
      { title: "Lihat Rapot", slug: "report/view" },
      {
        title: "Pengumuman",
        slug: "information/announcement",
      },
      {
        title: "Invoice",
        slug: "finance/invoice",
      },
    ];

    return titleList.find((item) => item.slug === nav)?.title;
  }

  return (
    <Stack
      component={Paper}
      elevation={1}
      sx={{
        height: 70,
        padding: { xs: 1, md: "0 32px" },
        flexDirection: "row",
        justifyContent: "space-between",
        width: "100%",
        alignItems: "center",
        position: "fixed",
        zIndex: 10,
      }}
    >
      <Link to="/dashboard" className="contents">
        <Stack
          sx={{
            display: { xs: "none", lg: "flex" },
            flexDirection: "row",
            height: "100%",
            alignItems: "center",
          }}
        >
          <Box sx={{ height: 36, width: 36, position: "relative" }}>
            {school.logo_url && (
              <Image
                alt="Web Image"
                src={school.logo_url}
                fill
                className="object-contain"
              />
            )}
          </Box>
          <Typography
            sx={{
              fontWeight: "700",
              ml: 1,
              fontSize: 18,
            }}
          >
            {school.name}
          </Typography>
        </Stack>
      </Link>
      <Stack
        sx={{
          display: { xs: "flex", lg: "none" },
          flexDirection: "row",
          height: "100%",
          alignItems: "center",
        }}
      >
        <Box
          component={Button}
          sx={{ fontSize: 24, height: 50 }}
          onClick={onMenuClick}
        >
          <Menu />
        </Box>
        <Typography
          sx={{
            fontWeight: "700",
            ml: 1,
            fontSize: { xs: 16, md: 18 },
          }}
        >
          {renderTitle()}
        </Typography>
      </Stack>
      <UserMenu />
    </Stack>
  );
}
