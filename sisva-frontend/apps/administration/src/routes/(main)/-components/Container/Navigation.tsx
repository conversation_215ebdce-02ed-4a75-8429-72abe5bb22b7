import { ArrowBackIosNewRounded, Dashboard } from "@mui/icons-material";
import {
  Box,
  Button,
  Divider,
  Modal,
  Paper,
  Stack,
  Typography,
} from "@mui/material";
import { useCurrentUser } from "@sisva/providers";
import { useSchool } from "@sisva/providers";
import { Image } from "@sisva/ui";
import { Link, type LinkProps, useMatchRoute } from "@tanstack/react-router";
import type { ReactElement, ReactNode } from "react";
import { useState } from "react";

import { AcademicIcon } from "#/assets/svgr/AcademicIcon";
import { FinanceIcon } from "#/assets/svgr/FinanceIcon";
import { InformationIcon } from "#/assets/svgr/InformationIcon";
import { ReportIcon } from "#/assets/svgr/ReportIcon";
import { SchoolIcon } from "#/assets/svgr/SchoolIcon";
import { StaffIcon } from "#/assets/svgr/StaffIcon";
import { StudentIcon } from "#/assets/svgr/StudentIcon";
import { env } from "#/env";

export default function Navigation({
  openMobileNav,
  onMobileNavClose,
  onNavLinkClick,
}: {
  openMobileNav: boolean;
  onMobileNavClose: () => void;
  onNavLinkClick: () => void;
}) {
  const school = useSchool();

  return (
    <Box>
      <Modal open={openMobileNav} onClose={onMobileNavClose}>
        <Stack
          component={Paper}
          elevation={2}
          sx={{
            position: "absolute",
            left: 0,
            top: 0,
            height: "100vh",
            width: 280,
            maxWidth: "80%",
            zIndex: { xs: 100, lg: 1 },
          }}
        >
          <Link to="/dashboard">
            <Stack
              sx={{
                flexDirection: "row",
                minHeight: 70,
                width: "100%",
                alignItems: "center",
                pl: 4,
                pr: 2,
              }}
            >
              {school.logo_url && (
                <Image
                  alt="Web Image"
                  src={school.logo_url}
                  height={36}
                  width={36}
                />
              )}
              <Typography
                sx={{
                  fontWeight: "700",
                  ml: 1,
                  fontSize: 18,
                }}
              >
                {school?.name}
              </Typography>
            </Stack>
          </Link>
          <Divider />
          <Box
            sx={{
              maxHeight: "100%",
              width: "100%",
              overflowY: "scroll",
            }}
          >
            <NavigationContent onNavLinkClick={onNavLinkClick} />
          </Box>
        </Stack>
      </Modal>
      <Stack
        component={Paper}
        elevation={2}
        sx={{
          display: { xs: "none", lg: "flex" },
          position: "fixed",
          left: 0,
          top: 0,
          height: "100vh",
          width: 280,
          maxWidth: "80%",
          pt: "70px",

          maxHeight: "100%",
          overflowY: "auto",
          zIndex: { xs: 100, lg: 1 },
        }}
      >
        <NavigationContent />
      </Stack>
    </Box>
  );
}

function NavigationContent({
  onNavLinkClick,
}: {
  onNavLinkClick?: () => void;
}) {
  const permissions = useCurrentUser().permissions;
  const roles = useCurrentUser().roles;

  const hideSchool =
    !roles.includes("admin") && !permissions.includes("manage_school");
  const hideStaff =
    !roles.includes("admin") && !permissions.includes("manage_staff");
  const hideStudent =
    !roles.includes("admin") && !permissions.includes("manage_student");
  const hideAcademic =
    !roles.includes("admin") && !permissions.includes("manage_academic");
  const hideReport =
    !roles.includes("admin") && !permissions.includes("manage_report");
  const hideInformation =
    !roles.includes("admin") && !permissions.includes("manage_information");
  const hideFinance =
    !roles.includes("admin") && !permissions.includes("manage_finance");

  return (
    <Stack
      direction="column"
      sx={{
        alignItems: "center",
        justifyContent: "center",
        gap: "8px",
        p: "16px",
        mt: "16px",
      }}
    >
      <SingleMenu title="Dashboard" icon={<Dashboard />} to="/dashboard" />
      <NestedMenu
        title="Sekolah"
        icon={<SchoolIcon />}
        hide={hideSchool}
        childrenLinks={[
          <ChildrenLink
            title="Profile Sekolah"
            onNavLinkClick={onNavLinkClick}
            to="/dashboard/school/profile"
            key="1"
          />,
        ]}
      />
      <NestedMenu
        title="Karyawan"
        icon={<StaffIcon />}
        hide={hideStaff}
        childrenLinks={[
          <ChildrenLink
            title="Daftar Karyawan"
            onNavLinkClick={onNavLinkClick}
            to="/dashboard/staff/profile"
            key="1"
          />,
          <ChildrenLink
            title="Kehadiran Karyawan"
            onNavLinkClick={onNavLinkClick}
            to="/dashboard/staff/attendance"
            key="2"
          />,
        ]}
      />
      <NestedMenu
        title="Siswa"
        icon={<StudentIcon />}
        hide={hideStudent}
        childrenLinks={[
          <ChildrenLink
            title="Daftar Siswa"
            onNavLinkClick={onNavLinkClick}
            to="/dashboard/student/profile"
            key="1"
          />,
          <ChildrenLink
            title="Kehadiran Siswa"
            onNavLinkClick={onNavLinkClick}
            to="/dashboard/student/attendance"
            key="2"
          />,
          <ChildrenLink
            title="Wali Siswa"
            onNavLinkClick={onNavLinkClick}
            to="/dashboard/student/guardian"
            key="3"
          />,
        ]}
      />
      <NestedMenu
        title="Akademik"
        icon={<AcademicIcon />}
        hide={hideAcademic}
        childrenLinks={[
          <ChildrenLink
            title="Program Studi"
            onNavLinkClick={onNavLinkClick}
            to="/dashboard/academic/study-program"
            key="1"
          />,
          <ChildrenLink
            title="Kurikulum"
            onNavLinkClick={onNavLinkClick}
            to="/dashboard/academic/curriculum"
            key="2"
          />,
          <ChildrenLink
            title="Periode"
            onNavLinkClick={onNavLinkClick}
            to="/dashboard/academic/period"
            key="3"
          />,
          <ChildrenLink
            title="Guru"
            onNavLinkClick={onNavLinkClick}
            to="/dashboard/academic/teacher"
            key="4"
          />,
          <ChildrenLink
            title="Kelas"
            onNavLinkClick={onNavLinkClick}
            to="/dashboard/academic/class"
            key="5"
          />,
          <ChildrenLink
            title="Jadwal Pelajaran"
            onNavLinkClick={onNavLinkClick}
            to="/dashboard/academic/schedule"
            key="6"
          />,
          <ChildrenLink
            title="Ekstrakurikuler"
            onNavLinkClick={onNavLinkClick}
            to="/dashboard/academic/extracurricular"
            key="7"
          />,
        ]}
      />
      <NestedMenu
        title="Rapot"
        icon={<ReportIcon />}
        hide={hideReport || !env.DEV}
        childrenLinks={[
          <ChildrenLink
            title="Template Rapot"
            onNavLinkClick={onNavLinkClick}
            to="/dashboard/report/template"
            key="1"
          />,
          <ChildrenLink
            title="Buat Rapot"
            onNavLinkClick={onNavLinkClick}
            to="/dashboard/report/list"
            key="2"
          />,
          <ChildrenLink
            title="Isi Rapot"
            onNavLinkClick={onNavLinkClick}
            to="/dashboard/report/filling"
            key="3"
          />,
          <ChildrenLink
            title="Lihat Rapot"
            onNavLinkClick={onNavLinkClick}
            to="/dashboard/report/view"
            key="4"
          />,
        ]}
      />
      <NestedMenu
        title="Informasi"
        icon={<InformationIcon />}
        hide={hideInformation}
        childrenLinks={[
          <ChildrenLink
            title="Pengumuman"
            onNavLinkClick={onNavLinkClick}
            to="/dashboard/information/announcement"
            key="1"
          />,
        ]}
      />
      <NestedMenu
        title="Keuangan"
        icon={<FinanceIcon />}
        hide={hideFinance}
        childrenLinks={[
          <ChildrenLink
            title="Invoice"
            onNavLinkClick={onNavLinkClick}
            to="/dashboard/finance/invoice"
            key="1"
          />,
        ]}
      />
    </Stack>
  );
}

function NestedMenu({
  title,
  icon,
  childrenLinks,
  hide,
}: {
  title: string;
  icon: ReactNode;
  childrenLinks: ReactElement<ChildrenLinkProps>[];
  hide?: boolean;
}) {
  const childPaths = childrenLinks.map((element) => element.props.to);
  const matchRoute = useMatchRoute();

  const isActive = childPaths.some((path) => {
    return !!matchRoute({ to: path, fuzzy: true });
  });

  const [open, setOpen] = useState(isActive);

  if (hide) return null;
  return (
    <Stack
      sx={{
        width: "100%",
        backgroundColor: open ? "base.base20" : "none",
        borderRadius: 2,
      }}
    >
      <Button
        sx={{
          width: "100%",
          fontSize: 20,
          justifyContent: "flex-start",
          padding: "8px 16px",
        }}
        onClick={() => {
          setOpen(!open);
        }}
      >
        <Box
          sx={{
            height: 30,
            width: 30,
            fontSize: 20,
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            color: isActive ? "primary.main" : "base.base50",
          }}
        >
          {icon}
        </Box>
        <Typography
          sx={{
            fontWeight: 600,
            ml: 2,
            color: isActive ? "primary.main" : "base.base50",
            flex: 1,
            textAlign: "left",
          }}
        >
          {title}
        </Typography>
        <ArrowBackIosNewRounded
          sx={{
            color: "base.base50",
            fontSize: 18,
            transform: open ? "rotate(90deg)" : "rotate(-90deg)",
          }}
        />
      </Button>
      <Stack
        sx={{
          gap: "8px",
          mt: 1,
          display: open ? "flex" : "none",
          mr: 2,
          mb: 2,
        }}
      >
        {childrenLinks}
      </Stack>
    </Stack>
  );
}

type ChildrenLinkProps = {
  title: string;
  to: LinkProps["to"];
  onNavLinkClick?: () => void;
};

function ChildrenLink({ title, to, onNavLinkClick }: ChildrenLinkProps) {
  const matchRoute = useMatchRoute();
  const isActive = !!matchRoute({ to, fuzzy: true });

  return (
    <Link to={to} className="contents">
      <Button
        onClick={onNavLinkClick}
        sx={{
          maxWidth: "100%",
          fontSize: 20,
          justifyContent: "flex-start",
          ml: "46px",
        }}
        variant={isActive ? "contained" : "text"}
      >
        <Typography
          sx={{
            fontWeight: 600,
            color: isActive ? "white" : "base.base50",
          }}
        >
          {title}
        </Typography>
      </Button>
    </Link>
  );
}

function SingleMenu({
  title,
  icon,
  to,
  hide,
  onNavLinkClick,
}: {
  title: string;
  icon: ReactNode;
  to: LinkProps["to"];
  hide?: boolean;
  onNavLinkClick?: () => void;
}) {
  const matchRoute = useMatchRoute();
  if (hide) return null;

  const isActive = !!matchRoute({ to });

  return (
    <Link to={to} className="contents">
      <Button
        onClick={onNavLinkClick}
        sx={{
          width: "100%",
          fontSize: 20,
          justifyContent: "flex-start",
          padding: "8px 16px",
        }}
        variant={isActive ? "contained" : "text"}
      >
        <Box
          sx={{
            height: 30,
            width: 30,
            fontSize: 20,
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            color: isActive ? "white" : "base.base50",
          }}
        >
          {icon}
        </Box>
        <Typography
          sx={{
            fontWeight: 600,
            ml: 2,
            color: isActive ? "white" : "base.base50",
          }}
        >
          {title}
        </Typography>
      </Button>
    </Link>
  );
}
