import { LogoutRounded, SettingsOutlined } from "@mui/icons-material";
import {
  Box,
  Button,
  Divider,
  Menu,
  MenuItem,
  Stack,
  Typography,
} from "@mui/material";
import { useDeleteSessionDataFromCookies } from "@sisva/hooks/query/useAuth";
import { useCurrentUser } from "@sisva/providers";
import { useSchool } from "@sisva/providers";
import { AvatarWithAcronymByID } from "@sisva/ui";
import { getRouteApi, Link } from "@tanstack/react-router";
import React from "react";
import toast from "react-hot-toast";

export default function UserMenu() {
  const routeApi = getRouteApi("/(main)/_layout");
  const navigate = routeApi.useNavigate();
  const school = useSchool();
  const currentUser = useCurrentUser();
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);
  const handleClose = () => {
    setAnchorEl(null);
  };

  const { mutate: deleteSessionDataFromCookies } =
    useDeleteSessionDataFromCookies({
      onSuccess() {
        toast.success("Berhasil keluar dari akun");
        navigate({ to: "/signin" });
      },
    });

  return (
    <Box>
      <Stack
        component={Button}
        id="profile-button"
        aria-controls={open ? "profile-menu" : undefined}
        aria-haspopup="true"
        aria-expanded={open ? "true" : undefined}
        onClick={(event) => {
          setAnchorEl(event.currentTarget);
        }}
        sx={{
          flexDirection: "row",
          alignItems: "center",
          justifyContent: "flex-start",
          height: 50,
          gap: 1,
        }}
      >
        <AvatarWithAcronymByID user_id={currentUser.id} size={36} />
        <Typography
          sx={{
            display: { xs: "none", lg: "block" },
            color: "black",
            fontWeight: 600,
            mr: 1,
          }}
        >
          {currentUser?.name}
        </Typography>
      </Stack>
      <Menu
        id="profile-menu"
        aria-labelledby="profile-button"
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "center",
        }}
        transformOrigin={{
          vertical: "top",
          horizontal: "center",
        }}
      >
        <Stack
          sx={{
            maxWidth: 280,
            flexDirection: "row",
            alignItems: "center",
            p: "0 8px 8px",
            gap: 1,
          }}
        >
          <AvatarWithAcronymByID user_id={currentUser.id} size={36} />
          <Typography
            color="black"
            sx={{
              fontWeight: 600,
              mr: 1,
            }}
          >
            {currentUser?.name}
          </Typography>
        </Stack>
        <Divider />
        {(currentUser.permissions.includes("manage_staff") ||
          currentUser.roles.includes("admin")) && (
          <Link
            to="/dashboard/staff/profile/$staff_username"
            params={{
              staff_username: currentUser.username,
            }}
            className="contents"
          >
            <MenuItem sx={{ maxWidth: 280 }}>
              <Stack
                sx={{
                  flexDirection: "row",
                }}
              >
                <SettingsOutlined />{" "}
                <Typography sx={{ ml: 1 }}>Profil Saya</Typography>
              </Stack>
            </MenuItem>
          </Link>
        )}
        <MenuItem
          onClick={() => deleteSessionDataFromCookies()}
          sx={{ maxWidth: 280 }}
        >
          <Stack
            sx={{
              flexDirection: "row",
            }}
          >
            <LogoutRounded /> <Typography sx={{ ml: 1 }}>Keluar</Typography>
          </Stack>
        </MenuItem>
      </Menu>
    </Box>
  );
}
