import {
  CloudDownload,
  FileDownload,
  UploadFileRounded,
} from "@mui/icons-material";
import {
  Button,
  Divider,
  Menu,
  MenuItem,
  Modal,
  Paper,
  Stack,
  Typography,
} from "@mui/material";
import { Link } from "@tanstack/react-router";
import { useToggle } from "ahooks";
import { type MouseEvent, useState } from "react";

import { ExcelIcon } from "#/assets/svgr/ExcelIcon";

export default function ExcelButton({
  showExport,
  showImport,
  importConfirmOnClick,
  downloadTemplateLink,
}: {
  showExport?: boolean;
  showImport?: boolean;
  importConfirmOnClick?: (file: File) => void;
  downloadTemplateLink?: string;
}) {
  const [file, setFile] = useState<File | null>(null);
  const [visible, { toggle }] = useToggle(false);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);
  const handleClick = (event: MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };
  return (
    <>
      <Button
        variant="outlined"
        startIcon={<ExcelIcon />}
        sx={{
          borderColor: "green",
          backgroundColor: "white",
        }}
        onClick={handleClick}
      >
        <Typography sx={{ color: "green", fontSize: 14 }}>Excel</Typography>
      </Button>
      <Menu anchorEl={anchorEl} open={open} onClose={handleClose}>
        {showExport && (
          <MenuItem>
            <Stack
              sx={{
                flexDirection: "row",
                gap: 0.5,
                alignItems: "center",
              }}
            >
              <FileDownload sx={{ fontSize: 18 }} />
              <Typography>Export</Typography>
            </Stack>
          </MenuItem>
        )}
        {showImport && (
          <MenuItem>
            <label className="cursor-pointer">
              <Stack
                sx={{
                  flexDirection: "row",
                  gap: 0.5,
                  alignItems: "center",
                }}
              >
                <UploadFileRounded sx={{ fontSize: 18 }} />
                <Typography>Import</Typography>
              </Stack>
              <input
                className="hidden"
                type="file"
                onChange={(event) => {
                  setFile(event.target.files?.[0] ?? null);
                  toggle();
                  event.target.value = "";
                }}
                multiple
              />
            </label>
          </MenuItem>
        )}
        {downloadTemplateLink && (
          <MenuItem>
            <Stack
              component="a"
              href={downloadTemplateLink}
              sx={{
                flexDirection: "row",
                gap: 0.5,
                alignItems: "center",
              }}
            >
              <CloudDownload sx={{ fontSize: 18 }} />
              <Typography>Unduh Template</Typography>
            </Stack>
          </MenuItem>
        )}
      </Menu>
      <Modal open={visible} onClose={toggle}>
        <Stack
          sx={{
            width: 1,
            height: 1,
            justifyContent: "center",
            alignItems: "center",
            p: 2,
          }}
        >
          <Paper
            component={"form"}
            sx={{
              py: 2,
              width: 1,
              maxWidth: "ms",
              gap: 2,
              display: "flex",
              flexDirection: "column",
            }}
          >
            <Typography sx={{ fontWeight: 600, px: 2 }}>
              Import Excel
            </Typography>
            <Divider />
            <Stack sx={{ gap: 2, px: 2 }}>
              <Stack
                sx={{
                  flexDirection: "row",
                  justifyContent: "space-between",
                }}
              >
                <Typography sx={{ fontWeight: 600 }}>Nama File</Typography>
                <Typography>{file?.name}</Typography>
              </Stack>
            </Stack>
            <Divider />
            <Stack sx={{ gap: 2, px: 2, flexDirection: "row" }}>
              <Button variant="outlined" fullWidth onClick={toggle}>
                Batal
              </Button>
              <Button
                variant="contained"
                fullWidth
                onClick={() => {
                  if (file) importConfirmOnClick?.(file);
                  toggle();
                }}
              >
                Import
              </Button>
            </Stack>
          </Paper>
        </Stack>
      </Modal>
    </>
  );
}
