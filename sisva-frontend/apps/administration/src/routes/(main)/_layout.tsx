import { Box } from "@mui/material";
import { currentUserQueryOptions } from "@sisva/hooks/query/user/useUsers";
import { currentSchoolQueryOptions } from "@sisva/hooks/query/useSchools";
import {
  CurrentUserProvider,
  MUIThemeProvider,
  SchoolProvider,
  type SchoolWithMoreData,
} from "@sisva/providers";
import type { School, User } from "@sisva/types/apiTypes";
import type { UserType } from "@sisva/types/types";
import {
  deleteSessionDataFromCookies,
  getFileUrl,
  themeConfigMUI,
} from "@sisva/utils";
import { createFileRoute, Outlet, redirect } from "@tanstack/react-router";
import { ConfigProvider } from "antd";
import idID from "antd/locale/id_ID";

import { Container } from "./-components/Container";

export const Route = createFileRoute("/(main)/_layout")({
  component: Layout,
  async beforeLoad({ context: { queryClient } }) {
    let currentUser: User;
    let currentSchool: School;
    try {
      currentUser = await queryClient.fetchQuery(currentUserQueryOptions);
      currentSchool = await queryClient.fetchQuery(currentSchoolQueryOptions);
    } catch {
      throw redirect({ to: "/signin" });
    }

    const allowedTypes: UserType[] = ["staff", "teacher"];
    if (!allowedTypes.includes(currentUser.type)) {
      deleteSessionDataFromCookies();
      throw redirect({ to: "/signin" });
    }

    return {
      currentUser,
      currentSchool,
    };
  },
  loader({ context: { currentSchool, currentUser } }) {
    return {
      currentUser,
      currentSchool,
    };
  },
});

function Layout() {
  const { currentUser, currentSchool } = Route.useLoaderData();
  const school = currentSchool as SchoolWithMoreData;

  school.logo_url = getFileUrl(school.logo_uri, school.id);
  school.landing_image_url = getFileUrl(school.landing_image_uri, school.id);

  themeConfigMUI.palette.primary.main = school.theme_json_text;

  return (
    <CurrentUserProvider currentUser={currentUser}>
      <SchoolProvider school={school}>
        <MUIThemeProvider themeConfig={themeConfigMUI}>
          <ConfigProvider
            theme={{
              token: {
                // ! theme_json_text somehow is not a json
                colorPrimary: school.theme_json_text,
              },
            }}
            locale={idID}
          >
            <Box
              sx={{
                height: "100vh",
                maxHeight: "100vh",
                width: "100%",
              }}
            >
              <Container>
                <div className="max-h-[90vh] overflow-auto h-full">
                  <Outlet />
                </div>
              </Container>
            </Box>
          </ConfigProvider>
        </MUIThemeProvider>
      </SchoolProvider>
    </CurrentUserProvider>
  );
}
