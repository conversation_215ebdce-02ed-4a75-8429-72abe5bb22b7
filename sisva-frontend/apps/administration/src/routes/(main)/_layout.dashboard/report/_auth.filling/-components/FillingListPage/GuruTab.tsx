import { Clear, Search, Sort } from "@mui/icons-material";
import {
  <PERSON><PERSON>,
  Chip,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  IconButton,
  InputAdornment,
  Stack,
  Typography,
  useMediaQuery,
} from "@mui/material";
import type { GridColDef } from "@mui/x-data-grid";
import { DataGrid, useGridApiContext } from "@mui/x-data-grid";
import { useClassesWithSubjectAndTeacherAndStudentGroup } from "@sisva/hooks/query/academic/useClasses";
import { useReport } from "@sisva/hooks/query/report/useReports";
import type { User } from "@sisva/types/apiTypes";
import { AvatarWithAcronymByID } from "@sisva/ui";
import { onlyUnique } from "@sisva/utils";
import { Link } from "@tanstack/react-router";
import { useToggle } from "ahooks";
import { useAtomValue } from "jotai";
import { useEffect } from "react";
import { useForm } from "react-hook-form";
import { SelectElement, TextFieldElement } from "react-hook-form-mui";

import EditIcon from "#/routes/(main)/-components/EditIcon";

import { reportIdAtom } from "./FillingListPage";
import { ReportIdSelector } from "./ReportIdSelector";

type Field =
  | "mobile_column"
  | "action"
  | "subjects"
  | "teacher"
  | "student_groups"
  | "status";
type TypeSafeColDef = GridColDef & { field: Field };

export default function GuruTab() {
  const isMobile = useMediaQuery((theme) => theme.breakpoints.down("lg"));
  const reportId = useAtomValue(reportIdAtom);
  const { data: report } = useReport({ report_id: reportId });
  const { data: classes = [] } =
    useClassesWithSubjectAndTeacherAndStudentGroup();
  const classesGroupByTeacherId = Object.entries(
    Object.groupBy(
      classes.filter(
        (class_) =>
          // filter by period_id
          class_.student_group?.period_id == report?.period_id &&
          // filter by student_group's study_program_id if report has one
          (!report?.study_program_id ||
            report?.study_program_id ==
              class_.student_group?.study_program_id) &&
          // filter by student_group's grade if report has one
          (!report?.grade || report?.grade == class_.student_group?.grade)
      ),
      (item) => item.teacher_id
    )
  ).map(([teacher_id, classes]) => {
    return {
      teacher_id,
      teacher: classes?.[0]?.teacher,
      classes,
    };
  });

  const columns: TypeSafeColDef[] = [
    {
      field: "mobile_column",
      headerName: "",
      valueGetter: (_, row) => row,
      flex: 1,
      display: "flex",
      renderCell: (args) => {
        const value = args.value as (typeof classesGroupByTeacherId)[0];
        return (
          <Stack sx={{ p: 1.5, gap: 2, width: "100%" }}>
            <Stack
              sx={{
                py: 1.5,
                flexDirection: "row",
                gap: 1,
                alignItems: "center",
              }}
            >
              <AvatarWithAcronymByID user_id={value.teacher?.id} />
              <Typography>{value.teacher?.name}</Typography>
            </Stack>
            <Stack>
              <Typography color="textSecondary">Mata Pelajaran</Typography>
              <Stack
                sx={{
                  py: 1,
                  flexDirection: "row",
                  gap: 0.5,
                  flexWrap: "wrap",
                }}
              >
                {value.classes
                  ?.map((class_) => class_.subject_name)
                  .filter(onlyUnique)
                  .map((item: string) => (
                    <Chip key={item} label={item} color="primary" />
                  ))}
              </Stack>
            </Stack>

            <Stack>
              <Typography color="textSecondary">Kelas</Typography>
              <Stack
                sx={{
                  py: 1,
                  flexDirection: "row",
                  gap: 0.5,
                  flexWrap: "wrap",
                }}
              >
                {value.classes
                  ?.map((class_) => class_.student_group_name)
                  .filter(onlyUnique)
                  .map((item: string) => (
                    <Chip key={item} label={item} color="primary" />
                  ))}
              </Stack>
            </Stack>
            <Stack>
              <Typography color="textSecondary">Status</Typography>
              {/* //TODO: hardcoded */}
              <Typography>Belum Lengkap</Typography>
            </Stack>
          </Stack>
        );
      },
    },
    {
      field: "teacher",
      headerName: "Guru",
      valueGetter: (_, row: (typeof classesGroupByTeacherId)[number]) => {
        return row.teacher;
      },
      display: "flex",
      flex: 1,
      minWidth: 250,
      renderCell: ({ value }) => {
        const teacher = value as User;
        return (
          <Stack
            sx={{ py: 1.5, flexDirection: "row", gap: 1, alignItems: "center" }}
          >
            <AvatarWithAcronymByID user_id={teacher.id} />
            <Typography>{teacher.name}</Typography>
          </Stack>
        );
      },
    },
    {
      field: "subjects",
      headerName: "Mata Pelajaran",
      display: "flex",
      flex: 1,
      minWidth: 350,
      valueGetter: (_, row: (typeof classesGroupByTeacherId)[number]) => {
        return (
          row.classes
            ?.map((class_) => class_.subject_name)
            .filter(onlyUnique) ?? []
        );
      },
      renderCell: ({ value }) => (
        <Stack
          sx={{
            p: 1,
            flexDirection: "row",
            gap: 0.5,
            flexWrap: "wrap",
          }}
        >
          {value.map((item: string) => (
            <Chip key={item} label={item} color="primary" />
          ))}
        </Stack>
      ),
    },

    {
      field: "student_groups",
      flex: 1,
      valueGetter: (_, row: (typeof classesGroupByTeacherId)[number]) => {
        return (
          row.classes
            ?.map((class_) => class_.student_group_name)
            .filter(onlyUnique) ?? []
        );
      },
      headerName: "Kelas",
      display: "flex",
      minWidth: 200,
      renderCell: ({ value }) => (
        <Stack
          sx={{
            p: 1,
            flexDirection: "row",
            gap: 0.5,
            flexWrap: "wrap",
          }}
        >
          {value.map((item: string) => (
            <Chip key={item} label={item} color="primary" />
          ))}
        </Stack>
      ),
    },
    {
      field: "status",
      headerName: "Status",
      width: 150,
      display: "flex",
      renderCell: ({ value }) => (
        //TODO: hardcoded
        <Stack sx={{ py: 1.5, flexDirection: "row", gap: 0.5 }}>
          Belum Lengkap
        </Stack>
      ),
    },
    {
      field: "action",
      headerName: "Aksi",
      width: 128,
      display: "flex",
      renderCell: ({
        row,
      }: {
        row: (typeof classesGroupByTeacherId)[number];
      }) => {
        if (!report || !row.teacher?.username) return;

        return (
          <Stack
            sx={{
              display: "flex",
              flexDirection: "row",
              gap: 1,
              p: 1,
            }}
          >
            <Link
              to="/dashboard/report/filling/$report_id"
              params={{ report_id: report?.id }}
              search={{
                teacher_username: row.teacher?.username,
              }}
            >
              <EditIcon />
            </Link>
          </Stack>
        );
      },
      sortable: false,
    },
  ];

  const columnVisibilityModel = Object.fromEntries(
    columns
      .map((column) => column.field)
      .filter((field) =>
        isMobile ? field !== "mobile_column" : field === "mobile_column"
      )
      .map((field) => [field, false])
  );

  return (
    <DataGrid
      sx={{
        "& .MuiDataGrid-cell:focus": {
          outline: " none",
        },
        border: "none",
      }}
      getRowId={(row: (typeof classesGroupByTeacherId)[number]) =>
        row.teacher_id
      }
      disableRowSelectionOnClick
      className="bg-white grow thick-scrollbar"
      rows={classesGroupByTeacherId}
      columns={columns}
      initialState={{ pagination: { paginationModel: { pageSize: 20 } } }}
      pageSizeOptions={[10, 20, 50]}
      getRowClassName={(params) =>
        params.indexRelativeToCurrentPage % 2 === 0
          ? "bg-neutral-50"
          : "bg-neutral-100"
      }
      slots={{
        toolbar: CustomToolbar,
      }}
      getRowHeight={() => "auto"}
      columnVisibilityModel={columnVisibilityModel}
      columnHeaderHeight={isMobile ? 0 : undefined}
      disableColumnMenu
      localeText={{
        noRowsLabel: report ? "Tidak ada data" : "Pilih Rapot terlebih dahulu",
        MuiTablePagination: {
          labelDisplayedRows: ({ from, to, count, page }) => {
            return `${from}-${to} dari ${count}`;
          },
        },
      }}
    />
  );
}

function CustomToolbar() {
  const api = useGridApiContext();
  const [open, { toggle }] = useToggle(false);
  const { control, handleSubmit, reset, setValue, watch } = useForm<{
    field: Field | "";
    sortDirection: "asc" | "desc";
    quickFilter: string;
  }>({
    defaultValues: {
      field: "",
      sortDirection: "asc",
      quickFilter: "",
    },
  });

  const sortSelectOptions: { label: string; id: Field }[] = [
    { label: "Guru", id: "teacher" },
    { label: "Status", id: "status" },
  ];

  const quickFilter = watch("quickFilter");
  useEffect(() => {
    api.current.setQuickFilterValues(
      quickFilter.split(" ").filter((s) => s !== "")
    );
  }, [api, quickFilter]);

  return (
    <div className="p-4 flex flex-col gap-2 border-b border-0 border-solid border-neutral-200">
      <div className=" flex justify-between ">
        <Stack
          sx={{
            flexDirection: "row",
            gap: 2,
          }}
        >
          <TextFieldElement
            control={control}
            name="quickFilter"
            placeholder="Cari"
            size="small"
            slotProps={{
              input: {
                endAdornment: (
                  <InputAdornment position="end">
                    {quickFilter ? (
                      <IconButton
                        size="small"
                        onClick={() => setValue("quickFilter", "")}
                      >
                        <Clear />
                      </IconButton>
                    ) : (
                      <Search />
                    )}
                  </InputAdornment>
                ),
              },
            }}
          />
          <ReportIdSelector />
        </Stack>
      </div>
      <Button
        startIcon={<Sort />}
        variant="outlined"
        onClick={toggle}
        sx={{
          display: { xs: "flex", lg: "none" },
        }}
      >
        Urutkan Data
      </Button>
      <Dialog open={open} onClose={toggle}>
        <DialogTitle>Urutkan</DialogTitle>
        <DialogContent
          sx={{
            width: "250px",
            display: "flex",
            flexDirection: "column",
            gap: 2,
            overflow: "initial",
          }}
        >
          <SelectElement
            control={control}
            name="field"
            options={sortSelectOptions}
            label="Pilih Kolom"
            slotProps={{
              input: {
                endAdornment: watch("field") && (
                  <InputAdornment position="end">
                    <IconButton onClick={() => setValue("field", "")}>
                      <Clear />
                    </IconButton>
                  </InputAdornment>
                ),
              },
            }}
          />
          <SelectElement
            control={control}
            name="sortDirection"
            options={[
              { label: "A-Z", id: "asc" },
              { label: "Z-A", id: "desc" },
            ]}
            label="Urutan"
          />
        </DialogContent>
        <DialogActions sx={{ pb: 2, px: 3 }}>
          <Button
            variant="outlined"
            onClick={() => {
              reset();
              toggle();
            }}
          >
            Batal
          </Button>
          <Button
            variant="contained"
            onClick={handleSubmit((value) => {
              if (value.field) {
                api.current.sortColumn(value.field, value.sortDirection);
              } else {
                api.current.sortColumn("name", null);
              }
              toggle();
            })}
          >
            Simpan
          </Button>
        </DialogActions>
      </Dialog>
    </div>
  );
}
