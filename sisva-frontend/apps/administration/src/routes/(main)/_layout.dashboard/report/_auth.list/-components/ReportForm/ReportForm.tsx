import { valibotResolver } from "@hookform/resolvers/valibot";
import { Button, Divider, Paper, Stack, Typography } from "@mui/material";
import {
  usePeriodCurriculums,
  usePeriods,
} from "@sisva/hooks/query/academic/usePeriods";
import { useStudyPrograms } from "@sisva/hooks/query/academic/useStudyPrograms";
import {
  useCreateReport,
  useReport,
  useUpdateReport,
} from "@sisva/hooks/query/report/useReports";
import { useTemplates } from "@sisva/hooks/query/report/useTemplates";
import { reportStatusOptions } from "@sisva/types/dropdownOptions";
import { unique } from "@sisva/utils";
import { Link, useNavigate } from "@tanstack/react-router";
import { useState } from "react";
import {
  AutocompleteElement,
  SelectElement,
  TextFieldElement,
  useForm,
} from "react-hook-form-mui";
import toast from "react-hot-toast";
import {
  type InferInput,
  type InferOutput,
  literal,
  minLength,
  nullable,
  number,
  object,
  pipe,
  string,
  union,
} from "valibot";

import ReportPreview from "../ReportPreview";

export default function ReportForm({ report_id }: { report_id?: number }) {
  const navigate = useNavigate();
  const [display, setDisplay] = useState<"report" | "preview">("report");
  const schema = object({
    name: pipe(string("Nama harus diisi"), minLength(1, "Nama harus diisi")),
    template_id: pipe(
      nullable(number("Template harus dipilih")),
      number("Template harus dipilih")
    ),
    period_id: pipe(
      nullable(number("Periode harus dipilih")),
      number("Periode harus dipilih")
    ),
    study_program_id: nullable(number()),
    grade: nullable(string()),
    status: union([literal("draft"), literal("filling"), literal("published")]),
  });
  type SchemaInput = InferInput<typeof schema>;
  type SchemaOutput = InferOutput<typeof schema>;

  const { data: report } = useReport({ report_id });

  const { control, watch, setValue, handleSubmit } = useForm<
    SchemaInput,
    unknown,
    SchemaOutput
  >({
    values: {
      name: report?.name ?? "",
      template_id: report?.template_id ?? null,
      period_id: report?.period_id ?? null,
      study_program_id: report?.study_program_id ?? null,
      grade: report?.grade ?? null,
      status: report?.status ?? "draft",
    },
    resolver: valibotResolver(schema),
  });

  const study_program_id = watch("study_program_id");
  const period_id = watch("period_id");

  const { data: templates = [] } = useTemplates();
  const { data: periods = [] } = usePeriods();
  const { data: periodCurriculums = [] } = usePeriodCurriculums();
  const { data: studyPrograms = [] } = useStudyPrograms();

  const studyProgramIds = unique(
    periodCurriculums
      .filter((periodCurriculum) => {
        return periodCurriculum.period_id === period_id;
      })
      .map((periodCurriculum) => periodCurriculum.study_program_id)
  );
  const filteredStudyPrograms = studyPrograms.filter((studyProgram) =>
    studyProgramIds.includes(studyProgram.id)
  );

  const grades = unique(
    periodCurriculums
      .filter((periodCurriculum) => {
        return (
          periodCurriculum.period_id === period_id &&
          periodCurriculum.study_program_id === study_program_id
        );
      })
      .map((periodCurriculum) => periodCurriculum.grade)
  );

  const { mutate: createReport } = useCreateReport();
  const { mutate: updateReport } = useUpdateReport();

  const onSubmit = handleSubmit((value) => {
    if (report_id) {
      return updateReport(
        {
          ...value,
          id: report_id,
          study_program_id: value.study_program_id ?? undefined,
          grade: value.grade ?? undefined,
        },
        {
          onSuccess() {
            toast.success("Rapot berhasil diperbarui");
            navigate({ to: "/dashboard/report/list" });
          },
          onError() {
            toast.error("Rapot gagal diperbarui");
          },
        }
      );
    }
    createReport(
      {
        ...value,
        study_program_id: value.study_program_id ?? undefined,
        grade: value.grade ?? undefined,
      },
      {
        onSuccess() {
          toast.success("Rapot berhasil dibuat");
          navigate({ to: "/dashboard/report/list" });
        },
        onError() {
          toast.error("Rapot gagal dibuat");
        },
      }
    );
  });

  return (
    <>
      <Paper
        sx={{
          display: display === "report" ? "flex" : "none",
          flexDirection: "column",
          gap: 2,
          p: 2,
        }}
        component="form"
        onSubmit={onSubmit}
      >
        <Typography sx={{ fontWeight: 600, fontSize: 18 }}>
          Informasi Rapot
        </Typography>
        <Stack sx={{ flexDirection: "column", gap: 2 }}>
          <TextFieldElement control={control} name="name" label="Nama Rapot" />
          <AutocompleteElement
            control={control}
            name="template_id"
            label="Template"
            matchId
            options={templates.map((item) => ({
              label: item.name,
              id: item.id,
            }))}
          />
          <AutocompleteElement
            control={control}
            name="period_id"
            label="Periode"
            matchId
            options={periods.map((item) => ({
              label: item.name,
              id: item.id,
            }))}
            autocompleteProps={{
              onChange(_, value) {
                const study_program_id = value?.id;
                if (
                  study_program_id &&
                  !studyProgramIds.includes(study_program_id)
                ) {
                  setValue("study_program_id", null);
                }
              },
            }}
          />
          <AutocompleteElement
            control={control}
            name="study_program_id"
            label="Program Studi"
            matchId
            options={filteredStudyPrograms.map((item) => ({
              label: item.name,
              id: item.id,
            }))}
            autocompleteProps={{
              onChange() {
                setValue("grade", null);
              },
              disabled: !period_id,
            }}
          />
          <AutocompleteElement
            control={control}
            name="grade"
            label="Tingkatan"
            matchId
            options={grades.map((item) => ({
              label: item,
              id: item,
            }))}
            autocompleteProps={{
              disabled: !study_program_id,
            }}
          />
          {report_id && (
            <SelectElement
              control={control}
              name="status"
              label="Status"
              options={reportStatusOptions.map((item) => ({
                id: item.value,
                label: item.label,
              }))}
            />
          )}
        </Stack>
        <Divider />
        <Stack sx={{ flexDirection: "row", gap: 2, justifyContent: "end" }}>
          <Link to="/dashboard/report/list">
            <Button variant="outlined">Batal</Button>
          </Link>
          <Button variant="outlined" onClick={() => setDisplay("preview")}>
            Preview
          </Button>
          <Button variant="contained" type="submit">
            Simpan
          </Button>
        </Stack>
      </Paper>
      <ReportPreview
        sx={display === "preview" ? undefined : { display: "none" }}
        onBackClick={() => setDisplay("report")}
      />
    </>
  );
}
