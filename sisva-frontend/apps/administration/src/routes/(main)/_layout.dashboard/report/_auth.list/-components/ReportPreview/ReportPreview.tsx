import type { SxProps } from "@mui/material";
import { Button, Paper, Stack } from "@mui/material";
import * as templates from "@sisva/template-collection";
import * as jspdf from "jspdf";
import * as jspdf_autotable from "jspdf-autotable";
import { useEffect, useRef, useState } from "react";

const template1Function = templates.schools.DEMO.template1Function;
const template1FunctionString = template1Function.toString();
const templateFunction = new Function(
  "param",
  `return ${template1FunctionString} `
)();

export default function ReportPreview({
  sx,
  onBackClick,
}: {
  sx?: SxProps;
  onBackClick?: () => void;
}) {
  const jsPDFPreview = useRef<HTMLIFrameElement>(null);
  const [urlPDF, setUrlPDF] = useState<string | null>(null);

  useEffect(() => {
    templateFunction({ jspdf, jspdf_autotable }).then((blob: Blob) => {
      const urlPDF = URL.createObjectURL(blob);
      setUrlPDF(urlPDF);
    });
  }, []);

  return (
    <Paper
      sx={{ display: "flex", flexDirection: "column", gap: 2, p: 2, ...sx }}
    >
      {urlPDF && (
        <iframe
          ref={jsPDFPreview}
          src={urlPDF}
          className="w-full h-[70svh]"
        ></iframe>
      )}
      <Stack sx={{ flexDirection: "row", gap: 2, justifyContent: "end" }}>
        <Button variant="outlined" onClick={onBackClick}>
          Kembali
        </Button>
      </Stack>
    </Paper>
  );
}
