import { reportsQueryOptions } from "@sisva/hooks/query/report/useReports";
import { createFileRoute } from "@tanstack/react-router";
import { getDefaultStore } from "jotai";

import AssessmentPage from "./-components/FillingListPage";
import { reportIdAtom } from "./-components/FillingListPage";
const store = getDefaultStore();

export const Route = createFileRoute(
  "/(main)/_layout/dashboard/report/_auth/filling/"
)({
  async beforeLoad({ context: { queryClient } }) {
    const reports = await queryClient.fetchQuery(reportsQueryOptions);
    const lastReportId = reports[reports.length - 1]?.id;
    const reportId = store.get(reportIdAtom);
    const isValidReportId = reports.some((report) => report.id === reportId);
    // set reportId to lastReportId if it's not set
    if (!reportId && !!lastReportId) {
      store.set(reportIdAtom, () => lastReportId);
    }
    // set reportId to something else if it's not valid (eg: the report was deleted)
    if (!!reportId && !isValidReportId) {
      store.set(reportIdAtom, () => lastReportId ?? null);
    }
  },
  head: () => ({
    meta: [
      {
        title: "Isi Rapot | Sisva",
      },
    ],
  }),
  component: AssessmentPage,
});
