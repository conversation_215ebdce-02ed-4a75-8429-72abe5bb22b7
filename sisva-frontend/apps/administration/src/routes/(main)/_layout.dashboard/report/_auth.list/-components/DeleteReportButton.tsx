import { DeleteForever } from "@mui/icons-material";
import {
  Button,
  Divide<PERSON>,
  <PERSON>dal,
  Paper,
  Stack,
  Typography,
} from "@mui/material";
import {
  useDeleteReport,
  useReport,
} from "@sisva/hooks/query/report/useReports";
import { useToggle } from "ahooks";
import type { ReactNode } from "react";
import toast from "react-hot-toast";

export default function DeleteReportButton({
  report_id,
  renderTrigger,
}: {
  report_id: number;
  renderTrigger?: (onClick: () => void) => ReactNode;
}) {
  const { data: report } = useReport({ report_id });

  const [visible, { toggle }] = useToggle(false);

  const { mutate: deleteStudyProgram } = useDeleteReport();

  return (
    <>
      {renderTrigger ? (
        renderTrigger(toggle)
      ) : (
        <Button
          variant="contained"
          startIcon={<DeleteForever />}
          color="error"
          onClick={toggle}
        >
          Hapus
        </Button>
      )}
      <Modal open={visible} onClose={toggle}>
        <Stack
          sx={{
            width: 1,
            height: 1,
            justifyContent: "center",
            alignItems: "center",
            p: 2,
          }}
        >
          <Paper
            component="form"
            sx={{
              py: 2,
              width: 1,
              maxWidth: "ms",
              gap: 2,
              display: "flex",
              flexDirection: "column",
            }}
            onSubmit={(e) => {
              e.preventDefault();
              deleteStudyProgram(
                { report_id },
                {
                  onSuccess: () => {
                    toggle();
                    toast.success("Rapot berhasil dihapus");
                  },
                  onError: () => {
                    toast.error("Rapot gagal dihapus");
                  },
                }
              );
            }}
          >
            <Typography sx={{ fontWeight: 600, px: 2 }}>Hapus Rapot</Typography>
            <Divider />
            <Stack sx={{ gap: 2, px: 2 }}>
              <Stack
                sx={{ flexDirection: "row", justifyContent: "space-between" }}
              >
                <Typography sx={{ fontWeight: 600 }}>Rapot</Typography>
                <Typography>{report?.name}</Typography>
              </Stack>
              <Stack
                sx={{ flexDirection: "row", justifyContent: "space-between" }}
              >
                <Typography sx={{ fontWeight: 600 }}>Template</Typography>
                <Typography>{report?.template?.name}</Typography>
              </Stack>
            </Stack>
            <Divider />
            <Stack sx={{ gap: 2, px: 2, flexDirection: "row" }}>
              <Button variant="outlined" fullWidth onClick={toggle}>
                Batal
              </Button>
              <Button type="submit" variant="contained" fullWidth color="error">
                Hapus
              </Button>
            </Stack>
          </Paper>
        </Stack>
      </Modal>
    </>
  );
}
