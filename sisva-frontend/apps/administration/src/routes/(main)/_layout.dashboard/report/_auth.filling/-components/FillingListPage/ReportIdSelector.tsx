import { Autocomplete, TextField } from "@mui/material";
import { useReports } from "@sisva/hooks/query/report/useReports";
import { useAtom } from "jotai";

import { reportIdAtom } from "./FillingListPage";

export function ReportIdSelector() {
  const [reportId, setReportId] = useAtom(reportIdAtom);
  const { data: reports = [] } = useReports();

  const report = reports.find((item) => item.id === reportId);

  return (
    <Autocomplete
      value={report ? { id: report.id, label: report.name } : null}
      options={reports.map((item) => ({
        id: item.id,
        label: item.name,
      }))}
      onChange={(_, value) => {
        setReportId(value?.id ?? null);
      }}
      sx={{
        minWidth: 200,
      }}
      renderInput={(params) => {
        return <TextField {...params} label="Rapot" />;
      }}
      size="small"
    />
  );
}
