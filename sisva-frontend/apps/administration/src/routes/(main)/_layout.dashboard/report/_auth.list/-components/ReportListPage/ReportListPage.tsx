import { Add, Clear, Search, Sort, Visibility } from "@mui/icons-material";
import {
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  IconButton,
  InputAdornment,
  Stack,
  Typography,
  useMediaQuery,
} from "@mui/material";
import type { GridColDef } from "@mui/x-data-grid";
import { DataGrid, useGridApiContext } from "@mui/x-data-grid";
import { useReports } from "@sisva/hooks/query/report/useReports";
import type { Report } from "@sisva/types/apiTypes";
import { getReportStatusText } from "@sisva/types/types";
import { Link } from "@tanstack/react-router";
import { useToggle } from "ahooks";
import { useEffect } from "react";
import { useForm } from "react-hook-form";
import { SelectElement, TextFieldElement } from "react-hook-form-mui";

import DeleteIcon from "#/routes/(main)/-components/DeleteIcon";
import EditIcon from "#/routes/(main)/-components/EditIcon";

import DeleteReportButton from "../DeleteReportButton";

type Field = "mobile_column" | "action" | keyof Report;
type TypeSafeColDef = GridColDef & { field: Field };

export default function ReportListPage() {
  const isMobile = useMediaQuery((theme) => theme.breakpoints.down("lg"));

  const { data: reports = [] } = useReports();

  const columns: TypeSafeColDef[] = [
    {
      field: "mobile_column",
      headerName: "",
      valueGetter: (_, row) => row,
      flex: 1,
      display: "flex",
      renderCell: (args) => {
        const value = args.value as (typeof reports)[number];
        return (
          <Stack sx={{ p: 1.5, gap: 2, width: "100%" }}>
            <Stack
              sx={{
                flexDirection: "row",
                justifyContent: "space-between",
                width: "100%",
              }}
            >
              <Stack>
                <Typography color="textSecondary">Nama Rapot</Typography>
                <Typography sx={{ fontSize: 17 }}>{value.name}</Typography>
              </Stack>
            </Stack>
            <Stack>
              <Typography color="textSecondary">Periode</Typography>
              <Typography>{value.period?.name}</Typography>
            </Stack>
            <Stack>
              <Typography color="textSecondary">Template</Typography>
              <Typography>{value.template?.name}</Typography>
            </Stack>
            <Stack>
              <Typography color="textSecondary">Program Studi</Typography>
              <Typography>{value.study_program?.name}</Typography>
            </Stack>
            <Stack>
              <Typography color="textSecondary">Tingkatan</Typography>
              <Typography>{value.grade}</Typography>
            </Stack>
            <Stack>
              <Typography color="textSecondary">Status</Typography>
              <Typography>{getReportStatusText(value.status)}</Typography>
            </Stack>
            <Stack
              sx={{
                display: "flex",
                flexDirection: "row",
                justifyContent: "end",
                gap: 1,
              }}
            >
              <Link
                to="/dashboard/report/list/$report_id/edit"
                params={{ report_id: value.id }}
              >
                <EditIcon />
              </Link>
              <DeleteReportButton
                report_id={value.id}
                renderTrigger={(onClick) => {
                  return <DeleteIcon onClick={onClick} />;
                }}
              />
            </Stack>
          </Stack>
        );
      },
    },
    {
      field: "name",
      headerName: "Nama",
      display: "flex",
      minWidth: 150,
      flex: 1,
      renderCell: ({ value }) => (
        <Typography sx={{ py: 1.5, fontWeight: 600 }}>{value}</Typography>
      ),
    },
    {
      field: "period_id",
      valueGetter: (_, row: (typeof reports)[number]) => row.period?.name,
      headerName: "Periode",
      display: "flex",
      renderCell: ({ row }: { row: (typeof reports)[number] }) => (
        <Stack sx={{ py: 1.5 }}>{row.period?.name}</Stack>
      ),
    },
    {
      field: "template_id",
      valueGetter: (_, row: (typeof reports)[number]) => row.template?.name,
      headerName: "Template",
      display: "flex",
      renderCell: ({ row }: { row: (typeof reports)[number] }) => (
        <Stack sx={{ py: 1.5, flexDirection: "row", gap: 0.5 }}>
          {row.template?.name}
        </Stack>
      ),
    },
    {
      field: "study_program_id",
      headerName: "Program Studi",
      minWidth: 150,
      valueGetter: (_, row: (typeof reports)[number]) =>
        row.study_program?.name,
      flex: 1,
      display: "flex",
      renderCell: ({ row }: { row: (typeof reports)[number] }) => (
        <Stack sx={{ py: 1.5, flexDirection: "row", gap: 0.5 }}>
          {row.study_program?.name}
        </Stack>
      ),
    },
    {
      field: "grade",
      headerName: "Tingkatan",
      width: 100,
      display: "flex",
      renderCell: ({ row }: { row: (typeof reports)[number] }) => (
        <Stack sx={{ py: 1.5, flexDirection: "row", gap: 0.5 }}>
          {row.grade}
        </Stack>
      ),
    },
    {
      field: "status",
      headerName: "Status",
      valueGetter: (_, row: (typeof reports)[number]) =>
        getReportStatusText(row.status ?? "draft"),
      width: 100,
      display: "flex",
      renderCell: ({ value }) => {
        return (
          <Stack sx={{ py: 1.5, flexDirection: "row", gap: 0.5 }}>
            {value}
          </Stack>
        );
      },
    },
    {
      field: "action",
      headerName: "Aksi",
      valueGetter: (_, row) => row.id,
      width: 128,
      display: "flex",
      renderCell: ({ value }) => (
        <Stack
          sx={{
            display: "flex",
            flexDirection: "row",
            gap: 1,
            p: 1,
          }}
        >
          <Link
            to="/dashboard/report/list/$report_id/edit"
            params={{ report_id: value }}
          >
            <EditIcon />
          </Link>
          <DeleteReportButton
            report_id={value}
            renderTrigger={(onClick) => {
              return <DeleteIcon onClick={onClick} />;
            }}
          />
        </Stack>
      ),
      sortable: false,
    },
  ];

  const columnVisibilityModel = Object.fromEntries(
    columns
      .map((column) => column.field)
      .filter((field) =>
        isMobile ? field !== "mobile_column" : field === "mobile_column"
      )
      .map((field) => [field, false])
  );

  return (
    <div className="size-full p-0 lg:p-8 flex flex-col gap-4">
      <Stack
        sx={{
          flexDirection: "row",
          gap: 2,
          justifyContent: "space-between",
          display: {
            xs: "none",
            lg: "flex",
          },
        }}
      >
        <Typography sx={{ fontWeight: 700, fontSize: 20 }}>
          Buat Rapot
        </Typography>
      </Stack>
      <DataGrid
        sx={{
          "& .MuiDataGrid-cell:focus": {
            outline: " none",
          },
        }}
        disableRowSelectionOnClick
        className="bg-white grow thick-scrollbar"
        rows={reports}
        columns={columns}
        initialState={{ pagination: { paginationModel: { pageSize: 20 } } }}
        pageSizeOptions={[10, 20, 50]}
        getRowClassName={(params) =>
          params.indexRelativeToCurrentPage % 2 === 0
            ? "bg-neutral-50"
            : "bg-neutral-100"
        }
        slots={{
          toolbar: CustomToolbar,
        }}
        getRowHeight={() => "auto"}
        columnVisibilityModel={columnVisibilityModel}
        columnHeaderHeight={isMobile ? 0 : undefined}
        disableColumnMenu
      />
    </div>
  );
}

function CustomToolbar() {
  const api = useGridApiContext();
  const [open, { toggle }] = useToggle(false);
  const { control, handleSubmit, reset, setValue, watch } = useForm<{
    field: Field | "";
    sortDirection: "asc" | "desc";
    quickFilter: string;
  }>({
    defaultValues: {
      field: "",
      sortDirection: "asc",
      quickFilter: "",
    },
  });

  const sortSelectOptions: { label: string; id: Field }[] = [
    { label: "Nama", id: "name" },
    { label: "Periode", id: "period_id" },
    { label: "Status", id: "status" },
    { label: "Template", id: "template_id" },
  ];

  const quickFilter = watch("quickFilter");
  useEffect(() => {
    api.current.setQuickFilterValues(
      quickFilter.split(" ").filter((s) => s !== "")
    );
  }, [api, quickFilter]);

  return (
    <div className="p-4 flex flex-col gap-2 border-b border-0 border-solid border-neutral-200">
      <div className=" flex justify-between ">
        <TextFieldElement
          control={control}
          name="quickFilter"
          placeholder="Cari Rapot"
          size="small"
          slotProps={{
            input: {
              endAdornment: (
                <InputAdornment position="end">
                  {quickFilter ? (
                    <IconButton
                      size="small"
                      onClick={() => setValue("quickFilter", "")}
                    >
                      <Clear />
                    </IconButton>
                  ) : (
                    <Search />
                  )}
                </InputAdornment>
              ),
            },
          }}
        />

        <Link to="/dashboard/report/list/new">
          <Button variant="contained" startIcon={<Add />}>
            Buat
          </Button>
        </Link>
      </div>
      <Button
        startIcon={<Sort />}
        variant="outlined"
        onClick={toggle}
        sx={{
          display: { xs: "flex", lg: "none" },
        }}
      >
        Urutkan Data
      </Button>
      <Dialog open={open} onClose={toggle}>
        <DialogTitle>Urutkan</DialogTitle>
        <DialogContent
          sx={{
            width: "250px",
            display: "flex",
            flexDirection: "column",
            gap: 2,
            overflow: "initial",
          }}
        >
          <SelectElement
            control={control}
            name="field"
            options={sortSelectOptions}
            label="Pilih Kolom"
            slotProps={{
              input: {
                endAdornment: watch("field") && (
                  <InputAdornment position="end">
                    <IconButton onClick={() => setValue("field", "")}>
                      <Clear />
                    </IconButton>
                  </InputAdornment>
                ),
              },
            }}
          />
          <SelectElement
            control={control}
            name="sortDirection"
            options={[
              { label: "A-Z", id: "asc" },
              { label: "Z-A", id: "desc" },
            ]}
            label="Urutan"
          />
        </DialogContent>
        <DialogActions sx={{ pb: 2, px: 3 }}>
          <Button
            variant="outlined"
            onClick={() => {
              reset();
              toggle();
            }}
          >
            Batal
          </Button>
          <Button
            variant="contained"
            onClick={handleSubmit((value) => {
              if (value.field) {
                api.current.sortColumn(value.field, value.sortDirection);
              } else {
                api.current.sortColumn("name", null);
              }
              toggle();
            })}
          >
            Simpan
          </Button>
        </DialogActions>
      </Dialog>
    </div>
  );
}
