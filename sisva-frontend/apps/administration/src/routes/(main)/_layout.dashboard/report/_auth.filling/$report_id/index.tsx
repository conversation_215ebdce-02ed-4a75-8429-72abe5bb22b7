import { reportsQueryOptions } from "@sisva/hooks/query/report/useReports";
import { usersQueryOptions } from "@sisva/hooks/query/user/useUsers";
import { createFileRoute, notFound } from "@tanstack/react-router";
import {
  check,
  number,
  object,
  optional,
  parse,
  pipe,
  string,
  transform,
  unknown,
} from "valibot";

import FillingPage from "./-components/FillingPage";

export const Route = createFileRoute(
  "/(main)/_layout/dashboard/report/_auth/filling/$report_id/"
)({
  params: {
    parse(params) {
      return parse(
        object({
          report_id: pipe(unknown(), transform(Number), number()),
        }),
        params
      );
    },
  },
  validateSearch: pipe(
    object({
      teacher_username: optional(string()),
      student_username: optional(string()),
    }),
    check((value) => {
      return !!(
        (value.teacher_username && !value.student_username) ||
        (!value.teacher_username && value.student_username)
      );
    }, "You must provide either teacher_username or student_username, but not both.")
  ),
  async beforeLoad({
    context: { queryClient },
    params: { report_id },
    search: { teacher_username, student_username },
  }) {
    const reports = await queryClient.fetchQuery(reportsQueryOptions);
    const users = await queryClient.fetchQuery(usersQueryOptions);
    const teacher = users.find((user) => user.username === teacher_username);
    const student = users.find((user) => user.username === student_username);
    const validReportIds = reports.map((report) => report.id);
    if (!validReportIds.includes(report_id) || (!teacher && !student)) {
      throw notFound();
    }
  },
  head: () => ({
    meta: [
      {
        title: "Isi Rapot | Sisva",
      },
    ],
  }),
  component: FillingPage,
});
