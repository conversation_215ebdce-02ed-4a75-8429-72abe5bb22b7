import { schools } from "@sisva/template-collection";
import Handlebars from "handlebars";
import * as jspdf from "jspdf";
import * as jspdf_autotable from "jspdf-autotable";
import { useEffect, useRef, useState } from "react";
const template = Handlebars.compile(schools.DEMO.template1);

const template1Function = schools.DEMO.template1Function;

const template1FunctionString = template1Function.toString();

const templateFunction = new Function(
  "param",
  `return ${template1FunctionString} `
)();

export default function PlaygroundPage() {
  const reportRef = useRef<HTMLDivElement>(null);
  const jsPDFPreview = useRef<HTMLIFrameElement>(null);
  const [urlPDF, setUrlPDF] = useState<string | null>(null);

  useEffect(() => {
    templateFunction({ jspdf, jspdf_autotable }).then((blob: Blob) => {
      const urlPDF = URL.createObjectURL(blob);
      setUrlPDF(urlPDF);
    });
  }, []);

  return (
    <div className="flex flex-col gap-4 p-4 items-start">
      {urlPDF && (
        <iframe
          ref={jsPDFPreview}
          src={urlPDF}
          className="w-full h-[85svh]"
        ></iframe>
      )}
      <div
        ref={reportRef}
        className="report"
        dangerouslySetInnerHTML={{
          __html: template(
            schools.DEMO.template1Input({
              student: {
                name: "M ISMAIL IRWAN",
                detail: {
                  grade: "XII",
                },
                nik: "123123123",
              },
              study_program: {
                code: "IPA",
              },
              kkm: 75,
              rank: 10,
              student_group: {
                size: 26,
              },
              attendance: {
                absent: {
                  count: 2,
                },
                sick: {
                  count: 2,
                },
                leave: {
                  count: 2,
                },
                present: {
                  count: 2,
                },
              },
              period: {
                name: "2024/2025",
              },
              score_sum_1: 1264,
              score_avg_1: 66.53,
              score_sum_2: 1425,
              score_avg_2: 74,
              user: {
                walikelas: {
                  name: "Muslim, S.Ag",
                  nik: "",
                },
                kepalamadrasah: {
                  name: "Ratna Dewi, S.Kom., M.M",
                  nik: "198005032007102000",
                },
              },
              class: {
                qurdis: {
                  score_num_1: 85,
                  score_num_2: 92,
                  score_text_1: "Delapan Puluh Lima",
                  score_text_2: "Sembilan Puluh Dua",
                  score_grade_1: "B",
                  subject_name: "Al Qur'an Hadis",
                  teacher_name: "Siti Maryam, S. Ud",
                },
                aqidah: {
                  score_num_1: 85,
                  score_num_2: 92,
                  score_text_1: "Delapan Puluh Lima",
                  score_text_2: "Sembilan Puluh Dua",
                  score_grade_1: "B",
                  subject_name: "Al Qur'an Hadis",
                  teacher_name: "Siti Maryam, S. Ud",
                },
                fiqih: {
                  score_num_1: 85,
                  score_num_2: 92,
                  score_text_1: "Delapan Puluh Lima",
                  score_text_2: "Sembilan Puluh Dua",
                  score_grade_1: "B",
                  subject_name: "Al Qur'an Hadis",
                  teacher_name: "Siti Maryam, S. Ud",
                },
                ski: {
                  score_num_1: 85,
                  score_num_2: 92,
                  score_text_1: "Delapan Puluh Lima",
                  score_text_2: "Sembilan Puluh Dua",
                  score_grade_1: "B",
                  subject_name: "Al Qur'an Hadis",
                  teacher_name: "Siti Maryam, S. Ud",
                },
                ppkn: {
                  score_num_1: 85,
                  score_num_2: 92,
                  score_text_1: "Delapan Puluh Lima",
                  score_text_2: "Sembilan Puluh Dua",
                  score_grade_1: "B",
                  subject_name: "Al Qur'an Hadis",
                  teacher_name: "Siti Maryam, S. Ud",
                },
                bindo: {
                  score_num_1: 85,
                  score_num_2: 92,
                  score_text_1: "Delapan Puluh Lima",
                  score_text_2: "Sembilan Puluh Dua",
                  score_grade_1: "B",
                  subject_name: "Al Qur'an Hadis",
                  teacher_name: "Siti Maryam, S. Ud",
                },
                barab: {
                  score_num_1: 85,
                  score_num_2: 92,
                  score_text_1: "Delapan Puluh Lima",
                  score_text_2: "Sembilan Puluh Dua",
                  score_grade_1: "B",
                  subject_name: "Al Qur'an Hadis",
                  teacher_name: "Siti Maryam, S. Ud",
                },
                mtk: {
                  score_num_1: 85,
                  score_num_2: 92,
                  score_text_1: "Delapan Puluh Lima",
                  score_text_2: "Sembilan Puluh Dua",
                  score_grade_1: "B",
                  subject_name: "Al Qur'an Hadis",
                  teacher_name: "Siti Maryam, S. Ud",
                },
                sindo: {
                  score_num_1: 85,
                  score_num_2: 92,
                  score_text_1: "Delapan Puluh Lima",
                  score_text_2: "Sembilan Puluh Dua",
                  score_grade_1: "B",
                  subject_name: "Al Qur'an Hadis",
                  teacher_name: "Siti Maryam, S. Ud",
                },
                bing: {
                  score_num_1: 85,
                  score_num_2: 92,
                  score_text_1: "Delapan Puluh Lima",
                  score_text_2: "Sembilan Puluh Dua",
                  score_grade_1: "B",
                  subject_name: "Al Qur'an Hadis",
                  teacher_name: "Siti Maryam, S. Ud",
                },
                sbu: {
                  score_num_1: 85,
                  score_num_2: 92,
                  score_text_1: "Delapan Puluh Lima",
                  score_text_2: "Sembilan Puluh Dua",
                  score_grade_1: "B",
                  subject_name: "Al Qur'an Hadis",
                  teacher_name: "Siti Maryam, S. Ud",
                },
                pjok: {
                  score_num_1: 85,
                  score_num_2: 92,
                  score_text_1: "Delapan Puluh Lima",
                  score_text_2: "Sembilan Puluh Dua",
                  score_grade_1: "B",
                  subject_name: "Al Qur'an Hadis",
                  teacher_name: "Siti Maryam, S. Ud",
                },
                pkwu: {
                  score_num_1: 85,
                  score_num_2: 92,
                  score_text_1: "Delapan Puluh Lima",
                  score_text_2: "Sembilan Puluh Dua",
                  score_grade_1: "B",
                  subject_name: "Al Qur'an Hadis",
                  teacher_name: "Siti Maryam, S. Ud",
                },
                keterampilanagama: {
                  score_num_1: 85,
                  score_num_2: 92,
                  score_text_1: "Delapan Puluh Lima",
                  score_text_2: "Sembilan Puluh Dua",
                  score_grade_1: "B",
                  subject_name: "Al Qur'an Hadis",
                  teacher_name: "Siti Maryam, S. Ud",
                },
                biologi: {
                  score_num_1: 85,
                  score_num_2: 92,
                  score_text_1: "Delapan Puluh Lima",
                  score_text_2: "Sembilan Puluh Dua",
                  score_grade_1: "B",
                  subject_name: "Al Qur'an Hadis",
                  teacher_name: "Siti Maryam, S. Ud",
                },
                fisika: {
                  score_num_1: 85,
                  score_num_2: 92,
                  score_text_1: "Delapan Puluh Lima",
                  score_text_2: "Sembilan Puluh Dua",
                  score_grade_1: "B",
                  subject_name: "Al Qur'an Hadis",
                  teacher_name: "Siti Maryam, S. Ud",
                },
                kimia: {
                  score_num_1: 85,
                  score_num_2: 92,
                  score_text_1: "Delapan Puluh Lima",
                  score_text_2: "Sembilan Puluh Dua",
                  score_grade_1: "B",
                  subject_name: "Al Qur'an Hadis",
                  teacher_name: "Siti Maryam, S. Ud",
                },
                sosiologi: {
                  score_num_1: 85,
                  score_num_2: 92,
                  score_text_1: "Delapan Puluh Lima",
                  score_text_2: "Sembilan Puluh Dua",
                  score_grade_1: "B",
                  subject_name: "Al Qur'an Hadis",
                  teacher_name: "Siti Maryam, S. Ud",
                },
                informatika: {
                  score_num_1: 85,
                  score_num_2: 92,
                  score_text_1: "Delapan Puluh Lima",
                  score_text_2: "Sembilan Puluh Dua",
                  score_grade_1: "B",
                  subject_name: "Al Qur'an Hadis",
                  teacher_name: "Siti Maryam, S. Ud",
                },
                rpl: {
                  score_num_1: 85,
                  score_num_2: 92,
                  score_text_1: "Delapan Puluh Lima",
                  score_text_2: "Sembilan Puluh Dua",
                  score_grade_1: "B",
                  subject_name: "Al Qur'an Hadis",
                  teacher_name: "Siti Maryam, S. Ud",
                },
                multimedia: {
                  score_num_1: 85,
                  score_num_2: 92,
                  score_text_1: "Delapan Puluh Lima",
                  score_text_2: "Sembilan Puluh Dua",
                  score_grade_1: "B",
                  subject_name: "Al Qur'an Hadis",
                  teacher_name: "Siti Maryam, S. Ud",
                },
              },
            })
          ),
        }}
      ></div>
    </div>
  );
}
