import { valibotResolver } from "@hookform/resolvers/valibot";
import { Clear } from "@mui/icons-material";
import {
  Button,
  Divider,
  IconButton,
  Paper,
  Stack,
  TextField,
  Typography,
  useMediaQuery,
} from "@mui/material";
import type { GridColDef } from "@mui/x-data-grid";
import { DataGrid } from "@mui/x-data-grid";
import { useClassesWithSubjectAndTeacherAndStudentGroup } from "@sisva/hooks/query/academic/useClasses";
import { useReport } from "@sisva/hooks/query/report/useReports";
import {
  useReportScores,
  useSetReportScores,
} from "@sisva/hooks/query/report/useReportScores";
import { useUserByUsername } from "@sisva/hooks/query/user/useUsers";
import { AvatarWithAcronymByID } from "@sisva/ui";
import { segmentArray } from "@sisva/utils";
import { getRoute<PERSON><PERSON>, Link } from "@tanstack/react-router";
import {
  type Control,
  useForm,
  type UseFormSetValue,
  useWatch,
} from "react-hook-form";
import toast from "react-hot-toast";
import {
  array,
  custom,
  type InferInput,
  maxLength,
  maxValue,
  minLength,
  minValue,
  nullable,
  number,
  object,
  pipe,
  string,
} from "valibot";

type FieldTeacher =
  | "student_name"
  | "student_group_name"
  | `score_${string}_num`
  | `score_${string}_text`;
type TypeSafeColDefTeacher = GridColDef & { field: FieldTeacher };

type FieldStudent =
  | "teacher_name"
  | "subject_name"
  | `score_${string}_num`
  | `score_${string}_text`;
type TypeSafeColDefStudent = GridColDef & { field: FieldStudent };

const min = 0;
const max = 100;
const allowedScoreText = ["A", "B", "C", "D", "E", "F"];

const schema = object({
  student_scores: array(
    object({
      student_id: string(),
      class_id: number(),
      scores: array(
        object({
          name: string(),
          value_num: nullable(pipe(number(), minValue(min), maxValue(max))),
          value_text: nullable(
            pipe(
              string(),
              minLength(1),
              maxLength(1),
              custom(
                (value) =>
                  typeof value === "string" && allowedScoreText.includes(value),
                "Must be A–F"
              )
            )
          ),
        })
      ),
    })
  ),
});
type SchemaInput = InferInput<typeof schema>;

export default function FillingPage() {
  const routeApi = getRouteApi(
    "/(main)/_layout/dashboard/report/_auth/filling/$report_id/"
  );
  const { report_id } = routeApi.useParams();
  const { teacher_username, student_username } = routeApi.useSearch();
  const isMobile = useMediaQuery((theme) => theme.breakpoints.down("lg"));

  const { data: report } = useReport({ report_id });

  // only either teacher_username or student_username is provided (should be)
  const { data: teacher } = useUserByUsername(teacher_username);
  const { data: student } = useUserByUsername(student_username);
  const { data: classes = [] } =
    useClassesWithSubjectAndTeacherAndStudentGroup();
  const { data: reportScores = [] } = useReportScores({ report_id });

  const filteredClasses = classes.filter(
    (class_) =>
      // filter by teacher_id or student's student_group_id
      (class_.teacher_id === teacher?.id ||
        class_.student_group_id === student?.student_group?.id) &&
      // filter by period_id
      class_.student_group?.period_id === report?.period_id &&
      // filter by student_group's study_program_id if report has one
      (!report?.study_program_id ||
        report?.study_program_id === class_.student_group?.study_program_id) &&
      // filter by student_group's grade if report has one
      (!report?.grade || report?.grade == class_.student_group?.grade)
  );

  const classGroupedBySubjectId = Object.entries(
    Object.groupBy(filteredClasses, (item) => item.subject_id)
  ).map(([subject_id, classes]) => {
    return {
      subject_id,
      subject: classes?.[0]?.subject,
      // assign student_group and class_id to each student
      classes: classes?.map((class_) => ({
        ...class_,
        student_group: {
          ...class_?.student_group,
          students: class_.student_group?.students.map((student) => ({
            ...student,
            student_group: class_.student_group,
            class_id: class_.id,
          })),
        },
      })),
    };
  });

  type RowTypeTeacher = NonNullable<
    NonNullable<
      (typeof classGroupedBySubjectId)[number]["classes"]
    >[number]["student_group"]["students"]
  >[number];

  type RowTypeStudent = (typeof filteredClasses)[number];

  const students = classGroupedBySubjectId
    .flatMap((item) =>
      item.classes?.flatMap((class_) => class_.student_group.students)
    )
    .filter((student) => !!student)
    // filter by student if student_username query params is provided
    .filter((student_) => !student || student_.id === student?.id);

  const student_score_initial = students.map((student) => {
    const scores =
      reportScores.find((reportScore) => {
        return (
          reportScore.student_id === student.id &&
          reportScore.report_id === report_id &&
          reportScore.class_id === student.class_id
        );
      })?.scores ?? [];
    return {
      class_id: student.class_id,
      student_id: student.id,
      scores: scores,
    };
  });

  const {
    control,
    setValue,
    watch,
    handleSubmit,
    formState: { errors },
  } = useForm<SchemaInput>({
    values: {
      student_scores: student_score_initial,
    },
    resolver: valibotResolver(schema),
  });

  const { mutateAsync: setReportScore } = useSetReportScores();

  const onSubmit = handleSubmit(async (value) => {
    const validStudentScores = value.student_scores.filter((studentScores) => {
      // BE doesn't allow empty array
      if (studentScores.scores.length === 0) return false;
      //TODO: BE doesn't allow null value, update later
      const containNullValue = studentScores.scores.some((score) => {
        return score.value_num === null || score.value_text === null;
      });
      if (containNullValue) return false;
      return true;
    });
    const segmentedScores = segmentArray(validStudentScores, 100);
    for (const studentScores of segmentedScores) {
      await setReportScore(
        studentScores.map((item) => ({
          ...item,
          report_id,
        })),
        {
          onSuccess() {
            toast.success("Nilai Rapot berhasil diperbarui");
          },
          onError() {
            toast.error("Nilai Rapot gagal diperbarui");
          },
        }
      );
    }
  });

  const columnsTeacher: TypeSafeColDefTeacher[] = [
    {
      field: "student_name",
      headerName: "Nama",
      display: "flex",
      minWidth: 250,
      flex: 1,
      renderCell: ({ row }: { row: RowTypeTeacher }) => (
        <Typography sx={{ py: 1.5 }}>{row.name}</Typography>
      ),
    },
    {
      field: "student_group_name",
      headerName: "Kelas",
      display: "flex",
      minWidth: 150,
      renderCell: ({ row }: { row: RowTypeTeacher }) => (
        <Typography sx={{ py: 1.5 }}>{row.student_group?.name}</Typography>
      ),
    },
    {
      field: "score_keterampilan_num",
      headerName: "Keterampilan (Angka)",
      minWidth: 180,
      display: "flex",
      renderCell: ({ row }: { row: RowTypeTeacher }) => {
        const index = student_score_initial.findIndex(
          (item) => item.student_id === row.id && item.class_id === row.class_id
        );
        return (
          <Stack
            sx={{ py: 1.5, flexDirection: "row", gap: 0.5, width: "100%" }}
          >
            <ScoreInput
              control={control}
              setValue={setValue}
              index={index}
              scoreType="num"
              scoreName="keterampilan"
            />
          </Stack>
        );
      },
    },
    {
      field: "score_keterampilan_text",
      headerName: "Keterampilan (Huruf)",
      minWidth: 180,
      display: "flex",
      renderCell: ({ row }: { row: RowTypeTeacher }) => {
        const index = student_score_initial.findIndex(
          (item) => item.student_id === row.id && item.class_id === row.class_id
        );
        return (
          <Stack
            sx={{ py: 1.5, flexDirection: "row", gap: 0.5, width: "100%" }}
          >
            <ScoreInput
              control={control}
              setValue={setValue}
              index={index}
              scoreType="text"
              scoreName="keterampilan"
            />
          </Stack>
        );
      },
    },
    {
      field: "score_sikap_num",
      headerName: "Sikap (Angka)",
      minWidth: 180,
      display: "flex",
      renderCell: ({ row }: { row: RowTypeTeacher }) => {
        const index = student_score_initial.findIndex(
          (item) => item.student_id === row.id && item.class_id === row.class_id
        );
        return (
          <Stack
            sx={{ py: 1.5, flexDirection: "row", gap: 0.5, width: "100%" }}
          >
            <ScoreInput
              control={control}
              setValue={setValue}
              index={index}
              scoreType="num"
              scoreName="sikap"
            />
          </Stack>
        );
      },
    },
    {
      field: "score_sikap_text",
      headerName: "Sikap (Huruf)",
      minWidth: 180,
      display: "flex",
      renderCell: ({ row }: { row: RowTypeTeacher }) => {
        const index = student_score_initial.findIndex(
          (item) => item.student_id === row.id && item.class_id === row.class_id
        );
        return (
          <Stack
            sx={{ py: 1.5, flexDirection: "row", gap: 0.5, width: "100%" }}
          >
            <ScoreInput
              control={control}
              setValue={setValue}
              index={index}
              scoreType="text"
              scoreName="sikap"
            />
          </Stack>
        );
      },
    },
  ];

  const columnsStudent: TypeSafeColDefStudent[] = [
    {
      field: "teacher_name",
      headerName: "Guru",
      display: "flex",
      minWidth: 250,
      flex: 1,
      renderCell: ({ row }: { row: RowTypeStudent }) => (
        <Typography sx={{ py: 1.5 }}>{row.teacher_name}</Typography>
      ),
    },
    {
      field: "subject_name",
      headerName: "Mata Pelajaran",
      display: "flex",
      minWidth: 250,
      renderCell: ({ row }: { row: RowTypeStudent }) => (
        <Typography sx={{ py: 1.5 }}>{row.subject_name}</Typography>
      ),
    },
    {
      field: "score_keterampilan_num",
      headerName: "Keterampilan (Angka)",
      minWidth: 180,
      display: "flex",
      renderCell: ({ row }: { row: RowTypeStudent }) => {
        const index = student_score_initial.findIndex(
          (item) => item.student_id === student?.id && item.class_id === row.id
        );
        return (
          <Stack
            sx={{ py: 1.5, flexDirection: "row", gap: 0.5, width: "100%" }}
          >
            <ScoreInput
              control={control}
              setValue={setValue}
              index={index}
              scoreType="num"
              scoreName="keterampilan"
            />
          </Stack>
        );
      },
    },
    {
      field: "score_keterampilan_text",
      headerName: "Keterampilan (Huruf)",
      minWidth: 180,
      display: "flex",
      renderCell: ({ row }: { row: RowTypeStudent }) => {
        const index = student_score_initial.findIndex(
          (item) => item.student_id === student?.id && item.class_id === row.id
        );
        return (
          <Stack
            sx={{ py: 1.5, flexDirection: "row", gap: 0.5, width: "100%" }}
          >
            <ScoreInput
              control={control}
              setValue={setValue}
              index={index}
              scoreType="text"
              scoreName="keterampilan"
            />
          </Stack>
        );
      },
    },
    {
      field: "score_sikap_num",
      headerName: "Sikap (Angka)",
      minWidth: 180,
      display: "flex",
      renderCell: ({ row }: { row: RowTypeStudent }) => {
        const index = student_score_initial.findIndex(
          (item) => item.student_id === student?.id && item.class_id === row.id
        );
        return (
          <Stack
            sx={{ py: 1.5, flexDirection: "row", gap: 0.5, width: "100%" }}
          >
            <ScoreInput
              control={control}
              setValue={setValue}
              index={index}
              scoreType="num"
              scoreName="sikap"
            />
          </Stack>
        );
      },
    },
    {
      field: "score_sikap_text",
      headerName: "Sikap (Huruf)",
      minWidth: 180,
      display: "flex",
      renderCell: ({ row }: { row: RowTypeStudent }) => {
        const index = student_score_initial.findIndex(
          (item) => item.student_id === student?.id && item.class_id === row.id
        );
        return (
          <Stack
            sx={{ py: 1.5, flexDirection: "row", gap: 0.5, width: "100%" }}
          >
            <ScoreInput
              control={control}
              setValue={setValue}
              index={index}
              scoreType="text"
              scoreName="sikap"
            />
          </Stack>
        );
      },
    },
  ];

  return (
    <div className="size-full p-8 flex flex-col gap-4">
      <Typography sx={{ fontWeight: 700, fontSize: 20 }}>Isi Rapot</Typography>
      <Paper
        variant="outlined"
        sx={{
          flex: 1,
          display: "flex",
          flexDirection: "column",
          p: 2,
          gap: 2,
        }}
        component="form"
        onSubmit={onSubmit}
      >
        <Typography sx={{ fontWeight: 600, fontSize: 16 }}>
          {report?.name}
        </Typography>
        <Typography>{report?.period?.name}</Typography>
        <Divider />
        <Stack sx={{ flexDirection: "row", gap: 2 }}>
          <AvatarWithAcronymByID
            size={50}
            user_id={teacher?.id ?? student?.id}
          />
          <Stack>
            <Typography sx={{ fontWeight: 600 }}>
              {teacher ? "Guru" : "Siswa"}
            </Typography>
            <Typography>{teacher?.name ?? student?.name}</Typography>
          </Stack>
        </Stack>

        {errors.student_scores && (
          <Typography color="error">
            Terdapat data yang kurang tepat, silakan periksa kembali
          </Typography>
        )}
        {/* should be rendered only when teacher_username is provided */}
        {teacher &&
          classGroupedBySubjectId.map((item) => {
            const students = item.classes?.[0]?.student_group?.students ?? [];
            return (
              <Stack sx={{ gap: 1, pb: 2, height: 450 }} key={item.subject_id}>
                <Typography sx={{ fontWeight: 600 }}>
                  {item.subject?.name}
                </Typography>
                <DataGrid
                  sx={{
                    "& .MuiDataGrid-cell:focus": {
                      outline: " none",
                    },
                  }}
                  disableRowSelectionOnClick
                  className="bg-white grow thick-scrollbar"
                  rows={students}
                  columns={columnsTeacher}
                  initialState={{
                    pagination: { paginationModel: { pageSize: 20 } },
                  }}
                  pageSizeOptions={[10, 20, 50]}
                  getRowClassName={(params) =>
                    params.indexRelativeToCurrentPage % 2 === 0
                      ? "bg-neutral-50"
                      : "bg-neutral-100"
                  }
                  getRowHeight={() => "auto"}
                  disableColumnMenu
                  hideFooter
                />
              </Stack>
            );
          })}

        {/* should be rendered only when student_username is provided */}
        {student && (
          <Stack sx={{ gap: 1, pb: 2, height: "50svh" }}>
            <DataGrid
              sx={{
                "& .MuiDataGrid-cell:focus": {
                  outline: " none",
                },
              }}
              disableRowSelectionOnClick
              className="bg-white grow thick-scrollbar"
              rows={filteredClasses}
              columns={columnsStudent}
              initialState={{
                pagination: { paginationModel: { pageSize: 20 } },
              }}
              pageSizeOptions={[10, 20, 50]}
              getRowClassName={(params) =>
                params.indexRelativeToCurrentPage % 2 === 0
                  ? "bg-neutral-50"
                  : "bg-neutral-100"
              }
              getRowHeight={() => "auto"}
              disableColumnMenu
              hideFooter
            />
          </Stack>
        )}
        <Stack sx={{ flexDirection: "row", gap: 1, justifyContent: "end" }}>
          <Link to="/dashboard/report/filling">
            <Button variant="outlined">Kembali</Button>
          </Link>
          <Button variant="contained" type="submit">
            Simpan
          </Button>
        </Stack>
      </Paper>
    </div>
  );
}

function ScoreInput({
  control,
  setValue,
  index,
  scoreType,
  scoreName,
}: {
  control: Control<SchemaInput>;
  setValue: UseFormSetValue<SchemaInput>;
  index: number;
  scoreType: "text" | "num";
  scoreName: string;
}) {
  const score = useWatch({
    control,
    name: `student_scores.${index}`,
  });
  const value_num =
    score.scores.find((score) => score.name === scoreName)?.value_num ?? null;
  const value_text =
    score.scores.find((score) => score.name === scoreName)?.value_text ?? null;

  function getValue() {
    if (scoreType === "num") return value_num;
    if (scoreType === "text") return value_text;
  }

  function updateValue({
    value,
    validate,
  }: {
    value: string | null;
    validate: boolean;
  }) {
    const newScores = [...score.scores].filter((score) => {
      return !(score.name === scoreName);
    });
    let newValueNum = value === null || value === "" ? null : Number(value);
    if (typeof newValueNum === "number" && validate) {
      if (newValueNum < min) newValueNum = min;
      if (newValueNum > max) newValueNum = max;
    }

    let newValueText = value ? value : null;
    if (newValueText?.length && newValueText.length > 1 && validate) {
      newValueText = newValueText[0]?.toUpperCase() ?? null;
    }
    if (newValueText && !allowedScoreText.includes(newValueText) && validate) {
      newValueText = null;
    }
    if (newValueText) {
      newValueText = newValueText.toUpperCase();
    }

    newScores.push({
      name: scoreName,
      value_num: scoreType === "num" ? newValueNum : value_num,
      value_text: scoreType === "text" ? newValueText : value_text,
    });
    setValue(`student_scores.${index}.scores`, newScores);
  }

  return (
    <Stack
      sx={{
        flexDirection: "row",
        gap: 0.5,
      }}
    >
      <TextField
        type={scoreType === "num" ? "number" : undefined}
        size="small"
        value={getValue() ?? ""}
        sx={{
          width: "100%",
        }}
        onChange={(event) => {
          updateValue({
            value: event.target.value,
            validate: false,
          });
        }}
        onBlur={(event) => {
          updateValue({
            value: event.target.value,
            validate: true,
          });
        }}
      />
      <IconButton
        onClick={() => {
          updateValue({
            value: null,
            validate: true,
          });
        }}
      >
        <Clear />
      </IconButton>
    </Stack>
  );
}
