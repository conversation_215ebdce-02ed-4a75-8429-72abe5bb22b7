import { reportsQueryOptions } from "@sisva/hooks/query/report/useReports";
import { createFileRoute, notFound } from "@tanstack/react-router";
import { number, object, parse, pipe, transform, unknown } from "valibot";

import EditPage from "./-components/EditPage";

export const Route = createFileRoute(
  "/(main)/_layout/dashboard/report/_auth/list/$report_id/edit/"
)({
  params: {
    parse(params) {
      return parse(
        object({
          report_id: pipe(unknown(), transform(Number), number()),
        }),
        params
      );
    },
  },
  async beforeLoad({ context: { queryClient }, params: { report_id } }) {
    const reports = await queryClient.fetchQuery(reportsQueryOptions);
    const validReportIds = reports.map((report) => report.id);
    if (!validReportIds.includes(report_id)) {
      throw notFound();
    }
  },
  head: () => ({
    meta: [
      {
        title: "Edit Rapot | Sisva",
      },
    ],
  }),
  component: EditPage,
});
