import { Clear, Search, Sort, Visibility } from "@mui/icons-material";
import {
  <PERSON>ton,
  <PERSON>alog,
  <PERSON>alogActions,
  DialogContent,
  DialogTitle,
  IconButton,
  InputAdornment,
  Stack,
  TextField,
  Typography,
  useMediaQuery,
} from "@mui/material";
import type { GridColDef } from "@mui/x-data-grid";
import { DataGrid, useGridApiContext } from "@mui/x-data-grid";
import { useToggle } from "ahooks";
import { useEffect } from "react";
import { useForm } from "react-hook-form";
import { SelectElement, TextFieldElement } from "react-hook-form-mui";

type Field =
  | "mobile_column"
  | "student"
  | "grade"
  | "program_study"
  | "student_group"
  | "action";
type TypeSafeColDef = GridColDef & { field: Field };

export default function ViewPage() {
  const isMobile = useMediaQuery((theme) => theme.breakpoints.down("lg"));

  const temp = [
    {
      id: 1,
      student: "<PERSON>",
      grade: "X",
      program_study: "IPA",
      student_group: "XI MIPA 1",
    },
    {
      id: 2,
      student: "<PERSON>",
      grade: "X",
      program_study: "IPA",
      student_group: "XI MIPA 2",
    },
    {
      id: 3,
      student: "John Doe",
      grade: "X",
      program_study: "IPS",
      student_group: "XI IPS 1",
    },
    {
      id: 4,
      student: "Alice Green",
      grade: "X",
      program_study: "IPA",
      student_group: "XI MIPA 3",
    },
    {
      id: 5,
      student: "Michael Smith",
      grade: "IX",
      program_study: "IPS",
      student_group: "IX IPS 1",
    },
    {
      id: 6,
      student: "Sophia Lee",
      grade: "X",
      program_study: "IPA",
      student_group: "XI MIPA 4",
    },
  ];

  const columns: TypeSafeColDef[] = [
    {
      field: "mobile_column",
      headerName: "",
      valueGetter: (_, row) => row,
      flex: 1,
      display: "flex",
      renderCell: (args) => {
        const value = args.value as (typeof temp)[0];
        return (
          <Stack sx={{ p: 1.5, gap: 2, width: "100%" }}>
            <Stack
              sx={{
                flexDirection: "row",
                justifyContent: "space-between",
                width: "100%",
              }}
            >
              <Stack>
                <Typography color="textSecondary">Siswa</Typography>
                <Typography sx={{ fontSize: 17 }}>{value.student}</Typography>
              </Stack>
            </Stack>
            <Stack>
              <Typography color="textSecondary">Tingkatan</Typography>
              <Typography>{value.grade}</Typography>
            </Stack>

            <Stack>
              <Typography color="textSecondary">Program Study</Typography>
              <Typography>{value.program_study}</Typography>
            </Stack>

            <Stack>
              <Typography color="textSecondary">Kelas</Typography>
              <Typography>{value.student_group}</Typography>
            </Stack>
          </Stack>
        );
      },
    },
    {
      field: "student",
      headerName: "Siswa",
      display: "flex",
      flex: 1,
      renderCell: ({ value }) => (
        <Typography sx={{ py: 1.5 }}>{value}</Typography>
      ),
    },
    {
      field: "grade",
      headerName: "Tingkatan",
      display: "flex",
      width: 100,
      renderCell: ({ value }) => <Stack sx={{ py: 1.5 }}>{value}</Stack>,
    },
    {
      field: "program_study",
      headerName: "Program Studi",
      width: 150,
      display: "flex",
      renderCell: ({ value }) => (
        <Stack sx={{ py: 1.5, flexDirection: "row", gap: 0.5 }}>{value}</Stack>
      ),
    },
    {
      field: "student_group",
      headerName: "Kelas",
      width: 100,
      display: "flex",
      renderCell: ({ value }) => (
        <Stack sx={{ py: 1.5, flexDirection: "row", gap: 0.5 }}>{value}</Stack>
      ),
    },
    {
      field: "action",
      headerName: "Aksi",
      valueGetter: (_, row) => row.id,
      width: 128,
      display: "flex",
      renderCell: ({ value }) => (
        <Stack>
          <Visibility />
        </Stack>
      ),
      sortable: false,
    },
  ];

  const columnVisibilityModel = Object.fromEntries(
    columns
      .map((column) => column.field)
      .filter((field) =>
        isMobile ? field !== "mobile_column" : field === "mobile_column"
      )
      .map((field) => [field, false])
  );

  return (
    <div className="size-full p-8 flex flex-col gap-4">
      <Typography sx={{ fontWeight: 700, fontSize: 20 }}>
        Lihat Rapot
      </Typography>
      <DataGrid
        sx={{
          "& .MuiDataGrid-cell:focus": {
            outline: " none",
          },
        }}
        disableRowSelectionOnClick
        className="bg-white grow"
        rows={temp}
        columns={columns}
        initialState={{ pagination: { paginationModel: { pageSize: 20 } } }}
        pageSizeOptions={[10, 20, 50]}
        getRowClassName={(params) =>
          params.indexRelativeToCurrentPage % 2 === 0
            ? "bg-neutral-50"
            : "bg-neutral-100"
        }
        slots={{
          toolbar: CustomToolbar,
        }}
        getRowHeight={() => "auto"}
        columnVisibilityModel={columnVisibilityModel}
        columnHeaderHeight={isMobile ? 0 : undefined}
        disableColumnMenu
      />
    </div>
  );
}

function CustomToolbar() {
  const api = useGridApiContext();
  const [open, { toggle }] = useToggle(false);
  const { control, handleSubmit, reset, setValue, watch } = useForm<{
    field: Field | "";
    sortDirection: "asc" | "desc";
    quickFilter: string;
  }>({
    defaultValues: {
      field: "",
      sortDirection: "asc",
      quickFilter: "",
    },
  });

  const sortSelectOptions: { label: string; id: Field }[] = [
    { label: "Siswa", id: "student" },
    { label: "Tingkatan", id: "grade" },
    { label: "Program Studi", id: "program_study" },
    { label: "Kelas", id: "student_group" },
  ];

  const quickFilter = watch("quickFilter");
  useEffect(() => {
    api.current.setQuickFilterValues(
      quickFilter.split(" ").filter((s) => s !== "")
    );
  }, [api, quickFilter]);

  return (
    <div className="p-4 flex flex-col gap-2 border-b border-0 border-solid border-neutral-200">
      <div className=" flex justify-between ">
        <TextFieldElement
          control={control}
          name="quickFilter"
          placeholder="Cari Rapot"
          size="small"
          slotProps={{
            input: {
              endAdornment: (
                <InputAdornment position="end">
                  {quickFilter ? (
                    <IconButton
                      size="small"
                      onClick={() => setValue("quickFilter", "")}
                    >
                      <Clear />
                    </IconButton>
                  ) : (
                    <Search />
                  )}
                </InputAdornment>
              ),
            },
          }}
        />
      </div>
      <Button
        startIcon={<Sort />}
        variant="outlined"
        onClick={toggle}
        sx={{
          display: { xs: "flex", lg: "none" },
        }}
      >
        Urutkan Data
      </Button>
      <Dialog open={open} onClose={toggle}>
        <DialogTitle>Urutkan</DialogTitle>
        <DialogContent
          sx={{
            width: "250px",
            display: "flex",
            flexDirection: "column",
            gap: 2,
            overflow: "initial",
          }}
        >
          <SelectElement
            control={control}
            name="field"
            options={sortSelectOptions}
            label="Pilih Kolom"
            slotProps={{
              input: {
                endAdornment: watch("field") && (
                  <InputAdornment position="end">
                    <IconButton onClick={() => setValue("field", "")}>
                      <Clear />
                    </IconButton>
                  </InputAdornment>
                ),
              },
            }}
          />
          <SelectElement
            control={control}
            name="sortDirection"
            options={[
              { label: "A-Z", id: "asc" },
              { label: "Z-A", id: "desc" },
            ]}
            label="Urutan"
          />
        </DialogContent>
        <DialogActions sx={{ pb: 2, px: 3 }}>
          <Button
            variant="outlined"
            onClick={() => {
              reset();
              toggle();
            }}
          >
            Batal
          </Button>
          <Button
            variant="contained"
            onClick={handleSubmit((value) => {
              if (value.field) {
                api.current.sortColumn(value.field, value.sortDirection);
              } else {
                api.current.sortColumn("name", null);
              }
              toggle();
            })}
          >
            Simpan
          </Button>
        </DialogActions>
      </Dialog>
    </div>
  );
}
