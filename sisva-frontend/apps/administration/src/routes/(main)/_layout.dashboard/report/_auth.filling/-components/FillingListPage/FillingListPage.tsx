import { Paper, Tab, Tabs, Typography } from "@mui/material";
import { atom } from "jotai";
import type { SyntheticEvent } from "react";
import { useState } from "react";

import GuruTab from "./GuruTab";
import SiswaTab from "./SiswaTab";

export const reportIdAtom = atom<number | null>(null);

export default function AssessmentPage() {
  const [tabIndex, setTabIndex] = useState(0);

  return (
    <div className="size-full p-8 flex flex-col gap-4">
      <Typography sx={{ fontWeight: 700, fontSize: 20 }}><PERSON><PERSON></Typography>
      <Paper
        variant="outlined"
        sx={{
          flex: 1,
          display: "flex",
          flexDirection: "column",
          overflow: "auto",
        }}
      >
        <Tabs
          value={tabIndex}
          onChange={(event: SyntheticEvent, newValue: number) => {
            setTabIndex(newValue);
          }}
          sx={{
            borderBottom: 1,
            borderColor: "divider",
          }}
        >
          <Tab
            label="Guru"
            sx={{ fontWeight: 600, textTransform: "none", fontSize: 14 }}
          />
          <Tab
            label="Siswa"
            sx={{ fontWeight: 600, textTransform: "none", fontSize: 14 }}
          />
        </Tabs>
        {(() => {
          switch (tabIndex) {
            case 0:
              return <GuruTab />;
            case 1:
              return <SiswaTab />;
          }
        })()}
      </Paper>
    </div>
  );
}
