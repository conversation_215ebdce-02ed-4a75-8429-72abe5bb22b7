import { Clear, Search, Sort, Visibility } from "@mui/icons-material";
import {
  <PERSON>ton,
  <PERSON>alog,
  DialogActions,
  DialogContent,
  DialogTitle,
  IconButton,
  InputAdornment,
  Stack,
  Typography,
  useMediaQuery,
} from "@mui/material";
import type { GridColDef } from "@mui/x-data-grid";
import { DataGrid, useGridApiContext } from "@mui/x-data-grid";
import { useTemplates } from "@sisva/hooks/query/report/useTemplates";
import { useGetFileUrl } from "@sisva/hooks/utils";
import type { Template } from "@sisva/types/apiTypes";
import { useToggle } from "ahooks";
import { useEffect } from "react";
import { useForm } from "react-hook-form";
import { SelectElement, TextFieldElement } from "react-hook-form-mui";

type Field = "mobile_column" | "description" | "action" | keyof Template;
type TypeSafeColDef = GridColDef & { field: Field };

export default function TemplateListPage() {
  const isMobile = useMediaQuery((theme) => theme.breakpoints.down("lg"));
  const getUrl = useGetFileUrl();
  let { data: templates = [] } = useTemplates();
  templates = templates.map((template) => ({
    ...template,
  }));

  const columns: TypeSafeColDef[] = [
    {
      field: "mobile_column",
      headerName: "",
      valueGetter: (_, row) => row,
      flex: 1,
      display: "flex",
      renderCell: (args) => {
        const value = args.value as Template;
        return (
          <Stack sx={{ p: 1.5, gap: 2, width: "100%" }}>
            <Stack
              sx={{
                flexDirection: "row",
                justifyContent: "space-between",
                width: "100%",
              }}
            >
              <Stack>
                <Typography color="textSecondary">Nama</Typography>
                <Typography sx={{ fontSize: 17 }}>{value.name}</Typography>
              </Stack>
            </Stack>
            <Stack>
              <Typography color="textSecondary">Deskripsi</Typography>
              <Typography>{value.description}</Typography>
            </Stack>

            <Stack
              sx={{
                flexDirection: "row",
                justifyContent: "end",
              }}
            >
              <a href={getUrl(value.ref_url)} download>
                <Visibility />
              </a>
            </Stack>
          </Stack>
        );
      },
    },
    {
      field: "name",
      headerName: "Nama",
      display: "flex",
      width: 200,

      renderCell: ({ value }) => (
        <Typography sx={{ py: 1.5, fontWeight: 600 }}>{value}</Typography>
      ),
    },
    {
      field: "description",
      headerName: "Deskripsi",
      flex: 1,
      display: "flex",
      renderCell: ({ value }) => <Stack sx={{ py: 1.5 }}>{value}</Stack>,
    },

    {
      field: "action",
      headerName: "Aksi",
      valueGetter: (_, row: Template) => row.ref_url,
      width: 128,
      display: "flex",
      renderCell: ({ value }) => (
        <Stack>
          <a href={getUrl(value)} download>
            <Visibility />
          </a>
        </Stack>
      ),
      sortable: false,
    },
  ];

  const columnVisibilityModel = Object.fromEntries(
    columns
      .map((column) => column.field)
      .filter((field) =>
        isMobile ? field !== "mobile_column" : field === "mobile_column"
      )
      .map((field) => [field, false])
  );

  return (
    <div className="size-full p-0 lg:p-8 flex flex-col gap-4">
      <Typography
        sx={{
          fontWeight: 700,
          fontSize: 20,
          display: {
            xs: "none",
            lg: "block",
          },
        }}
      >
        Template Rapot
      </Typography>
      <DataGrid
        sx={{
          "& .MuiDataGrid-cell:focus": {
            outline: " none",
          },
        }}
        disableRowSelectionOnClick
        className="bg-white grow"
        rows={templates}
        columns={columns}
        initialState={{ pagination: { paginationModel: { pageSize: 20 } } }}
        pageSizeOptions={[10, 20, 50]}
        getRowClassName={(params) =>
          params.indexRelativeToCurrentPage % 2 === 0
            ? "bg-neutral-50"
            : "bg-neutral-100"
        }
        slots={{
          toolbar: CustomToolbar,
        }}
        getRowHeight={() => "auto"}
        columnVisibilityModel={columnVisibilityModel}
        columnHeaderHeight={isMobile ? 0 : undefined}
        disableColumnMenu
      />
    </div>
  );
}

function CustomToolbar() {
  const api = useGridApiContext();
  const [open, { toggle }] = useToggle(false);
  const { control, handleSubmit, reset, setValue, watch } = useForm<{
    field: Field | "";
    sortDirection: "asc" | "desc";
    quickFilter: string;
  }>({
    defaultValues: {
      field: "",
      sortDirection: "asc",
      quickFilter: "",
    },
  });

  const sortSelectOptions: { label: string; id: Field }[] = [
    { label: "Nama", id: "name" },
    { label: "Deskripsi", id: "description" },
  ];

  const quickFilter = watch("quickFilter");
  useEffect(() => {
    api.current.setQuickFilterValues(
      quickFilter.split(" ").filter((s) => s !== "")
    );
  }, [api, quickFilter]);

  return (
    <div className="p-4 flex flex-col gap-2 border-b border-0 border-solid border-neutral-200">
      <div className=" flex justify-between ">
        <TextFieldElement
          control={control}
          name="quickFilter"
          placeholder="Cari Template Rapot"
          size="small"
          slotProps={{
            input: {
              endAdornment: (
                <InputAdornment position="end">
                  {quickFilter ? (
                    <IconButton
                      size="small"
                      onClick={() => setValue("quickFilter", "")}
                    >
                      <Clear />
                    </IconButton>
                  ) : (
                    <Search />
                  )}
                </InputAdornment>
              ),
            },
          }}
        />
      </div>
      <Button
        startIcon={<Sort />}
        variant="outlined"
        onClick={toggle}
        sx={{
          display: { xs: "flex", lg: "none" },
        }}
      >
        Urutkan Data
      </Button>
      <Dialog open={open} onClose={toggle}>
        <DialogTitle>Urutkan</DialogTitle>
        <DialogContent
          sx={{
            width: "250px",
            display: "flex",
            flexDirection: "column",
            gap: 2,
            overflow: "initial",
          }}
        >
          <SelectElement
            control={control}
            name="field"
            options={sortSelectOptions}
            label="Pilih Kolom"
            slotProps={{
              input: {
                endAdornment: watch("field") && (
                  <InputAdornment position="end">
                    <IconButton onClick={() => setValue("field", "")}>
                      <Clear />
                    </IconButton>
                  </InputAdornment>
                ),
              },
            }}
          />
          <SelectElement
            control={control}
            name="sortDirection"
            options={[
              { label: "A-Z", id: "asc" },
              { label: "Z-A", id: "desc" },
            ]}
            label="Urutan"
          />
        </DialogContent>
        <DialogActions sx={{ pb: 2, px: 3 }}>
          <Button
            variant="outlined"
            onClick={() => {
              reset();
              toggle();
            }}
          >
            Batal
          </Button>
          <Button
            variant="contained"
            onClick={handleSubmit((value) => {
              if (value.field) {
                api.current.sortColumn(value.field, value.sortDirection);
              } else {
                api.current.sortColumn("name", null);
              }
              toggle();
            })}
          >
            Simpan
          </Button>
        </DialogActions>
      </Dialog>
    </div>
  );
}
