import { Paper, Stack, Typography } from "@mui/material";
import { useStaffAnnouncements } from "@sisva/hooks/query/academic/useAnnouncements";
import { useTeachersSubjectTeachers } from "@sisva/hooks/query/academic/useSubjectTeachers";
import { useCurrentUser } from "@sisva/providers";
import { useSchool } from "@sisva/providers";
import { getUserTypeText } from "@sisva/types/types";
import { AvatarWithAcronymByID } from "@sisva/ui";
import { SisvaScheduleComponent } from "@sisva/ui";
import { getFileUrl } from "@sisva/utils";
import { Empty, Image } from "antd";

export default function DashboardPage() {
  const currentUser = useCurrentUser();
  const school = useSchool();
  const { data: subjectTeachers = [] } = useTeachersSubjectTeachers(
    currentUser.id
  );
  const { data: announcements = [] } = useStaffAnnouncements();
  return (
    <Stack
      sx={{
        p: 2,
        gap: 2,
      }}
    >
      <Typography
        sx={{
          fontSize: 24,
          fontWeight: 600,
        }}
      >
        Beranda
      </Typography>
      <Stack>
        <Stack sx={{ gap: 2 }}>
          <Paper
            sx={{
              display: "flex",
              gap: 2,
              p: 2,
            }}
          >
            <AvatarWithAcronymByID user_id={currentUser.id} size={64} />
            <Stack sx={{ gap: 1 }}>
              <Typography
                sx={{ fontWeight: 500, fontSize: 16 }}
                color="primary"
              >
                {currentUser.name}
              </Typography>
              <Stack sx={{ flexDirection: "row", gap: 2 }}>
                <Stack>
                  <Typography sx={{ fontSize: 12 }} color="textSecondary">
                    Username
                  </Typography>
                  <Typography>{currentUser.username}</Typography>
                </Stack>
                <Stack>
                  <Typography sx={{ fontSize: 12 }} color="textSecondary">
                    Status
                  </Typography>
                  <Typography>{getUserTypeText(currentUser.type)}</Typography>
                </Stack>
              </Stack>
              {subjectTeachers.length > 0 && (
                <Stack>
                  <Typography sx={{ fontSize: 12 }} color="textSecondary">
                    Mata Pelajaran
                  </Typography>
                  <Typography>
                    {subjectTeachers
                      .map((subjectTeacher) => subjectTeacher.subject_name)
                      .join(", ")}
                  </Typography>
                </Stack>
              )}
            </Stack>
          </Paper>
          {currentUser.type === "teacher" && (
            <Paper
              sx={{ gap: 2, p: 2, display: "flex", flexDirection: "column" }}
            >
              <Typography sx={{ fontSize: 15, fontWeight: 600 }}>
                Jadwal Hari Ini
              </Typography>
              <SisvaScheduleComponent
                user_id={currentUser.id}
                currentView="Day"
              />
            </Paper>
          )}
          <Paper
            sx={{ gap: 2, p: 2, display: "flex", flexDirection: "column" }}
          >
            <Typography sx={{ fontSize: 15, fontWeight: 600 }}>
              Pengumuman
            </Typography>
            <Stack sx={{ gap: 2 }}>
              {announcements.length === 0 && <Empty />}
              {announcements.slice(0, 3).map((announcement) => {
                const imageUrl = getFileUrl(announcement.image_uri, school.id);
                return (
                  <Paper
                    key={announcement.id}
                    sx={{ p: 2, display: "flex", gap: 2 }}
                  >
                    <Stack>
                      {imageUrl && (
                        <Image
                          src={imageUrl}
                          alt={announcement.title}
                          width={96}
                          height={96}
                          className="min-w-24 object-cover"
                        />
                      )}
                    </Stack>

                    <Stack>
                      <Typography sx={{ fontSize: 14, fontWeight: 600 }}>
                        {announcement.title}
                      </Typography>
                      <Typography>{announcement.text}</Typography>
                    </Stack>
                  </Paper>
                );
              })}
            </Stack>
          </Paper>
        </Stack>
      </Stack>
    </Stack>
  );
}
