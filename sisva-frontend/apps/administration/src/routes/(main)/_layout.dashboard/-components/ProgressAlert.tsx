import {
  Dialog,
  DialogContent,
  DialogTitle,
  Divider,
  List,
  ListItem,
} from "@mui/material";

export default function ProgressAlert({
  open,
  report,
  title,
}: {
  open: boolean;
  report: string[];
  title: string;
}) {
  return (
    <Dialog
      open={open}
      maxWidth="md"
      fullWidth={true}
      sx={{
        "& .MuiDialog-paper": {
          borderRadius: 2,
        },
      }}
    >
      <DialogTitle id="alert-dialog-title">{title}</DialogTitle>
      <Divider />
      <DialogContent>
        {report.map((report, index) => {
          return (
            <List key={index}>
              <ListItem>{report}</ListItem>
            </List>
          );
        })}
      </DialogContent>
    </Dialog>
  );
}
