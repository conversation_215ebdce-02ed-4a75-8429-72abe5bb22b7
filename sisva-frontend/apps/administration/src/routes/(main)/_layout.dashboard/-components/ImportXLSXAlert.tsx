import {
  Button,
  Dialog,
  <PERSON>alogActions,
  DialogContent,
  DialogTitle,
  Divider,
  List,
  ListItem,
  Stack,
} from "@mui/material";

export default function ImportXLSXAlert({
  open,
  handleClose,
  importReport,
  title,
}: {
  open: boolean;
  handleClose: () => void;
  importReport: string[];
  title: string;
}) {
  return (
    <Dialog
      open={open}
      onClose={handleClose}
      aria-labelledby="alert-dialog-title"
      aria-describedby="alert-dialog-description"
      maxWidth="md"
      fullWidth={true}
      sx={{
        "& .MuiDialog-paper": {
          borderRadius: 2,
        },
      }}
    >
      <DialogTitle id="alert-dialog-title">{title}</DialogTitle>
      <Divider />
      <DialogContent>
        {importReport.map((report, index) => {
          return (
            <List key={index}>
              <ListItem>{report}</ListItem>
            </List>
          );
        })}
      </DialogContent>
      {importReport.length > 0 && <Divider />}
      <DialogActions>
        <Stack
          sx={{
            p: 1,
          }}
        >
          <Button
            onClick={handleClose}
            autoFocus
            variant="contained"
            sx={{
              px: 5,
            }}
          >
            OK
          </Button>
        </Stack>
      </DialogActions>
    </Dialog>
  );
}
