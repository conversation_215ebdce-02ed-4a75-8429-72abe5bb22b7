import { yupResolver } from "@hookform/resolvers/yup";
import {
  BorderColorRounded,
  Check,
  DashboardCustomize,
  Info,
  Notes,
  Palette,
} from "@mui/icons-material";
import {
  Box,
  Button,
  Divider,
  Stack,
  TextField,
  Typography,
} from "@mui/material";
import { useUploadFile } from "@sisva/hooks/query/file/useFiles";
import { invalidateCurrentSchool } from "@sisva/hooks/query/useAuth";
import { useUpdateSchool } from "@sisva/hooks/query/useSchools";
import { useGetFileUrl } from "@sisva/hooks/utils";
import { useSchool } from "@sisva/providers";
import {
  schoolEducationLevelOptions,
  schoolOwnershipTypeOptions,
  schoolTypeOptions,
} from "@sisva/types/dropdownOptions";
import { schoolSchema } from "@sisva/types/formTypes";
import { useQueryClient } from "@tanstack/react-query";
import { useRouter } from "@tanstack/react-router";
import { useToggle } from "ahooks";
import { Image } from "antd";
import { SelectElement, TextFieldElement } from "react-hook-form-mui";
import { useForm } from "react-hook-form-mui";
import toast from "react-hot-toast";

import fallback from "#/assets/images/fallback.png";

const paletteOptions = [
  "#4D7C0F",
  "#15803D",
  "#047857",
  "#0F766E",
  "#0E7490",
  "#0369A1",
  "#1D4ED8",
  "#4338CA",
  "#6D28D9",
  "#7E22CE",
  "#A21CAF",
  "#44403C",
  "#404040",
  "#3F3F46",
  "#374151",
  "#334155",
  "#c10007",
  "#f54a00",
  "#ca3500",
  "#cc8400",
  "#5ea500",
  "#00a63e",
  "#059669",
  "#0d9488",
  "#0891b2",
  "#0284c7",
  "#2563eb",
  "#4f46e5",
  "#7c3aed",
  "#c026d3",
  "#db2777",
  "#e11d48",
  "#64748b",
  "#78716c",
];

export default function ProfilTab() {
  const router = useRouter();
  const school = useSchool();
  const { mutateAsync: uploadFile } = useUploadFile();
  const getUrl = useGetFileUrl();
  const queryClient = useQueryClient();

  const jsonText = (() => {
    try {
      return JSON.parse(school.additional_json_text);
    } catch (error) {
      return {};
    }
  })();

  const [edit, { toggle }] = useToggle(false);
  const { control, handleSubmit, reset, watch, setValue } = useForm({
    values: {
      name: school?.name ?? "",
      abbreviation: school?.abbreviation ?? "",
      education_level: school?.education_level ?? "",
      education_ownership_type: school?.education_ownership_type ?? "",
      education_type: school?.education_type ?? "",
      identifier_type: school?.identifier_type ?? "",
      identifier_value: school?.identifier_value ?? "",
      landing_image_uri: school?.landing_image_uri ?? "",
      logo_uri: school?.logo_uri ?? "",
      theme_json_text: school?.theme_json_text ?? "",
      address: jsonText.address ?? "",
      email: jsonText.email ?? "",
      phone: jsonText.phone ?? "",
    },
    resolver: yupResolver(schoolSchema),
  });
  const { mutate: updateSchool } = useUpdateSchool({
    async onSuccess() {
      invalidateCurrentSchool(queryClient);
      await router.invalidate();
      toggle();
      toast.success("Profil sekolah berhasil diperbarui");
    },
    onError() {
      toast.error("Profile sekolah gagal diperbarui");
    },
  });

  const commonProps = {
    slotProps: {
      input: {
        readOnly: !edit,
        ...(edit ? {} : { disableUnderline: true }),
      },
      inputLabel: {
        sx: {
          fontSize: 16,
        },
      },
      select: {
        IconComponent: edit ? undefined : () => null,
      },
    },
    variant: edit ? undefined : ("standard" as const),
  };

  const logo_url = getUrl(watch("logo_uri"));
  const landing_image_url = getUrl(watch("landing_image_uri"));
  const themeColor = watch("theme_json_text");

  return (
    <Stack
      sx={{ p: 2, gap: 2 }}
      component={"form"}
      onSubmit={handleSubmit((value) => {
        updateSchool(value);
      })}
    >
      <Stack
        sx={{
          flexDirection: {
            xs: "column",
            md: "row",
          },
          gap: 2,
          py: 1,
        }}
      >
        <Notes color="primary" />
        <Stack sx={{ gap: 2, flex: 1 }}>
          <Box
            sx={{
              display: "grid",
              gridTemplateColumns: {
                xs: "repeat(1, 1fr)",
                md: "repeat(2, 1fr)",
              },
              gap: 2,
            }}
          >
            <TextFieldElement
              name="name"
              label="Nama Sekolah"
              control={control}
              {...commonProps}
            />
            <TextField
              value={school.code}
              label="Kode Sekolah"
              {...commonProps}
              disabled={edit ? true : undefined}
            />
            <TextFieldElement
              name="abbreviation"
              label="Nama Singkatan"
              control={control}
              {...commonProps}
            />
            <TextFieldElement
              name="identifier_value"
              label="NPSN"
              control={control}
              {...commonProps}
            />
            <TextFieldElement
              name="email"
              label="Email"
              control={control}
              {...commonProps}
            />
            <TextFieldElement
              name="phone"
              label="Nomor Telepon"
              control={control}
              {...commonProps}
            />
            <TextFieldElement
              name="address"
              label="Alamat"
              control={control}
              {...commonProps}
            />
          </Box>
          <Stack
            sx={{
              gap: 1,
            }}
          >
            <Typography color="textSecondary" sx={{ fontSize: 13 }}>
              Logo Sekolah
            </Typography>
            <Stack
              sx={{
                flexDirection: "row",
                gap: 1,
                gridColumn: "",
              }}
            >
              <Image
                src={logo_url ? logo_url : fallback}
                alt={"Logo Sekolah"}
                width={120}
                height={120}
                className="min-w-20 object-cover"
                fallback={fallback}
                preview={!!logo_url}
              />
              {edit && (
                <Stack
                  sx={{
                    gap: 1,
                    flex: 1,
                    justifyContent: "end",
                    maxWidth: 160,
                  }}
                >
                  <Typography
                    sx={{
                      fontSize: 12,
                      display: "flex",
                      gap: 0.5,
                      alignItems: "center",
                    }}
                    color="textSecondary"
                  >
                    <Info fontSize="small" />
                    Max 500kb
                  </Typography>
                  <Button component="label" variant="contained" size="small">
                    Ubah Logo
                    <input
                      accept="image/*"
                      className="hidden"
                      type="file"
                      onChange={async (e) => {
                        if (!e.target.files?.[0]) return;
                        const formData = new FormData();
                        formData.append("file", e.target.files[0]);
                        const uri = await uploadFile(formData);
                        setValue("logo_uri", uri);
                        e.target.value = "";
                      }}
                      multiple
                    />
                  </Button>
                  <Button
                    // NOTE: BE doesn't allow empty string or null
                    sx={{ display: "none" }}
                    variant="outlined"
                    size="small"
                    color="error"
                    onClick={() => {
                      setValue("logo_uri", "");
                    }}
                  >
                    Hapus
                  </Button>
                </Stack>
              )}
            </Stack>
          </Stack>
        </Stack>
      </Stack>
      <Divider />
      <Stack
        sx={{
          flexDirection: {
            xs: "column",
            md: "row",
          },
          gap: 2,
          py: 1,
        }}
      >
        <DashboardCustomize color="primary" />
        <Box
          sx={{
            display: "grid",
            gridTemplateColumns: {
              xs: "repeat(1, 1fr)",
              md: "repeat(2, 1fr)",
            },
            gap: 2,
            flex: 1,
          }}
        >
          <SelectElement
            name="education_type"
            label="Jenis Sekolah"
            control={control}
            options={schoolTypeOptions.map((option) => ({
              label: option.label,
              id: option.value,
            }))}
            {...commonProps}
          />
          <SelectElement
            name="education_level"
            label="Tingkatan Sekolah"
            control={control}
            options={schoolEducationLevelOptions.map((option) => ({
              label: option.label,
              id: option.value,
            }))}
            {...commonProps}
          />
          <SelectElement
            name="education_ownership_type"
            label="Kepemilikan Sekolah"
            control={control}
            options={schoolOwnershipTypeOptions.map((option) => ({
              label: option.label,
              id: option.value,
            }))}
            {...commonProps}
          />
        </Box>
      </Stack>
      <Divider />
      <Stack
        sx={{
          flexDirection: {
            xs: "column",
            md: "row",
          },
          gap: 2,
          py: 1,
        }}
      >
        <Palette color="primary" />
        <Stack sx={{ gap: 2 }}>
          <Stack
            sx={{
              gap: 1,
              flex: 1,
            }}
          >
            <Typography color="textSecondary" sx={{ fontSize: 13 }}>
              Gambar Latar
            </Typography>
            <Stack
              sx={{
                flexDirection: "row",
                gap: 1,
                gridColumn: "",
              }}
            >
              <Image
                src={landing_image_url ? landing_image_url : fallback}
                alt={"Gambar Latar"}
                width={120}
                height={120}
                className="min-w-20 object-cover"
                fallback={fallback}
                preview={!!landing_image_url}
              />
              {edit && (
                <Stack
                  sx={{
                    gap: 1,
                    flex: 1,
                    justifyContent: "end",
                    maxWidth: 160,
                  }}
                >
                  <Typography
                    sx={{
                      fontSize: 12,
                      display: "flex",
                      gap: 0.5,
                      alignItems: "center",
                    }}
                    color="textSecondary"
                  >
                    <Info fontSize="small" />
                    Max 500kb
                  </Typography>
                  <Button component="label" variant="contained" size="small">
                    Ubah Gambar Latar
                    <input
                      accept="image/*"
                      className="hidden"
                      type="file"
                      onChange={async (e) => {
                        if (!e.target.files?.[0]) return;
                        const formData = new FormData();
                        formData.append("file", e.target.files[0]);
                        const uri = await uploadFile(formData);
                        setValue("landing_image_uri", uri);
                        e.target.value = "";
                      }}
                      multiple
                    />
                  </Button>
                  <Button
                    // NOTE: BE doesn't allow empty string or null
                    sx={{ display: "none" }}
                    variant="outlined"
                    size="small"
                    color="error"
                    onClick={() => {
                      setValue("landing_image_uri", "");
                    }}
                  >
                    Hapus
                  </Button>
                </Stack>
              )}
            </Stack>
          </Stack>
          <Stack
            sx={{
              gap: 1,
              flex: 1,
            }}
          >
            <Typography color="textSecondary" sx={{ fontSize: 13 }}>
              Warna Tema
            </Typography>
            <Stack
              sx={{
                flexDirection: "row",
                gap: 1,
                gridColumn: "",
                flexWrap: "wrap",
              }}
            >
              {edit ? (
                paletteOptions.map((color) => (
                  <Box
                    onClick={() => setValue("theme_json_text", color)}
                    key={color}
                    bgcolor={color}
                    width={120}
                    height={40}
                    borderRadius={2}
                    sx={{
                      display: "flex",
                      justifyContent: "center",
                      alignItems: "center",
                      cursor: "pointer",
                    }}
                  >
                    {color === themeColor && (
                      <Check
                        sx={{
                          color: "white",
                          fontSize: 32,
                        }}
                      />
                    )}
                  </Box>
                ))
              ) : (
                <Box
                  bgcolor={themeColor}
                  width={120}
                  height={40}
                  borderRadius={2}
                />
              )}
            </Stack>
          </Stack>
        </Stack>
      </Stack>

      <Stack
        sx={{
          flexDirection: "row",
          justifyContent: "end",
          gap: 2,
        }}
      >
        <Button
          onClick={() => {
            toggle();
            reset();
          }}
          startIcon={edit ? undefined : <BorderColorRounded />}
          variant="outlined"
        >
          {edit ? "Batal" : "Edit"}
        </Button>
        <Button
          type="submit"
          variant="contained"
          sx={{ display: edit ? undefined : "none" }}
        >
          Simpan
        </Button>
      </Stack>
    </Stack>
  );
}
