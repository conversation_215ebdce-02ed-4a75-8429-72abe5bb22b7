import { Box, Paper, Stack, Tab, Tabs, Typography } from "@mui/material";
import { useSchool } from "@sisva/providers";
import { Image } from "@sisva/ui";
import type { SyntheticEvent } from "react";
import { useState } from "react";

import EntitasTab from "./EntitasTab";
import ProfilTab from "./ProfilTab";

export default function ProfileDetailPage() {
  const [tabIndex, setTabIndex] = useState(0);
  const school = useSchool();

  return (
    <Stack
      sx={{
        height: "100%",
        width: "100%",
        p: {
          xs: 2,
          lg: 4,
        },
        gap: 2,
      }}
    >
      <Typography sx={{ fontSize: 20, fontWeight: 600 }}>
        Profil Sekolah
      </Typography>
      <Paper
        variant="outlined"
        sx={{
          display: "flex",
          flexDirection: "row",
          px: {
            xs: 2,
            lg: 4,
          },
          py: 2,
          gap: 2,
          alignItems: "center",
        }}
      >
        <Box sx={{ height: 70, width: 70, position: "relative" }}>
          {school.logo_url && (
            <Image
              alt="Logo Sekolah"
              src={school.logo_url}
              fill
              className="object-contain"
            />
          )}
        </Box>
        <Stack sx={{}}>
          <Typography
            sx={{
              fontSize: 18,
              fontWeight: 600,
            }}
          >
            {school?.name}
          </Typography>
          <Typography
            sx={{
              fontSize: 14,
            }}
          >
            {school?.abbreviation}
          </Typography>
        </Stack>
      </Paper>
      <Paper
        variant="outlined"
        sx={{
          flex: 1,
          overflow: "auto",
        }}
      >
        <Tabs
          value={tabIndex}
          onChange={(event: SyntheticEvent, newValue: number) => {
            setTabIndex(newValue);
          }}
          sx={{
            borderBottom: 1,
            borderColor: "divider",
          }}
        >
          <Tab
            label="Profil"
            sx={{ fontWeight: 600, textTransform: "none", fontSize: 14 }}
          />
          <Tab
            label="Entitas"
            sx={{ fontWeight: 600, textTransform: "none", fontSize: 14 }}
          />
        </Tabs>
        {(() => {
          switch (tabIndex) {
            case 0:
              return <ProfilTab />;
            case 1:
              return <EntitasTab />;
          }
        })()}
      </Paper>
    </Stack>
  );
}
