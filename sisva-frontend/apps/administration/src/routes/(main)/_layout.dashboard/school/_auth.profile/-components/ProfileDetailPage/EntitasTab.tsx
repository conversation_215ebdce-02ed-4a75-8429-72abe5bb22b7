import { valibotResolver } from "@hookform/resolvers/valibot";
import { BorderColorRounded } from "@mui/icons-material";
import { Box, Button, Stack, Typography } from "@mui/material";
import {
  useCreateEntity,
  useDeleteEntity,
  useEntities,
  useUpdateEntity,
} from "@sisva/hooks/query/academic/useEntities";
import { useStaffTeachers } from "@sisva/hooks/query/user/useStaffTeachers";
import { useUser } from "@sisva/hooks/query/user/useUsers";
import { AvatarWithAcronymByID } from "@sisva/ui";
import { useToggle } from "ahooks";
import type { ReactNode } from "react";
import { AutocompleteElement } from "react-hook-form-mui";
import { useForm } from "react-hook-form-mui";
import toast from "react-hot-toast";
import {
  type InferInput,
  type InferOutput,
  nullable,
  object,
  pipe,
  string,
} from "valibot";

export default function EntitasTab() {
  const { data: entities = [] } = useEntities();
  const { data: staffTeachers = [] } = useStaffTeachers();

  const headmasterEntity = entities.find((item) => item.type === "headmaster");
  const viceHeadmasterEntity = entities.find(
    (item) => item.type === "vice_headmaster"
  );

  const { data: headmaster } = useUser(headmasterEntity?.user_id);
  const { data: viceHeadmaster } = useUser(viceHeadmasterEntity?.user_id);

  const schema = object({
    headmaster_id: pipe(nullable(string())),
    vice_headmaster_id: pipe(nullable(string())),
  });

  type SchemaInput = InferInput<typeof schema>;
  type SchemaOutput = InferOutput<typeof schema>;

  const [edit, { toggle }] = useToggle(false);
  const { control, handleSubmit, reset, watch, setValue } = useForm<
    SchemaInput,
    unknown,
    SchemaOutput
  >({
    values: {
      headmaster_id: headmasterEntity?.user_id ?? null,
      vice_headmaster_id: viceHeadmasterEntity?.user_id ?? null,
    },
    resolver: valibotResolver(schema),
  });

  const { mutateAsync: createEntity } = useCreateEntity();
  const { mutateAsync: updateEntity } = useUpdateEntity();
  const { mutateAsync: deleteEntity } = useDeleteEntity();

  const onSubmit = handleSubmit(async (value) => {
    try {
      if (headmasterEntity) {
        // update
        if (value.headmaster_id) {
          await updateEntity({
            id: headmasterEntity.id,
            type: "headmaster",
            user_id: value.headmaster_id,
          });
          // delete
        } else {
          await deleteEntity(headmasterEntity.id);
        }
      } else {
        // create
        if (value.headmaster_id) {
          await createEntity({
            type: "headmaster",
            user_id: value.headmaster_id,
          });
        }
      }

      if (viceHeadmasterEntity) {
        // update
        if (value.vice_headmaster_id) {
          await updateEntity({
            id: viceHeadmasterEntity.id,
            type: "vice_headmaster",
            user_id: value.vice_headmaster_id,
          });
          // delete
        } else {
          await deleteEntity(viceHeadmasterEntity.id);
        }
      } else {
        // create
        if (value.vice_headmaster_id) {
          await createEntity({
            type: "vice_headmaster",
            user_id: value.vice_headmaster_id,
          });
        }
      }

      toast.error("Entitas berhasil diperbarui");
    } catch {
      toast.error("Entitas gagal diperbarui");
    }
  });

  return (
    <Stack sx={{ p: 2, gap: 2 }} component={"form"} onSubmit={onSubmit}>
      <Stack
        sx={{
          flexDirection: {
            xs: "column",
            md: "row",
          },
          gap: 2,
          py: 1,
        }}
      >
        <Stack sx={{ gap: 2, flex: 1 }}>
          <Box
            sx={{
              display: "grid",
              gridTemplateColumns: {
                xs: "repeat(1, 1fr)",
                md: "repeat(2, 1fr)",
              },
              gap: 2,
            }}
          >
            <EditableField
              edit={edit}
              label="Kepala Sekolah"
              value={
                headmaster && (
                  <Stack
                    sx={{ flexDirection: "row", gap: 1, alignItems: "center" }}
                  >
                    <AvatarWithAcronymByID user_id={headmaster?.id} />
                    <Typography>{headmaster?.name}</Typography>
                  </Stack>
                )
              }
              inputForm={
                <AutocompleteElement
                  name="headmaster_id"
                  label="Kepala Sekolah"
                  control={control}
                  matchId
                  options={staffTeachers.map((item) => {
                    return {
                      id: item.id,
                      label: item.name,
                    };
                  })}
                />
              }
            />
            <EditableField
              edit={edit}
              label="Wakil Kepala Sekolah"
              value={
                viceHeadmaster && (
                  <Stack
                    sx={{ flexDirection: "row", gap: 1, alignItems: "center" }}
                  >
                    <AvatarWithAcronymByID user_id={viceHeadmaster?.id} />
                    <Typography>{viceHeadmaster?.name}</Typography>
                  </Stack>
                )
              }
              inputForm={
                <AutocompleteElement
                  name="vice_headmaster_id"
                  label="Wakil Kepala Sekolah"
                  control={control}
                  matchId
                  options={staffTeachers.map((item) => {
                    return {
                      id: item.id,
                      label: item.name,
                    };
                  })}
                />
              }
            />
          </Box>
        </Stack>
      </Stack>

      <Stack
        sx={{
          flexDirection: "row",
          justifyContent: "end",
          gap: 2,
        }}
      >
        <Button
          onClick={() => {
            toggle();
            reset();
          }}
          startIcon={edit ? undefined : <BorderColorRounded />}
          variant="outlined"
        >
          {edit ? "Batal" : "Edit"}
        </Button>
        <Button
          type="submit"
          variant="contained"
          sx={{ display: edit ? undefined : "none" }}
        >
          Simpan
        </Button>
      </Stack>
    </Stack>
  );
}

function EditableField({
  label,
  value,
  inputForm,
  edit,
}: {
  label: string;
  value: ReactNode;
  inputForm: ReactNode;
  edit: boolean;
}) {
  if (!edit) {
    return (
      <Stack sx={{ gap: 1 }}>
        <Typography variant="caption" color="textSecondary">
          {label}
        </Typography>
        {value}
      </Stack>
    );
  } else {
    return inputForm;
  }
}
