import { Clear, Search, Sort } from "@mui/icons-material";
import {
  <PERSON>ton,
  Chip,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  IconButton,
  InputAdornment,
  Stack,
  Typography,
  useMediaQuery,
} from "@mui/material";
import type { GridColDef, GridFilterOperator } from "@mui/x-data-grid";
import { DataGrid, useGridApiContext } from "@mui/x-data-grid";
import {
  type InvoiceWithBillAndUser,
  useInvoicesWithBillAndUser,
} from "@sisva/hooks/query/finance/useInvoices";
import type { InvoiceStatusText } from "@sisva/types/types";
import { getInvoiceStatusText } from "@sisva/types/types";
import { formatToRupiah } from "@sisva/utils";
import { onlyUnique } from "@sisva/utils";
import { useToggle } from "ahooks";
import dayjs from "dayjs";
import { sort } from "fast-sort";
import { useEffect } from "react";
import { useForm } from "react-hook-form";
import {
  AutocompleteElement,
  SelectElement,
  TextFieldElement,
} from "react-hook-form-mui";
import { DatePickerElement } from "react-hook-form-mui/date-pickers";

import DeleteIcon from "#/routes/(main)/-components/DeleteIcon";
import EditIcon from "#/routes/(main)/-components/EditIcon";

import CreateInvoiceButton from "../CreateInvoiceButton";
import DeleteInvoiceButton from "../DeleteInvoiceButton";

type Field =
  | "mobile_column"
  | "user_name"
  | "bill_name"
  | "bill_deadline"
  | "bill_amount"
  | keyof InvoiceWithBillAndUser
  | typeof filter_field
  | "action";
type TypeSafeColDef = GridColDef & { field: Field };

const filter_field = "filter_field";
const field = {
  filter1: "status",
  filter2: "bill_name",
  filter3: "start_date",
  filter4: "end_date",
} satisfies Record<string, Field | "start_date" | "end_date">;

type FilterValue = Record<(typeof field)[keyof typeof field], string>;

export default function TagihanTab() {
  const isMobile = useMediaQuery((theme) => theme.breakpoints.down("lg"));
  const { data: invoices = [] } = useInvoicesWithBillAndUser();

  const customOperator: GridFilterOperator<InvoiceWithBillAndUser> = {
    label: "Custom Operator",
    value: "customOperator",
    getApplyFilterFn: (filterItem, column) => {
      if (!filterItem.field || !filterItem.value || !filterItem.operator) {
        return null;
      }
      const filterValue = filterItem.value as FilterValue;

      return (value, row, column, apiRef) => {
        let pass = true;
        if (filterValue.status) {
          if (getInvoiceStatusText(row.status) !== filterValue.status)
            pass = false;
        }
        if (filterValue.bill_name) {
          if (row.bill?.name !== filterValue.bill_name) pass = false;
        }
        if (filterValue.start_date) {
          if (
            dayjs(row.bill?.deadline, "DD/MM/YYYY h:mm A Z").isBefore(
              dayjs(filterValue.start_date)
            )
          )
            pass = false;
        }
        if (filterValue.end_date) {
          if (
            dayjs(row.bill?.deadline, "DD/MM/YYYY h:mm A Z").isAfter(
              dayjs(filterValue.end_date)
            )
          )
            pass = false;
        }
        return pass;
      };
    },
  };

  const columns: TypeSafeColDef[] = [
    {
      field: "mobile_column",
      headerName: "",
      valueGetter: (_, row) => row,
      flex: 1,
      display: "flex",
      renderCell: (args) => {
        const value = args.value as InvoiceWithBillAndUser;
        return (
          <Stack sx={{ p: 1.5, gap: 1, width: "100%" }}>
            <Stack
              sx={{
                flexDirection: "row",
                justifyContent: "space-between",
                width: "100%",
              }}
            >
              <Typography sx={{ fontWeight: 600 }}>No Invoice</Typography>
              <Typography>{value.id}</Typography>
            </Stack>
            <Stack
              sx={{
                flexDirection: "row",
                justifyContent: "space-between",
                width: "100%",
              }}
            >
              <Typography sx={{ fontWeight: 600 }}>Nama</Typography>
              <Typography>{value?.user?.name}</Typography>
            </Stack>
            <Stack
              sx={{
                flexDirection: "row",
                justifyContent: "space-between",
                width: "100%",
              }}
            >
              <Typography sx={{ fontWeight: 600 }}>Nama Tagihan</Typography>
              <Typography>{value?.bill?.name}</Typography>
            </Stack>
            <Stack
              sx={{
                flexDirection: "row",
                justifyContent: "space-between",
                width: "100%",
              }}
            >
              <Typography sx={{ fontWeight: 600 }}>Total Harga</Typography>
              <Typography>{formatToRupiah(value.bill?.amount)}</Typography>
            </Stack>
            <Stack
              sx={{
                flexDirection: "row",
                justifyContent: "space-between",
                width: "100%",
              }}
            >
              <Typography sx={{ fontWeight: 600 }}>Nilai Invoice</Typography>
              <Typography>{formatToRupiah(value.amount)}</Typography>
            </Stack>
            <Stack
              sx={{
                flexDirection: "row",
                justifyContent: "space-between",
                width: "100%",
              }}
            >
              <Typography sx={{ fontWeight: 600 }}>Status</Typography>

              <StatusChip value={getInvoiceStatusText(value.status)} />
            </Stack>
            <Stack
              sx={{
                flexDirection: "row",
                justifyContent: "flex-end",
                gap: 1,
                width: "100%",
              }}
            >
              <CreateInvoiceButton
                invoice_id={value.id}
                renderTrigger={(onClick) => <EditIcon onClick={onClick} />}
              />
              <DeleteInvoiceButton
                invoice_id={value.id}
                renderTrigger={(onClick) => <DeleteIcon onClick={onClick} />}
              />
            </Stack>
          </Stack>
        );
      },
    },
    {
      field: "id",
      headerName: "No Invoice",
      display: "flex",
      width: 120,
      renderCell: ({ value }) => <Stack sx={{ py: 1.3 }}>{value}</Stack>,
    },
    {
      field: "user_name",
      valueGetter: (_, row: InvoiceWithBillAndUser) => row.user?.name,
      headerName: "Nama",
      display: "flex",
      flex: 1,
      renderCell: ({ value }) => <Stack sx={{ py: 1.3 }}>{value}</Stack>,
    },
    {
      field: "bill_name",
      valueGetter: (_, row: InvoiceWithBillAndUser) => row.bill?.name,
      headerName: "Nama Tagihan",
      display: "flex",
      flex: 1,
      renderCell: ({ value }) => <Stack sx={{ py: 1.3 }}>{value}</Stack>,
    },
    {
      field: "bill_deadline",
      headerName: "Tenggat Waktu",
      valueGetter: (_, row: InvoiceWithBillAndUser) =>
        dayjs(row.bill?.deadline, "DD/MM/YYYY h:mm A Z").toDate(),
      display: "flex",
      width: 120,
      renderCell: ({ value }) => (
        <Stack
          sx={{ py: 1.3, flexDirection: "row", alignItems: "center", gap: 2 }}
        >
          {dayjs(value).format("D MMM YYYY")}
        </Stack>
      ),
    },
    {
      field: "bill_amount",
      valueGetter: (_, row: InvoiceWithBillAndUser) => row.bill?.amount,
      headerName: "Total Harga",
      display: "flex",
      renderCell: ({ value }) => (
        <Stack
          sx={{ py: 1.3, flexDirection: "row", alignItems: "center", gap: 2 }}
        >
          {formatToRupiah(value)}
        </Stack>
      ),
    },
    {
      field: "amount",
      valueGetter: (_, row: InvoiceWithBillAndUser) => row.amount,
      headerName: "Nilai Invoice",
      display: "flex",
      renderCell: ({ value }) => (
        <Stack
          sx={{ py: 1.3, flexDirection: "row", alignItems: "center", gap: 2 }}
        >
          {formatToRupiah(value)}
        </Stack>
      ),
    },
    {
      field: "status",
      valueGetter: (_, row: InvoiceWithBillAndUser) =>
        getInvoiceStatusText(row.status),
      headerName: "Status",
      display: "flex",
      renderCell: ({ value }) => (
        <Stack sx={{ py: 1.3 }}>
          <StatusChip value={value} />
        </Stack>
      ),
    },
    {
      field: "action",
      headerName: "Aksi",
      display: "flex",
      sortable: false,
      renderCell: ({ row }) => (
        <Stack
          sx={{ py: 1.3, flexDirection: "row", alignItems: "center", gap: 1 }}
        >
          <CreateInvoiceButton
            invoice_id={row.id}
            renderTrigger={(onClick) => <EditIcon onClick={onClick} />}
          />
          <DeleteInvoiceButton
            invoice_id={row.id}
            renderTrigger={(onClick) => <DeleteIcon onClick={onClick} />}
          />
        </Stack>
      ),
    },
    {
      field: filter_field,
      filterOperators: [customOperator],
    },
  ];

  const columnVisibilityModel = Object.fromEntries(
    columns
      .map((column) => column.field)
      .filter((field) =>
        isMobile ? field !== "mobile_column" : field === "mobile_column"
      )
      .map((field) => [field, false])
  );
  columnVisibilityModel[filter_field] = false;

  return (
    <DataGrid
      sx={{
        "& .MuiDataGrid-cell:focus": {
          outline: " none",
        },
        border: "none",
      }}
      getRowId={(row: InvoiceWithBillAndUser) => `${row.id}`}
      disableRowSelectionOnClick
      className="bg-white grow"
      rows={invoices}
      columns={columns}
      initialState={{ pagination: { paginationModel: { pageSize: 20 } } }}
      pageSizeOptions={[10, 20, 50]}
      getRowClassName={(params) =>
        params.indexRelativeToCurrentPage % 2 === 0
          ? "bg-neutral-50"
          : "bg-neutral-100"
      }
      slots={{
        toolbar: CustomToolbar,
      }}
      getRowHeight={() => "auto"}
      columnVisibilityModel={columnVisibilityModel}
      columnHeaderHeight={isMobile ? 0 : undefined}
      disableColumnMenu
      disableColumnFilter
    />
  );
}

function CustomToolbar() {
  const api = useGridApiContext();
  const [open, { toggle }] = useToggle(false);

  const { control, handleSubmit, reset, setValue, watch } = useForm<{
    field: Field | "";
    sortDirection: "asc" | "desc";
    quickFilter: string;
    [field.filter1]: string;
    [field.filter2]: string;
    [field.filter3]: string;
    [field.filter4]: string;
  }>({
    defaultValues: {
      field: "",
      sortDirection: "asc",
      quickFilter: "",
      [field.filter1]: "",
      [field.filter2]: "",
      [field.filter3]: "",
      [field.filter4]: "",
    },
  });

  const rows = Array.from(api.current.getRowModels()).map(
    ([key, value]) => value
  ) as InvoiceWithBillAndUser[];
  const statuses = rows
    .map((row) => getInvoiceStatusText(row.status))
    .filter((item) => item)
    .filter(onlyUnique);
  const billNames = sort(rows)
    .desc((r) => r.bill?.id)
    .map((row) => row.bill?.name)
    .filter((item) => item)
    .filter(onlyUnique);

  // apply filter
  const filter1 = watch(field.filter1);
  const filter2 = watch(field.filter2);
  const filter3 = watch(field.filter3);
  const filter4 = watch(field.filter4);
  useEffect(() => {
    const value: FilterValue = {
      [field.filter1]: filter1,
      [field.filter2]: filter2,
      [field.filter3]: filter3,
      [field.filter4]: filter4,
    };
    api.current.upsertFilterItem({
      field: filter_field,
      operator: "customOperator",
      value,
    });
  }, [api, filter1, filter2, filter3, filter4]);

  const sortSelectOptions: { label: string; id: Field }[] = [
    { label: "No Invoice", id: "id" },
    { label: "Nama", id: "user_name" },
    { label: "Nama Tagihan", id: "bill_name" },
    { label: "Total Harga", id: "bill_amount" },
    { label: "Nilai Invoice", id: "amount" },
    { label: "Status", id: "status" },
  ];

  const quickFilter = watch("quickFilter");
  useEffect(() => {
    api.current.setQuickFilterValues(
      quickFilter.split(" ").filter((s) => s !== "")
    );
  }, [api, quickFilter]);

  return (
    <div className="p-4 flex flex-col gap-2 border-b border-0 border-solid border-neutral-200">
      <div className=" flex justify-between gap-4 items-center thick-scrollbar">
        <Stack
          sx={{
            overflow: {
              xs: "auto",
              ["2xl"]: "initial",
            },
            py: {
              xs: 1,
              ["2xl"]: 0,
            },
          }}
        >
          <Stack sx={{ flexDirection: "row", gap: 2 }}>
            <TextFieldElement
              control={control}
              name="quickFilter"
              placeholder="Cari"
              size="small"
              sx={{
                minWidth: 150,
              }}
              slotProps={{
                input: {
                  endAdornment: (
                    <InputAdornment position="end">
                      {quickFilter ? (
                        <IconButton
                          size="small"
                          onClick={() => setValue("quickFilter", "")}
                        >
                          <Clear />
                        </IconButton>
                      ) : (
                        <Search />
                      )}
                    </InputAdornment>
                  ),
                },
              }}
            />
            <SelectElement
              size="small"
              sx={{ minWidth: "180px" }}
              control={control}
              name={field.filter1}
              options={statuses.map((item) => ({
                id: item,
                label: item,
              }))}
              label="Status"
              slotProps={{
                input: {
                  endAdornment: watch(field.filter1) && (
                    <InputAdornment position="end">
                      <IconButton
                        onClick={() => setValue(field.filter1, "")}
                        size="small"
                      >
                        <Clear />
                      </IconButton>
                    </InputAdornment>
                  ),
                },
              }}
            />
            <AutocompleteElement
              control={control}
              name={field.filter2}
              matchId
              options={billNames.map((item) => ({
                id: item,
                label: item,
              }))}
              label="Tagihan"
              autocompleteProps={{
                size: "small",
                sx: { minWidth: "180px" },
              }}
            />
            <DatePickerElement
              transform={{
                input(dateString) {
                  if (!dateString) return null;
                  return dayjs(dateString);
                },
                output(date) {
                  return date?.format() ?? "";
                },
              }}
              label="Tanggal Awal"
              control={control}
              name={field.filter3}
              format="DD MMMM YYYY"
              inputProps={{
                size: "small",
                sx: {
                  minWidth: 180,
                },
              }}
              slotProps={{
                actionBar: {
                  actions: ["clear"],
                },
              }}
            />
            <DatePickerElement
              transform={{
                input(dateString) {
                  if (!dateString) return null;
                  return dayjs(dateString);
                },
                output(date) {
                  return date?.format() ?? "";
                },
              }}
              label="Tanggal Akhir"
              control={control}
              name={field.filter4}
              format="DD MMMM YYYY"
              inputProps={{
                size: "small",
                sx: {
                  minWidth: 180,
                },
              }}
              slotProps={{
                actionBar: {
                  actions: ["clear"],
                },
              }}
            />
          </Stack>
        </Stack>
        <Stack
          sx={{
            minWidth: 110,
          }}
        >
          <CreateInvoiceButton />
        </Stack>
      </div>
      <Button
        startIcon={<Sort />}
        variant="outlined"
        onClick={toggle}
        sx={{
          display: { xs: "flex", lg: "none" },
        }}
      >
        Urutkan Data
      </Button>
      <Dialog open={open} onClose={toggle}>
        <DialogTitle>Urutkan</DialogTitle>
        <DialogContent
          sx={{
            width: "250px",
            display: "flex",
            flexDirection: "column",
            gap: 2,
            overflow: "initial",
          }}
        >
          <SelectElement
            control={control}
            name="field"
            options={sortSelectOptions}
            label="Pilih Kolom"
            slotProps={{
              input: {
                endAdornment: watch("field") && (
                  <InputAdornment position="end">
                    <IconButton onClick={() => setValue("field", "")}>
                      <Clear />
                    </IconButton>
                  </InputAdornment>
                ),
              },
            }}
          />
          <SelectElement
            control={control}
            name="sortDirection"
            options={[
              { label: "A-Z", id: "asc" },
              { label: "Z-A", id: "desc" },
            ]}
            label="Urutan"
          />
        </DialogContent>
        <DialogActions sx={{ pb: 2, px: 3 }}>
          <Button
            variant="outlined"
            onClick={() => {
              reset();
              toggle();
            }}
          >
            Batal
          </Button>
          <Button
            variant="contained"
            onClick={handleSubmit((value) => {
              if (value.field) {
                api.current.sortColumn(value.field, value.sortDirection);
              } else {
                api.current.sortColumn("name", null);
              }
              toggle();
            })}
          >
            Simpan
          </Button>
        </DialogActions>
      </Dialog>
    </div>
  );
}

function StatusChip({ value }: { value: string }) {
  return (
    <Chip
      label={value}
      color={(() => {
        switch (value as InvoiceStatusText) {
          case "Pending":
            return "warning";
          case "Verifikasi":
            return "info";
          case "Lunas":
            return "success";
        }
      })()}
    ></Chip>
  );
}
