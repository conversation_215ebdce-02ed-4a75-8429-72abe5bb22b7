import { yupResolver } from "@hookform/resolvers/yup";
import { Add } from "@mui/icons-material";
import {
  <PERSON><PERSON>,
  Divider,
  Modal,
  Paper,
  Stack,
  Typography,
} from "@mui/material";
import {
  useBill,
  useCreateBill,
  useUpdateBill,
} from "@sisva/hooks/query/finance/useBills";
import { billSchema } from "@sisva/types/formTypes";
import { useToggle } from "ahooks";
import currency from "currency.js";
import dayjs from "dayjs";
import { type ReactNode } from "react";
import {
  MultiSelectElement,
  TextareaAutosizeElement,
  TextFieldElement,
  useForm,
} from "react-hook-form-mui";
import { DatePickerElement } from "react-hook-form-mui/date-pickers";
import toast from "react-hot-toast";
/**
 * this component has 2 modes:
 * 1. create new bill, no props
 * 2. edit bill, by passing bill_id
 */
export default function CreateBillButton({
  // if bill_id provided, then it's edit mode
  bill_id,
  renderTrigger,
}: {
  bill_id?: number;
  renderTrigger?: (onClick: () => void) => ReactNode;
}) {
  const { data: bill } = useBill(bill_id);

  const [visible, { toggle }] = useToggle(false);
  const { control, handleSubmit, reset, watch } = useForm({
    values: {
      amount: bill?.amount ?? 0,
      deadline: bill?.deadline ?? "",
      name: bill?.name ?? "",
      target_user_types: bill?.target_user_types ?? [],
      custom_id: bill?.custom_id ?? "",
      description: bill?.description ?? "",
      status: bill?.status ?? "draft",
    },
    resolver: yupResolver(billSchema),
  });

  const { mutate: createBill } = useCreateBill({
    onSuccess: () => {
      toggle();
      reset();
      toast.success("Tagihan berhasil dibuat");
    },
    onError: () => {
      toast.error("Tagihan gagal dibuat");
    },
  });

  const { mutate: updateBill } = useUpdateBill({
    onSuccess: () => {
      toggle();
      reset();
      toast.success("Tagihan berhasil diperbarui");
    },
    onError: () => {
      toast.error("Tagihan gagal diperbarui");
    },
  });

  return (
    <>
      {renderTrigger ? (
        renderTrigger(toggle)
      ) : (
        <Button variant="contained" startIcon={<Add />} onClick={toggle}>
          Tambah
        </Button>
      )}
      <Modal open={visible} onClose={toggle}>
        <Stack
          sx={{
            width: 1,
            height: 1,
            justifyContent: "center",
            alignItems: "center",
            p: 2,
          }}
        >
          <Paper
            component={"form"}
            sx={{
              py: 2,
              width: 1,
              maxWidth: "ms",
              gap: 2,
              display: "flex",
              flexDirection: "column",
            }}
            onSubmit={handleSubmit((value) => {
              if (bill_id) {
                updateBill({
                  bill_id,
                  ...value,
                });
              } else {
                createBill(value);
              }
            })}
          >
            <Typography sx={{ fontWeight: 600, px: 2 }}>
              {(() => {
                if (bill_id) {
                  return "Edit Tagihan";
                } else {
                  return "Buat Tagihan";
                }
              })()}
            </Typography>
            <Divider />
            <Stack sx={{ gap: 2, px: 2 }}>
              <TextFieldElement
                name="name"
                label="Nama Tagihan"
                control={control}
              />

              <TextFieldElement
                transform={{
                  input(value) {
                    return currency(value, {
                      symbol: "Rp",
                      separator: ".",
                      precision: 0,
                    }).format();
                  },
                  output(e) {
                    return currency(e.target.value.replace(/[^0-9]/g, ""))
                      .value;
                  },
                }}
                name="amount"
                label="Jumlah Tagihan"
                control={control}
              />

              <TextFieldElement
                name="custom_id"
                label="No Tagihan"
                control={control}
              />
              <DatePickerElement
                transform={{
                  input(dateString) {
                    if (!dateString) return null;
                    return dayjs(dateString, "DD/MM/YYYY h:mm A Z");
                  },
                  output(date) {
                    return date?.format("DD/MM/YYYY h:mm A Z") ?? "";
                  },
                }}
                label="Batas Waktu"
                control={control}
                name="deadline"
                format="dddd, DD MMMM YYYY"
              />
              <TextareaAutosizeElement
                resizeStyle="vertical"
                name="description"
                label="Deskripsi"
                control={control}
                rows={4}
              />
              <MultiSelectElement
                itemKey="id"
                itemLabel="name"
                label="Target"
                name="target_user_types"
                control={control}
                options={[
                  {
                    id: "student",
                    name: "Siswa",
                  },
                  {
                    id: "staff",
                    name: "Staff",
                  },
                ]}
                showChips={!bill_id}
                readOnly={!!bill_id}
              />
            </Stack>
            <Divider />
            <Stack sx={{ gap: 2, px: 2, flexDirection: "row" }}>
              <Button variant="outlined" fullWidth onClick={toggle}>
                Batal
              </Button>
              <Button type="submit" variant="contained" fullWidth>
                Simpan
              </Button>
            </Stack>
          </Paper>
        </Stack>
      </Modal>
    </>
  );
}
