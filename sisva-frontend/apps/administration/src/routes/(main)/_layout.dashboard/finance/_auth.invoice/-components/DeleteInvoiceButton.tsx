import { DeleteForever } from "@mui/icons-material";
import {
  <PERSON><PERSON>,
  Button,
  Divider,
  Modal,
  Paper,
  Stack,
  Typography,
} from "@mui/material";
import {
  useDeleteInvoice,
  useInvoiceWithBillAndUser,
} from "@sisva/hooks/query/finance/useInvoices";
import { getInvoiceStatusText } from "@sisva/types/types";
import { formatToRupiah } from "@sisva/utils";
import { useToggle } from "ahooks";
import type { ReactNode } from "react";
import toast from "react-hot-toast";

export default function DeleteInvoiceButton({
  invoice_id,
  renderTrigger,
}: {
  invoice_id: number;
  renderTrigger?: (onClick: () => void) => ReactNode;
}) {
  const { data: invoice } = useInvoiceWithBillAndUser(invoice_id);

  const canBeDeleted = invoice?.status === "pending";

  const [visible, { toggle }] = useToggle(false);

  const { mutate: deleteInvoice } = useDeleteInvoice({
    onSuccess: () => {
      toggle();
      toast.success("Invoice berhasil dihapus");
    },
    onError: () => {
      toast.error("Invoice gagal dihapus");
    },
  });

  return (
    <>
      {renderTrigger ? (
        renderTrigger(toggle)
      ) : (
        <Button
          variant="contained"
          startIcon={<DeleteForever />}
          color="error"
          onClick={toggle}
        >
          Hapus
        </Button>
      )}
      <Modal open={visible} onClose={toggle}>
        <Stack
          sx={{
            width: 1,
            height: 1,
            justifyContent: "center",
            alignItems: "center",
            p: 2,
          }}
        >
          <Paper
            component="form"
            sx={{
              py: 2,
              width: 1,
              maxWidth: "ms",
              gap: 2,
              display: "flex",
              flexDirection: "column",
            }}
            onSubmit={(e) => {
              e.preventDefault();
              deleteInvoice(invoice_id);
            }}
          >
            <Typography sx={{ fontWeight: 600, px: 2 }}>
              Hapus Invoice
            </Typography>
            <Divider />
            <Stack sx={{ gap: 2, px: 2 }}>
              {!canBeDeleted && (
                <Alert severity="warning">
                  Hanya dapat menghapus Invoice dengan status Pending
                </Alert>
              )}
              <Stack
                sx={{ flexDirection: "row", justifyContent: "space-between" }}
              >
                <Typography sx={{ fontWeight: 600 }}>Nama</Typography>
                <Typography>{invoice?.user?.name}</Typography>
              </Stack>
              <Stack
                sx={{ flexDirection: "row", justifyContent: "space-between" }}
              >
                <Typography sx={{ fontWeight: 600 }}>Nama Tagihan</Typography>
                <Typography>{invoice?.bill?.name}</Typography>
              </Stack>
              <Stack
                sx={{ flexDirection: "row", justifyContent: "space-between" }}
              >
                <Typography sx={{ fontWeight: 600 }}>Total Harga</Typography>
                <Typography>{formatToRupiah(invoice?.bill?.amount)}</Typography>
              </Stack>
              <Stack
                sx={{ flexDirection: "row", justifyContent: "space-between" }}
              >
                <Typography sx={{ fontWeight: 600 }}>Nilai Invoice</Typography>
                <Typography>{formatToRupiah(invoice?.amount)}</Typography>
              </Stack>
              <Stack
                sx={{ flexDirection: "row", justifyContent: "space-between" }}
              >
                <Typography sx={{ fontWeight: 600 }}>Status</Typography>
                <Typography>
                  {invoice &&
                    getInvoiceStatusText(invoice?.status ?? "pending")}
                </Typography>
              </Stack>
            </Stack>
            <Divider />
            <Stack sx={{ gap: 2, px: 2, flexDirection: "row" }}>
              <Button variant="outlined" fullWidth onClick={toggle}>
                Batal
              </Button>
              <Button
                type="submit"
                variant="contained"
                fullWidth
                color="error"
                disabled={!canBeDeleted}
              >
                Hapus
              </Button>
            </Stack>
          </Paper>
        </Stack>
      </Modal>
    </>
  );
}
