import { DeleteForever } from "@mui/icons-material";
import {
  <PERSON>ton,
  Divider,
  <PERSON>dal,
  Paper,
  Stack,
  Typography,
} from "@mui/material";
import {
  useBillWithInvoices,
  useDeleteBill,
} from "@sisva/hooks/query/finance/useBills";
import { useToggle } from "ahooks";
import currency from "currency.js";
import type { ReactNode } from "react";
import toast from "react-hot-toast";

export default function DeleteBillButton({
  bill_id,
  renderTrigger,
}: {
  bill_id: number;
  renderTrigger?: (onClick: () => void) => ReactNode;
}) {
  const { data: bill } = useBillWithInvoices(bill_id);

  const [visible, { toggle }] = useToggle(false);

  const { mutate: deleteBill } = useDeleteBill({
    onSuccess: () => {
      toggle();
      toast.success("Tagihan berhasil dihapus");
    },
    onError: () => {
      toast.error("Tagihan gagal dihapus");
    },
  });

  return (
    <>
      {renderTrigger ? (
        renderTrigger(toggle)
      ) : (
        <Button
          variant="contained"
          startIcon={<DeleteForever />}
          color="error"
          onClick={toggle}
        >
          Hapus
        </Button>
      )}
      <Modal open={visible} onClose={toggle}>
        <Stack
          sx={{
            width: 1,
            height: 1,
            justifyContent: "center",
            alignItems: "center",
            p: 2,
          }}
        >
          <Paper
            component="form"
            sx={{
              py: 2,
              width: 1,
              maxWidth: "ms",
              gap: 2,
              display: "flex",
              flexDirection: "column",
            }}
            onSubmit={(e) => {
              e.preventDefault();
              deleteBill(bill_id);
            }}
          >
            <Typography sx={{ fontWeight: 600, px: 2 }}>
              Hapus Tagihan
            </Typography>
            <Divider />
            <Stack sx={{ gap: 2, px: 2 }}>
              <Stack
                sx={{ flexDirection: "row", justifyContent: "space-between" }}
              >
                <Typography sx={{ fontWeight: 600 }}>Tagihan</Typography>
                <Typography>{bill?.name}</Typography>
              </Stack>
              <Stack
                sx={{ flexDirection: "row", justifyContent: "space-between" }}
              >
                <Typography sx={{ fontWeight: 600 }}>Total Harga</Typography>
                <Typography>
                  {currency(bill?.amount ?? 0, {
                    symbol: "Rp",
                    separator: ".",
                    precision: 0,
                  }).format()}
                </Typography>
              </Stack>
            </Stack>
            <Divider />
            <Stack sx={{ gap: 2, px: 2, flexDirection: "row" }}>
              <Button variant="outlined" fullWidth onClick={toggle}>
                Batal
              </Button>
              <Button type="submit" variant="contained" fullWidth color="error">
                Hapus
              </Button>
            </Stack>
          </Paper>
        </Stack>
      </Modal>
    </>
  );
}
