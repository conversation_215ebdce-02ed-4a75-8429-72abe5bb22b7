import { Clear, Search, Sort } from "@mui/icons-material";
import {
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  IconButton,
  InputAdornment,
  Stack,
  Typography,
  useMediaQuery,
} from "@mui/material";
import type { GridColDef, GridFilterOperator } from "@mui/x-data-grid";
import { DataGrid, useGridApiContext } from "@mui/x-data-grid";
import type { BillWithInvoices } from "@sisva/hooks/query/finance/useBills";
import { useBillsWithInvoices } from "@sisva/hooks/query/finance/useBills";
import { formatToRupiah } from "@sisva/utils";
import { onlyUnique } from "@sisva/utils";
import { useToggle } from "ahooks";
import dayjs from "dayjs";
import { useEffect } from "react";
import { useForm } from "react-hook-form";
import { SelectElement, TextFieldElement } from "react-hook-form-mui";
import { DatePickerElement } from "react-hook-form-mui/date-pickers";

import DeleteIcon from "#/routes/(main)/-components/DeleteIcon";
import EditIcon from "#/routes/(main)/-components/EditIcon";

import CreateBillButton from "../CreateBillButton";
import DeleteBillButton from "../DeleteBillButton";

type Field =
  | "mobile_column"
  | keyof BillWithInvoices
  | typeof filter_field
  | "invoice_count"
  | "action";
type TypeSafeColDef = GridColDef & { field: Field };

const filter_field = "filter_field";
const field = {
  filter1: "status",
  filter2: "start_date",
  filter3: "end_date",
} satisfies Record<string, Field | "start_date" | "end_date">;

type FilterValue = Record<(typeof field)[keyof typeof field], string>;

export default function TagihanTab() {
  const isMobile = useMediaQuery((theme) => theme.breakpoints.down("lg"));
  const { data: bills = [] } = useBillsWithInvoices();

  const customOperator: GridFilterOperator<BillWithInvoices> = {
    label: "Custom Operator",
    value: "customOperator",
    getApplyFilterFn: (filterItem, column) => {
      if (!filterItem.field || !filterItem.value || !filterItem.operator) {
        return null;
      }
      const filterValue = filterItem.value as FilterValue;
      const statusFilter = filterValue.status as "Pending" | "Lunas";

      return (value, row, column, apiRef) => {
        let pass = true;

        const paidCount = row.invoices.filter(
          (invoice) => invoice.status === "done"
        ).length;
        const status =
          paidCount !== 0 && paidCount === row.invoices.length
            ? "Lunas"
            : "Pending";
        if (statusFilter) {
          if (statusFilter !== status) pass = false;
        }
        if (filterValue.start_date) {
          if (
            dayjs(row.deadline, "DD/MM/YYYY h:mm A Z").isBefore(
              dayjs(filterValue.start_date)
            )
          )
            pass = false;
        }
        if (filterValue.end_date) {
          if (
            dayjs(row.deadline, "DD/MM/YYYY h:mm A Z").isAfter(
              dayjs(filterValue.end_date)
            )
          )
            pass = false;
        }
        return pass;
      };
    },
  };

  const columns: TypeSafeColDef[] = [
    {
      field: "mobile_column",
      headerName: "",
      valueGetter: (_, row) => row,
      flex: 1,
      display: "flex",
      renderCell: (args) => {
        const value = args.value as BillWithInvoices;
        return (
          <Stack sx={{ p: 1.5, gap: 1, width: "100%" }}>
            <Stack
              sx={{
                flexDirection: "row",
                justifyContent: "space-between",
                width: "100%",
              }}
            >
              <Typography sx={{ fontWeight: 600 }}>No Tagihan</Typography>
              <Typography>{value.custom_id}</Typography>
            </Stack>
            <Stack
              sx={{
                flexDirection: "row",
                justifyContent: "space-between",
                width: "100%",
              }}
            >
              <Typography sx={{ fontWeight: 600 }}>Nama Tagihan</Typography>
              <Typography>{value.name}</Typography>
            </Stack>
            <Stack
              sx={{
                flexDirection: "row",
                justifyContent: "space-between",
                width: "100%",
              }}
            >
              <Typography sx={{ fontWeight: 600 }}>Total Harga</Typography>
              <Typography>{formatToRupiah(value.amount)}</Typography>
            </Stack>
            <Stack
              sx={{
                flexDirection: "row",
                justifyContent: "space-between",
                width: "100%",
              }}
            >
              <Typography sx={{ fontWeight: 600 }}>
                Jumlah Pembayaran
              </Typography>
              <Typography>
                {(() => {
                  const paidCount = value.invoices.filter(
                    (invoice) => invoice.status === "done"
                  ).length;
                  return `${paidCount}/${value.invoices.length}`;
                })()}
              </Typography>
            </Stack>
            <Stack
              sx={{
                flexDirection: "row",
                justifyContent: "space-between",
                width: "100%",
              }}
            >
              <Typography sx={{ fontWeight: 600 }}>Tenggat Waktu</Typography>
              <Typography>
                {dayjs(value.deadline, "DD/MM/YYYY h:mm A Z").format(
                  "D MMM YYYY"
                )}
              </Typography>
            </Stack>
            <Stack
              sx={{
                flexDirection: "row",
                justifyContent: "flex-end",
                gap: 1,
                width: "100%",
              }}
            >
              <CreateBillButton
                bill_id={value.id}
                renderTrigger={(onClick) => <EditIcon onClick={onClick} />}
              />
              <DeleteBillButton
                bill_id={value.id}
                renderTrigger={(onClick) => <DeleteIcon onClick={onClick} />}
              />
            </Stack>
          </Stack>
        );
      },
    },
    {
      field: "custom_id",
      headerName: "No Tagihan",
      display: "flex",
      renderCell: ({ value }) => <Stack sx={{ py: 1.3 }}>{value}</Stack>,
      flex: 1,
    },
    {
      field: "name",
      headerName: "Nama Tagihan",
      display: "flex",
      flex: 1,
      renderCell: ({ value }) => <Stack sx={{ py: 1.3 }}>{value}</Stack>,
    },
    {
      field: "amount",
      headerName: "Total Harga",
      display: "flex",
      flex: 1,
      renderCell: ({ value }) => (
        <Stack
          sx={{ py: 1.3, flexDirection: "row", alignItems: "center", gap: 2 }}
        >
          {formatToRupiah(value)}
        </Stack>
      ),
    },
    {
      field: "invoice_count",
      headerName: "Jumlah Pembayaran",
      valueGetter: (_, row: BillWithInvoices) => row.invoices.length,
      display: "flex",
      flex: 1,
      renderCell: ({ row }: { row: BillWithInvoices }) => {
        const paidCount = row.invoices.filter(
          (invoice) => invoice.status === "done"
        ).length;

        return (
          <Stack
            sx={{ py: 1.3, flexDirection: "row", alignItems: "center", gap: 2 }}
          >
            {`${paidCount}/${row.invoices.length}`}
          </Stack>
        );
      },
    },
    {
      field: "deadline",
      headerName: "Tenggat Waktu",
      valueGetter: (_, row) =>
        dayjs(row.deadline, "DD/MM/YYYY h:mm A Z").toDate(),
      display: "flex",
      flex: 1,
      renderCell: ({ value }) => (
        <Stack
          sx={{ py: 1.3, flexDirection: "row", alignItems: "center", gap: 2 }}
        >
          {dayjs(value).format("D MMM YYYY")}
        </Stack>
      ),
    },
    {
      field: "action",
      headerName: "Aksi",
      display: "flex",
      flex: 1,
      sortable: false,
      renderCell: ({ row }) => (
        <Stack
          sx={{ py: 1.3, flexDirection: "row", alignItems: "center", gap: 1 }}
        >
          <CreateBillButton
            bill_id={row.id}
            renderTrigger={(onClick) => <EditIcon onClick={onClick} />}
          />
          <DeleteBillButton
            bill_id={row.id}
            renderTrigger={(onClick) => <DeleteIcon onClick={onClick} />}
          />
        </Stack>
      ),
    },
    {
      field: filter_field,
      filterOperators: [customOperator],
    },
  ];

  const columnVisibilityModel = Object.fromEntries(
    columns
      .map((column) => column.field)
      .filter((field) =>
        isMobile ? field !== "mobile_column" : field === "mobile_column"
      )
      .map((field) => [field, false])
  );
  columnVisibilityModel[filter_field] = false;

  return (
    <DataGrid
      sx={{
        "& .MuiDataGrid-cell:focus": {
          outline: " none",
        },
        border: "none",
      }}
      getRowId={(row: BillWithInvoices) => `${row.id}`}
      disableRowSelectionOnClick
      className="bg-white grow"
      rows={bills}
      columns={columns}
      initialState={{ pagination: { paginationModel: { pageSize: 20 } } }}
      pageSizeOptions={[10, 20, 50]}
      getRowClassName={(params) =>
        params.indexRelativeToCurrentPage % 2 === 0
          ? "bg-neutral-50"
          : "bg-neutral-100"
      }
      slots={{
        toolbar: CustomToolbar,
      }}
      getRowHeight={() => "auto"}
      columnVisibilityModel={columnVisibilityModel}
      columnHeaderHeight={isMobile ? 0 : undefined}
      disableColumnMenu
      disableColumnFilter
    />
  );
}

function CustomToolbar() {
  const api = useGridApiContext();
  const [open, { toggle }] = useToggle(false);

  const { control, handleSubmit, reset, setValue, watch } = useForm<{
    field: Field | "";
    sortDirection: "asc" | "desc";
    quickFilter: string;
    [field.filter1]: string;
    [field.filter2]: string;
    [field.filter3]: string;
  }>({
    defaultValues: {
      field: "",
      sortDirection: "asc",
      quickFilter: "",
      [field.filter1]: "",
      [field.filter2]: "",
      [field.filter3]: "",
    },
  });

  const rows = Array.from(api.current.getRowModels()).map(
    ([key, value]) => value
  ) as BillWithInvoices[];
  // we are not using this status attribute, we evalue status based on invoices count
  const statuses = rows
    .map((row) => row.status)
    .filter((item) => item)
    .filter(onlyUnique);

  // apply filter
  const filter1 = watch(field.filter1);
  const filter2 = watch(field.filter2);
  const filter3 = watch(field.filter3);
  useEffect(() => {
    const value: FilterValue = {
      [field.filter1]: filter1,
      [field.filter2]: filter2,
      [field.filter3]: filter3,
    };
    api.current.upsertFilterItem({
      field: filter_field,
      operator: "customOperator",
      value,
    });
  }, [api, filter1, filter2, filter3]);

  const sortSelectOptions: { label: string; id: Field }[] = [
    { label: "No Tagihan", id: "custom_id" },
    { label: "Nama Tagihan", id: "name" },
    { label: "Total Harga", id: "amount" },
    { label: "Jumlah Pembayaran", id: "invoice_count" },
    { label: "Tenggat Waktu", id: "deadline" },
    { label: "Status", id: "status" },
  ];

  const quickFilter = watch("quickFilter");
  useEffect(() => {
    api.current.setQuickFilterValues(
      quickFilter.split(" ").filter((s) => s !== "")
    );
  }, [api, quickFilter]);

  return (
    <div className="p-4 flex flex-col gap-2 border-b border-0 border-solid border-neutral-200">
      <div className=" flex justify-between gap-4 thick-scrollbar items-center">
        <Stack
          sx={{
            overflow: {
              xs: "auto",
              xl: "initial",
            },
            py: {
              xs: 1,
              xl: 0,
            },
          }}
        >
          <Stack sx={{ flexDirection: "row", gap: 2 }}>
            <TextFieldElement
              control={control}
              name="quickFilter"
              placeholder="Cari"
              size="small"
              sx={{
                minWidth: "150px",
              }}
              slotProps={{
                input: {
                  endAdornment: (
                    <InputAdornment position="end">
                      {quickFilter ? (
                        <IconButton
                          size="small"
                          onClick={() => setValue("quickFilter", "")}
                        >
                          <Clear />
                        </IconButton>
                      ) : (
                        <Search />
                      )}
                    </InputAdornment>
                  ),
                },
              }}
            />
            <SelectElement
              size="small"
              sx={{ minWidth: "150px" }}
              control={control}
              name={field.filter1}
              options={[
                {
                  id: "Pending",
                  label: "Pending",
                },
                {
                  id: "Lunas",
                  label: "Lunas",
                },
              ]}
              label="Status"
              slotProps={{
                input: {
                  endAdornment: watch(field.filter1) && (
                    <InputAdornment position="end">
                      <IconButton
                        onClick={() => setValue(field.filter1, "")}
                        size="small"
                      >
                        <Clear />
                      </IconButton>
                    </InputAdornment>
                  ),
                },
              }}
            />
            <DatePickerElement
              transform={{
                input(dateString) {
                  if (!dateString) return null;
                  return dayjs(dateString);
                },
                output(date) {
                  return date?.format() ?? "";
                },
              }}
              label="Tanggal Awal"
              control={control}
              name={field.filter2}
              format="DD MMMM YYYY"
              inputProps={{
                size: "small",
                sx: {
                  minWidth: 180,
                },
              }}
              slotProps={{
                actionBar: {
                  actions: ["clear"],
                },
              }}
            />
            <DatePickerElement
              transform={{
                input(dateString) {
                  if (!dateString) return null;
                  return dayjs(dateString);
                },
                output(date) {
                  return date?.format() ?? "";
                },
              }}
              label="Tanggal Akhir"
              control={control}
              name={field.filter3}
              format="DD MMMM YYYY"
              inputProps={{
                size: "small",
                sx: {
                  minWidth: 180,
                },
              }}
              slotProps={{
                actionBar: {
                  actions: ["clear"],
                },
              }}
            />
          </Stack>
        </Stack>
        <Stack sx={{ minWidth: 110 }}>
          <CreateBillButton />
        </Stack>
      </div>
      <Button
        startIcon={<Sort />}
        variant="outlined"
        onClick={toggle}
        sx={{
          display: { xs: "flex", lg: "none" },
        }}
      >
        Urutkan Data
      </Button>
      <Dialog open={open} onClose={toggle}>
        <DialogTitle>Urutkan</DialogTitle>
        <DialogContent
          sx={{
            width: "250px",
            display: "flex",
            flexDirection: "column",
            gap: 2,
            overflow: "initial",
          }}
        >
          <SelectElement
            control={control}
            name="field"
            options={sortSelectOptions}
            label="Pilih Kolom"
            slotProps={{
              input: {
                endAdornment: watch("field") && (
                  <InputAdornment position="end">
                    <IconButton onClick={() => setValue("field", "")}>
                      <Clear />
                    </IconButton>
                  </InputAdornment>
                ),
              },
            }}
          />
          <SelectElement
            control={control}
            name="sortDirection"
            options={[
              { label: "A-Z", id: "asc" },
              { label: "Z-A", id: "desc" },
            ]}
            label="Urutan"
          />
        </DialogContent>
        <DialogActions sx={{ pb: 2, px: 3 }}>
          <Button
            variant="outlined"
            onClick={() => {
              reset();
              toggle();
            }}
          >
            Batal
          </Button>
          <Button
            variant="contained"
            onClick={handleSubmit((value) => {
              if (value.field) {
                api.current.sortColumn(value.field, value.sortDirection);
              } else {
                api.current.sortColumn("name", null);
              }
              toggle();
            })}
          >
            Simpan
          </Button>
        </DialogActions>
      </Dialog>
    </div>
  );
}
