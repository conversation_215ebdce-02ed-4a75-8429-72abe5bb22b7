import { DeleteForever } from "@mui/icons-material";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Divide<PERSON>,
  <PERSON>dal,
  Paper,
  Stack,
  Typography,
} from "@mui/material";
import {
  useDeleteUserBill,
  useUserBillWithUserAndBill,
} from "@sisva/hooks/query/finance/useUserBills";
import { useToggle } from "ahooks";
import currency from "currency.js";
import type { ReactNode } from "react";
import toast from "react-hot-toast";

export default function DeleteUserBillButton({
  user_bill_id,
  renderTrigger,
}: {
  user_bill_id: number;
  renderTrigger?: (onClick: () => void) => ReactNode;
}) {
  const { data: userBill } = useUserBillWithUserAndBill(user_bill_id);

  const canBeDeleted = !userBill?.bill?.invoices?.filter(
    (invoice) => invoice.user_bill_id === user_bill_id
  ).length;

  const [visible, { toggle }] = useToggle(false);

  const { mutate: deleteUserBill } = useDeleteUserBill({
    onSuccess: () => {
      toggle();
      toast.success("Tagihan Pengguna berhasil dihapus");
    },
    onError: () => {
      toast.error("Tagihan Pengguna gagal dihapus");
    },
  });

  return (
    <>
      {renderTrigger ? (
        renderTrigger(toggle)
      ) : (
        <Button
          variant="contained"
          startIcon={<DeleteForever />}
          color="error"
          onClick={toggle}
        >
          Hapus
        </Button>
      )}
      <Modal open={visible} onClose={toggle}>
        <Stack
          sx={{
            width: 1,
            height: 1,
            justifyContent: "center",
            alignItems: "center",
            p: 2,
          }}
        >
          <Paper
            component="form"
            sx={{
              py: 2,
              width: 1,
              maxWidth: "ms",
              gap: 2,
              display: "flex",
              flexDirection: "column",
            }}
            onSubmit={(e) => {
              e.preventDefault();
              deleteUserBill({ id: user_bill_id });
            }}
          >
            <Typography sx={{ fontWeight: 600, px: 2 }}>
              Hapus Tagihan Pengguna
            </Typography>
            <Divider />
            <Stack sx={{ gap: 2, px: 2 }}>
              {!canBeDeleted && (
                <Alert severity="warning">
                  Tagihan Pengguna ini tidak dapat dihapus karena tagihan ini
                  telah tertagih
                </Alert>
              )}
              <Stack
                sx={{ flexDirection: "row", justifyContent: "space-between" }}
              >
                <Typography sx={{ fontWeight: 600 }}>Nama Tagihan</Typography>
                <Typography>{userBill?.bill?.name}</Typography>
              </Stack>
              <Stack
                sx={{ flexDirection: "row", justifyContent: "space-between" }}
              >
                <Typography sx={{ fontWeight: 600 }}>Nama Pengguna</Typography>
                <Typography>{userBill?.user?.name}</Typography>
              </Stack>
              <Stack
                sx={{ flexDirection: "row", justifyContent: "space-between" }}
              >
                <Typography sx={{ fontWeight: 600 }}>Total Harga</Typography>
                <Typography>
                  {currency(userBill?.bill?.amount ?? 0, {
                    symbol: "Rp",
                    separator: ".",
                    precision: 0,
                  }).format()}
                </Typography>
              </Stack>
              <Stack
                sx={{ flexDirection: "row", justifyContent: "space-between" }}
              >
                <Typography sx={{ fontWeight: 600 }}>
                  Jumlah Tertagih
                </Typography>
                <Typography>
                  {currency(
                    userBill?.bill?.invoices
                      .filter(
                        (invoice) => invoice.user_bill_id === user_bill_id
                      )
                      .reduce((acc, cur) => acc + cur.amount, 0) ?? 0,
                    {
                      symbol: "Rp",
                      separator: ".",
                      precision: 0,
                    }
                  ).format()}
                </Typography>
              </Stack>
            </Stack>
            <Divider />
            <Stack sx={{ gap: 2, px: 2, flexDirection: "row" }}>
              <Button variant="outlined" fullWidth onClick={toggle}>
                Batal
              </Button>
              <Button
                type="submit"
                variant="contained"
                fullWidth
                color="error"
                disabled={!canBeDeleted}
              >
                Hapus
              </Button>
            </Stack>
          </Paper>
        </Stack>
      </Modal>
    </>
  );
}
