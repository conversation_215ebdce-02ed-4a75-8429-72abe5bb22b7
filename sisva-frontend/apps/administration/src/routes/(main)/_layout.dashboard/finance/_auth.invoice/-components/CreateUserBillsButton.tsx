import { yup<PERSON>esolver } from "@hookform/resolvers/yup";
import { Add, Clear, Search, Sort } from "@mui/icons-material";
import {
  Button,
  Checkbox,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Divider,
  IconButton,
  InputAdornment,
  Modal,
  Paper,
  Stack,
  Typography,
} from "@mui/material";
import type { GridColDef, GridFilterOperator } from "@mui/x-data-grid";
import { DataGrid, useGridApiContext, useGridApiRef } from "@mui/x-data-grid";
import { useBillsWithInvoices } from "@sisva/hooks/query/finance/useBills";
import { useCreateUserBills } from "@sisva/hooks/query/finance/useUserBills";
import type { UserWithBillsAndStudentGroupAndStudyProgram } from "@sisva/hooks/query/user/useUsers";
import { useUsersWithBillsAndStudentGroupAndStudyProgram } from "@sisva/hooks/query/user/useUsers";
import type { UserTypeText } from "@sisva/types/types";
import { getUserTypeText } from "@sisva/types/types";
import { onlyUnique } from "@sisva/utils";
import { useToggle } from "ahooks";
import { deepEqual } from "fast-equals";
import { sort } from "fast-sort";
import { type ReactNode, useCallback, useEffect, useState } from "react";
import {
  AutocompleteElement,
  SelectElement,
  TextFieldElement,
  useForm,
} from "react-hook-form-mui";
import toast from "react-hot-toast";
import { array, boolean, object, string } from "yup";

type Field =
  | keyof UserWithBillsAndStudentGroupAndStudyProgram
  | typeof filter_field
  | "create_invoice_checkbox"
  | "period"
  | "study_program"
  | "grade"
  | "student_group";
type TypeSafeColDef = GridColDef & { field: Field };

const filter_field = "filter_field";
const field = {
  filter1: "type",
  filter2: "period",
  filter3: "study_program",
  filter4: "grade",
  filter5: "student_group",
} satisfies Record<string, Field>;

type FilterValue = Record<(typeof field)[keyof typeof field], string>;

export default function CreateUserBillsButton({
  renderTrigger,
}: {
  renderTrigger?: (onClick: () => void) => ReactNode;
}) {
  const apiRef = useGridApiRef();
  const [visible, { toggle }] = useToggle(false);
  const [userWithCreateInvoiceIds, setUserWithCreateInvoiceIds] = useState<
    string[]
  >([]);

  const { data: bills = [] } = useBillsWithInvoices();
  const { control: billControl, watch: billWatch } = useForm({
    values: {
      bill_id: 0,
    },
  });

  const { control, handleSubmit, reset, watch, setValue } = useForm({
    resolver: yupResolver(
      object({
        user_options: array()
          .of(
            object({
              user_id: string().required(),
              create_invoice: boolean().required(),
            })
          )
          .required(),
      })
    ),
  });

  const bill_id = billWatch("bill_id");
  const bill = bills.find((bill) => bill.id === bill_id);

  const { mutate: createUserBills } = useCreateUserBills({
    onSuccess: ({ failCount, userBillCreatedCount, invoiceCreatedCount }) => {
      if (userBillCreatedCount)
        toast.success(
          `${userBillCreatedCount} Tagihan pengguna berhasil dibuat`
        );
      if (invoiceCreatedCount)
        toast.success(`${invoiceCreatedCount} Invoice berhasil dibuat`);
      if (failCount) {
        toast.error(`${failCount} Tagihan pengguna atau Invoice gagal dibuat`);
        return;
      }
      toggle();
      reset();
    },
    onError: () => {
      toast.error("Tagihan pengguna gagal dibuat");
    },
    bill_id: bill_id,
  });

  const { data: users = [] } =
    useUsersWithBillsAndStudentGroupAndStudyProgram();

  const includeStudent = bill?.target_user_types.includes("student") ?? false;
  const includeStaff = bill?.target_user_types.includes("staff") ?? false;

  const filteredUsers = users
    // filter based of bill target
    .filter((user) => {
      let include = false;
      if (includeStudent && user.type === "student") include = true;
      if (
        includeStaff &&
        ["staff", "teacher", "management"].includes(user.type)
      )
        include = true;
      return include;
    })
    // do not include user that already have this bill
    .filter(
      (user) =>
        !user.bills
          ?.map((bill) => bill?.id)
          .filter((item) => item)
          .includes(bill?.id ?? 0)
    );

  const customOperator: GridFilterOperator<UserWithBillsAndStudentGroupAndStudyProgram> =
    {
      label: "Custom Operator",
      value: "customOperator",
      getApplyFilterFn: (filterItem, column) => {
        if (!filterItem.field || !filterItem.value || !filterItem.operator) {
          return null;
        }
        const filterValue = filterItem.value as FilterValue;
        const typeFilter = filterValue.type as UserTypeText;

        return (value, row, column, apiRef) => {
          let pass = true;
          if (typeFilter === "Siswa") {
            if (row.type !== "student") pass = false;
          }
          if (typeFilter === "Staf") {
            if (!["staff", "teacher", "management"].includes(row.type))
              pass = false;
          }
          if (filterValue.period) {
            if (row.student_group?.period_name !== filterValue.period)
              pass = false;
          }
          if (filterValue.study_program) {
            if (row.study_program?.name !== filterValue.study_program)
              pass = false;
          }
          if (filterValue.grade) {
            if (row.detail.grade !== filterValue.grade) pass = false;
          }
          if (filterValue.student_group) {
            if (row.student_group?.name !== filterValue.student_group)
              pass = false;
          }
          return pass;
        };
      },
    };

  const columns: TypeSafeColDef[] = [
    {
      field: "name",
      headerName: "Nama",
      display: "flex",
      flex: 1,
      minWidth: 430,
      renderCell: ({ value }) => <Stack sx={{ py: 1.3 }}>{value}</Stack>,
    },
    {
      field: "type",
      headerName: "Tipe",
      display: "flex",
      valueGetter: (_, row: UserWithBillsAndStudentGroupAndStudyProgram) =>
        getUserTypeText(row.type),
      renderCell: ({ value }) => <Stack sx={{ py: 1.3 }}>{value}</Stack>,
    },
    {
      field: "create_invoice_checkbox",
      headerName: "Buat Invoice",
      display: "flex",
      sortable: false,
      width: 150,
      renderCell: ({
        row,
      }: {
        row: UserWithBillsAndStudentGroupAndStudyProgram;
      }) => (
        <Stack>
          <Checkbox
            checked={userWithCreateInvoiceIds.includes(row.id)}
            onChange={(_, checked) => {
              if (checked) {
                setUserWithCreateInvoiceIds((old) => [...old, row.id]);
                apiRef.current.selectRow(row.id);
              } else {
                setUserWithCreateInvoiceIds((old) =>
                  old.filter((id) => id !== row.id)
                );
                updateUserOptionsValue();
              }
            }}
          />
        </Stack>
      ),
      renderHeader: () => (
        <Stack sx={{ flexDirection: "row", alignItems: "center" }}>
          <Checkbox
            onChange={(_, checked) => {
              if (checked) {
                setUserWithCreateInvoiceIds(
                  users
                    .map((user) => user.id)
                    .filter((id) => apiRef.current.getSelectedRows().get(id))
                );
              } else {
                setUserWithCreateInvoiceIds([]);
              }
              updateUserOptionsValue();
            }}
          />
          <Typography sx={{ fontWeight: 500, fontSize: 13 }}>
            Buat Invoice
          </Typography>
        </Stack>
      ),
    },
    {
      field: "period",
      headerName: "Periode",
      display: "flex",
      valueGetter: (_, row: UserWithBillsAndStudentGroupAndStudyProgram) =>
        row.student_group?.period_name,
      renderCell: ({ value }) => <Stack sx={{ py: 1.3 }}>{value}</Stack>,
    },
    {
      field: "study_program",
      headerName: "Program Studi",
      minWidth: 200,
      display: "flex",
      valueGetter: (_, row: UserWithBillsAndStudentGroupAndStudyProgram) =>
        row.study_program?.name,
      renderCell: ({ value }) => <Stack sx={{ py: 1.3 }}>{value}</Stack>,
    },
    {
      field: "grade",
      headerName: "Tingkatan",
      display: "flex",
      valueGetter: (_, row: UserWithBillsAndStudentGroupAndStudyProgram) =>
        row.detail.grade,
      renderCell: ({ value }) => <Stack sx={{ py: 1.3 }}>{value}</Stack>,
    },
    {
      field: "student_group",
      headerName: "Kelas",
      display: "flex",
      valueGetter: (_, row: UserWithBillsAndStudentGroupAndStudyProgram) =>
        row.student_group?.name,
      renderCell: ({ value }) => <Stack sx={{ py: 1.3 }}>{value}</Stack>,
    },
    {
      field: filter_field,
      filterOperators: [customOperator],
    },
  ];

  const columnVisibilityModel: Partial<Record<Field, boolean>> = {
    [filter_field]: false,
  };

  const updateUserOptionsValue = useCallback(() => {
    const ids = apiRef.current.getSelectedRows
      ? (Array.from(apiRef.current.getSelectedRows().keys()) as string[])
      : [];
    setValue(
      "user_options",
      ids.map((id) => {
        return {
          user_id: id,
          create_invoice: userWithCreateInvoiceIds.includes(id),
        };
      })
    );
    const filteredUserWithCreateInvoiceIds = userWithCreateInvoiceIds.filter(
      (id) => ids.includes(id)
    );
    if (!deepEqual(userWithCreateInvoiceIds, filteredUserWithCreateInvoiceIds))
      setUserWithCreateInvoiceIds(filteredUserWithCreateInvoiceIds);
  }, [apiRef, setValue, userWithCreateInvoiceIds]);

  useEffect(() => {
    updateUserOptionsValue();
  }, [apiRef, setValue, updateUserOptionsValue]);

  return (
    <>
      {renderTrigger ? (
        renderTrigger(toggle)
      ) : (
        <Button variant="contained" startIcon={<Add />} onClick={toggle}>
          Tambah
        </Button>
      )}
      <Modal open={visible} onClose={toggle}>
        <Stack
          sx={{
            width: 1,
            height: 1,
            justifyContent: "center",
            alignItems: "center",
            p: 2,
          }}
        >
          <Paper
            component={"form"}
            sx={{
              py: 2,
              width: 1,
              maxWidth: "ml",
              gap: 2,
              display: "flex",
              flexDirection: "column",
            }}
            onSubmit={handleSubmit((value) => {
              createUserBills(value);
            })}
          >
            <Typography sx={{ fontWeight: 600, px: 2 }}>
              Buat Tagihan Pengguna
            </Typography>
            <Divider />
            <Stack sx={{ gap: 2, px: 2 }}>
              <AutocompleteElement
                control={billControl}
                name="bill_id"
                matchId
                options={sort(bills)
                  .desc((b) => b.id)
                  .map((item) => ({
                    id: item.id,
                    label: `${item.name}`,
                  }))}
                label="Tagihan"
                autocompleteProps={{
                  size: "small",
                }}
              />

              <Stack
                sx={{
                  height: "50svh",
                }}
              >
                <Typography
                  sx={{
                    fontWeight: 600,
                  }}
                >
                  Daftar Pengguna
                </Typography>
                <DataGrid
                  sx={{
                    "& .MuiDataGrid-cell:focus": {
                      outline: " none",
                    },
                    borderTop: "none",
                    borderRight: "none",
                    borderLeft: "none",
                  }}
                  getRowId={(
                    row: UserWithBillsAndStudentGroupAndStudyProgram
                  ) => `${row.id}`}
                  disableRowSelectionOnClick
                  className="bg-white grow thick-scrollbar"
                  rows={filteredUsers}
                  columns={columns}
                  initialState={{
                    pagination: { paginationModel: { pageSize: 20 } },
                  }}
                  pageSizeOptions={[10, 20, 50]}
                  getRowClassName={(params) =>
                    params.indexRelativeToCurrentPage % 2 === 0
                      ? "bg-neutral-50"
                      : "bg-neutral-100"
                  }
                  slots={{
                    toolbar: CustomToolbar,
                  }}
                  getRowHeight={() => "auto"}
                  disableColumnMenu
                  disableColumnFilter
                  columnVisibilityModel={columnVisibilityModel}
                  checkboxSelection
                  apiRef={apiRef}
                  onRowSelectionModelChange={updateUserOptionsValue}
                  localeText={{
                    noRowsLabel: "Pilih Tagihan terlebih dahulu",
                    footerRowSelected: (count) =>
                      `${count.toLocaleString()} baris terpilih`,
                    footerTotalVisibleRows: (visibleCount, totalCount) =>
                      `${visibleCount.toLocaleString()} dari ${totalCount.toLocaleString()}`,
                    MuiTablePagination: {
                      labelDisplayedRows: ({ from, to, count, page }) => {
                        return `${from}-${to} dari ${count}`;
                      },
                    },
                  }}
                />
              </Stack>
            </Stack>
            <Divider />
            <Stack sx={{ gap: 2, px: 2, flexDirection: "row" }}>
              <Button variant="outlined" fullWidth onClick={toggle}>
                Batal
              </Button>
              <Button type="submit" variant="contained" fullWidth>
                Simpan
              </Button>
            </Stack>
          </Paper>
        </Stack>
      </Modal>
    </>
  );
}

function CustomToolbar() {
  const api = useGridApiContext();
  const [open, { toggle }] = useToggle(false);

  const { control, handleSubmit, reset, setValue, watch } = useForm<{
    field: Field | "";
    sortDirection: "asc" | "desc";
    quickFilter: string;
    [field.filter1]: string;
    [field.filter2]: string;
    [field.filter3]: string;
    [field.filter4]: string;
    [field.filter5]: string;
  }>({
    defaultValues: {
      field: "",
      sortDirection: "asc",
      quickFilter: "",
      [field.filter1]: "",
      [field.filter2]: "",
      [field.filter3]: "",
      [field.filter4]: "",
      [field.filter5]: "",
    },
  });

  const rows = Array.from(api.current.getRowModels()).map(
    ([key, value]) => value
  ) as UserWithBillsAndStudentGroupAndStudyProgram[];
  const userTypes = rows
    .map((row) => getUserTypeText(row.type))
    .filter((item) => item)
    .filter((item) => item === "Siswa" || item === "Staf")
    .filter(onlyUnique);
  const periodNames = sort(rows)
    .desc((r) => r.student_group?.period_id)
    .map((row) => row.student_group?.period_name)
    .filter((item) => item)
    .filter(onlyUnique);
  const studyProgramNames = rows
    .map((row) => row.study_program?.name)
    .filter((item) => item)
    .filter(onlyUnique);
  const grades = rows
    .map((row) => row.detail.grade)
    .filter((item) => item)
    .filter(onlyUnique);
  const studentGroupNames = rows
    .map((row) => row.student_group?.name)
    .filter((item) => item)
    .filter(onlyUnique);

  // apply filter
  const filter1 = watch(field.filter1);
  const filter2 = watch(field.filter2);
  const filter3 = watch(field.filter3);
  const filter4 = watch(field.filter4);
  const filter5 = watch(field.filter5);
  useEffect(() => {
    const value: FilterValue = {
      [field.filter1]: filter1,
      [field.filter2]: filter2,
      [field.filter3]: filter3,
      [field.filter4]: filter4,
      [field.filter5]: filter5,
    };

    api.current.upsertFilterItem({
      field: filter_field,
      operator: "customOperator",
      value,
    });
  }, [api, filter1, filter2, filter3, filter4, filter5]);

  const sortSelectOptions: { label: string; id: Field }[] = [];

  const quickFilter = watch("quickFilter");
  useEffect(() => {
    api.current.setQuickFilterValues(
      quickFilter.split(" ").filter((s) => s !== "")
    );
  }, [api, quickFilter]);

  return (
    <div className="pb-2 flex flex-col gap-2 border-b border-0 border-solid border-neutral-200 thick-scrollbar">
      <div className=" flex justify-between overflow-auto py-2 pe-1">
        <Stack sx={{ flexDirection: "row", gap: 2 }}>
          <TextFieldElement
            control={control}
            name="quickFilter"
            placeholder="Cari"
            size="small"
            slotProps={{
              input: {
                endAdornment: (
                  <InputAdornment position="end">
                    {quickFilter ? (
                      <IconButton
                        size="small"
                        onClick={() => setValue("quickFilter", "")}
                      >
                        <Clear />
                      </IconButton>
                    ) : (
                      <Search />
                    )}
                  </InputAdornment>
                ),
              },
            }}
          />
          <SelectElement
            control={control}
            name={field.filter1}
            options={
              userTypes.length
                ? userTypes.map((item) => ({
                    id: item,
                    label: item,
                    disabled: false,
                  }))
                : [
                    {
                      id: "Tidak ada opsi",
                      label: "Tidak ada opsi",
                      disabled: true,
                    },
                  ]
            }
            label="Tipe"
            size="small"
            sx={{
              width: "180px",
            }}
            slotProps={{
              input: {
                endAdornment: (
                  <InputAdornment position="end">
                    {filter1 ? (
                      <IconButton
                        size="small"
                        onClick={() => setValue(field.filter1, "")}
                      >
                        <Clear />
                      </IconButton>
                    ) : null}
                  </InputAdornment>
                ),
              },
            }}
          />
          <AutocompleteElement
            control={control}
            name={field.filter2}
            matchId
            options={periodNames.map((item) => ({
              id: item,
              label: item,
            }))}
            label="Periode"
            autocompleteProps={{
              size: "small",
              sx: { width: "180px" },
            }}
          />
          <AutocompleteElement
            control={control}
            name={field.filter3}
            matchId
            options={studyProgramNames.map((item) => ({
              id: item,
              label: item,
            }))}
            label="Program Studi"
            autocompleteProps={{
              size: "small",
              sx: { width: "180px" },
            }}
          />
          <AutocompleteElement
            control={control}
            name={field.filter4}
            matchId
            options={grades.map((item) => ({
              id: item,
              label: item,
            }))}
            label="Tingkatan"
            autocompleteProps={{
              size: "small",
              sx: { width: "180px" },
            }}
          />
          <AutocompleteElement
            control={control}
            name={field.filter5}
            matchId
            options={studentGroupNames.map((item) => ({
              id: item,
              label: item,
            }))}
            label="Kelas"
            autocompleteProps={{
              size: "small",
              sx: { width: "180px" },
            }}
          />
        </Stack>
      </div>
      <Button
        startIcon={<Sort />}
        variant="outlined"
        onClick={toggle}
        sx={{
          //NOTE: hiding this just in case we need sort button in mobile
          display: { xs: "none", lg: "none" },
        }}
      >
        Urutkan Data
      </Button>
      <Dialog open={open} onClose={toggle}>
        <DialogTitle>Urutkan</DialogTitle>
        <DialogContent
          sx={{
            width: "250px",
            display: "flex",
            flexDirection: "column",
            gap: 2,
            overflow: "initial",
          }}
        >
          <SelectElement
            control={control}
            name="field"
            options={sortSelectOptions}
            label="Pilih Kolom"
            slotProps={{
              input: {
                endAdornment: watch("field") && (
                  <InputAdornment position="end">
                    <IconButton onClick={() => setValue("field", "")}>
                      <Clear />
                    </IconButton>
                  </InputAdornment>
                ),
              },
            }}
          />
          <SelectElement
            control={control}
            name="sortDirection"
            options={[
              { label: "A-Z", id: "asc" },
              { label: "Z-A", id: "desc" },
            ]}
            label="Urutan"
          />
        </DialogContent>
        <DialogActions sx={{ pb: 2, px: 3 }}>
          <Button
            variant="outlined"
            onClick={() => {
              reset();
              toggle();
            }}
          >
            Batal
          </Button>
          <Button
            variant="contained"
            onClick={handleSubmit((value) => {
              if (value.field) {
                api.current.sortColumn(value.field, value.sortDirection);
              } else {
                api.current.sortColumn("name", null);
              }
              toggle();
            })}
          >
            Simpan
          </Button>
        </DialogActions>
      </Dialog>
    </div>
  );
}
