import { yupResolver } from "@hookform/resolvers/yup";
import { Add } from "@mui/icons-material";
import {
  Button,
  Divider,
  Modal,
  Paper,
  Stack,
  Typography,
} from "@mui/material";
import { useUploadFile } from "@sisva/hooks/query/file/useFiles";
import { useBillsWithInvoices } from "@sisva/hooks/query/finance/useBills";
import {
  useCreateInvoice,
  useInvoiceWithBillAndUser,
  useUpdateInvoice,
} from "@sisva/hooks/query/finance/useInvoices";
import { useUserBillsWithUserAndBill } from "@sisva/hooks/query/finance/useUserBills";
import { useSchool } from "@sisva/providers";
import { invoiceStatusOptions } from "@sisva/types/dropdownOptions";
import { invoiceSchema } from "@sisva/types/formTypes";
import { formatToRupiah } from "@sisva/utils";
import { getFileUrl } from "@sisva/utils";
import { useToggle } from "ahooks";
import { Image } from "antd";
import currency from "currency.js";
import dayjs from "dayjs";
import { sort } from "fast-sort";
import { type ReactNode } from "react";
import {
  AutocompleteElement,
  SelectElement,
  TextareaAutosizeElement,
  TextFieldElement,
  useForm,
} from "react-hook-form-mui";
import toast from "react-hot-toast";

import fallback from "#/assets/images/fallback.png";

/**
 * this component has 2 modes:
 * 1. create new invoice, no props
 * 2. edit invoice, by passing invoice_id
 */
export default function CreateInvoiceButton({
  // if invoice_id provided, then it's edit mode
  invoice_id,
  renderTrigger,
}: {
  invoice_id?: number;
  renderTrigger?: (onClick: () => void) => ReactNode;
}) {
  const school = useSchool();
  const { data: invoice } = useInvoiceWithBillAndUser(invoice_id);
  const { mutateAsync: uploadFile } = useUploadFile();

  const { data: bills = [] } = useBillsWithInvoices();
  const { control: billControl, watch: billWatch } = useForm({
    values: {
      bill_id: invoice?.bill?.id ?? 0,
    },
  });
  const bill_id = billWatch("bill_id");
  const bill = bills.find((bill) => bill.id === bill_id);

  const { data: userBills = [] } = useUserBillsWithUserAndBill();
  const filteredUserBills = userBills.filter(
    (userBill) => userBill.bill_id === bill?.id
  );

  const [visible, { toggle }] = useToggle(false);
  const { control, handleSubmit, reset, watch, setValue } = useForm({
    values: {
      user_bill_id: invoice?.user_bill_id ?? 0,
      status: invoice?.status ?? "pending",
      amount: invoice?.amount ?? 0,
      note: invoice?.note,
      payment_proof_uri: invoice?.payment_proof?.uri,
      payment_proof_note: invoice?.payment_proof?.note,
    },
    resolver: yupResolver(invoiceSchema),
  });

  const { mutate: createInvoice } = useCreateInvoice({
    onSuccess: () => {
      toggle();
      reset();
      toast.success("Invoice berhasil dibuat");
    },
    onError: () => {
      toast.error("Invoice gagal dibuat");
    },
  });

  const { mutate: updateInvoice } = useUpdateInvoice({
    onSuccess: () => {
      toggle();
      reset();
      toast.success("Invoice berhasil diperbarui");
    },
    onError: () => {
      toast.error("Invoice gagal diperbarui");
    },
  });

  const url = getFileUrl(watch("payment_proof_uri"), school.id);

  const jumlahTertagih = bill?.invoices
    .filter((invoice) => invoice.user_bill_id === watch("user_bill_id"))
    .reduce((acc, cur) => acc + cur.amount, 0);

  const jumlahTerbayar = bill?.invoices
    .filter((invoice) => invoice.user_bill_id === watch("user_bill_id"))
    .filter((invoice) => invoice.status === "done")
    .reduce((acc, cur) => acc + cur.amount, 0);

  const sisaTagihan = currency(bill?.amount ?? 0).subtract(
    currency(jumlahTerbayar ?? 0)
  ).value;

  return (
    <>
      {renderTrigger ? (
        renderTrigger(toggle)
      ) : (
        <Button variant="contained" startIcon={<Add />} onClick={toggle}>
          Tambah
        </Button>
      )}
      <Modal open={visible} onClose={toggle}>
        <Stack
          sx={{
            width: 1,
            height: 1,
            justifyContent: "center",
            alignItems: "center",
            p: 2,
          }}
        >
          <Paper
            component={"form"}
            sx={{
              py: 2,
              width: 1,
              maxWidth: "md",
              gap: 2,
              display: "flex",
              flexDirection: "column",
              overflow: "auto",
            }}
            onSubmit={handleSubmit((value) => {
              if (invoice_id) {
                updateInvoice({
                  invoice_id: invoice_id,
                  ...value,
                });
              } else {
                createInvoice(value);
              }
            })}
          >
            <Typography sx={{ fontWeight: 600, px: 2 }}>
              {(() => {
                if (invoice_id) {
                  return "Edit Invoice";
                } else {
                  return "Buat Invoice";
                }
              })()}
            </Typography>
            <Divider />
            <Stack sx={{ gap: 2, px: 2 }}>
              <AutocompleteElement
                control={billControl}
                name="bill_id"
                matchId
                options={sort(bills)
                  .desc((b) => b.id)
                  .map((item) => ({
                    id: item.id,
                    label: `${item.name}`,
                  }))}
                label="Pilih Tagihan"
                autocompleteProps={{
                  readOnly: !!invoice_id,
                }}
              />
              <AutocompleteElement
                control={control}
                name="user_bill_id"
                matchId
                options={sort(filteredUserBills)
                  .desc((b) => b.id)
                  .map((item) => ({
                    id: item.id,
                    label: `${item.user?.name}`,
                  }))}
                label="Pilih Pengguna"
                autocompleteProps={{
                  disabled: !bill,
                  readOnly: !!invoice_id,
                }}
              />
              {bill && !!watch("user_bill_id") && (
                <Stack sx={{ gap: 1 }}>
                  <Stack
                    sx={{
                      flexDirection: "row",
                      justifyContent: "space-between",
                    }}
                  >
                    <Typography sx={{ fontWeight: 600 }}>
                      Total Harga
                    </Typography>
                    <Typography>{formatToRupiah(bill?.amount)}</Typography>
                  </Stack>
                  <Stack
                    sx={{
                      flexDirection: "row",
                      justifyContent: "space-between",
                    }}
                  >
                    <Typography sx={{ fontWeight: 600 }}>
                      Tenggat Waktu
                    </Typography>
                    <Typography>
                      {dayjs(bill?.deadline, "DD/MM/YYYY h:mm A Z").format(
                        "D MMM YYYY"
                      )}
                    </Typography>
                  </Stack>
                  <Stack
                    sx={{
                      flexDirection: "row",
                      justifyContent: "space-between",
                    }}
                  >
                    <Typography sx={{ fontWeight: 600 }}>
                      Jumlah Tertagih
                    </Typography>
                    <Typography>{formatToRupiah(jumlahTertagih)}</Typography>
                  </Stack>
                  <Stack
                    sx={{
                      flexDirection: "row",
                      justifyContent: "space-between",
                    }}
                  >
                    <Typography sx={{ fontWeight: 600 }}>
                      Jumlah Terbayar
                    </Typography>
                    <Typography>{formatToRupiah(jumlahTerbayar)}</Typography>
                  </Stack>
                  <Stack
                    sx={{
                      flexDirection: "row",
                      justifyContent: "space-between",
                    }}
                  >
                    <Typography sx={{ fontWeight: 600 }}>
                      Sisa Tagihan
                    </Typography>
                    <Typography>{formatToRupiah(sisaTagihan)}</Typography>
                  </Stack>
                </Stack>
              )}
              <Stack sx={{ flexDirection: "row", gap: 2 }}>
                {invoice_id && (
                  <SelectElement
                    control={control}
                    name="status"
                    options={invoiceStatusOptions.map((item) => ({
                      id: item.value,
                      label: item.label,
                    }))}
                    label="Status"
                    sx={{
                      width: 200,
                    }}
                  />
                )}
                <TextFieldElement
                  sx={{ flex: 1 }}
                  transform={{
                    input(value) {
                      return currency(value, {
                        symbol: "Rp",
                        separator: ".",
                        precision: 0,
                      }).format();
                    },
                    output(e) {
                      return currency(e.target.value.replace(/[^0-9]/g, ""))
                        .value;
                    },
                  }}
                  name="amount"
                  label="Nilai Invoice"
                  control={control}
                  disabled={!bill}
                />
              </Stack>
              <TextareaAutosizeElement
                resizeStyle="vertical"
                name="note"
                label="Deskripsi"
                control={control}
                rows={2}
                disabled={!bill}
              />

              {invoice_id && (
                <>
                  <Stack
                    sx={{
                      flexDirection: "row",
                      gap: 2,
                    }}
                  >
                    <Image
                      src={url ? url : fallback}
                      alt={"Bukti Pembayaran"}
                      width={80}
                      height={80}
                      className="min-w-20 object-cover"
                      rootClassName="!z-[1310] relative"
                      fallback={fallback}
                      preview={!!url}
                    />
                    <Stack
                      sx={{
                        gap: 2,
                        flex: 1,
                        justifyContent: "space-between",
                      }}
                    >
                      <Button
                        component="label"
                        variant="contained"
                        size="small"
                      >
                        {invoice?.payment_proof.uri
                          ? "Ubah Bukti Pembayaran"
                          : "Tambah Bukti Pembayaran"}
                        <input
                          accept="image/*"
                          className="hidden"
                          type="file"
                          onChange={async (event) => {
                            if (!event.target.files?.[0]) return;
                            const formData = new FormData();
                            formData.append("file", event.target.files[0]);
                            const uri = await uploadFile(formData);
                            setValue("payment_proof_uri", uri);
                            if (watch("status") === "pending")
                              setValue("status", "inreview");
                            event.target.value = "";
                          }}
                          multiple
                        />
                      </Button>
                      {!invoice?.payment_proof.uri && (
                        <Button
                          variant="outlined"
                          size="small"
                          color="error"
                          onClick={() => {
                            setValue("payment_proof_uri", "");
                          }}
                        >
                          Hapus
                        </Button>
                      )}
                    </Stack>
                  </Stack>
                  {watch("payment_proof_uri") && (
                    <TextareaAutosizeElement
                      resizeStyle="vertical"
                      name="payment_proof_note"
                      label="Catatan pembayaran"
                      control={control}
                      rows={2}
                      disabled={!bill}
                    />
                  )}
                </>
              )}
            </Stack>
            <Divider />
            <Stack sx={{ gap: 2, px: 2, flexDirection: "row" }}>
              <Button variant="outlined" fullWidth onClick={toggle}>
                Batal
              </Button>
              <Button type="submit" variant="contained" fullWidth>
                Simpan
              </Button>
            </Stack>
          </Paper>
        </Stack>
      </Modal>
    </>
  );
}
