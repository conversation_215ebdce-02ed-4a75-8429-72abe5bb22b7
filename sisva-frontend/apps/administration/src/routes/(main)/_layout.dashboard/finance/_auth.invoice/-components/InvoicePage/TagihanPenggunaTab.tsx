import { Clear, DeleteForever, Search, Sort } from "@mui/icons-material";
import {
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  IconButton,
  InputAdornment,
  Stack,
  Typography,
  useMediaQuery,
} from "@mui/material";
import type { GridColDef, GridFilterOperator } from "@mui/x-data-grid";
import { DataGrid, useGridApiContext } from "@mui/x-data-grid";
import type { UserBillWithUserAndBill } from "@sisva/hooks/query/finance/useUserBills";
import { useUserBillsWithUserAndBill } from "@sisva/hooks/query/finance/useUserBills";
import type { UserTypeText } from "@sisva/types/types";
import { getUserTypeText } from "@sisva/types/types";
import { formatToRupiah } from "@sisva/utils";
import { onlyUnique } from "@sisva/utils";
import { useToggle } from "ahooks";
import dayjs from "dayjs";
import { sort } from "fast-sort";
import { useEffect } from "react";
import { useForm } from "react-hook-form";
import {
  AutocompleteElement,
  SelectElement,
  TextFieldElement,
} from "react-hook-form-mui";
import { DatePickerElement } from "react-hook-form-mui/date-pickers";

import DeleteIcon from "#/routes/(main)/-components/DeleteIcon";

import CreateUserBillsButton from "../CreateUserBillsButton";
import DeleteUserBillButton from "../DeleteUserBillButton";

type Field =
  | "mobile_column"
  | "bill_custom_id"
  | "bill_deadline"
  | "user_name"
  | "bill_name"
  | "bill_amount"
  | "total_invoice_amount"
  | "paid_amount"
  | "user_type"
  | "user_period"
  | "user_study_program"
  | "user_grade"
  | "user_student_group"
  | keyof UserBillWithUserAndBill
  | typeof filter_field
  | "action";
type TypeSafeColDef = GridColDef & { field: Field };

const filter_field = "filter_field";
const field = {
  filter1: "bill_name",
  filter2: "start_date",
  filter3: "end_date",
  filter4: "user_type",
  filter5: "user_period",
  filter6: "user_study_program",
  filter7: "user_grade",
  filter8: "user_student_group",
} satisfies Record<string, Field | "start_date" | "end_date">;

type FilterValue = Record<(typeof field)[keyof typeof field], string>;

export default function TagihanTab() {
  const isMobile = useMediaQuery((theme) => theme.breakpoints.down("lg"));
  const { data: userBills = [] } = useUserBillsWithUserAndBill();

  const customOperator: GridFilterOperator<UserBillWithUserAndBill> = {
    label: "Custom Operator",
    value: "customOperator",
    getApplyFilterFn: (filterItem, column) => {
      if (!filterItem.field || !filterItem.value || !filterItem.operator) {
        return null;
      }
      const filterValue = filterItem.value as FilterValue;
      const typeFilter = filterValue.user_type as UserTypeText;

      return (value, row, column, apiRef) => {
        let pass = true;
        if (filterValue.bill_name) {
          if (row.bill?.name !== filterValue.bill_name) pass = false;
        }
        if (filterValue.start_date) {
          if (
            dayjs(row.bill?.deadline, "DD/MM/YYYY h:mm A Z").isBefore(
              dayjs(filterValue.start_date)
            )
          )
            pass = false;
        }
        if (filterValue.end_date) {
          if (
            dayjs(row.bill?.deadline, "DD/MM/YYYY h:mm A Z").isAfter(
              dayjs(filterValue.end_date)
            )
          )
            pass = false;
        }
        if (typeFilter === "Siswa") {
          if (row.user?.type !== "student") pass = false;
        }
        if (typeFilter === "Staf") {
          if (
            !["staff", "teacher", "management"].includes(row.user?.type ?? "")
          )
            pass = false;
        }
        if (filterValue.user_period) {
          if (row.user?.student_group?.period_name !== filterValue.user_period)
            pass = false;
        }
        if (filterValue.user_study_program) {
          if (row.user?.study_program?.name !== filterValue.user_study_program)
            pass = false;
        }
        if (filterValue.user_grade) {
          if (row.user?.detail.grade !== filterValue.user_grade) pass = false;
        }
        if (filterValue.user_student_group) {
          if (row.user?.student_group?.name !== filterValue.user_student_group)
            pass = false;
        }
        return pass;
      };
    },
  };

  const columns: TypeSafeColDef[] = [
    {
      field: "mobile_column",
      headerName: "",
      valueGetter: (_, row) => row,
      flex: 1,
      display: "flex",
      renderCell: (args) => {
        const value = args.value as UserBillWithUserAndBill;
        return (
          <Stack sx={{ p: 1.5, gap: 1, width: "100%" }}>
            <Stack
              sx={{
                flexDirection: "row",
                justifyContent: "space-between",
                width: "100%",
              }}
            >
              <Typography sx={{ fontWeight: 600 }}>No Tagihan</Typography>
              <Typography>{value.bill?.name}</Typography>
            </Stack>
            <Stack
              sx={{
                flexDirection: "row",
                justifyContent: "space-between",
                width: "100%",
              }}
            >
              <Typography sx={{ fontWeight: 600 }}>Tenggat Waktu</Typography>
              <Typography>
                {dayjs(value.bill?.deadline, "DD/MM/YYYY h:mm A Z").format(
                  "D MMM YYYY"
                )}
              </Typography>
            </Stack>
            <Stack
              sx={{
                flexDirection: "row",
                justifyContent: "space-between",
                width: "100%",
              }}
            >
              <Typography sx={{ fontWeight: 600 }}>Nama</Typography>
              <Typography>{value.user?.name}</Typography>
            </Stack>
            <Stack
              sx={{
                flexDirection: "row",
                justifyContent: "space-between",
                width: "100%",
              }}
            >
              <Typography sx={{ fontWeight: 600 }}>Nama Tagihan</Typography>
              <Typography>{value.bill?.name}</Typography>
            </Stack>
            <Stack
              sx={{
                flexDirection: "row",
                justifyContent: "space-between",
                width: "100%",
              }}
            >
              <Typography sx={{ fontWeight: 600 }}>Total Harga</Typography>
              <Typography>{formatToRupiah(value.bill?.amount)}</Typography>
            </Stack>
            <Stack
              sx={{
                flexDirection: "row",
                justifyContent: "space-between",
                width: "100%",
              }}
            >
              <Typography sx={{ fontWeight: 600 }}>Jumlah Tagihan</Typography>
              <Typography>
                {formatToRupiah(
                  value.bill?.invoices
                    .filter((invoice) => invoice.user_bill_id === value.id)
                    .reduce((acc, cur) => acc + cur.amount, 0)
                )}
              </Typography>
            </Stack>
            <Stack
              sx={{
                flexDirection: "row",
                justifyContent: "space-between",
                width: "100%",
              }}
            >
              <Typography sx={{ fontWeight: 600 }}>Jumlah Terbayar</Typography>
              <Typography>
                {formatToRupiah(
                  value.bill?.invoices
                    .filter((invoice) => invoice.user_bill_id === value.id)
                    .filter((invoice) => invoice.status === "done")
                    .reduce((acc, cur) => acc + cur.amount, 0)
                )}
              </Typography>
            </Stack>
            <Stack
              sx={{
                flexDirection: "row",
                justifyContent: "space-between",
                width: "100%",
              }}
            >
              <Typography sx={{ fontWeight: 600 }}>Tipe</Typography>
              <Typography>
                {value.user?.type
                  ? getUserTypeText(value.user?.type)
                  : undefined}
              </Typography>
            </Stack>
            <Stack
              sx={{
                flexDirection: "row",
                justifyContent: "space-between",
                width: "100%",
              }}
            >
              <Typography sx={{ fontWeight: 600 }}>Periode</Typography>
              <Typography>{value.user?.student_group?.period_name}</Typography>
            </Stack>
            <Stack
              sx={{
                flexDirection: "row",
                justifyContent: "space-between",
                width: "100%",
              }}
            >
              <Typography sx={{ fontWeight: 600 }}>Program Studi</Typography>
              <Typography>{value.user?.study_program?.name}</Typography>
            </Stack>
            <Stack
              sx={{
                flexDirection: "row",
                justifyContent: "space-between",
                width: "100%",
              }}
            >
              <Typography sx={{ fontWeight: 600 }}>Tingkatan</Typography>
              <Typography>{value.user?.detail?.grade}</Typography>
            </Stack>
            <Stack
              sx={{
                flexDirection: "row",
                justifyContent: "space-between",
                width: "100%",
              }}
            >
              <Typography sx={{ fontWeight: 600 }}>Kelas</Typography>
              <Typography>{value.user?.student_group?.name}</Typography>
            </Stack>
            <Stack
              sx={{
                flexDirection: "row",
                justifyContent: "flex-end",
                gap: 1,
                width: "100%",
              }}
            >
              <DeleteUserBillButton
                user_bill_id={value.id}
                renderTrigger={(onClick) => <DeleteIcon onClick={onClick} />}
              />
            </Stack>
          </Stack>
        );
      },
    },
    {
      field: "bill_custom_id",
      headerName: "No Tagihan",
      valueGetter: (_, row: UserBillWithUserAndBill) => row.bill?.custom_id,
      display: "flex",
      renderCell: ({ value }) => <Stack sx={{ py: 1.3 }}>{value}</Stack>,
    },
    {
      field: "bill_deadline",
      headerName: "Tenggat Waktu",
      valueGetter: (_, row: UserBillWithUserAndBill) =>
        dayjs(row.bill?.deadline, "DD/MM/YYYY h:mm A Z").toDate(),
      display: "flex",
      width: 120,
      renderCell: ({ value }) => (
        <Stack
          sx={{ py: 1.3, flexDirection: "row", alignItems: "center", gap: 2 }}
        >
          {dayjs(value).format("D MMM YYYY")}
        </Stack>
      ),
    },
    {
      field: "user_name",
      headerName: "Nama",
      display: "flex",
      valueGetter: (_, row: UserBillWithUserAndBill) => row.user?.name,
      flex: 1,
      minWidth: 180,
      renderCell: ({ value }) => <Stack sx={{ py: 1.3 }}>{value}</Stack>,
    },
    {
      field: "bill_name",
      headerName: "Nama Tagihan",
      display: "flex",
      minWidth: 180,
      valueGetter: (_, row: UserBillWithUserAndBill) => row.bill?.name,
      flex: 1,
      renderCell: ({ value }) => <Stack sx={{ py: 1.3 }}>{value}</Stack>,
    },
    {
      field: "bill_amount",
      headerName: "Total Harga",
      valueGetter: (_, row: UserBillWithUserAndBill) => row.bill?.amount,
      display: "flex",
      renderCell: ({ value }) => (
        <Stack
          sx={{ py: 1.3, flexDirection: "row", alignItems: "center", gap: 2 }}
        >
          {formatToRupiah(value)}
        </Stack>
      ),
    },
    {
      field: "total_invoice_amount",
      headerName: "Jumlah Tertagih",
      valueGetter: (_, row: UserBillWithUserAndBill) =>
        row.bill?.invoices
          .filter((invoice) => invoice.user_bill_id === row.id)
          .reduce((acc, cur) => acc + cur.amount, 0),
      display: "flex",
      width: 120,
      renderCell: ({ value }) => {
        return (
          <Stack
            sx={{ py: 1.3, flexDirection: "row", alignItems: "center", gap: 2 }}
          >
            {formatToRupiah(value)}
          </Stack>
        );
      },
    },
    {
      field: "paid_amount",
      headerName: "Jumlah Terbayar",
      valueGetter: (_, row: UserBillWithUserAndBill) =>
        row.bill?.invoices
          .filter((invoice) => invoice.user_bill_id === row.id)
          .filter((invoice) => invoice.status === "done")
          .reduce((acc, cur) => acc + cur.amount, 0),
      display: "flex",
      width: 120,
      renderCell: ({ value }) => {
        return (
          <Stack
            sx={{ py: 1.3, flexDirection: "row", alignItems: "center", gap: 2 }}
          >
            {formatToRupiah(value)}
          </Stack>
        );
      },
    },
    {
      field: "user_type",
      headerName: "Tipe",
      display: "flex",
      valueGetter: (_, row: UserBillWithUserAndBill) =>
        row.user?.type ? getUserTypeText(row.user?.type) : undefined,
      renderCell: ({ value }) => <Stack sx={{ py: 1.3 }}>{value}</Stack>,
    },
    {
      field: "user_period",
      headerName: "Periode",
      display: "flex",
      valueGetter: (_, row: UserBillWithUserAndBill) =>
        row.user?.student_group?.period_name,
      renderCell: ({ value }) => <Stack sx={{ py: 1.3 }}>{value}</Stack>,
    },
    {
      field: "user_study_program",
      headerName: "Program Studi",
      minWidth: 200,
      display: "flex",
      valueGetter: (_, row: UserBillWithUserAndBill) =>
        row.user?.study_program?.name,
      renderCell: ({ value }) => <Stack sx={{ py: 1.3 }}>{value}</Stack>,
    },
    {
      field: "user_grade",
      headerName: "Tingkatan",
      display: "flex",
      valueGetter: (_, row: UserBillWithUserAndBill) => row.user?.detail.grade,
      renderCell: ({ value }) => <Stack sx={{ py: 1.3 }}>{value}</Stack>,
    },
    {
      field: "user_student_group",
      headerName: "Kelas",
      display: "flex",
      valueGetter: (_, row: UserBillWithUserAndBill) =>
        row.user?.student_group?.name,
      renderCell: ({ value }) => <Stack sx={{ py: 1.3 }}>{value}</Stack>,
    },
    {
      field: "action",
      headerName: "Aksi",
      display: "flex",
      sortable: false,
      renderCell: ({ row }) => (
        <Stack
          sx={{ py: 1.3, flexDirection: "row", alignItems: "center", gap: 1 }}
        >
          <DeleteUserBillButton
            user_bill_id={row.id}
            renderTrigger={(onClick) => <DeleteIcon onClick={onClick} />}
          />
        </Stack>
      ),
    },
    {
      field: filter_field,
      filterOperators: [customOperator],
    },
  ];

  const columnVisibilityModel = Object.fromEntries(
    columns
      .map((column) => column.field)
      .filter((field) =>
        isMobile ? field !== "mobile_column" : field === "mobile_column"
      )
      .map((field) => [field, false])
  );
  columnVisibilityModel[filter_field] = false;

  return (
    <DataGrid
      sx={{
        "& .MuiDataGrid-cell:focus": {
          outline: " none",
        },
        border: "none",
      }}
      getRowId={(row: UserBillWithUserAndBill) => `${row.id}`}
      disableRowSelectionOnClick
      className="bg-white grow thick-scrollbar"
      rows={userBills}
      columns={columns}
      initialState={{ pagination: { paginationModel: { pageSize: 20 } } }}
      pageSizeOptions={[10, 20, 50]}
      getRowClassName={(params) =>
        params.indexRelativeToCurrentPage % 2 === 0
          ? "bg-neutral-50"
          : "bg-neutral-100"
      }
      slots={{
        toolbar: CustomToolbar,
      }}
      getRowHeight={() => "auto"}
      columnVisibilityModel={columnVisibilityModel}
      columnHeaderHeight={isMobile ? 0 : undefined}
      disableColumnMenu
      disableColumnFilter
    />
  );
}

function CustomToolbar() {
  const api = useGridApiContext();
  const [open, { toggle }] = useToggle(false);

  const { control, handleSubmit, reset, setValue, watch } = useForm<{
    field: Field | "";
    sortDirection: "asc" | "desc";
    quickFilter: string;
    [field.filter1]: string;
    [field.filter2]: string;
    [field.filter3]: string;
    [field.filter4]: string;
    [field.filter5]: string;
    [field.filter6]: string;
    [field.filter7]: string;
    [field.filter8]: string;
  }>({
    defaultValues: {
      field: "",
      sortDirection: "asc",
      quickFilter: "",
      [field.filter1]: "",
      [field.filter2]: "",
      [field.filter3]: "",
      [field.filter4]: "",
      [field.filter5]: "",
      [field.filter6]: "",
      [field.filter7]: "",
      [field.filter8]: "",
    },
  });

  const rows = Array.from(api.current.getRowModels()).map(
    ([key, value]) => value
  ) as UserBillWithUserAndBill[];
  const billNames = sort(rows)
    .desc((r) => r.bill?.id)
    .map((row) => row.bill?.name)
    .filter((item) => item)
    .filter(onlyUnique);
  const userTypes = rows
    .map((row) =>
      row.user?.type ? getUserTypeText(row.user?.type) : undefined
    )
    .filter((item) => item)
    .filter((item) => item === "Siswa" || item === "Staf")
    .filter(onlyUnique);
  const periodNames = sort(rows)
    .desc((r) => r.user?.student_group?.period_id)
    .map((row) => row.user?.student_group?.period_name)
    .filter((item) => item)
    .filter(onlyUnique);
  const studyProgramNames = rows
    .map((row) => row.user?.study_program?.name)
    .filter((item) => item)
    .filter(onlyUnique);
  const grades = rows
    .map((row) => row.user?.detail.grade)
    .filter((item) => item)
    .filter(onlyUnique);
  const studentGroupNames = rows
    .map((row) => row.user?.student_group?.name)
    .filter((item) => item)
    .filter(onlyUnique);

  // apply filter
  const filter1 = watch(field.filter1);
  const filter2 = watch(field.filter2);
  const filter3 = watch(field.filter3);
  const filter4 = watch(field.filter4);
  const filter5 = watch(field.filter5);
  const filter6 = watch(field.filter6);
  const filter7 = watch(field.filter7);
  const filter8 = watch(field.filter8);
  useEffect(() => {
    const value: FilterValue = {
      [field.filter1]: filter1,
      [field.filter2]: filter2,
      [field.filter3]: filter3,
      [field.filter4]: filter4,
      [field.filter5]: filter5,
      [field.filter6]: filter6,
      [field.filter7]: filter7,
      [field.filter8]: filter8,
    };

    api.current.upsertFilterItem({
      field: filter_field,
      operator: "customOperator",
      value,
    });
  }, [
    api,
    filter1,
    filter2,
    filter3,
    filter4,
    filter5,
    filter6,
    filter7,
    filter8,
  ]);

  const sortSelectOptions: { label: string; id: Field }[] = [
    { label: "No Tagihan", id: "bill_custom_id" },
    { label: "Tenggat Waktu", id: "bill_deadline" },
    { label: "Nama", id: "user_name" },
    { label: "Nama Tagihan", id: "bill_name" },
    { label: "Total Harga", id: "bill_amount" },
    { label: "Jumlah Tertagih", id: "total_invoice_amount" },
    { label: "Jumlah Terbayar", id: "paid_amount" },
    { label: "Tipe", id: "user_type" },
    { label: "Periode", id: "user_period" },
    { label: "Program Studi", id: "user_study_program" },
    { label: "Tingkatan", id: "user_grade" },
    { label: "Kelas", id: "user_student_group" },
  ];

  const quickFilter = watch("quickFilter");
  useEffect(() => {
    api.current.setQuickFilterValues(
      quickFilter.split(" ").filter((s) => s !== "")
    );
  }, [api, quickFilter]);

  return (
    <div className="p-4 flex flex-col gap-2 border-b border-0 border-solid border-neutral-200">
      <div className=" flex justify-between gap-4 thick-scrollbar items-center ">
        <Stack
          sx={{
            overflow: "auto",
            py: 1,
          }}
        >
          <Stack sx={{ flexDirection: "row", gap: 2 }}>
            <TextFieldElement
              control={control}
              name="quickFilter"
              placeholder="Cari"
              size="small"
              sx={{
                minWidth: 150,
              }}
              slotProps={{
                input: {
                  endAdornment: (
                    <InputAdornment position="end">
                      {quickFilter ? (
                        <IconButton
                          size="small"
                          onClick={() => setValue("quickFilter", "")}
                        >
                          <Clear />
                        </IconButton>
                      ) : (
                        <Search />
                      )}
                    </InputAdornment>
                  ),
                },
              }}
            />
            <AutocompleteElement
              control={control}
              name={field.filter1}
              matchId
              options={billNames.map((item) => ({
                id: item,
                label: item,
              }))}
              label="Tagihan"
              autocompleteProps={{
                size: "small",
                sx: { minWidth: "180px" },
              }}
            />
            <DatePickerElement
              transform={{
                input(dateString) {
                  if (!dateString) return null;
                  return dayjs(dateString);
                },
                output(date) {
                  return date?.format() ?? "";
                },
              }}
              label="Tanggal Awal"
              control={control}
              name={field.filter2}
              format="DD MMMM YYYY"
              inputProps={{
                size: "small",
                sx: {
                  minWidth: 180,
                },
              }}
              slotProps={{
                actionBar: {
                  actions: ["clear"],
                },
              }}
            />
            <DatePickerElement
              transform={{
                input(dateString) {
                  if (!dateString) return null;
                  return dayjs(dateString);
                },
                output(date) {
                  return date?.format() ?? "";
                },
              }}
              label="Tanggal Akhir"
              control={control}
              name={field.filter3}
              format="DD MMMM YYYY"
              inputProps={{
                size: "small",
                sx: {
                  minWidth: 180,
                },
              }}
              slotProps={{
                actionBar: {
                  actions: ["clear"],
                },
              }}
            />
            <SelectElement
              control={control}
              name={field.filter4}
              options={
                userTypes.length
                  ? userTypes.map((item) => ({
                      id: item,
                      label: item,
                      disabled: false,
                    }))
                  : [
                      {
                        id: "Tidak ada opsi",
                        label: "Tidak ada opsi",
                        disabled: true,
                      },
                    ]
              }
              label="Tipe"
              size="small"
              sx={{
                minWidth: "180px",
              }}
              slotProps={{
                input: {
                  endAdornment: (
                    <InputAdornment position="end">
                      {filter4 ? (
                        <IconButton
                          size="small"
                          onClick={() => setValue(field.filter4, "")}
                        >
                          <Clear />
                        </IconButton>
                      ) : null}
                    </InputAdornment>
                  ),
                },
              }}
            />
            <AutocompleteElement
              control={control}
              name={field.filter5}
              matchId
              options={periodNames.map((item) => ({
                id: item,
                label: item,
              }))}
              label="Periode"
              autocompleteProps={{
                size: "small",
                sx: { minWidth: "180px" },
              }}
            />
            <AutocompleteElement
              control={control}
              name={field.filter6}
              matchId
              options={studyProgramNames.map((item) => ({
                id: item,
                label: item,
              }))}
              label="Program Studi"
              autocompleteProps={{
                size: "small",
                sx: { minWidth: "180px" },
              }}
            />
            <AutocompleteElement
              control={control}
              name={field.filter7}
              matchId
              options={grades.map((item) => ({
                id: item,
                label: item,
              }))}
              label="Tingkatan"
              autocompleteProps={{
                size: "small",
                sx: { minWidth: "180px" },
              }}
            />
            <AutocompleteElement
              control={control}
              name={field.filter8}
              matchId
              options={studentGroupNames.map((item) => ({
                id: item,
                label: item,
              }))}
              label="Kelas"
              autocompleteProps={{
                size: "small",
                sx: { minWidth: "180px" },
              }}
            />
          </Stack>
        </Stack>
        <Stack sx={{ minWidth: 110 }}>
          <CreateUserBillsButton />
        </Stack>
      </div>
      <Button
        startIcon={<Sort />}
        variant="outlined"
        onClick={toggle}
        sx={{
          display: { xs: "flex", lg: "none" },
        }}
      >
        Urutkan Data
      </Button>
      <Dialog open={open} onClose={toggle}>
        <DialogTitle>Urutkan</DialogTitle>
        <DialogContent
          sx={{
            width: "250px",
            display: "flex",
            flexDirection: "column",
            gap: 2,
            overflow: "initial",
          }}
        >
          <SelectElement
            control={control}
            name="field"
            options={sortSelectOptions}
            label="Pilih Kolom"
            slotProps={{
              input: {
                endAdornment: watch("field") && (
                  <InputAdornment position="end">
                    <IconButton onClick={() => setValue("field", "")}>
                      <Clear />
                    </IconButton>
                  </InputAdornment>
                ),
              },
            }}
          />
          <SelectElement
            control={control}
            name="sortDirection"
            options={[
              { label: "A-Z", id: "asc" },
              { label: "Z-A", id: "desc" },
            ]}
            label="Urutan"
          />
        </DialogContent>
        <DialogActions sx={{ pb: 2, px: 3 }}>
          <Button
            variant="outlined"
            onClick={() => {
              reset();
              toggle();
            }}
          >
            Batal
          </Button>
          <Button
            variant="contained"
            onClick={handleSubmit((value) => {
              if (value.field) {
                api.current.sortColumn(value.field, value.sortDirection);
              } else {
                api.current.sortColumn("name", null);
              }
              toggle();
            })}
          >
            Simpan
          </Button>
        </DialogActions>
      </Dialog>
    </div>
  );
}
