import {
  But<PERSON>,
  Divider,
  <PERSON><PERSON>,
  Paper,
  Stack,
  Typography,
} from "@mui/material";
import { useDeleteGuardian } from "@sisva/hooks/query/user/useGuardians";
import { useUser } from "@sisva/hooks/query/user/useUsers";
import { AvatarWithAcronym } from "@sisva/ui";
import { useToggle } from "ahooks";
import toast from "react-hot-toast";

import DeleteIcon from "#/routes/(main)/-components/DeleteIcon";

export default function DeleteGuardianButton({
  guardian_id,
}: {
  guardian_id: string;
}) {
  const [visible, { toggle }] = useToggle(false);
  const { mutate: deleteGuardian } = useDeleteGuardian({
    onSuccess() {
      toggle();
      toast.success("Wali berhasil dihapus");
    },
    onError() {
      toast.error("Wali gagal dihapus");
    },
  });

  return (
    <>
      <DeleteIcon onClick={toggle} />
      <Modal open={visible} onClose={toggle}>
        <div className="flex items-center justify-center size-full px-4">
          <Paper className="flex flex-col gap-4 w-full max-w-sm py-4">
            <div className="px-4 font-semibold">Hapus Wali</div>
            <Divider />
            <Stack className="px-4 gap-2">
              <Typography>Anda akan menghapus wali berikut:</Typography>
              <Preview guardian_id={guardian_id} />
            </Stack>
            <Divider />
            <div className="flex gap-4 px-4">
              <Button variant="outlined" fullWidth onClick={toggle}>
                Batal
              </Button>
              <Button
                variant="contained"
                color="error"
                fullWidth
                onClick={() => deleteGuardian(guardian_id)}
              >
                Hapus
              </Button>
            </div>
          </Paper>
        </div>
      </Modal>
    </>
  );
}

function Preview({ guardian_id }: { guardian_id: string }) {
  const { data: user } = useUser(guardian_id);
  return (
    <div className="flex items-center gap-2 bg-neutral-200 rounded-md p-2">
      <AvatarWithAcronym uri={user?.profile_image_uri} name={user?.name} />
      <Stack>
        <div className="font-semibold text-lg">{user?.name}</div>
        <div>{user?.username}</div>
      </Stack>
    </div>
  );
}
