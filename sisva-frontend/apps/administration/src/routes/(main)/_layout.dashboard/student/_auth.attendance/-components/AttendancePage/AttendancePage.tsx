import {
  Paper,
  Stack,
  Tab,
  Tabs,
  Typography,
  useMediaQuery,
} from "@mui/material";
import type { SyntheticEvent } from "react";
import { useState } from "react";

import ImportExcelStudentAttendanceButton from "../ImportExcelStudentAttendanceButton";
import KehadiranKelasTab from "./KehadiranKelasTab";
import KehadiranTab from "./KehadiranTab";

export default function AttendancePage() {
  const isMobile = useMediaQuery((theme) => theme.breakpoints.down("lg"));

  const [tabIndex, setTabIndex] = useState(0);
  return (
    <Stack
      sx={{
        height: "100%",
        width: "100%",
        p: { xs: 0, lg: 4 },
        gap: 2,
      }}
    >
      <Stack
        sx={{
          flexDirection: "row",
          justifyContent: "space-between",
          display: {
            xs: "none",
            lg: "flex",
          },
        }}
      >
        <Typography
          sx={{
            fontWeight: 700,
            fontSize: 20,
          }}
        >
          Kehadiran Siswa
        </Typography>
        {tabIndex === 0 && <ImportExcelStudentAttendanceButton />}
      </Stack>
      <Paper
        variant="outlined"
        sx={{
          flex: 1,
          display: "flex",
          flexDirection: "column",
          overflow: "auto",
        }}
      >
        <Tabs
          value={tabIndex}
          onChange={(event: SyntheticEvent, newValue: number) => {
            setTabIndex(newValue);
          }}
          sx={{
            borderBottom: 1,
            borderColor: "divider",
          }}
          variant={isMobile ? "fullWidth" : "standard"}
        >
          <Tab
            label="Kehadiran Sekolah"
            sx={{ fontWeight: 600, textTransform: "none", fontSize: 14 }}
          />
          <Tab
            label="Kehadiran Kelas"
            sx={{ fontWeight: 600, textTransform: "none", fontSize: 14 }}
          />
        </Tabs>
        {(() => {
          switch (tabIndex) {
            case 0:
              return <KehadiranTab />;
            case 1:
              return <KehadiranKelasTab />;
          }
        })()}
      </Paper>
    </Stack>
  );
}
