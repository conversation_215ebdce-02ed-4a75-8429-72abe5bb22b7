import {
  Paper,
  Stack,
  Tab,
  Tabs,
  Typography,
  useMediaQuery,
} from "@mui/material";
import type { SyntheticEvent } from "react";
import { useState } from "react";

import MuridTab from "./MuridTab";
import WaliSiswaTab from "./WaliSiswaTab";

export default function GuardianPage() {
  const isMobile = useMediaQuery((theme) => theme.breakpoints.down("lg"));

  const [tabIndex, setTabIndex] = useState(0);
  return (
    <Stack
      sx={{
        height: "100%",
        width: "100%",
        p: { xs: 0, lg: 4 },
        gap: 2,
      }}
    >
      <Stack
        sx={{
          flexDirection: "row",
          justifyContent: "space-between",
          display: {
            xs: "none",
            lg: "flex",
          },
        }}
      >
        <Typography
          sx={{
            fontWeight: 700,
            fontSize: 20,
          }}
        >
          Wali Siswa
        </Typography>
      </Stack>
      <Paper
        variant="outlined"
        sx={{
          flex: 1,
          display: "flex",
          flexDirection: "column",
          overflow: "auto",
        }}
      >
        <Tabs
          value={tabIndex}
          onChange={(event: SyntheticEvent, newValue: number) => {
            setTabIndex(newValue);
          }}
          sx={{
            borderBottom: 1,
            borderColor: "divider",
          }}
          variant={isMobile ? "fullWidth" : "standard"}
        >
          <Tab
            label="Wali Siswa"
            sx={{ fontWeight: 600, textTransform: "none", fontSize: 14 }}
          />
          <Tab
            label="Murid"
            sx={{ fontWeight: 600, textTransform: "none", fontSize: 14 }}
          />
        </Tabs>
        {(() => {
          switch (tabIndex) {
            case 0:
              return <WaliSiswaTab />;
            case 1:
              return <MuridTab />;
          }
        })()}
      </Paper>
    </Stack>
  );
}
