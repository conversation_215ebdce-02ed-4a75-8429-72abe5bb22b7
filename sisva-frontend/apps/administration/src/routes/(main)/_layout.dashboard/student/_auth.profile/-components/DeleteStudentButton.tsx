import {
  But<PERSON>,
  Divider,
  <PERSON><PERSON>,
  Paper,
  Stack,
  Typography,
} from "@mui/material";
import { useDeleteStudent } from "@sisva/hooks/query/user/useStudents";
import { useUser } from "@sisva/hooks/query/user/useUsers";
import { AvatarWithAcronym } from "@sisva/ui";
import { useToggle } from "ahooks";
import toast from "react-hot-toast";

import DeleteIcon from "#/routes/(main)/-components/DeleteIcon";

export default function DeleteStudentButton({
  student_id,
}: {
  student_id: string;
}) {
  const [visible, { toggle }] = useToggle(false);
  const { mutate: deleteStudent } = useDeleteStudent({
    onSuccess() {
      toggle();
      toast.success("Siswa berhasil dihapus");
    },
    onError() {
      toast.error("Siswa gagal dihapus");
    },
  });

  return (
    <>
      <DeleteIcon onClick={toggle} />
      <Modal open={visible} onClose={toggle}>
        <div className="flex items-center justify-center size-full px-4">
          <Paper className="flex flex-col gap-4 w-full max-w-sm py-4">
            <div className="px-4 font-semibold">Hapus Siswa</div>
            <Divider />
            <Stack className="px-4 gap-2">
              <Typography>Anda akan menghapus siswa berikut:</Typography>
              <Preview student_id={student_id} />
            </Stack>
            <Divider />
            <div className="flex gap-4 px-4">
              <Button variant="outlined" fullWidth onClick={toggle}>
                Batal
              </Button>
              <Button
                variant="contained"
                color="error"
                fullWidth
                onClick={() => deleteStudent(student_id)}
              >
                Hapus
              </Button>
            </div>
          </Paper>
        </div>
      </Modal>
    </>
  );
}

function Preview({ student_id }: { student_id: string }) {
  const { data: user } = useUser(student_id);
  return (
    <div className="flex items-center gap-2 bg-neutral-200 rounded-md p-3">
      <AvatarWithAcronym uri={user?.profile_image_uri} name={user?.name} />
      <Stack>
        <div className="font-semibold">{user?.name}</div>
        <div className="text-sm">{user?.username}</div>
      </Stack>
    </div>
  );
}
