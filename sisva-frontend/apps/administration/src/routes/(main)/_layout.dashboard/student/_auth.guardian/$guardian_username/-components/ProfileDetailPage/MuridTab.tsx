import { yupResolver } from "@hookform/resolvers/yup";
import { <PERSON><PERSON>, Stack } from "@mui/material";
import { useGuardiansStudents } from "@sisva/hooks/query/user/useGuardians";
import {
  useStudents,
  useUpdateStudentsGuardian,
} from "@sisva/hooks/query/user/useStudents";
import { useUser } from "@sisva/hooks/query/user/useUsers";
import { updateStudentsGuardianSchema } from "@sisva/types/formTypes";
import { useToggle } from "ahooks";
import { AutocompleteElement } from "react-hook-form-mui";
import { useForm } from "react-hook-form-mui";
import toast from "react-hot-toast";

// NOTE: this will trigger key spread warning, see https://github.com/dohomi/react-hook-form-mui/issues/247
export default function MuridTab({ user_id }: { user_id: string }) {
  const { data: user } = useUser(user_id);
  const { data: students = [] } = useStudents();
  const { data: usersStudents = [] } = useGuardiansStudents(user_id);
  const [enableToast, { toggle }] = useToggle(true);

  const { control, handleSubmit, reset } = useForm({
    values: {
      guardian_id: user?.id || "",
      student_ids: usersStudents.map((student) => {
        return {
          label: student.name,
          id: student.id,
        };
      }),
    },
    resolver: yupResolver(updateStudentsGuardianSchema),
  });

  const { mutateAsync: updateStudentsGuardian } = useUpdateStudentsGuardian({
    onSuccess() {
      if (!enableToast) return;
      toast.success("Murid berhasil diubah");
    },
    onError() {
      toast.error("Murid gagal diubah");
    },
  });

  function getGuardianExistLabel(guardian_id: string) {
    // HACK: BE default guardian_id is 00000000-0000-0000-0000-000000000000 instead of null or empty string
    if (
      guardian_id &&
      guardian_id !== "00000000-0000-0000-0000-000000000000" &&
      guardian_id !== user_id
    ) {
      return " (Murid ini sudah mempunyai wali)";
    }
    return "";
  }

  return (
    <Stack
      sx={{ p: 2, gap: 2 }}
      component={"form"}
      onSubmit={handleSubmit(async (values) => {
        const usersStudentIds = usersStudents.map(({ id }) => id);
        // do not show toast for resetting guardian_id
        toggle();
        await updateStudentsGuardian({
          guardian_id: "",
          student_ids: usersStudentIds,
        });
        toggle();
        await updateStudentsGuardian({
          guardian_id: user_id,
          student_ids: values.student_ids.map((student) => student.id),
        });
      })}
    >
      <AutocompleteElement
        label="Murid Wali"
        control={control}
        multiple
        name="student_ids"
        options={students.map((student) => {
          return {
            label: `${student.name}${getGuardianExistLabel(student.detail.guardian_id)}`,
            id: student.id,
          };
        })}
        showCheckbox
      />
      <Stack
        sx={{
          justifyContent: "end",
          flexDirection: "row",
          gap: 2,
        }}
      >
        <Button onClick={() => reset()} variant="outlined">
          Batal
        </Button>
        <Button type="submit" variant="contained">
          Simpan
        </Button>
      </Stack>
    </Stack>
  );
}
