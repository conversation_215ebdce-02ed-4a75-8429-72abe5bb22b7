import { Add, ExpandMore } from "@mui/icons-material";
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Badge,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Divider,
  Stack,
  Typography,
} from "@mui/material";
import { DataGrid } from "@mui/x-data-grid";
import { useClass } from "@sisva/hooks/query/academic/useClasses";
import {
  useStudentClassAttendances,
  useUpdateStudentAttendanceMultiple,
} from "@sisva/hooks/query/attendance/useAttendance";
import type { StudentClassAttendance } from "@sisva/types/apiTypes";
import { getAttendanceText } from "@sisva/types/types";
import { useToggle } from "ahooks";
import type { Dayjs } from "dayjs";
import dayjs from "dayjs";
import { sort } from "fast-sort";
import { useEffect } from "react";
import { SwitchElement, useForm, useWatch } from "react-hook-form-mui";
import { DatePickerElement } from "react-hook-form-mui/date-pickers";
import toast from "react-hot-toast";

const today = dayjs();

export default function BulkAddButton() {
  const [open, { toggle }] = useToggle();
  const { control, setValue, handleSubmit, reset } = useForm<{
    class_id: number | null;
    date: Dayjs;
    all: boolean;
  }>({
    values: {
      class_id: null,
      date: today,
      all: true,
    },
  });
  const date = useWatch({ name: "date", control });
  const date_id = Number(date.format("YYYYMMDD"));
  const { data: studentClassAttendances = [] } = useStudentClassAttendances({
    date_id: date_id,
  });

  const studentClassAttendancesGroupedByClassId = sort(
    Object.entries(
      Object.groupBy(studentClassAttendances, (item) => item.class_id)
    )
  ).desc((item) => item[1]!.length);

  // set class_id to null when all is true
  const all = useWatch({ name: "all", control });
  const selected_class_id = useWatch({ name: "class_id", control });
  useEffect(() => {
    if (all && selected_class_id !== null) setValue("class_id", null);
  }, [all, selected_class_id, setValue]);

  const { mutateAsync: updateStudentAttendanceMultiple } =
    useUpdateStudentAttendanceMultiple({
      onSuccess({ count }) {
        toast.success(`${count} data absensi berhasil ditambahkan`);
      },
      onError: () => {
        toast.error(`Terjadi kesalahan saat menambahkan data absensi`);
      },
    });

  return (
    <>
      <Button variant="contained" onClick={toggle} startIcon={<Add />}>
        Tambah
      </Button>
      <Dialog
        open={open}
        onClose={toggle}
        fullWidth
        maxWidth="md"
        component="form"
        onSubmit={handleSubmit(async (values) => {
          const fitleredStudentClassAttenances = (() => {
            if (values.all) {
              const student_ids: string[] = [];
              return studentClassAttendances.filter((attendance) => {
                const include = !student_ids.includes(attendance.student_id);
                student_ids.push(attendance.student_id);
                return include;
              });
            }
            return studentClassAttendances.filter(
              (attendance) => attendance.class_id === values.class_id
            );
          })();

          const toastId = toast.loading("Sedang menambahkan...");
          await updateStudentAttendanceMultiple(fitleredStudentClassAttenances);
          toast.remove(toastId);

          reset();
          toggle();
        })}
      >
        <DialogTitle>Tambah Kehadiran</DialogTitle>
        <Divider />
        <DialogContent sx={{ p: 1 }}>
          <Stack sx={{ gap: 2 }}>
            <Stack
              sx={{
                p: 1,
                gap: 2,
              }}
            >
              <SwitchElement
                control={control}
                name="all"
                label="Tambah Keseluruhan"
              />
              <DatePickerElement
                control={control}
                name="date"
                format="dddd, DD MMMM YYYY"
              />
            </Stack>
            <Stack
              sx={{
                p: 1,
                maxHeight: "40svh",
                overflow: "auto",
              }}
            >
              {studentClassAttendancesGroupedByClassId.length > 0 ? (
                studentClassAttendancesGroupedByClassId.map(
                  ([class_id, studentClassAttendances]) => {
                    return (
                      <ClassCard
                        selected_class_id={selected_class_id}
                        onClick={() => setValue("class_id", Number(class_id))}
                        class_id={Number(class_id)}
                        key={class_id}
                        studentClassAttendances={studentClassAttendances!}
                      />
                    );
                  }
                )
              ) : (
                <Typography sx={{ textAlign: "center" }} color="textSecondary">
                  Tidak ada data yang tersedia
                </Typography>
              )}
            </Stack>
          </Stack>
        </DialogContent>
        <DialogActions>
          <Button
            onClick={() => {
              reset();
              toggle();
            }}
            variant="outlined"
          >
            Batal
          </Button>
          <Button variant="contained" type="submit">
            Tambah
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
}

function ClassCard({
  class_id,
  selected_class_id,
  studentClassAttendances,
  onClick,
}: {
  class_id: number;
  selected_class_id: number | null;
  studentClassAttendances: StudentClassAttendance[];
  onClick: () => void;
}) {
  const { data: class_ } = useClass(class_id);

  return (
    <Accordion
      onClick={onClick}
      sx={{
        ":hover": { bgcolor: "action.hover" },
        border: selected_class_id === class_id ? "2px solid" : undefined,
        borderColor: "primary.main",
      }}
    >
      <AccordionSummary expandIcon={<ExpandMore />}>
        <Stack sx={{ gap: 0.5 }}>
          <Stack sx={{ flexDirection: "row", gap: 2, alignItems: "center" }}>
            <Typography sx={{ fontWeight: 500 }}>
              {class_?.subject_name} - {class_?.student_group_name}
            </Typography>
            <Badge
              badgeContent={studentClassAttendances.length}
              color="primary"
            />
          </Stack>
          <Typography color="textSecondary">{class_?.teacher_name}</Typography>
        </Stack>
      </AccordionSummary>
      <AccordionDetails>
        <DataGrid
          columns={[
            {
              field: "student_name",
              display: "flex",
              flex: 1,
            },
            {
              field: "status",
              display: "flex",
              flex: 1,
              renderCell(params) {
                return getAttendanceText(params.value);
              },
            },
          ]}
          rows={studentClassAttendances}
          getRowId={(row) =>
            row.student_id + row.class_id + row.date_id + row.status
          }
          initialState={{
            pagination: {
              paginationModel: {
                pageSize: 5,
              },
            },
          }}
          disableColumnMenu
          disableRowSelectionOnClick
        />
      </AccordionDetails>
    </Accordion>
  );
}
