import AuthAPI from "@sisva/api/auth";
import UsersAP<PERSON> from "@sisva/api/users";
import type { User } from "@sisva/types/apiTypes";
import type {
  GenderText,
  NationalityText,
  ReligionText,
  UserType,
} from "@sisva/types/types";
import { getGender, getNationality, getReligion } from "@sisva/types/types";
import * as XLSX from "xlsx";

/*
# Array structure

- index 0 - 2 is the header
- index 3 - MAX_ROW is the data
- set MAX_ROW to be 1000 max
- index in data array hold this information:
  0: name
  1: username
  2: password
  3: email
  4: phone
  5: gender
  6: nationality
  7: personal_id
  8: education_id
  9: religion
  10: address
  11: profile_image_uri

*/

const MAX_ROW = 1000;

function getJsonText(data: Record<string, string>) {
  return JSON.stringify({
    email: data.email,
    phone: data.phone,
    gender: data.gender,
    nationality: data.nationality,
    address: data.address,
    religion: data.religion,
    education_id: data.education_id,
    personal_id: data.personal_id,
  });
}

function getUserByName(users: User[], name: string) {
  return users.find((user) => user.name === name);
}

function getUserByUsername(users: User[], username: string) {
  return users.find((user) => user.username === username);
}

function getUser(users: User[], user: { name: string; username: string }) {
  if (user.username) return getUserByUsername(users, user.username);
  return getUserByName(users, user.name);
}

export default function handleXLSXUploadStudent({
  file,
  onSuccess,
  onError,
}: {
  file: File;
  onSuccess: (reportText: string[]) => void;
  onError: (reportText: string[]) => void;
}) {
  const reader = new FileReader();
  const reportText: string[] = [];
  reader.onload = async (e) => {
    const file = e.target!.result;
    try {
      const users: User[] = (await UsersAPI.getAllUsers("student")).data.data;
      const filteredData = users
        .map((user) => {
          const additionalJson = (() => {
            try {
              return JSON.parse(user.detail.json_text);
            } catch (error) {
              return {};
            }
          })();
          return { ...additionalJson, ...user };
        })
        .filter((user) => user.status == "active");
      const names = filteredData.map((user) => user.name) as string[];
      const usernames = filteredData.map((user) => user.username) as string[];

      const template = XLSX.read(file);
      const sheet = template.Sheets[template.SheetNames[0]!];
      const rawData = XLSX.utils.sheet_to_json(sheet!, {
        header: 1,
      }) as unknown[][];
      const rawDataWithoutHeader = rawData
        .slice(3, MAX_ROW)
        .filter((row) => row[0] && row.length !== 0);

      const dataObject = rawDataWithoutHeader.map((row) => {
        const role: UserType = "student";
        return {
          name: row[0] as string,
          username: row[1] as string,
          type: role,
          password: row[2] as string,
          email: row[3] as string,
          phone: row[4] as string,
          gender: getGender(row[5] as GenderText),
          nationality: getNationality(row[6] as NationalityText),
          personal_id: row[7] as string,
          education_id: row[8] as string,
          religion: getReligion(row[9] as ReligionText),
          address: row[10] as string,
          profile_image_uri: row[11] as string,
        };
      });

      const dataUpdate = dataObject.filter(
        (data) =>
          names.includes(data.name) &&
          (!data.username || usernames.includes(data.username))
      );
      const dataCreate = dataObject.filter(
        (user) => !names.includes(user.name)
      );

      let countCreateUser = 0;
      for (const data of dataCreate) {
        await new Promise((resolve) => setTimeout(resolve, 100));
        const payload = {
          user: {
            name: data.name,
            type: data.type,
            detail: {
              json_text: getJsonText(data),
            },
            nik: data.personal_id ?? "",
            profile_image_uri: data.profile_image_uri,
            roles: [data.type],
            permissions: [],
          },
          password: String(data.password),
        };
        await UsersAPI.createUser(payload);
        countCreateUser += 1;
      }
      if (countCreateUser > 0)
        reportText.push(`${countCreateUser} baris user berhasil ditambahkan`);

      const promisesUpdate = dataUpdate.map((data) => {
        const payload = {
          name: data.name,
          nik: data.personal_id,
          detail: {
            json_text: getJsonText(data),
          },
          profile_image_uri: data.profile_image_uri,
        };
        return UsersAPI.updateUserById(
          payload,
          getUser(filteredData, { name: data.name, username: data.username })!
            .id
        );
      });

      const promisesUpdatePassword = dataUpdate.map((data) => {
        const payload = {
          user_id: getUser(filteredData, {
            name: data.name,
            username: data.username,
          })!.id,
          new_password: String(data.password),
        };
        return AuthAPI.resetUserPass(payload);
      });

      const res = await Promise.all([
        ...promisesUpdate,
        ...promisesUpdatePassword,
      ]);
      if (promisesUpdate.length > 0)
        reportText.push(
          `${promisesUpdate.length} baris user berhasil diupdate`
        );
      if (promisesUpdatePassword.length > 0)
        reportText.push(
          `${promisesUpdatePassword.length} baris password berhasil diupdate`
        );
      onSuccess(reportText);
    } catch (error) {
      console.log(error);
      onError(reportText);
    }
  };
  reader.readAsArrayBuffer(file);
}
