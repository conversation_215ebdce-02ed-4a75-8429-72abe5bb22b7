import { usersQueryOptions } from "@sisva/hooks/query/user/useUsers";
import { createFileRoute, notFound } from "@tanstack/react-router";

import ProfileDetailPage from "./-components/ProfileDetailPage";

export const Route = createFileRoute(
  "/(main)/_layout/dashboard/student/_auth/profile/$student_username/"
)({
  async beforeLoad({ context: { queryClient }, params: { student_username } }) {
    const users = await queryClient.fetchQuery(usersQueryOptions);
    const student = users.find((user) => user.username === student_username);
    if (!student || student.type !== "student") {
      throw notFound();
    }
  },
  head: () => ({
    meta: [
      {
        title: "Profil Siswa | Sisva",
      },
    ],
  }),
  component: RouteComponent,
});

function RouteComponent() {
  const { student_username } = Route.useParams();
  return <ProfileDetailPage username={student_username} />;
}
