import { Box, <PERSON>, Stack, Typography } from "@mui/material";
import { usePeriodCurriculums } from "@sisva/hooks/query/academic/usePeriods";
import { useStudentsStudentGroup } from "@sisva/hooks/query/academic/useStudentGroups";
import { useUser } from "@sisva/hooks/query/user/useUsers";
import { useCurrentUser } from "@sisva/providers";
import { useSchool } from "@sisva/providers";

export default function StudiTab({ user_id }: { user_id: string }) {
  const currentUser = useCurrentUser();
  const { data: student } = useUser(user_id);
  const { data: studentGroup } = useStudentsStudentGroup(user_id);
  const { data: periodCurriculums = [] } = usePeriodCurriculums();

  const periodCurriculum = periodCurriculums.find(
    (periodCurriculum) =>
      periodCurriculum.period_id === studentGroup?.period_id &&
      periodCurriculum.study_program_id === studentGroup?.study_program_id &&
      periodCurriculum.grade === student?.detail.grade
  );

  return (
    <Stack sx={{ p: 2 }}>
      <Box
        sx={{
          display: "grid",
          gridTemplateColumns: {
            xs: "repeat(1, 1fr)",
            md: "repeat(2, 1fr)",
          },
          gap: 2,
        }}
      >
        <Stack>
          <Typography sx={{ fontWeight: 600 }}>Program Studi</Typography>
          <Typography>{studentGroup?.study_program_name}</Typography>
        </Stack>
        <Stack>
          <Typography sx={{ fontWeight: 600 }}>Tingkatan</Typography>
          <Typography>{student?.detail.grade}</Typography>
        </Stack>
        <Stack>
          <Typography sx={{ fontWeight: 600 }}>Kelas</Typography>
          <Typography>{studentGroup?.name}</Typography>
        </Stack>
        <Stack>
          <Typography sx={{ fontWeight: 600 }}>Wali Kelas</Typography>
          {(() => {
            if (
              currentUser.permissions.includes("manage_staff") ||
              currentUser.roles.includes("admin")
            )
              return (
                studentGroup?.detail.homeroom_teacher_id && (
                  <TeacherLink
                    teacher_id={studentGroup?.detail?.homeroom_teacher_id}
                  />
                )
              );

            return (
              <Typography>
                {studentGroup?.detail.homeroom_teacher_name}
              </Typography>
            );
          })()}
        </Stack>
        <Stack>
          <Typography sx={{ fontWeight: 600 }}>Periode</Typography>
          <Typography>{studentGroup?.period_name}</Typography>
        </Stack>
        <Stack>
          <Typography sx={{ fontWeight: 600 }}>Kurilulum</Typography>
          <Typography>{periodCurriculum?.curriculum_name}</Typography>
        </Stack>
      </Box>
    </Stack>
  );
}

function TeacherLink({ teacher_id }: { teacher_id: string }) {
  const { data: user } = useUser(teacher_id);
  const school = useSchool();

  return (
    <Link
      href={`/administration/${school.code}/dashboard/staff/profile/${user?.username}`}
    >
      <Typography>{user?.name}</Typography>
    </Link>
  );
}
