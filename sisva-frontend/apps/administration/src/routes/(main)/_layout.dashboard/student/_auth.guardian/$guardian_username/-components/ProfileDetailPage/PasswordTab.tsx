import { yupResolver } from "@hookform/resolvers/yup";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>ack, Typography } from "@mui/material";
import {
  useResetPassword,
  useUpdatePassword,
} from "@sisva/hooks/query/user/useUsers";
import {
  resetPasswordSchema,
  updatePasswordSchema,
} from "@sisva/types/formTypes";
import { PasswordElement, useForm } from "react-hook-form-mui";
import toast from "react-hot-toast";

export default function PasswordTab({ user_id }: { user_id: string }) {
  return (
    <>
      <UpdatePasswordForm user_id={user_id} />
      <Divider />
      <ResetPasswordForm user_id={user_id} />
    </>
  );
}

function UpdatePasswordForm({ user_id }: { user_id: string }) {
  const { control, handleSubmit, reset } = useForm({
    resolver: yupResolver(updatePasswordSchema),
  });
  const { mutate: updatePassword } = useUpdatePassword({
    onSuccess() {
      reset();
      toast.success("Password berhasil diubah");
    },
    onError() {
      toast.error("Password gagal diubah");
    },
  });
  return (
    <Stack
      sx={{ p: 2, gap: 2 }}
      component={"form"}
      onSubmit={handleSubmit((value) => {
        updatePassword({
          user_id: user_id,
          new_password: value.new_password,
          current_password: value.current_password,
        });
      })}
    >
      <Typography
        sx={{
          fontWeight: 600,
          fontSize: 16,
        }}
      >
        Ubah Password
      </Typography>
      <PasswordElement
        name="current_password"
        label="Password Lama"
        control={control}
      />
      <PasswordElement
        name="new_password"
        label="Password Baru"
        control={control}
      />{" "}
      <PasswordElement
        name="confirm_password"
        label="Konfirmasi Password Baru"
        control={control}
      />
      <Stack
        sx={{
          flexDirection: "row",
          justifyContent: "end",
          gap: 2,
        }}
      >
        <Button
          onClick={() => {
            reset();
          }}
          variant="outlined"
        >
          Batal
        </Button>
        <Button type="submit" variant="contained">
          Simpan
        </Button>
      </Stack>
    </Stack>
  );
}

function ResetPasswordForm({ user_id }: { user_id: string }) {
  const { control, handleSubmit, reset } = useForm({
    resolver: yupResolver(resetPasswordSchema),
  });
  const { mutate: resetPassword } = useResetPassword({
    onSuccess() {
      reset();
      toast.success("Password berhasil direset");
    },
    onError() {
      toast.error("Password gagal direset");
    },
  });
  return (
    <Stack
      sx={{ p: 2, gap: 2 }}
      component={"form"}
      onSubmit={handleSubmit((value) => {
        resetPassword({
          user_id: user_id,
          new_password: value.new_password,
        });
      })}
    >
      <Typography
        sx={{
          fontWeight: 600,
          fontSize: 16,
        }}
      >
        Reset Password
      </Typography>
      <PasswordElement
        name="new_password"
        label="Password Baru"
        control={control}
      />
      <PasswordElement
        name="confirm_password"
        label="Konfirmasi Password Baru"
        control={control}
      />
      <Stack
        sx={{
          flexDirection: "row",
          justifyContent: "end",
          gap: 2,
        }}
      >
        <Button
          onClick={() => {
            reset();
          }}
          variant="outlined"
        >
          Batal
        </Button>
        <Button type="submit" variant="contained" color="error">
          Reset
        </Button>
      </Stack>
    </Stack>
  );
}
