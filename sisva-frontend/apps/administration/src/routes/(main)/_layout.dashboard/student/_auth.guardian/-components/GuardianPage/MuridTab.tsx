import { Clear, Search, Sort } from "@mui/icons-material";
import {
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  IconButton,
  InputAdornment,
  Stack,
  Typography,
  useMediaQuery,
} from "@mui/material";
import type { GridColDef } from "@mui/x-data-grid";
import { DataGrid, useGridApiContext } from "@mui/x-data-grid";
import { useGuardians } from "@sisva/hooks/query/user/useGuardians";
import { useStudents } from "@sisva/hooks/query/user/useStudents";
import type { User } from "@sisva/types/apiTypes";
import { AvatarWithAcronymByID } from "@sisva/ui";
import { useToggle } from "ahooks";
import { useEffect } from "react";
import { useForm } from "react-hook-form";
import { SelectElement, TextFieldElement } from "react-hook-form-mui";

import DetailStudentButton from "../DetailStudentButton";
import GuardianCell from "./GuardianCell";

type Field = "mobile_column" | keyof User | "guardian" | "action";
type TypeSafeColDef = GridColDef & { field: Field };

export default function MuridTab() {
  const isMobile = useMediaQuery((theme) => theme.breakpoints.down("lg"));
  const { data: students = [] } = useStudents();
  const { data: guardians = [] } = useGuardians();
  const studentsWithGrade = students.filter((student) => student.detail.grade);

  const columns: TypeSafeColDef[] = [
    {
      field: "mobile_column",
      headerName: "",
      valueGetter: (_, row) => row,
      flex: 1,
      display: "flex",
      renderCell: (args) => {
        const value = args.value as User;
        return (
          <Stack sx={{ p: 1.5, gap: 2, width: "100%" }}>
            <Stack
              sx={{
                flexDirection: "row",
                justifyContent: "space-between",
                width: "100%",
              }}
            >
              <Stack
                sx={{ flexDirection: "row", gap: 1, alignItems: "center" }}
              >
                <AvatarWithAcronymByID user_id={value.id} />
                <Typography sx={{ fontSize: 17 }}>{value.name}</Typography>
              </Stack>
              <Stack sx={{ gap: 1, flexDirection: "row", alignItems: "start" }}>
                <DetailStudentButton student_username={value.username} />
              </Stack>
            </Stack>
            <Stack>
              <Typography color="textSecondary">username</Typography>
              <Typography>{value.username}</Typography>
            </Stack>
            <Stack>
              <Typography color="textSecondary">Wali Siswa</Typography>
              {(() => {
                if (
                  value.detail.guardian_id &&
                  value.detail.guardian_id !==
                    "00000000-0000-0000-0000-000000000000"
                ) {
                  return <GuardianCell user_id={value.detail.guardian_id} />;
                } else {
                  return <Typography>-</Typography>;
                }
              })()}
            </Stack>
          </Stack>
        );
      },
    },
    {
      field: "name",
      headerName: "Nama",
      display: "flex",
      flex: 1,
      renderCell: ({ row }: { row: User }) => (
        <Stack
          sx={{ py: 1.3, flexDirection: "row", alignItems: "center", gap: 2 }}
        >
          <AvatarWithAcronymByID user_id={row.id} size={40} />
          {row.name}
        </Stack>
      ),
    },
    {
      field: "username",
      headerName: "Username",
      display: "flex",
      flex: 1,
      valueGetter: (_, row: User) => row.username,
      renderCell: ({ value }) => <Stack sx={{ py: 1.3 }}>{value}</Stack>,
    },
    {
      field: "guardian",
      headerName: "Wali Siswa",
      display: "flex",
      flex: 1,
      valueGetter: (_, row: User) =>
        guardians.find((guardian) => guardian.id === row.detail.guardian_id)
          ?.name,
      renderCell: ({ row }: { row: User }) => (
        <Stack sx={{ py: 1.3 }}>
          {row.detail.guardian_id &&
            row.detail.guardian_id !==
              "00000000-0000-0000-0000-000000000000" && (
              <GuardianCell user_id={row.detail.guardian_id} />
            )}
        </Stack>
      ),
    },
    {
      field: "action",
      headerName: "Aksi",
      width: 128,
      display: "flex",
      renderCell: ({ row }: { row: User }) => (
        <Stack sx={{ flexDirection: "row", gap: 1, py: 1.5 }}>
          <DetailStudentButton student_username={row.username} />
        </Stack>
      ),
      sortable: false,
    },
  ];

  const columnVisibilityModel = Object.fromEntries(
    columns
      .map((column) => column.field)
      .filter((field) =>
        isMobile ? field !== "mobile_column" : field === "mobile_column"
      )
      .map((field) => [field, false])
  );

  return (
    <DataGrid
      sx={{
        "& .MuiDataGrid-cell:focus": {
          outline: " none",
        },
        border: "none",
      }}
      getRowId={(row: User) => `${row.id}`}
      disableRowSelectionOnClick
      className="bg-white grow"
      rows={studentsWithGrade}
      columns={columns}
      initialState={{ pagination: { paginationModel: { pageSize: 20 } } }}
      pageSizeOptions={[10, 20, 50]}
      getRowClassName={(params) =>
        params.indexRelativeToCurrentPage % 2 === 0
          ? "bg-neutral-50"
          : "bg-neutral-100"
      }
      slots={{
        toolbar: CustomToolbar,
      }}
      getRowHeight={() => "auto"}
      columnVisibilityModel={columnVisibilityModel}
      columnHeaderHeight={isMobile ? 0 : undefined}
      disableColumnMenu
      disableColumnFilter
    />
  );
}

function CustomToolbar() {
  const api = useGridApiContext();
  const [open, { toggle }] = useToggle(false);

  const { control, handleSubmit, reset, setValue, watch } = useForm<{
    field: Field | "";
    sortDirection: "asc" | "desc";
    quickFilter: string;
  }>({
    defaultValues: {
      field: "",
      sortDirection: "asc",
      quickFilter: "",
    },
  });

  const sortSelectOptions: { label: string; id: Field }[] = [
    { label: "Nama", id: "name" },
    { label: "Username", id: "username" },
    { label: "Wali Siswa", id: "guardian" },
  ];

  const quickFilter = watch("quickFilter");
  useEffect(() => {
    api.current.setQuickFilterValues(
      quickFilter.split(" ").filter((s) => s !== "")
    );
  }, [api, quickFilter]);

  return (
    <div className="p-4 flex flex-col gap-2 border-b border-0 border-solid border-neutral-200">
      <div className=" flex justify-between ">
        <Stack sx={{ flexDirection: "row", gap: 2 }}>
          <TextFieldElement
            control={control}
            name="quickFilter"
            placeholder="Cari Siswa"
            size="small"
            slotProps={{
              input: {
                endAdornment: (
                  <InputAdornment position="end">
                    {quickFilter ? (
                      <IconButton
                        size="small"
                        onClick={() => setValue("quickFilter", "")}
                      >
                        <Clear />
                      </IconButton>
                    ) : (
                      <Search />
                    )}
                  </InputAdornment>
                ),
              },
            }}
          />
        </Stack>
      </div>
      <Button
        startIcon={<Sort />}
        variant="outlined"
        onClick={toggle}
        sx={{
          display: { xs: "flex", lg: "none" },
        }}
      >
        Urutkan Data
      </Button>
      <Dialog open={open} onClose={toggle}>
        <DialogTitle>Urutkan</DialogTitle>
        <DialogContent
          sx={{
            width: "250px",
            display: "flex",
            flexDirection: "column",
            gap: 2,
            overflow: "initial",
          }}
        >
          <SelectElement
            control={control}
            name="field"
            options={sortSelectOptions}
            label="Pilih Kolom"
            slotProps={{
              input: {
                endAdornment: watch("field") && (
                  <InputAdornment position="end">
                    <IconButton onClick={() => setValue("field", "")}>
                      <Clear />
                    </IconButton>
                  </InputAdornment>
                ),
              },
            }}
          />
          <SelectElement
            control={control}
            name="sortDirection"
            options={[
              { label: "A-Z", id: "asc" },
              { label: "Z-A", id: "desc" },
            ]}
            label="Urutan"
          />
        </DialogContent>
        <DialogActions sx={{ pb: 2, px: 3 }}>
          <Button
            variant="outlined"
            onClick={() => {
              reset();
              toggle();
            }}
          >
            Batal
          </Button>
          <Button
            variant="contained"
            onClick={handleSubmit((value) => {
              if (value.field) {
                api.current.sortColumn(value.field, value.sortDirection);
              } else {
                api.current.sortColumn("name", null);
              }
              toggle();
            })}
          >
            Simpan
          </Button>
        </DialogActions>
      </Dialog>
    </div>
  );
}
