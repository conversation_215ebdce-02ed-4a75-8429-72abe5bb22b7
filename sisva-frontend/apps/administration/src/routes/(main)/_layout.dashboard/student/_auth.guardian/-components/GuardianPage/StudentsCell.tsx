import { Chip } from "@mui/material";
import { useGuardiansStudents } from "@sisva/hooks/query/user/useGuardians";

export default function StudentsCell({ user_id }: { user_id: string }) {
  const { data: students = [] } = useGuardiansStudents(user_id);

  return (
    <div className="flex gap-1 py-2 size-full items-center flex-wrap">
      {students.map(({ id, name }) => (
        <Chip key={id} label={name} color="primary" />
      ))}
    </div>
  );
}
