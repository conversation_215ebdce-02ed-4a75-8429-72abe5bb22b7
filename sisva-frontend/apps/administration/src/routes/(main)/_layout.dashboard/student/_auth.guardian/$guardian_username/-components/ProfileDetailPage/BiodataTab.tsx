import { yupResolver } from "@hookform/resolvers/yup";
import { BorderColorRounded } from "@mui/icons-material";
import { Box, Button, Divider, Stack } from "@mui/material";
import { useUploadFile } from "@sisva/hooks/query/file/useFiles";
import { useUpdateGuardian } from "@sisva/hooks/query/user/useGuardians";
import { useUser } from "@sisva/hooks/query/user/useUsers";
import {
  educationLevelOptions,
  genderOptions,
  incomeLevelOptions,
  lifeStatusOptions,
  nationalityOptions,
  religionOptions,
} from "@sisva/types/dropdownOptions";
import { updateGuardianSchema } from "@sisva/types/formTypes";
import { AvatarWithAcronym } from "@sisva/ui";
import { useToggle } from "ahooks";
import { SelectElement, TextFieldElement } from "react-hook-form-mui";
import { useForm } from "react-hook-form-mui";
import toast from "react-hot-toast";

export default function BiodataTab({ user_id }: { user_id: string }) {
  const { data: user } = useUser(user_id);
  const { mutateAsync: uploadFile } = useUploadFile();
  const jsonText = (() => {
    try {
      return JSON.parse(user!.detail.json_text);
    } catch (error) {
      return {};
    }
  })();

  const [edit, { toggle }] = useToggle(false);
  const { control, handleSubmit, reset, watch, setValue } = useForm({
    values: {
      name: user?.name ?? "",
      email: jsonText.email ?? "",
      phone: jsonText.phone ?? "",
      gender: jsonText.gender ?? "",
      nationality: jsonText.nationality ?? "",
      nik: user?.nik ?? "",
      religion: jsonText.religion ?? "",
      address: jsonText.address ?? "",
      profile_image_uri: user?.profile_image_uri ?? "",
      occupation: jsonText.occupation ?? "",
      education_level: jsonText.education_level ?? "",
      income_level: jsonText.income_level ?? "",
      life_status: jsonText.life_status ?? "",
      birth_year: jsonText.birth_year ?? "",
    },
    resolver: yupResolver(updateGuardianSchema),
  });
  const { mutate: updateGuardian } = useUpdateGuardian(user_id, {
    onSuccess() {
      toggle();
      toast.success("Biodata berhasil diubah");
    },
    onError() {
      toast.error("Biodata gagal diubah");
    },
  });

  const commonProps = {
    slotProps: {
      input: {
        readOnly: !edit,
        ...(edit ? {} : { disableUnderline: true }),
      },
      inputLabel: {
        sx: {
          fontSize: 16,
        },
      },
      select: {
        IconComponent: edit ? undefined : () => null,
      },
    },
    variant: edit ? undefined : ("standard" as const),
  };

  return (
    <Stack
      sx={{ p: 2, gap: 2 }}
      component={"form"}
      onSubmit={handleSubmit((value) => {
        updateGuardian(value);
      })}
    >
      <Stack
        sx={{
          gap: 2,
          alignItems: "center",
          flexDirection: "row",
          justifyContent: "center",
        }}
      >
        <AvatarWithAcronym
          name={user?.name}
          uri={watch("profile_image_uri")}
          size={64}
        />
        {edit && (
          <Stack sx={{ gap: 1 }}>
            <Button component="label" variant="contained" size="small">
              Ubah Foto Profil
              <input
                accept="image/*"
                className="hidden"
                type="file"
                onChange={async (event) => {
                  if (!event.target.files?.[0]) return;
                  const formData = new FormData();
                  formData.append("file", event.target.files[0]);
                  const uri = await uploadFile(formData);
                  setValue("profile_image_uri", uri);
                }}
                multiple
              />
            </Button>
            <Button
              variant="outlined"
              size="small"
              color="error"
              onClick={() => {
                setValue("profile_image_uri", "");
              }}
            >
              Hapus
            </Button>
          </Stack>
        )}
      </Stack>
      <Divider />
      <Box
        sx={{
          display: "grid",
          gridTemplateColumns: {
            xs: "repeat(1, 1fr)",
            md: "repeat(2, 1fr)",
          },
          gap: 2,
        }}
      >
        <TextFieldElement
          name="email"
          label="Email"
          control={control}
          {...commonProps}
        />
        <TextFieldElement
          name="phone"
          label="Nomor Telepon"
          control={control}
          {...commonProps}
        />
        <SelectElement
          name="gender"
          label="Jenis Kelamin"
          control={control}
          options={genderOptions.map((option) => ({
            label: option.label,
            id: option.value,
          }))}
          {...commonProps}
        />
        <SelectElement
          name="nationality"
          label="Kewarganegaraan"
          control={control}
          options={nationalityOptions.map((option) => ({
            label: option.label,
            id: option.value,
          }))}
          {...commonProps}
        />
        <TextFieldElement
          name="nik"
          label="NIK"
          control={control}
          {...commonProps}
        />
        <SelectElement
          name="religion"
          label="Agama"
          control={control}
          options={religionOptions.map((option) => ({
            label: option.label,
            id: option.value,
          }))}
          {...commonProps}
        />
        <TextFieldElement
          name="address"
          label="Alamat"
          control={control}
          {...commonProps}
        />
        <TextFieldElement
          name="occupation"
          label="Profesi"
          control={control}
          {...commonProps}
        />
        <SelectElement
          name="education_level"
          label="Pendidikan Terakhir"
          control={control}
          options={educationLevelOptions.map((option) => ({
            label: option.label,
            id: option.value,
          }))}
          {...commonProps}
        />
        <SelectElement
          name="income_level"
          label="Pemasukan Bulanan"
          control={control}
          options={incomeLevelOptions.map((option) => ({
            label: option.label,
            id: option.value,
          }))}
          {...commonProps}
        />
        <SelectElement
          name="life_status"
          label="Status"
          control={control}
          options={lifeStatusOptions.map((option) => ({
            label: option.label,
            id: option.value,
          }))}
          {...commonProps}
        />
        <TextFieldElement
          name="birth_year"
          label="Tahun Lahir"
          control={control}
          {...commonProps}
        />
      </Box>

      <Stack
        sx={{
          flexDirection: "row",
          justifyContent: "end",
          gap: 2,
        }}
      >
        <Button
          onClick={() => {
            toggle();
            reset();
          }}
          startIcon={edit ? undefined : <BorderColorRounded />}
          variant="outlined"
        >
          {edit ? "Batal" : "Edit"}
        </Button>
        <Button
          type="submit"
          variant="contained"
          sx={{ display: edit ? undefined : "none" }}
        >
          Simpan
        </Button>
      </Stack>
    </Stack>
  );
}
