import { BorderColor } from "@mui/icons-material";
import { IconButton, Stack, Typography } from "@mui/material";
import { useUser } from "@sisva/hooks/query/user/useUsers";
import { AvatarWithAcronymByID } from "@sisva/ui";
import { Link } from "@tanstack/react-router";

export default function WaliMuridTab({ user_id }: { user_id: string }) {
  const { data: student } = useUser(user_id);
  const { data: guardian } = useUser(student?.detail?.guardian_id);

  // HACK: BE default guardian_id is 00000000-0000-0000-0000-000000000000 instead of null or empty string
  if (
    !student?.detail.guardian_id ||
    student?.detail.guardian_id === "00000000-0000-0000-0000-000000000000"
  )
    return (
      <Stack sx={{ p: 2 }}>
        <Typography color="textSecondary">Data tidak tersedia</Typography>
      </Stack>
    );

  return (
    <Stack sx={{ p: 2 }}>
      {guardian && (
        <Stack sx={{ flexDirection: "row", alignItems: "center", gap: 1 }}>
          <AvatarWithAcronymByID user_id={guardian.id} size={50} />
          <Stack>
            <Typography>{guardian?.name}</Typography>
            <Typography color="textSecondary" sx={{ fontSize: 14 }}>
              {guardian?.username}
            </Typography>
          </Stack>
          <Link
            to="/dashboard/student/guardian/$guardian_username"
            params={{
              guardian_username: guardian?.username,
            }}
          >
            <IconButton color="primary">
              <BorderColor />
            </IconButton>
          </Link>
        </Stack>
      )}
    </Stack>
  );
}
