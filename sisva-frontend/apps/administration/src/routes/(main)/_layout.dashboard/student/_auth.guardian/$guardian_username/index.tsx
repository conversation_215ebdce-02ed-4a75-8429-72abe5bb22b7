import { usersQueryOptions } from "@sisva/hooks/query/user/useUsers";
import { createFileRoute, notFound } from "@tanstack/react-router";

import ProfileDetailPage from "./-components/ProfileDetailPage";

export const Route = createFileRoute(
  "/(main)/_layout/dashboard/student/_auth/guardian/$guardian_username/"
)({
  async beforeLoad({
    context: { queryClient },
    params: { guardian_username },
  }) {
    const users = await queryClient.fetchQuery(usersQueryOptions);
    const guardian = users.find((user) => user.username === guardian_username);
    if (!guardian || guardian.type !== "student_guardian") {
      throw notFound();
    }
  },
  head: () => ({
    meta: [
      {
        title: "Profil Wali Siswa | Sisva",
      },
    ],
  }),
  component: RouteComponent,
});

function RouteComponent() {
  const { guardian_username } = Route.useParams();
  return <ProfileDetailPage guardian_username={guardian_username} />;
}
