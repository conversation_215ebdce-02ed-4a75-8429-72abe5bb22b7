import { Clear, Search, Sort } from "@mui/icons-material";
import {
  <PERSON>ton,
  <PERSON><PERSON>,
  <PERSON>alogActions,
  <PERSON>alogContent,
  <PERSON>alogTitle,
  IconButton,
  InputAdornment,
  Stack,
  Typography,
  useMediaQuery,
} from "@mui/material";
import type { GridColDef, GridFilterOperator } from "@mui/x-data-grid";
import { DataGrid, useGridApiContext } from "@mui/x-data-grid";
import type { StudentWithStudentGroup } from "@sisva/hooks/query/user/useStudents";
import { useStudentsWithStudentGroup } from "@sisva/hooks/query/user/useStudents";
import { onlyUnique } from "@sisva/utils";
import { useToggle } from "ahooks";
import { useEffect } from "react";
import { useForm } from "react-hook-form";
import {
  AutocompleteElement,
  SelectElement,
  TextFieldElement,
} from "react-hook-form-mui";

type Field =
  | "mobile_column"
  | keyof StudentWithStudentGroup
  | typeof filter_field
  | "grade"
  | "student_group_name"
  | "period_name";
type TypeSafeColDef = GridColDef & { field: Field };

const filter_field = "filter_field";
const field = {
  filter1: "grade",
  filter2: "student_group_name",
  filter3: "period_name",
} satisfies Record<string, Field | "start_date" | "end_date">;

type FilterValue = Record<(typeof field)[keyof typeof field], string>;

export default function KelasTab() {
  const isMobile = useMediaQuery((theme) => theme.breakpoints.down("lg"));
  const { data: students } = useStudentsWithStudentGroup();

  const customOperator: GridFilterOperator<StudentWithStudentGroup> = {
    label: "Custom Operator",
    value: "customOperator",
    getApplyFilterFn: (filterItem, column) => {
      if (!filterItem.field || !filterItem.value || !filterItem.operator) {
        return null;
      }
      const filterValue = filterItem.value as FilterValue;

      return (value, row, column, apiRef) => {
        let pass = true;
        if (filterValue.grade) {
          if (row.detail.grade !== filterValue.grade) pass = false;
        }
        if (filterValue.student_group_name) {
          if (row.student_group?.name !== filterValue.student_group_name)
            pass = false;
        }
        if (filterValue.period_name) {
          if (row.student_group?.period_name !== filterValue.period_name)
            pass = false;
        }
        return pass;
      };
    },
  };

  const columns: TypeSafeColDef[] = [
    {
      field: "mobile_column",
      headerName: "",
      valueGetter: (_, row) => row,
      flex: 1,
      display: "flex",
      renderCell: (args) => {
        const value = args.value as StudentWithStudentGroup;
        return (
          <Stack sx={{ p: 1.5, gap: 2, width: "100%" }}>
            <Stack
              sx={{
                flexDirection: "row",
                justifyContent: "space-between",
                width: "100%",
              }}
            >
              <Typography sx={{ fontWeight: 600 }}>Tingkatan</Typography>
              <Typography>{value.detail.grade}</Typography>
            </Stack>
            <Stack
              sx={{
                flexDirection: "row",
                justifyContent: "space-between",
                width: "100%",
              }}
            >
              <Typography sx={{ fontWeight: 600 }}>Kelas</Typography>
              <Typography>{value.student_group?.name}</Typography>
            </Stack>
            <Stack
              sx={{
                flexDirection: "row",
                justifyContent: "space-between",
                width: "100%",
              }}
            >
              <Typography sx={{ fontWeight: 600 }}>Nama</Typography>
              <Typography>{value.name}</Typography>
            </Stack>
          </Stack>
        );
      },
    },
    {
      field: "period_name",
      headerName: "Periode",
      display: "flex",
      valueGetter: (_, row: StudentWithStudentGroup) =>
        row.student_group?.period_name,
      renderCell: ({ value }) => <Stack sx={{ py: 1.3 }}>{value}</Stack>,
    },
    {
      field: "grade",
      headerName: "Tingkatan",
      display: "flex",
      valueGetter: (_, row: StudentWithStudentGroup) => row.detail.grade,
      renderCell: ({ value }) => <Stack sx={{ py: 1.3 }}>{value}</Stack>,
    },
    {
      field: "student_group_name",
      headerName: "Kelas",
      display: "flex",
      valueGetter: (_, row: StudentWithStudentGroup) => row.student_group?.name,
      renderCell: ({ value }) => <Stack sx={{ py: 1.3 }}>{value}</Stack>,
    },
    {
      field: "name",
      headerName: "Nama",
      display: "flex",
      flex: 1,
      renderCell: ({ row }: { row: StudentWithStudentGroup }) => (
        <Stack
          sx={{ py: 1.3, flexDirection: "row", alignItems: "center", gap: 2 }}
        >
          {row.name}
        </Stack>
      ),
    },
    {
      field: filter_field,
      filterOperators: [customOperator],
    },
  ];

  const columnVisibilityModel = Object.fromEntries(
    columns
      .map((column) => column.field)
      .filter((field) =>
        isMobile ? field !== "mobile_column" : field === "mobile_column"
      )
      .map((field) => [field, false])
  );
  columnVisibilityModel[filter_field] = false;

  return (
    <DataGrid
      sx={{
        "& .MuiDataGrid-cell:focus": {
          outline: " none",
        },
        border: "none",
      }}
      getRowId={(row: StudentWithStudentGroup) => `${row.id}`}
      disableRowSelectionOnClick
      className="bg-white grow"
      rows={students}
      columns={columns}
      initialState={{ pagination: { paginationModel: { pageSize: 20 } } }}
      pageSizeOptions={[10, 20, 50]}
      getRowClassName={(params) =>
        params.indexRelativeToCurrentPage % 2 === 0
          ? "bg-neutral-50"
          : "bg-neutral-100"
      }
      slots={{
        toolbar: CustomToolbar,
      }}
      getRowHeight={() => "auto"}
      columnVisibilityModel={columnVisibilityModel}
      columnHeaderHeight={isMobile ? 0 : undefined}
      disableColumnMenu
      disableColumnFilter
    />
  );
}

function CustomToolbar() {
  const api = useGridApiContext();
  const [open, { toggle }] = useToggle(false);

  const { control, handleSubmit, reset, setValue, watch } = useForm<{
    field: Field | "";
    sortDirection: "asc" | "desc";
    quickFilter: string;
    [field.filter1]: string;
    [field.filter2]: string;
    [field.filter3]: string;
  }>({
    defaultValues: {
      field: "",
      sortDirection: "asc",
      quickFilter: "",
      [field.filter1]: "",
      [field.filter2]: "",
      [field.filter3]: "",
    },
  });

  const filter1 = watch(field.filter1);
  const filter2 = watch(field.filter2);
  const filter3 = watch(field.filter3);

  const rows = Array.from(api.current.getRowModels()).map(
    ([key, value]) => value
  ) as StudentWithStudentGroup[];
  const periodNames = rows
    .map((row) => row.student_group?.period_name)
    .filter((item) => item)
    .filter(onlyUnique);
  const grades = rows
    .filter((row) =>
      filter3 ? filter3 === row.student_group?.period_name : true
    )
    .map((row) => row.detail.grade)
    .filter((grade) => grade)
    .filter(onlyUnique);
  const studentGroupNames = rows
    .filter((row) =>
      filter3 ? filter3 === row.student_group?.period_name : true
    )
    .filter((row) => (filter1 ? filter1 === row.student_group?.grade : true))
    .map((row) => row.student_group?.name)
    .filter((a) => a)
    .filter(onlyUnique);

  // apply filter
  useEffect(() => {
    const value: FilterValue = {
      [field.filter1]: filter1,
      [field.filter2]: filter2,
      [field.filter3]: filter3,
    };
    api.current.upsertFilterItem({
      field: filter_field,
      operator: "customOperator",
      value,
    });
  }, [api, filter1, filter2, filter3]);

  // reset filter2 when filter1 is changed
  useEffect(() => {
    setValue(field.filter2, "");
  }, [filter1, setValue]);

  // reset fitler1 and filter2 when filter3 is changed
  useEffect(() => {
    setValue(field.filter1, "");
    setValue(field.filter2, "");
  }, [filter3, setValue]);

  const sortSelectOptions: { label: string; id: Field }[] = [
    { label: "Periode", id: "period_name" },
    { label: "Tingkatan", id: "grade" },
    { label: "Kelas", id: "student_group_name" },
    { label: "Nama", id: "name" },
  ];

  const quickFilter = watch("quickFilter");
  useEffect(() => {
    api.current.setQuickFilterValues(
      quickFilter.split(" ").filter((s) => s !== "")
    );
  }, [api, quickFilter]);

  return (
    <div className="p-4 flex flex-col gap-2 border-b border-0 border-solid border-neutral-200">
      <div className=" flex justify-between gap-4 items-center thick-scrollbar">
        <Stack
          sx={{
            overflow: {
              xs: "auto",
              lg: "initial",
            },
            py: {
              xs: 1,
              lg: 0,
            },
          }}
        >
          <Stack sx={{ flexDirection: "row", gap: 2 }}>
            <TextFieldElement
              control={control}
              name="quickFilter"
              placeholder="Cari Siswa"
              size="small"
              sx={{
                minWidth: 150,
              }}
              slotProps={{
                input: {
                  endAdornment: (
                    <InputAdornment position="end">
                      {quickFilter ? (
                        <IconButton
                          size="small"
                          onClick={() => setValue("quickFilter", "")}
                        >
                          <Clear />
                        </IconButton>
                      ) : (
                        <Search />
                      )}
                    </InputAdornment>
                  ),
                },
              }}
            />
            <AutocompleteElement
              control={control}
              name={field.filter3}
              matchId
              options={periodNames.map((item) => ({
                id: item,
                label: item,
              }))}
              label="Periode"
              autocompleteProps={{
                size: "small",
                sx: { minWidth: "180px" },
              }}
            />
            <AutocompleteElement
              control={control}
              name={field.filter1}
              matchId
              options={grades.map((item) => ({
                id: item,
                label: item,
              }))}
              label="Tingkatan"
              autocompleteProps={{
                size: "small",
                sx: { minWidth: "150px" },
              }}
            />
            <AutocompleteElement
              control={control}
              name={field.filter2}
              matchId
              options={studentGroupNames.map((item) => ({
                id: item,
                label: item,
              }))}
              label="Kelas"
              autocompleteProps={{
                size: "small",
                sx: { minWidth: "180px" },
              }}
            />
          </Stack>
        </Stack>
      </div>
      <Button
        startIcon={<Sort />}
        variant="outlined"
        onClick={toggle}
        sx={{
          display: { xs: "flex", lg: "none" },
        }}
      >
        Urutkan Data
      </Button>
      <Dialog open={open} onClose={toggle}>
        <DialogTitle>Urutkan</DialogTitle>
        <DialogContent
          sx={{
            width: "250px",
            display: "flex",
            flexDirection: "column",
            gap: 2,
            overflow: "initial",
          }}
        >
          <SelectElement
            control={control}
            name="field"
            options={sortSelectOptions}
            label="Pilih Kolom"
            slotProps={{
              input: {
                endAdornment: watch("field") && (
                  <InputAdornment position="end">
                    <IconButton onClick={() => setValue("field", "")}>
                      <Clear />
                    </IconButton>
                  </InputAdornment>
                ),
              },
            }}
          />
          <SelectElement
            control={control}
            name="sortDirection"
            options={[
              { label: "A-Z", id: "asc" },
              { label: "Z-A", id: "desc" },
            ]}
            label="Urutan"
          />
        </DialogContent>
        <DialogActions sx={{ pb: 2, px: 3 }}>
          <Button
            variant="outlined"
            onClick={() => {
              reset();
              toggle();
            }}
          >
            Batal
          </Button>
          <Button
            variant="contained"
            onClick={handleSubmit((value) => {
              if (value.field) {
                api.current.sortColumn(value.field, value.sortDirection);
              } else {
                api.current.sortColumn("name", null);
              }
              toggle();
            })}
          >
            Simpan
          </Button>
        </DialogActions>
      </Dialog>
    </div>
  );
}
