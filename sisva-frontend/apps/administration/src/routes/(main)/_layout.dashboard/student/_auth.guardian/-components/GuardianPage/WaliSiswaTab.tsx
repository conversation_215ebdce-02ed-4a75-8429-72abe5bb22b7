import { Clear, Search, Sort } from "@mui/icons-material";
import {
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  IconButton,
  InputAdornment,
  Stack,
  Typography,
  useMediaQuery,
} from "@mui/material";
import type { GridColDef } from "@mui/x-data-grid";
import { DataGrid, useGridApiContext } from "@mui/x-data-grid";
import { useGuardians } from "@sisva/hooks/query/user/useGuardians";
import type { User } from "@sisva/types/apiTypes";
import { AvatarWithAcronym, AvatarWithAcronymByID } from "@sisva/ui";
import { useToggle } from "ahooks";
import { useEffect } from "react";
import { useForm } from "react-hook-form";
import { SelectElement, TextFieldElement } from "react-hook-form-mui";

import CreateGuardianButton from "../CreateGuardianButton";
import DeleteGuardianButton from "../DeleteGuardianButton";
import DetailGuardianButton from "../DetailGuardianButton";
import StudentsCell from "./StudentsCell";

export default function WaliSiswaTab() {
  const isMobile = useMediaQuery((theme) => theme.breakpoints.down("lg"));

  const { data: guardians = [] } = useGuardians();
  const columns: GridColDef[] = [
    {
      field: "mobile_column",
      headerName: "",
      valueGetter: (_, row) => row,
      flex: 1,
      display: "flex",
      renderCell: (args) => {
        const value = args.value as User;
        return (
          <Stack sx={{ p: 1.5, gap: 2, width: "100%" }}>
            <Stack
              sx={{
                flexDirection: "row",
                justifyContent: "space-between",
                width: "100%",
              }}
            >
              <Stack
                sx={{ flexDirection: "row", gap: 1, alignItems: "center" }}
              >
                <AvatarWithAcronym
                  uri={value.profile_image_uri}
                  name={value.name}
                />
                <Typography sx={{ fontSize: 17 }}>{value.name}</Typography>
              </Stack>
              <Stack sx={{ gap: 1, flexDirection: "row", alignItems: "start" }}>
                <DetailGuardianButton guardian_username={value.username} />
                <DeleteGuardianButton guardian_id={value.id} />
              </Stack>
            </Stack>
            <Stack>
              <Typography color="textSecondary">username</Typography>
              <Typography>{value.username}</Typography>
            </Stack>
          </Stack>
        );
      },
    },
    {
      field: "name",
      headerName: "Nama",
      display: "flex",
      flex: 1,
      renderCell: ({ row }: { row: User }) => (
        <Stack
          sx={{ py: 1.3, flexDirection: "row", alignItems: "center", gap: 2 }}
        >
          <AvatarWithAcronymByID user_id={row.id} size={40} />
          {row.name}
        </Stack>
      ),
    },
    {
      field: "username",
      headerName: "Username",
      flex: 1,
      display: "flex",
    },
    {
      field: "students",
      flex: 1,
      valueGetter: (_, row) => row.id,
      headerName: "Murid",
      renderCell: ({ value }) => <StudentsCell user_id={value} />,
      sortable: false,
    },
    {
      field: "action",
      headerName: "Aksi",
      width: 128,
      renderCell: ({ row }: { row: User }) => (
        <div className="flex gap-2 justify-center items-center size-full">
          <DetailGuardianButton guardian_username={row.username} />
          <DeleteGuardianButton guardian_id={row.id} />
        </div>
      ),
      sortable: false,
    },
  ];

  const columnVisibilityModel = Object.fromEntries(
    columns
      .map((column) => column.field)
      .filter((field) =>
        isMobile ? field !== "mobile_column" : field === "mobile_column"
      )
      .map((field) => [field, false])
  );

  return (
    <DataGrid
      sx={{
        "& .MuiDataGrid-cell:focus": {
          outline: " none",
        },
        border: "none",
      }}
      disableRowSelectionOnClick
      className="bg-white grow"
      rows={guardians}
      columns={columns}
      initialState={{ pagination: { paginationModel: { pageSize: 20 } } }}
      pageSizeOptions={[10, 20, 50]}
      getRowClassName={(params) =>
        params.indexRelativeToCurrentPage % 2 === 0
          ? "bg-neutral-50"
          : "bg-neutral-100"
      }
      slots={{
        toolbar: CustomToolbar,
      }}
      getRowHeight={() => "auto"}
      columnVisibilityModel={columnVisibilityModel}
      columnHeaderHeight={isMobile ? 0 : undefined}
      disableColumnMenu
    />
  );
}

function CustomToolbar() {
  const api = useGridApiContext();
  const [open, { toggle }] = useToggle(false);
  const { control, handleSubmit, reset, setValue, watch } = useForm<{
    field: string;
    sortDirection: "asc" | "desc";
    quickFilter: string;
  }>({
    defaultValues: {
      field: "",
      sortDirection: "asc",
      quickFilter: "",
    },
  });

  const quickFilter = watch("quickFilter");
  useEffect(() => {
    api.current.setQuickFilterValues(
      quickFilter.split(" ").filter((s) => s !== "")
    );
  }, [api, quickFilter]);

  return (
    <div className="p-4 flex flex-col gap-2 border-b border-0 border-solid border-neutral-200">
      <div className=" flex justify-between ">
        <TextFieldElement
          control={control}
          name="quickFilter"
          placeholder="Cari Wali"
          size="small"
          slotProps={{
            input: {
              endAdornment: (
                <InputAdornment position="end">
                  {quickFilter ? (
                    <IconButton
                      size="small"
                      onClick={() => setValue("quickFilter", "")}
                    >
                      <Clear />
                    </IconButton>
                  ) : (
                    <Search />
                  )}
                </InputAdornment>
              ),
            },
          }}
        />

        <CreateGuardianButton />
      </div>
      <Button
        startIcon={<Sort />}
        variant="outlined"
        onClick={toggle}
        sx={{
          display: { xs: "flex", lg: "none" },
        }}
      >
        Urutkan Data
      </Button>
      <Dialog open={open} onClose={toggle}>
        <DialogTitle>Urutkan</DialogTitle>
        <DialogContent
          sx={{
            width: "250px",
            display: "flex",
            flexDirection: "column",
            gap: 2,
            overflow: "initial",
          }}
        >
          <SelectElement
            control={control}
            name="field"
            options={[
              { label: "Nama", id: "name" },
              { label: "Username", id: "username" },
            ]}
            label="Pilih Kolom"
            slotProps={{
              input: {
                endAdornment: watch("field") && (
                  <InputAdornment position="end">
                    <IconButton onClick={() => setValue("field", "")}>
                      <Clear />
                    </IconButton>
                  </InputAdornment>
                ),
              },
            }}
          />
          <SelectElement
            control={control}
            name="sortDirection"
            options={[
              { label: "A-Z", id: "asc" },
              { label: "Z-A", id: "desc" },
            ]}
            label="Urutan"
          />
        </DialogContent>
        <DialogActions sx={{ pb: 2, px: 3 }}>
          <Button
            variant="outlined"
            onClick={() => {
              reset();
              toggle();
            }}
          >
            Batal
          </Button>
          <Button
            variant="contained"
            onClick={handleSubmit((value) => {
              if (value.field) {
                api.current.sortColumn(value.field, value.sortDirection);
              } else {
                api.current.sortColumn("name", null);
              }
              toggle();
            })}
          >
            Simpan
          </Button>
        </DialogActions>
      </Dialog>
    </div>
  );
}
