import { yup<PERSON><PERSON>olver } from "@hookform/resolvers/yup";
import { Add } from "@mui/icons-material";
import {
  Button,
  Divider,
  Modal,
  Paper,
  Stack,
  Typography,
} from "@mui/material";
import { useCreateGuardian } from "@sisva/hooks/query/user/useGuardians";
import { createGuardianSchema } from "@sisva/types/formTypes";
import { useToggle } from "ahooks";
import {
  PasswordElement,
  TextFieldElement,
  useForm,
} from "react-hook-form-mui";
import toast from "react-hot-toast";

export default function CreateGuardianButton() {
  const [visible, { toggle }] = useToggle(false);
  const { control, handleSubmit, reset } = useForm({
    resolver: yupResolver(createGuardianSchema),
  });
  const { mutate: createGuardian } = useCreateGuardian({
    onSuccess: () => {
      toggle();
      reset();
      toast.success("Wali berhasil ditambahkan");
    },
    onError: () => {
      toast.error("Wali gagal ditambahkan");
    },
  });

  return (
    <>
      <Button variant="contained" startIcon={<Add />} onClick={toggle}>
        Tambah
      </Button>
      <Modal open={visible} onClose={toggle}>
        <Stack
          sx={{
            width: 1,
            height: 1,
            justifyContent: "center",
            alignItems: "center",
            p: 2,
          }}
        >
          <Paper
            component={"form"}
            sx={{
              py: 2,
              width: 1,
              maxWidth: "ms",
              gap: 2,
              display: "flex",
              flexDirection: "column",
            }}
            onSubmit={handleSubmit((value) => {
              createGuardian(value);
            })}
          >
            <Typography sx={{ fontWeight: 600, px: 2 }}>Tambah Wali</Typography>
            <Divider />
            <Stack sx={{ gap: 2, px: 2 }}>
              <TextFieldElement control={control} name="name" label="Nama" />
              <PasswordElement
                control={control}
                name="password"
                label="Password"
              />
              <PasswordElement
                control={control}
                name="confirm_password"
                label="Konfirmasi Password"
              />
            </Stack>
            <Divider />
            <Stack sx={{ gap: 2, px: 2, flexDirection: "row" }}>
              <Button variant="outlined" fullWidth onClick={toggle}>
                Batal
              </Button>
              <Button type="submit" variant="contained" fullWidth>
                Simpan
              </Button>
            </Stack>
          </Paper>
        </Stack>
      </Modal>
    </>
  );
}
