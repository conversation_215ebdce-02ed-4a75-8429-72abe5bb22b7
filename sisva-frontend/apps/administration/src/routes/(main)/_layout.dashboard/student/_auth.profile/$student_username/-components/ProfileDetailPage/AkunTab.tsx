import { yupResolver } from "@hookform/resolvers/yup";
import { BorderColorRounded } from "@mui/icons-material";
import { Button, Stack } from "@mui/material";
import { useUpdateStudent } from "@sisva/hooks/query/user/useStudents";
import { useUser } from "@sisva/hooks/query/user/useUsers";
import { updateStudentSchema } from "@sisva/types/formTypes";
import { useToggle } from "ahooks";
import { TextFieldElement } from "react-hook-form-mui";
import { useForm } from "react-hook-form-mui";
import toast from "react-hot-toast";

export default function AkunTab({ user_id }: { user_id: string }) {
  const { data: user } = useUser(user_id);
  const [edit, { toggle }] = useToggle(false);
  const { control, handleSubmit, reset } = useForm({
    values: {
      name: user?.name ?? "",
    },
    resolver: yupResolver(updateStudentSchema),
  });
  const { mutate: updateStudent } = useUpdateStudent(user_id, {
    onSuccess() {
      toggle();
      toast.success("Siswa berhasil diperbarui");
    },
    onError() {
      toast.error("Siswa gagal diperbarui");
    },
  });

  return (
    <Stack
      sx={{ p: 2, gap: 2 }}
      component={"form"}
      onSubmit={handleSubmit((value) => {
        updateStudent(value);
      })}
    >
      <TextFieldElement
        name="name"
        label="Nama"
        control={control}
        variant={edit ? undefined : "standard"}
        slotProps={{
          input: {
            readOnly: !edit,
            ...(edit ? {} : { disableUnderline: true }),
          },
          inputLabel: {
            sx: {
              fontSize: 16,
            },
          },
        }}
      />
      <Stack
        sx={{
          flexDirection: "row",
          justifyContent: "end",
          gap: 2,
        }}
      >
        <Button
          onClick={() => {
            toggle();
            reset();
          }}
          startIcon={edit ? undefined : <BorderColorRounded />}
          variant="outlined"
        >
          {edit ? "Batal" : "Edit"}
        </Button>
        <Button
          type="submit"
          variant="contained"
          sx={{ display: edit ? undefined : "none" }}
        >
          Simpan
        </Button>
      </Stack>
    </Stack>
  );
}
