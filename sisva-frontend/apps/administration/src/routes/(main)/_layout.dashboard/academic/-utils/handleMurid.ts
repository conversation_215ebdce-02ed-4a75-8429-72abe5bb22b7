import AcademicAPI from "@sisva/api/academic";
import UsersAPI from "@sisva/api/users";
import type {
  StudentGroup,
  StudentInStudentGroup,
  User,
} from "@sisva/types/apiTypes";

import type { MuridInputData } from "./types";

function getUserByName(users: User[], name: string) {
  return users.find((user) => user.name === name);
}

function getUserByUsername(users: User[], username: string) {
  return users.find((user) => user.username === username);
}

function getUser(users: User[], user: { name: string; username: string }) {
  if (user.username) return getUserByUsername(users, user.username);
  return getUserByName(users, user.name);
}

function getStudentGroup(allStudentGroup: StudentGroup[], name: string) {
  return allStudentGroup.find((studentGroup) => studentGroup.name === name);
}

export default async function handleMurid(data: MuridInputData) {
  // students
  const allStudent: User[] = (
    await UsersAPI.getAllUsers("student")
  ).data.data.filter((student: User) => student.status === "active");
  const studentNames = allStudent.map((student) => student.name);
  const studentUsernames = allStudent.map((student) => student.username);

  // student groups
  const allStudentGroup: StudentGroup[] = (
    await AcademicAPI.getAllStudentGroup()
  ).data.data;
  const studentGroupNames = allStudentGroup.map(
    (studentGroup) => studentGroup.name
  );

  // student group students
  let allStudentGroupStudent = (await AcademicAPI.getStudentsInStudentGroup())
    .data.data;

  const dataObject = data
    .map((row) => {
      return {
        nama_kelas: row[0],
        nama_siswa: row[1],
        username_siswa: row[2],
      };
    })
    .filter(
      (data) =>
        studentNames.includes(data.nama_siswa) &&
        (!data.username_siswa ||
          studentUsernames.includes(data.username_siswa)) &&
        studentGroupNames.includes(data.nama_kelas)
    );

  // remove student's student group if they have any
  for (const data of dataObject) {
    const student = getUser(allStudent, {
      name: data.nama_siswa,
      username: data.username_siswa,
    });
    const studentInStudentGroup = allStudentGroupStudent.find(
      (studentInStudentGroup) =>
        studentInStudentGroup.student_id === student?.id
    );
    if (studentInStudentGroup) {
      await AcademicAPI.removeStudentFromGroup(
        studentInStudentGroup?.student_group_id,
        {
          student_id: student?.id,
        }
      );
      // refetch student in student group
      allStudentGroupStudent = (await AcademicAPI.getStudentsInStudentGroup())
        .data.data;
    }
  }

  const updateObjects = dataObject.filter(
    (data) =>
      !allStudentGroupStudent.some((studentGroupStudent) => {
        const student = getUser(allStudent, {
          name: data.nama_siswa,
          username: data.username_siswa,
        });
        return studentGroupStudent.student_id === student?.id;
      })
  );

  const promisesUpdate = updateObjects.map((data) => {
    const student = getUser(allStudent, {
      name: data.nama_siswa,
      username: data.username_siswa,
    });
    const studentGroup = getStudentGroup(allStudentGroup, data.nama_kelas);
    const payload = {
      student_id: student?.id,
    };
    return AcademicAPI.insertStudentToStudentGroup(studentGroup?.id, payload);
  });

  const res = await Promise.all(promisesUpdate);
  const reportText: string[] = [];
  if (promisesUpdate.length)
    reportText.push(`${promisesUpdate.length} baris Murid berhasil diperbarui`);
  return reportText.join(", ");
}
