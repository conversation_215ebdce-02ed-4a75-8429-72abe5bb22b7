import { DeleteForever } from "@mui/icons-material";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Divider,
  <PERSON>dal,
  Paper,
  Stack,
  Typography,
} from "@mui/material";
import {
  useDeleteStudyProgram,
  useStudyProgram,
} from "@sisva/hooks/query/academic/useStudyPrograms";
import { useIsStudyProgramGradeCanBeEdited } from "@sisva/hooks/utils";
import { useToggle } from "ahooks";
import type { ReactNode } from "react";
import toast from "react-hot-toast";

export default function DeleteStudyProgramButton({
  study_program_id,
  renderTrigger,
}: {
  study_program_id: number;
  renderTrigger?: (onClick: () => void) => ReactNode;
}) {
  const { data: studyProgram } = useStudyProgram(study_program_id);

  // HACK: we use this hook until BE provide `deletable` attribute to study program
  const canStudyProgramBeDeleted =
    useIsStudyProgramGradeCanBeEdited(study_program_id);

  const [visible, { toggle }] = useToggle(false);

  const { mutate: deleteStudyProgram } = useDeleteStudyProgram({
    onSuccess: () => {
      toggle();
      toast.success("Program Studi berhasil dihapus");
    },
    onError: () => {
      toast.error("Program Studi gagal dihapus");
    },
  });

  return (
    <>
      {renderTrigger ? (
        renderTrigger(toggle)
      ) : (
        <Button
          variant="contained"
          startIcon={<DeleteForever />}
          color="error"
          onClick={toggle}
        >
          Hapus
        </Button>
      )}
      <Modal open={visible} onClose={toggle}>
        <Stack
          sx={{
            width: 1,
            height: 1,
            justifyContent: "center",
            alignItems: "center",
            p: 2,
          }}
        >
          <Paper
            component="form"
            sx={{
              py: 2,
              width: 1,
              maxWidth: "ms",
              gap: 2,
              display: "flex",
              flexDirection: "column",
            }}
            onSubmit={(e) => {
              e.preventDefault();
              deleteStudyProgram(study_program_id);
            }}
          >
            <Typography sx={{ fontWeight: 600, px: 2 }}>
              Hapus Program Studi
            </Typography>
            <Divider />
            <Stack sx={{ gap: 2, px: 2 }}>
              {!canStudyProgramBeDeleted && (
                <Alert severity="warning">
                  Program Studi tidak dapat dihapus karena Program Studi ini
                  sedang digunakan.
                </Alert>
              )}
              <Stack
                sx={{ flexDirection: "row", justifyContent: "space-between" }}
              >
                <Typography sx={{ fontWeight: 600 }}>Program Studi</Typography>
                <Typography>{studyProgram?.name}</Typography>
              </Stack>
              <Stack
                sx={{ flexDirection: "row", justifyContent: "space-between" }}
              >
                <Typography sx={{ fontWeight: 600 }}>Kode</Typography>
                <Typography>{studyProgram?.code}</Typography>
              </Stack>
            </Stack>
            <Divider />
            <Stack sx={{ gap: 2, px: 2, flexDirection: "row" }}>
              <Button variant="outlined" fullWidth onClick={toggle}>
                Batal
              </Button>
              <Button
                type="submit"
                variant="contained"
                fullWidth
                color="error"
                disabled={!canStudyProgramBeDeleted}
              >
                Hapus
              </Button>
            </Stack>
          </Paper>
        </Stack>
      </Modal>
    </>
  );
}
