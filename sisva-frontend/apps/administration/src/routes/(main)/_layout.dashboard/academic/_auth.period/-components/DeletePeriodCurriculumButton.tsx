import { Delete<PERSON><PERSON>ver } from "@mui/icons-material";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  Stack,
  Typography,
} from "@mui/material";
import {
  usePeriod,
  usePeriodCurriculum,
  useRemoveCurriculumInPeriod,
} from "@sisva/hooks/query/academic/usePeriods";
import { useStudentGroups } from "@sisva/hooks/query/academic/useStudentGroups";
import type { Grade } from "@sisva/types/apiTypes";
import { getPeriodStatusText } from "@sisva/types/types";
import { useToggle } from "ahooks";
import dayjs from "dayjs";
import id from "dayjs/locale/id";
import type { ReactNode } from "react";
import toast from "react-hot-toast";
dayjs.locale(id);

export default function DeletePeriodCurriculumButton({
  periodCurriculumId,
  renderTrigger,
}: {
  periodCurriculumId: {
    curriculum_id: number;
    period_id: number;
    study_program_id: number;
    grade: Grade;
  };
  renderTrigger?: (onClick: () => void) => ReactNode;
}) {
  const [visible, { toggle }] = useToggle(false);
  const { data: periodCurriculum } = usePeriodCurriculum({
    period_id: periodCurriculumId.period_id,
    curriculum_id: periodCurriculumId.curriculum_id,
    study_program_id: periodCurriculumId.study_program_id,
    grade: periodCurriculumId.grade,
  });

  //  ──────────────── logic to decide if this can be modified ────────────────
  const { data: period } = usePeriod(periodCurriculum?.period_id);
  const { data: studentGroups = [] } = useStudentGroups();
  const filteredStudentGroups = studentGroups.filter(
    (studentGroup) =>
      studentGroup.period_id === periodCurriculumId.period_id &&
      studentGroup.study_program_id === periodCurriculumId.study_program_id &&
      studentGroup.grade === periodCurriculumId.grade
  );

  const studentGroupsLength = filteredStudentGroups.length;
  const periodInactive = period?.status === "inactive";
  //  ──────────────── logic to decide if this can be modified ────────────────

  const { mutate: removeCurriculumInPeriod } = useRemoveCurriculumInPeriod({
    onSuccess: () => {
      toggle();
      toast.success("Tingkatan pada Kurikulum berhasil dihapus");
    },
    onError: () => {
      toast.error("Tingkatan pada Kurikulum gagal dihapus");
    },
  });

  return (
    <>
      {renderTrigger ? (
        renderTrigger(toggle)
      ) : (
        <Button
          variant="contained"
          startIcon={<DeleteForever />}
          color="error"
          onClick={toggle}
        >
          Hapus
        </Button>
      )}
      <Modal open={visible} onClose={toggle}>
        <Stack
          sx={{
            width: 1,
            height: 1,
            justifyContent: "center",
            alignItems: "center",
            p: 2,
          }}
        >
          <Paper
            component="form"
            sx={{
              py: 2,
              width: 1,
              maxWidth: "ms",
              gap: 2,
              display: "flex",
              flexDirection: "column",
            }}
            onSubmit={(e) => {
              e.preventDefault();
              removeCurriculumInPeriod({
                curriculum_id: periodCurriculumId.curriculum_id,
                period_id: periodCurriculumId.period_id,
                study_program_id: periodCurriculumId.study_program_id,
                grade: periodCurriculumId.grade,
              });
            }}
          >
            <Typography sx={{ fontWeight: 600, px: 2 }}>
              Hapus Tingkatan pada Kurikulum
            </Typography>
            <Divider />
            <Stack sx={{ gap: 2, px: 2 }}>
              {(!periodInactive || studentGroupsLength > 0) && (
                <Alert severity="warning">
                  {!periodInactive &&
                    `Hanya dapat menghapus Tingkatan pada Kurikulum yang Periodenya belum aktif. `}
                  {studentGroupsLength > 0 &&
                    `Terdapat ${studentGroupsLength} Kelas yang sedang menggunakan Kurikulum dengan Tingkatan ini`}
                </Alert>
              )}
              <Stack
                sx={{ flexDirection: "row", justifyContent: "space-between" }}
              >
                <Typography sx={{ fontWeight: 600 }}>Kurikulum</Typography>
                <Typography>{periodCurriculum?.curriculum_name}</Typography>
              </Stack>
              <Stack
                sx={{ flexDirection: "row", justifyContent: "space-between" }}
              >
                <Typography sx={{ fontWeight: 600 }}>Periode</Typography>
                <Typography>{periodCurriculum?.period_name}</Typography>
              </Stack>
              <Stack
                sx={{ flexDirection: "row", justifyContent: "space-between" }}
              >
                <Typography sx={{ fontWeight: 600 }}>Status</Typography>
                <Typography>
                  {period?.status && getPeriodStatusText(period?.status)}
                </Typography>
              </Stack>
              <Stack
                sx={{ flexDirection: "row", justifyContent: "space-between" }}
              >
                <Typography sx={{ fontWeight: 600 }}>Program Studi</Typography>
                <Typography>{periodCurriculum?.study_program_name}</Typography>
              </Stack>
              <Stack
                sx={{ flexDirection: "row", justifyContent: "space-between" }}
              >
                <Typography sx={{ fontWeight: 600 }}>Tingkatan</Typography>
                <Typography>{periodCurriculum?.grade}</Typography>
              </Stack>
            </Stack>
            <Divider />
            <Stack sx={{ gap: 2, px: 2, flexDirection: "row" }}>
              <Button variant="outlined" fullWidth onClick={toggle}>
                Batal
              </Button>
              <Button
                type="submit"
                variant="contained"
                fullWidth
                color="error"
                disabled={!periodInactive || studentGroupsLength > 0}
              >
                Hapus
              </Button>
            </Stack>
          </Paper>
        </Stack>
      </Modal>
    </>
  );
}
