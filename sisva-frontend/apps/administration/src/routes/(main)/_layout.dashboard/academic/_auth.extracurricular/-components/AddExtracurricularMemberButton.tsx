import { yupResolver } from "@hookform/resolvers/yup";
import { Add } from "@mui/icons-material";
import {
  Button,
  Divider,
  Modal,
  Paper,
  Stack,
  Typography,
} from "@mui/material";
import {
  useAddExtracurricularMember,
  useEditExtracurricularMember,
  useExtracurricularMembers,
} from "@sisva/hooks/query/academic/useExtracurricularMembers";
import { useExtracurriculars } from "@sisva/hooks/query/academic/useExtracurriculars";
import { useStudents } from "@sisva/hooks/query/user/useStudents";
import { useToggle } from "ahooks";
import type { ReactNode } from "react";
import { AutocompleteElement, useForm } from "react-hook-form-mui";
import toast from "react-hot-toast";
import { number, object, string } from "yup";

export default function AddExtracurricularMemberButton({
  // if either provided, then it's edit mode
  old_extracurricular_id,
  old_student_id,
  renderTrigger,
}:
  | {
      old_extracurricular_id: number;
      old_student_id: string;
      renderTrigger?: (onClick: () => void) => ReactNode;
    }
  | {
      old_extracurricular_id?: never;
      old_student_id?: never;
      renderTrigger?: (onClick: () => void) => ReactNode;
    }) {
  const { data: extracurriculars = [] } = useExtracurriculars();
  const { data: extracurricularMembers = [] } = useExtracurricularMembers();
  const { data: students = [] } = useStudents();

  const [visible, { toggle }] = useToggle(false);
  const { control, handleSubmit, reset, watch } = useForm({
    values: old_extracurricular_id
      ? {
          extracurricular_id: old_extracurricular_id,
          student_id: old_student_id,
        }
      : undefined,
    resolver: yupResolver(
      object({
        extracurricular_id: number().required("Wajib diisi."),
        student_id: string().required("Wajib diisi."),
      })
    ),
  });

  const filteredStudents = students.filter((student) => {
    if (old_extracurricular_id) return true;
    return !extracurricularMembers.some((extracurricularMember) => {
      return (
        extracurricularMember.student_id === student.id &&
        extracurricularMember.extracurricular_id === watch("extracurricular_id")
      );
    });
  });

  const { mutate: addExtracurricularMember } = useAddExtracurricularMember({
    onSuccess: () => {
      toggle();
      reset();
      toast.success("Anggota berhasil ditambahkan");
    },
    onError: () => {
      toast.error("Anggota gagal ditambahkan");
    },
  });

  const { mutate: editExtracurricularMember } = useEditExtracurricularMember({
    onSuccess: () => {
      toggle();
      reset();
      toast.success("Anggota berhasil diperbarui");
    },
    onError: () => {
      toast.error("Anggota gagal diperbarui");
    },
  });

  return (
    <>
      {renderTrigger ? (
        renderTrigger(toggle)
      ) : (
        <Button variant="contained" startIcon={<Add />} onClick={toggle}>
          Tambah
        </Button>
      )}
      <Modal open={visible} onClose={toggle}>
        <Stack
          sx={{
            width: 1,
            height: 1,
            justifyContent: "center",
            alignItems: "center",
            p: 2,
          }}
        >
          <Paper
            component={"form"}
            sx={{
              py: 2,
              width: 1,
              maxWidth: "ms",
              gap: 2,
              display: "flex",
              flexDirection: "column",
            }}
            onSubmit={handleSubmit((value) => {
              if (old_extracurricular_id) {
                editExtracurricularMember({
                  old_extracurricular_id,
                  new_extracurricular_id: value.extracurricular_id,
                  student_id: value.student_id,
                });
              } else {
                addExtracurricularMember(value);
              }
            })}
          >
            <Typography sx={{ fontWeight: 600, px: 2 }}>
              Tambah Anggota
            </Typography>
            <Divider />
            <Stack sx={{ gap: 2, px: 2 }}>
              <AutocompleteElement
                name="extracurricular_id"
                label="Ekstrakurikuler"
                control={control}
                matchId
                options={extracurriculars.map((item) => ({
                  id: item.id,
                  label: item.name,
                }))}
              />
              <AutocompleteElement
                name="student_id"
                label="Anggota"
                control={control}
                matchId
                autocompleteProps={{
                  disabled: !watch("extracurricular_id") && !old_student_id,
                  readOnly: !!old_student_id,
                }}
                options={filteredStudents.map((item) => ({
                  id: item.id,
                  label: item.name,
                }))}
              />
            </Stack>
            <Divider />
            <Stack sx={{ gap: 2, px: 2, flexDirection: "row" }}>
              <Button variant="outlined" fullWidth onClick={toggle}>
                Batal
              </Button>
              <Button type="submit" variant="contained" fullWidth>
                Simpan
              </Button>
            </Stack>
          </Paper>
        </Stack>
      </Modal>
    </>
  );
}
