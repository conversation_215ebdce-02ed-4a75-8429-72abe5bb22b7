import { EventBusy } from "@mui/icons-material";
import { Divider } from "@mui/material";
import { getDayText } from "@sisva/types/types";
import type { ConflictingSchedule, ScheduleConflictError } from "@sisva/utils";
import dayjs from "dayjs";
import { type Toast, toast } from "react-hot-toast";

function ScheduleErrorToast({
  t,
  conflictingSchedules,
}: {
  t: Toast;
  conflictingSchedules: ConflictingSchedule[];
}) {
  return (
    <div
      className="flex flex-col gap-2 cursor-pointer"
      onClick={() => {
        toast.dismiss(t.id);
      }}
    >
      <div className="font-medium">
        <PERSON>rda<PERSON><PERSON> jadwal konflik, periksa dan sesuaikan jadwal.
      </div>
      <Divider />
      <div className="flex flex-col gap-4">
        {conflictingSchedules.map((schedule) => {
          return (
            <div key={schedule.unique_id}>
              {(() => {
                if (schedule.type === "class_schedule") {
                  return (
                    <div className="flex flex-col">
                      <div>{schedule.subject_name}</div>
                      <div>{schedule.teacher_name}</div>
                      <div className=" flex gap-2 items-center">
                        <div>
                          {getDayText(schedule.day)}
                          {", "}
                          {dayjs(schedule.start_time, "h:mm A Z").format(
                            "h:mm A"
                          )}{" "}
                          -{" "}
                          {dayjs(schedule.end_time, "h:mm A Z").format(
                            "h:mm A"
                          )}
                        </div>
                        <EventBusy color="error" fontSize="small" />
                      </div>
                    </div>
                  );
                }
                if (schedule.type === "non_learning_schedule") {
                  return (
                    <div className="flex flex-col">
                      <div>{schedule.name}</div>
                      <div className=" flex gap-2 items-center">
                        <div>
                          {getDayText(schedule.day)}
                          {", "}
                          {dayjs(schedule.start_time, "h:mm A Z").format(
                            "h:mm A"
                          )}{" "}
                          -{" "}
                          {dayjs(schedule.end_time, "h:mm A Z").format(
                            "h:mm A"
                          )}
                        </div>
                        <EventBusy color="error" fontSize="small" />
                      </div>
                    </div>
                  );
                }
              })()}
            </div>
          );
        })}
      </div>
    </div>
  );
}

export function toastScheduleError(conflictError: ScheduleConflictError) {
  return toast.error(
    (t) => (
      <ScheduleErrorToast
        t={t}
        conflictingSchedules={conflictError.conflictingSchedules}
      />
    ),
    {
      duration: Infinity,
    }
  );
}
