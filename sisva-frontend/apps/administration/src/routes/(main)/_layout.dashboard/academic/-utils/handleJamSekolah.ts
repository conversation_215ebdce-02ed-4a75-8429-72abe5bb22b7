import AcademicAPI from "@sisva/api/academic";
import type {
  Period,
  SchoolSchedules,
  StudyProgram,
} from "@sisva/types/apiTypes";
import { getDay } from "@sisva/types/types";
import { dateFromOADate } from "@sisva/utils";
import dayjs from "dayjs";
import customParseFormat from "dayjs/plugin/customParseFormat";

import type { JamSekolahInputData } from "./types";

dayjs.extend(customParseFormat);

function getStudyProgram(studyPrograms: StudyProgram[], name: string) {
  return studyPrograms.find((programStudi) => programStudi.name === name);
}

function getPeriod(periods: Period[], name: string) {
  return periods.find((period) => period.name === name);
}

export default async function handleJamSekolah(data: JamSekolahInputData) {
  // periods
  const periods: Period[] = (await AcademicAPI.getAllPeriod()).data.data;
  const periodNames = periods.map((period) => period.name);
  const periodIds = periods.map((period) => period.id);

  // school schedules
  const schoolSchedules: SchoolSchedules[] = await (async () => {
    const arrayOfSchoolSchedules: SchoolSchedules[][] = [];
    for (const id of periodIds) {
      arrayOfSchoolSchedules.push(
        (await AcademicAPI.getAllSchoolSchedules({ period_id: id })).data.data
      );
    }
    return arrayOfSchoolSchedules.flat();
  })();

  // study programs
  const studyPrograms: StudyProgram[] = (await AcademicAPI.getAllProdi()).data
    .data;
  const studyProgramNames = studyPrograms.map(
    (studyProgram) => studyProgram.name
  );

  const dataObjects = data
    .map((row) => {
      return {
        nama_periode: row[0],
        nama_program_studi: row[1],
        grade: row[2],
        day: getDay(row[3]),
        jam_mulai: dayjs(dateFromOADate(row[4])).format("h:mm A Z"),
        jam_selesai: dayjs(dateFromOADate(row[5])).format("h:mm A Z"),
      };
    })
    .filter((data) => {
      const startTime = dayjs(data.jam_mulai, "h:mm A Z");
      const endTime = dayjs(data.jam_selesai, "h:mm A Z");
      return startTime.isBefore(endTime);
    });

  const createObjects = dataObjects.filter((data) => {
    return (
      !schoolSchedules.some(
        (schoolSchedule) =>
          schoolSchedule.period_id ===
            getPeriod(periods, data.nama_periode)?.id &&
          schoolSchedule.study_program_id ===
            getStudyProgram(studyPrograms, data.nama_program_studi)?.id &&
          schoolSchedule.grade === data.grade &&
          schoolSchedule.day === data.day
      ) &&
      periodNames.includes(data.nama_periode) &&
      studyProgramNames.includes(data.nama_program_studi)
    );
  });

  const updateObjects = dataObjects.filter((data) => {
    return (
      schoolSchedules.some(
        (schoolSchedule) =>
          schoolSchedule.period_id ===
            getPeriod(periods, data.nama_periode)?.id &&
          schoolSchedule.study_program_id ===
            getStudyProgram(studyPrograms, data.nama_program_studi)?.id &&
          schoolSchedule.grade === data.grade &&
          schoolSchedule.day === data.day
      ) &&
      periodNames.includes(data.nama_periode) &&
      studyProgramNames.includes(data.nama_program_studi)
    );
  });

  const promisesCreate = createObjects.map((data) => {
    const period_id = getPeriod(periods, data.nama_periode)?.id;
    const study_program_id = getStudyProgram(
      studyPrograms,
      data.nama_program_studi
    )?.id;

    const payload = {
      period_id,
      study_program_id,
      grade: data.grade,
      day: data.day,
      start_time: data.jam_mulai,
      end_time: data.jam_selesai,
    };

    return AcademicAPI.createSchoolSchedule(payload);
  });

  const promisesUpdate = updateObjects.map((data) => {
    const period_id = getPeriod(periods, data.nama_periode)?.id;
    const study_program_id = getStudyProgram(
      studyPrograms,
      data.nama_program_studi
    )?.id;

    const school_schedule_id = schoolSchedules.find(
      (schoolSchedule) =>
        schoolSchedule.period_id ===
          getPeriod(periods, data.nama_periode)?.id &&
        schoolSchedule.study_program_id ===
          getStudyProgram(studyPrograms, data.nama_program_studi)?.id &&
        schoolSchedule.grade === data.grade &&
        schoolSchedule.day === data.day
    )?.id;

    const payload = {
      period_id,
      study_program_id,
      grade: data.grade,
      day: data.day,
      start_time: data.jam_mulai,
      end_time: data.jam_selesai,
    };

    return AcademicAPI.editSchoolSchedule(payload, school_schedule_id);
  });

  const res = await Promise.all(promisesCreate);
  const reportText: string[] = [];
  if (promisesCreate.length)
    reportText.push(
      `${promisesCreate.length} baris Jam Sekolah berhasil ditambahkan`
    );
  if (promisesUpdate.length)
    reportText.push(
      `${promisesUpdate.length} baris Jam Sekolah berhasil diperbarui`
    );
  return reportText.join(", ");
}
