import { yupResolver } from "@hookform/resolvers/yup";
import { Add } from "@mui/icons-material";
import {
  Button,
  Divider,
  Modal,
  Paper,
  Stack,
  Typography,
} from "@mui/material";
import {
  usePeriod,
  usePeriodCurriculums,
  usePeriods,
} from "@sisva/hooks/query/academic/usePeriods";
import {
  useCreateStudentGroup,
  useStudentGroup,
  useUpdateStudentGroup,
} from "@sisva/hooks/query/academic/useStudentGroups";
import { useStudyPrograms } from "@sisva/hooks/query/academic/useStudyPrograms";
import { useTeachers } from "@sisva/hooks/query/user/useTeachers";
import type { Grade } from "@sisva/types/apiTypes";
import { studentGroupSchema } from "@sisva/types/formTypes";
import { useToggle } from "ahooks";
import { sort } from "fast-sort";
import { type ReactNode, useEffect } from "react";
import {
  AutocompleteElement,
  TextFieldElement,
  useForm,
} from "react-hook-form-mui";
import toast from "react-hot-toast";

/**
 * this component has 2 modes:
 * 1. create new student_group, no props
 * 2. edit student_group, by passing student_group_id
 */
export default function CreateStudentGroupButton({
  // if student_group_id provided, then it's edit mode
  student_group_id,
  renderTrigger,
}: {
  student_group_id?: number;
  renderTrigger?: (onClick: () => void) => ReactNode;
}) {
  const { data: studentGroup } = useStudentGroup(student_group_id);
  const { data: periods = [] } = usePeriods();
  const { data: studyPrograms = [] } = useStudyPrograms();
  const { data: periodCurriculum = [] } = usePeriodCurriculums();
  const { data: teachers = [] } = useTeachers();

  const [visible, { toggle }] = useToggle(false);
  const { control, handleSubmit, reset, watch, setValue } = useForm({
    values: {
      name: studentGroup?.name ?? "",
      period_id: studentGroup?.period_id ?? 0,
      study_program_id: studentGroup?.study_program_id ?? 0,
      grade: studentGroup?.grade ?? "",
      type: studentGroup?.type ?? "homeroom",
      homeroom_teacher_id: studentGroup?.detail?.homeroom_teacher_id ?? "",
    },
    resolver: yupResolver(studentGroupSchema),
  });

  const study_program_id = watch("study_program_id");
  const period_id = watch("period_id");
  const grade = watch("grade");

  const { data: period } = usePeriod(period_id);
  const studyProgramIds = period?.study_programs?.map((item) => item.id) ?? [];
  const filteredStudyPrograms = studyPrograms.filter((item) =>
    studyProgramIds.includes(item.id)
  );

  const filteredPeriodCurriculum = periodCurriculum.filter(
    (item) =>
      item.period_id === period_id && item.study_program_id === study_program_id
  );
  const grades = filteredPeriodCurriculum.map((item) => item.grade);

  const { mutate: createStudentGroup } = useCreateStudentGroup({
    onSuccess: () => {
      toggle();
      reset();
      toast.success("Kelas berhasil dibuat");
    },
    onError: () => {
      toast.error("Kelas gagal dibuat");
    },
  });

  const { mutate: updateStudentGroup } = useUpdateStudentGroup({
    onSuccess: () => {
      toggle();
      reset();
      toast.success("Kelas berhasil diperbarui");
    },
    onError: () => {
      toast.error("Kelas gagal diperbarui");
    },
  });

  const studyProgramIdAvailable = studyProgramIds.includes(study_program_id);
  const gradeAvailable = grades.includes(grade as Grade);
  useEffect(() => {
    if (!studyProgramIdAvailable) setValue("study_program_id", 0);
    if (!gradeAvailable) setValue("grade", "");
  }, [gradeAvailable, setValue, studyProgramIdAvailable]);

  return (
    <>
      {renderTrigger ? (
        renderTrigger(toggle)
      ) : (
        <Button variant="contained" startIcon={<Add />} onClick={toggle}>
          Tambah
        </Button>
      )}
      <Modal open={visible} onClose={toggle}>
        <Stack
          sx={{
            width: 1,
            height: 1,
            justifyContent: "center",
            alignItems: "center",
            p: 2,
          }}
        >
          <Paper
            component={"form"}
            sx={{
              py: 2,
              width: 1,
              maxWidth: "ms",
              gap: 2,
              display: "flex",
              flexDirection: "column",
            }}
            onSubmit={handleSubmit((value) => {
              if (student_group_id) {
                updateStudentGroup({
                  student_group_id,
                  ...value,
                });
              } else {
                createStudentGroup(value);
              }
            })}
          >
            <Typography sx={{ fontWeight: 600, px: 2 }}>
              {(() => {
                if (student_group_id) {
                  return "Edit Kelas";
                } else {
                  return "Buat Kelas";
                }
              })()}
            </Typography>
            <Divider />
            <Stack sx={{ gap: 2, px: 2 }}>
              <TextFieldElement
                name="name"
                label="Nama Kelas"
                control={control}
              />
              <AutocompleteElement
                control={control}
                name="period_id"
                matchId
                options={sort(periods)
                  .desc((period) => period.id)
                  .map((period) => ({
                    id: period.id,
                    label: period.name,
                  }))}
                label="Periode"
                autocompleteProps={{
                  readOnly: !!student_group_id,
                }}
              />
              <AutocompleteElement
                control={control}
                name="study_program_id"
                matchId
                options={filteredStudyPrograms.map((item) => ({
                  id: item.id,
                  label: item.name,
                }))}
                label="Program Studi"
                autocompleteProps={{
                  readOnly: !!student_group_id,
                }}
              />
              <AutocompleteElement
                control={control}
                name="grade"
                matchId
                options={grades.map((item) => ({
                  id: item,
                  label: item,
                }))}
                label="Tingkatan"
                autocompleteProps={{
                  readOnly: !!student_group_id,
                }}
              />
              <AutocompleteElement
                control={control}
                name="homeroom_teacher_id"
                matchId
                options={teachers.map((item) => ({
                  id: item.id,
                  label: item.name,
                }))}
                label="Wali Kelas"
              />
            </Stack>
            <Divider />
            <Stack sx={{ gap: 2, px: 2, flexDirection: "row" }}>
              <Button variant="outlined" fullWidth onClick={toggle}>
                Batal
              </Button>
              <Button type="submit" variant="contained" fullWidth>
                Simpan
              </Button>
            </Stack>
          </Paper>
        </Stack>
      </Modal>
    </>
  );
}
