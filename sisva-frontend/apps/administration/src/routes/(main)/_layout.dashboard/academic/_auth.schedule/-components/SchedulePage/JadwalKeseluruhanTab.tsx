import { Add } from "@mui/icons-material";
import { <PERSON><PERSON>, <PERSON><PERSON>, Divider } from "@mui/material";
import {
  usePeriod,
  usePeriods,
  useSelectedPeriod,
} from "@sisva/hooks/query/academic/usePeriods";
import { useSchoolSchedules } from "@sisva/hooks/query/academic/useSchoolSchedules";
import {
  useStudentGroup,
  useStudentGroups,
} from "@sisva/hooks/query/academic/useStudentGroups";
import {
  useStudyProgram,
  useStudyPrograms,
} from "@sisva/hooks/query/academic/useStudyPrograms";
import { type Day, getDayText } from "@sisva/types/types";
import { SisvaScheduleComponent } from "@sisva/ui";
import { cn, romanToInt } from "@sisva/utils";
import { useDeepCompareEffect } from "ahooks";
import { Skeleton } from "antd";
import type { Dayjs } from "dayjs";
import dayjs from "dayjs";
import { sort } from "fast-sort";
import { type ReactNode, useEffect, useState, useTransition } from "react";
import { AutocompleteElement, useForm } from "react-hook-form-mui";

import CreateClassScheduleButton from "../CreateClassScheduleButton";
import CreateNonLearningScheduleButton from "../CreateNonLearningScheduleButton";

export default function JadwalKeseluruhanTab() {
  const { data: periods = [] } = usePeriods();
  const { data: selectedPeriod } = useSelectedPeriod();
  const { data: studyPrograms = [] } = useStudyPrograms();

  const { control, watch, setValue } = useForm<{
    period_id: number | null;
    study_program_id: number | null;
    grade: string;
    student_group_id: number | null;
    day: Day | null;
  }>({
    values: {
      period_id: selectedPeriod?.id ?? null,
      study_program_id: null,
      grade: "",
      student_group_id: null,
      day: null,
    },
  });

  const period_id = watch("period_id");
  const study_program_id = watch("study_program_id");
  const grade = watch("grade");
  const student_group_id = watch("student_group_id");
  const day = watch("day");
  const disabled = !period_id;

  const { data: studentGroup } = useStudentGroup(student_group_id);

  const { data: period } = usePeriod(period_id);
  const studyProgramIds = [
    ...new Set(period?.study_programs?.map((item) => item.id) ?? []),
  ];
  const fitleredStudyPrograms = studyPrograms.filter((item) =>
    studyProgramIds.includes(item.id)
  );

  const { data: studyProgram } = useStudyProgram(study_program_id);
  const grades = studyProgram?.grades ?? [];

  const { data: studentGroups = [] } = useStudentGroups();
  const filteredStudentGroupsOptions = studentGroups.filter(
    (item) =>
      (!period_id || item.period_id === period_id) &&
      (!study_program_id || item.study_program_id === study_program_id) &&
      (!grade || item.grade === grade)
  );

  const filteredStudentGroups = filteredStudentGroupsOptions.filter(
    (item) => !student_group_id || item.id === student_group_id
  );

  const { data: schoolSchedules = [] } = useSchoolSchedules(period_id);
  const days = [...new Set(schoolSchedules.map((item) => item.day))];

  const filteredDays = ([1, 2, 3, 4, 5, 6, 7] as Day[]).filter(
    (item) => !day || item === day
  );

  const timeMap: Record<
    Day,
    { startHour?: string; endHour?: string } | undefined
  > = {
    1: undefined,
    2: undefined,
    3: undefined,
    4: undefined,
    5: undefined,
    6: undefined,
    7: undefined,
  };

  filteredDays.forEach((day) => {
    const startHours: Dayjs[] = [];
    const endHours: Dayjs[] = [];
    filteredStudentGroups.forEach((studentGroup) => {
      const schoolSchedule = schoolSchedules.find(
        (item) =>
          item.study_program_id === studentGroup.study_program_id &&
          item.period_id === studentGroup.period_id &&
          item.grade === studentGroup.grade &&
          item.day === day
      );
      const startHour = schoolSchedule
        ? dayjs(schoolSchedule.start_time, "h:mm A Z")
        : undefined;
      const endHour = schoolSchedule
        ? dayjs(schoolSchedule.end_time, "h:mm A Z")
        : undefined;
      if (startHour) startHours.push(startHour);
      if (endHour) endHours.push(endHour);
    });
    const startHour = sort(startHours).asc("hour")[0]?.format("HH:mm");
    const endHour = sort(endHours).desc("hour")[0]?.format("HH:mm");
    if (startHour && endHour) timeMap[day] = { startHour, endHour };
  });

  // auto select student_group_id and grade when studentGroup changed
  useEffect(() => {
    if (studentGroup) {
      setValue("study_program_id", studentGroup.study_program_id);
      setValue("grade", studentGroup.grade);
    }
  }, [setValue, studentGroup]);

  //HACK: using useState and useTransition to reduce lag when rendering schedule component
  const [expensiveComponent, setExpensiveComponent] = useState<ReactNode>(null);
  const [isRendering, startIsRendering] = useTransition();
  useDeepCompareEffect(() => {
    startIsRendering(() => {
      setExpensiveComponent(
        <div className="flex-1 sm:overflow-y-auto overflow-x-auto flex flex-col min-h-[70svh] sm:min-h-0 me-4 sm:me-0">
          {filteredDays.map((day) => {
            if (!timeMap[day]) return null;
            return (
              <div key={day} className="flex flex-col w-fit py-4">
                <div className="px-4 text-2xl font-bold flex ">
                  {getDayText(day)}
                </div>
                <div className=" px-4 pb-4 gap-2 flex flex-1 ">
                  {sort(filteredStudentGroups)
                    .asc((item) => romanToInt(item.grade))
                    .map((studentGroup, i) => {
                      return (
                        <div
                          key={studentGroup.id}
                          className={cn(
                            "flex flex-col gap-2 min-w-[350px] flex-1", // min width 350px
                            "[&_.e-outer-table_>_tbody_>_tr:first-child]:hidden", // hide row "Jam" dan "Hari"
                            "[&_.e-outer-table_>_tbody_>_tr:last-child_>_td:first-child]:w-[85px]", // column jam width 85px
                            {
                              //HACK: first-child harusnya 85px lebih lebar, tapi gak tau caranya pakai flex-grow, jadi kira 1.1 atau 1.2
                              ["flex-[1.1]"]: i === 0,
                              ["flex-[1.2]"]:
                                i === 0 && filteredStudentGroups.length > 2,
                              ["min-w-[435px]"]: i === 0, // min width 350+85px
                              ["[&_.e-left-indent]:hidden"]: i !== 0, // hide column jam
                              ["[&_.e-outer-table_>_tbody_>_tr:last-child_>_td:first-child]:hidden"]:
                                i !== 0, // hide column jam
                            }
                          )}
                        >
                          <div className="font-semibold text-lg border-[0] border-b border-solid border-neutral-300">
                            {studentGroup.name}
                          </div>
                          <SisvaScheduleComponent
                            overwriteStartHour={timeMap[day]?.startHour}
                            overwriteEndHour={timeMap[day]?.endHour}
                            currentView="Day"
                            selectedDate={day as Day}
                            student_group_id={studentGroup.id}
                            renderCell={({ scheduleData, defaultRender }) => {
                              if (scheduleData.type === "non-learning-schedule")
                                return (
                                  <CreateNonLearningScheduleButton
                                    period_id={period_id}
                                    non_learning_schedule_id={scheduleData.id}
                                    renderTrigger={(onClick) => {
                                      return (
                                        <div
                                          className="cursor-pointer size-full"
                                          onClick={onClick}
                                        >
                                          {defaultRender}
                                        </div>
                                      );
                                    }}
                                  />
                                );
                              if (scheduleData.type === "class-schedule")
                                return (
                                  <CreateClassScheduleButton
                                    period_id={period_id}
                                    class_schedule_id={scheduleData.id}
                                    renderTrigger={(onClick) => {
                                      return (
                                        <div
                                          className="cursor-pointer size-full"
                                          onClick={onClick}
                                        >
                                          {defaultRender}
                                        </div>
                                      );
                                    }}
                                  />
                                );
                              return defaultRender;
                            }}
                          />
                        </div>
                      );
                    })}
                </div>
                <Divider />
              </div>
            );
          })}
        </div>
      );
    });
  }, [filteredDays, filteredStudentGroups, period_id, timeMap]);

  return (
    <div className="flex flex-col overflow-y-auto flex-1">
      <div className="flex gap-4 p-4 border-b border-0 border-solid border-neutral-200 justify-between thick-scrollbar sm:items-center flex-col sm:flex-row">
        <div className="flex gap-2 overflow-auto py-2 flex-col sm:flex-row flex-1">
          <AutocompleteElement
            control={control}
            name="period_id"
            matchId
            options={periods.map((item) => ({ id: item.id, label: item.name }))}
            label="Periode"
            autocompleteProps={{
              size: "small",
              sx: { minWidth: "180px" },
              onChange() {
                setValue("study_program_id", null);
                setValue("grade", "");
                setValue("student_group_id", null);
              },
            }}
          />
          <AutocompleteElement
            control={control}
            name="study_program_id"
            matchId
            options={fitleredStudyPrograms.map((item) => ({
              id: item.id,
              label: item.name,
            }))}
            label="Program Studi"
            autocompleteProps={{
              disabled: !period_id,
              size: "small",
              sx: { minWidth: "180px" },
              onChange() {
                setValue("grade", "");
                setValue("student_group_id", null);
              },
            }}
          />
          <AutocompleteElement
            control={control}
            name="grade"
            matchId
            options={grades.map((item) => ({
              id: item,
              label: item,
            }))}
            label="Tingkatan"
            autocompleteProps={{
              disabled: !study_program_id,
              size: "small",
              sx: { minWidth: "120px" },
              onChange() {
                setValue("student_group_id", null);
              },
            }}
          />
          <AutocompleteElement
            control={control}
            name="student_group_id"
            matchId
            options={filteredStudentGroupsOptions.map((item) => ({
              id: item.id,
              label: item.name,
            }))}
            label="Kelas"
            autocompleteProps={{
              disabled: !period_id,
              size: "small",
              sx: { minWidth: "180px" },
            }}
          />
          <AutocompleteElement
            control={control}
            name="day"
            matchId
            options={sort(days)
              .asc((item) => item)
              .map((item) => ({
                id: item,
                label: getDayText(item as Day),
              }))}
            label="Hari"
            autocompleteProps={{
              disabled: !period_id,
              size: "small",
              sx: { minWidth: "130px" },
            }}
          />
        </div>
        <div className="flex gap-2 flex-col md:flex-row">
          <CreateNonLearningScheduleButton
            period_id={period_id}
            study_program_id={study_program_id}
            grade={grade}
            renderTrigger={(onClick) => {
              return (
                <Button
                  variant="contained"
                  startIcon={<Add />}
                  onClick={onClick}
                  disabled={disabled}
                  sx={{ width: "100%", minWidth: 180 }}
                >
                  Jadwal Non KBM
                </Button>
              );
            }}
          />
          <CreateClassScheduleButton
            student_group_id={student_group_id}
            grade={grade}
            period_id={period_id}
            study_program_id={study_program_id}
            renderTrigger={(onClick) => {
              return (
                <Button
                  variant="contained"
                  startIcon={<Add />}
                  onClick={onClick}
                  disabled={disabled}
                  sx={{ width: "100%", minWidth: 150 }}
                >
                  Jadwal Kelas
                </Button>
              );
            }}
          />
        </div>
      </div>
      {disabled && (
        <div className="p-4 border-b border-0 border-solid border-neutral-200 ">
          <Alert severity="warning">
            Lengkapi filter <span className="font-medium">Periode</span>{" "}
            terlebih dahulu
          </Alert>
        </div>
      )}
      {(() => {
        if (disabled) return null;
        if (filteredStudentGroups.length === 0) {
          return (
            <div className="size-full flex flex-col justify-center items-center">
              Kelas belum tersedia
            </div>
          );
        }
        if (isRendering)
          return (
            <div className="flex justify-center items-center p-6">
              <Skeleton active />
            </div>
          );
        return expensiveComponent;
      })()}
    </div>
  );
}
