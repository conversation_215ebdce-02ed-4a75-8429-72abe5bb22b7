import { Clear, Search, Sort } from "@mui/icons-material";
import {
  <PERSON><PERSON>,
  <PERSON>,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  IconButton,
  InputAdornment,
  Stack,
  Typography,
  useMediaQuery,
} from "@mui/material";
import type { GridColDef, GridFilterOperator } from "@mui/x-data-grid";
import { DataGrid, useGridApiContext } from "@mui/x-data-grid";
import { useCurriculums } from "@sisva/hooks/query/academic/useCurriculums";
import type { Curriculum } from "@sisva/types/apiTypes";
import { onlyUnique } from "@sisva/utils";
import { useToggle } from "ahooks";
import { useEffect } from "react";
import { useForm } from "react-hook-form";
import { SelectElement, TextFieldElement } from "react-hook-form-mui";

import DeleteIcon from "#/routes/(main)/-components/DeleteIcon";
import EditIcon from "#/routes/(main)/-components/EditIcon";

import CreateCurriculumButton from "../CreateCurriculumButton";
import DeleteCurriculumButton from "../DeleteCurriculumButton";

type Field =
  | "mobile_column"
  | keyof Curriculum
  | typeof filter_field
  | "action";
type TypeSafeColDef = GridColDef & { field: Field };

const filter_field = "filter_field";
const field = {} satisfies Record<string, Field>;

type FilterValue = Record<(typeof field)[keyof typeof field], string>;

export default function KurikulumTab() {
  const isMobile = useMediaQuery((theme) => theme.breakpoints.down("lg"));
  const { data: curriculums = [] } = useCurriculums();

  const customOperator: GridFilterOperator<Curriculum> = {
    label: "Custom Operator",
    value: "customOperator",
    getApplyFilterFn: (filterItem, column) => {
      if (!filterItem.field || !filterItem.value || !filterItem.operator) {
        return null;
      }
      const filterValue = filterItem.value as FilterValue;

      return (value, row, column, apiRef) => {
        const pass = true;
        return pass;
      };
    },
  };

  const columns: TypeSafeColDef[] = [
    {
      field: "mobile_column",
      headerName: "",
      valueGetter: (_, row) => row,
      flex: 1,
      display: "flex",
      renderCell: (args) => {
        const value = args.value as Curriculum;
        return (
          <Stack sx={{ p: 1.5, gap: 1, width: "100%" }}>
            <Stack
              sx={{
                flexDirection: "row",
                justifyContent: "space-between",
                width: "100%",
              }}
            >
              <Typography sx={{ fontWeight: 600 }}>Kurikulum</Typography>
              <Typography>{value.name}</Typography>
            </Stack>
            <Stack
              sx={{
                flexDirection: "row",
                justifyContent: "space-between",
                width: "100%",
              }}
            >
              <Typography sx={{ fontWeight: 600 }}>Program Studi</Typography>
              <Stack sx={{ flexDirection: "row", gap: 1, flexWrap: "wrap" }}>
                {value.study_programs
                  ?.map((item) => item.code)
                  .filter(onlyUnique)
                  .map((code) => (
                    <Chip key={code} label={code} color="primary" />
                  ))}
              </Stack>
            </Stack>
            <Stack
              sx={{
                flexDirection: "row",
                justifyContent: "space-between",
                width: "100%",
              }}
            >
              <Typography sx={{ fontWeight: 600 }}>KKM</Typography>
              <Typography>{value.passing_grade}</Typography>
            </Stack>
            <Stack
              sx={{
                flexDirection: "row",
                justifyContent: "flex-end",
                gap: 1,
                width: "100%",
              }}
            >
              <CreateCurriculumButton
                curriculum_id={value.id}
                renderTrigger={(onClick) => <EditIcon onClick={onClick} />}
              />
              <DeleteCurriculumButton
                curriculum_id={value.id}
                renderTrigger={(onClick) => <DeleteIcon onClick={onClick} />}
              />
            </Stack>
          </Stack>
        );
      },
    },
    {
      field: "name",
      headerName: "Kurikulum",
      display: "flex",
      flex: 1,
      renderCell: ({ value }) => <Stack sx={{ py: 1.3 }}>{value}</Stack>,
    },
    {
      field: "study_programs",
      headerName: "Program Studi",
      display: "flex",
      flex: 1,
      sortable: false,
      renderCell: ({ row }: { row: Curriculum }) => (
        <Stack sx={{ py: 1.3 }}>
          <Stack sx={{ flexDirection: "row", gap: 1, flexWrap: "wrap" }}>
            {row.study_programs
              ?.map((item) => item.code)
              .filter(onlyUnique)
              .map((code) => <Chip key={code} label={code} color="primary" />)}
          </Stack>
        </Stack>
      ),
    },
    {
      field: "passing_grade",
      headerName: "KKM",
      display: "flex",
      renderCell: ({ value }) => <Stack sx={{ py: 1.3 }}>{value}</Stack>,
    },

    {
      field: "action",
      headerName: "Aksi",
      display: "flex",
      sortable: false,
      renderCell: ({ row }: { row: Curriculum }) => (
        <Stack
          sx={{ py: 1.3, flexDirection: "row", alignItems: "center", gap: 1 }}
        >
          <CreateCurriculumButton
            curriculum_id={row.id}
            renderTrigger={(onClick) => <EditIcon onClick={onClick} />}
          />
          <DeleteCurriculumButton
            curriculum_id={row.id}
            renderTrigger={(onClick) => <DeleteIcon onClick={onClick} />}
          />
        </Stack>
      ),
    },
    {
      field: filter_field,
      filterOperators: [customOperator],
    },
  ];

  const columnVisibilityModel = Object.fromEntries(
    columns
      .map((column) => column.field)
      .filter((field) =>
        isMobile ? field !== "mobile_column" : field === "mobile_column"
      )
      .map((field) => [field, false])
  );
  columnVisibilityModel[filter_field] = false;

  return (
    <DataGrid
      sx={{
        "& .MuiDataGrid-cell:focus": {
          outline: " none",
        },
        border: "none",
      }}
      getRowId={(row: Curriculum) => `${row.id}`}
      disableRowSelectionOnClick
      className="bg-white grow"
      rows={curriculums}
      columns={columns}
      initialState={{ pagination: { paginationModel: { pageSize: 20 } } }}
      pageSizeOptions={[10, 20, 50]}
      getRowClassName={(params) =>
        params.indexRelativeToCurrentPage % 2 === 0
          ? "bg-neutral-50"
          : "bg-neutral-100"
      }
      slots={{
        toolbar: CustomToolbar,
      }}
      getRowHeight={() => "auto"}
      columnVisibilityModel={columnVisibilityModel}
      columnHeaderHeight={isMobile ? 0 : undefined}
      disableColumnMenu
      disableColumnFilter
    />
  );
}

function CustomToolbar() {
  const api = useGridApiContext();
  const [open, { toggle }] = useToggle(false);

  const { control, handleSubmit, reset, setValue, watch } = useForm<{
    field: Field | "";
    sortDirection: "asc" | "desc";
    quickFilter: string;
  }>({
    defaultValues: {
      field: "",
      sortDirection: "asc",
      quickFilter: "",
    },
  });

  const rows = Array.from(api.current.getRowModels()).map(
    ([key, value]) => value
  ) as Curriculum[];

  // apply filter
  useEffect(() => {
    const value: FilterValue = {};
    api.current.upsertFilterItem({
      field: filter_field,
      operator: "customOperator",
      value,
    });
  }, [api]);

  const sortSelectOptions: { label: string; id: Field }[] = [
    { label: "Kurikulum", id: "name" },
  ];

  const quickFilter = watch("quickFilter");
  useEffect(() => {
    api.current.setQuickFilterValues(
      quickFilter.split(" ").filter((s) => s !== "")
    );
  }, [api, quickFilter]);

  return (
    <div className="p-4 flex flex-col gap-2 border-b border-0 border-solid border-neutral-200">
      <div className=" flex justify-between gap-4 items-center thick-scrollbar">
        <Stack
          sx={{
            overflow: {
              xs: "auto",
              lg: "initial",
            },
            py: {
              xs: 1,
              lg: 0,
            },
          }}
        >
          <Stack sx={{ flexDirection: "row", gap: 2 }}>
            <TextFieldElement
              control={control}
              name="quickFilter"
              placeholder="Cari"
              size="small"
              sx={{
                minWidth: 150,
              }}
              slotProps={{
                input: {
                  endAdornment: (
                    <InputAdornment position="end">
                      {quickFilter ? (
                        <IconButton
                          size="small"
                          onClick={() => setValue("quickFilter", "")}
                        >
                          <Clear />
                        </IconButton>
                      ) : (
                        <Search />
                      )}
                    </InputAdornment>
                  ),
                },
              }}
            />
          </Stack>
        </Stack>
        <Stack
          sx={{
            minWidth: 110,
          }}
        >
          <CreateCurriculumButton />
        </Stack>
      </div>
      <Button
        startIcon={<Sort />}
        variant="outlined"
        onClick={toggle}
        sx={{
          display: { xs: "flex", lg: "none" },
        }}
      >
        Urutkan Data
      </Button>
      <Dialog open={open} onClose={toggle}>
        <DialogTitle>Urutkan</DialogTitle>
        <DialogContent
          sx={{
            width: "250px",
            display: "flex",
            flexDirection: "column",
            gap: 2,
            overflow: "initial",
          }}
        >
          <SelectElement
            control={control}
            name="field"
            options={sortSelectOptions}
            label="Pilih Kolom"
            slotProps={{
              input: {
                endAdornment: watch("field") && (
                  <InputAdornment position="end">
                    <IconButton onClick={() => setValue("field", "")}>
                      <Clear />
                    </IconButton>
                  </InputAdornment>
                ),
              },
            }}
          />
          <SelectElement
            control={control}
            name="sortDirection"
            options={[
              { label: "A-Z", id: "asc" },
              { label: "Z-A", id: "desc" },
            ]}
            label="Urutan"
          />
        </DialogContent>
        <DialogActions sx={{ pb: 2, px: 3 }}>
          <Button
            variant="outlined"
            onClick={() => {
              reset();
              toggle();
            }}
          >
            Batal
          </Button>
          <Button
            variant="contained"
            onClick={handleSubmit((value) => {
              if (value.field) {
                api.current.sortColumn(value.field, value.sortDirection);
              } else {
                api.current.sortColumn("name", null);
              }
              toggle();
            })}
          >
            Simpan
          </Button>
        </DialogActions>
      </Dialog>
    </div>
  );
}
