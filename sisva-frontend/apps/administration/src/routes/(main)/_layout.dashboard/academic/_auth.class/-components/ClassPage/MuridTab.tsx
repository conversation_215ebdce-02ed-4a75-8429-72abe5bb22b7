import { Clear, Search, Sort } from "@mui/icons-material";
import {
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  IconButton,
  InputAdornment,
  Stack,
  Typography,
  useMediaQuery,
} from "@mui/material";
import type { GridColDef, GridFilterOperator } from "@mui/x-data-grid";
import { DataGrid, useGridApiContext } from "@mui/x-data-grid";
import type { StudentInStudentGroupsWithStudentAndStudentGroup } from "@sisva/hooks/query/academic/useStudentGroups";
import { useStudentInStudentGroupsWithStudentAndStudentGroup } from "@sisva/hooks/query/academic/useStudentGroups";
import { onlyUnique } from "@sisva/utils";
import { useToggle } from "ahooks";
import { useEffect } from "react";
import { useForm } from "react-hook-form";
import {
  AutocompleteElement,
  SelectElement,
  TextFieldElement,
} from "react-hook-form-mui";

import DeleteIcon from "#/routes/(main)/-components/DeleteIcon";
import EditIcon from "#/routes/(main)/-components/EditIcon";

import DeleteStudentInStudenrGroupButton from "../DeleteStudentInStudentGroupButton";
import UpdateStudentGroupsStudentsButton from "../UpdateStudentGroupsStudentsButton";

type Field =
  | "mobile_column"
  | "grade"
  | keyof StudentInStudentGroupsWithStudentAndStudentGroup
  | typeof filter_field
  | "action";
type TypeSafeColDef = GridColDef & { field: Field };

const filter_field = "filter_field";
const field = {
  filter1: "grade",
  filter2: "student_group_name",
} satisfies Record<string, Field>;

type FilterValue = Record<(typeof field)[keyof typeof field], string>;

export default function MuridTab() {
  const isMobile = useMediaQuery((theme) => theme.breakpoints.down("lg"));
  const { data: studentInStudentGroups = [] } =
    useStudentInStudentGroupsWithStudentAndStudentGroup();

  const customOperator: GridFilterOperator<StudentInStudentGroupsWithStudentAndStudentGroup> =
    {
      label: "Custom Operator",
      value: "customOperator",
      getApplyFilterFn: (filterItem, column) => {
        if (!filterItem.field || !filterItem.value || !filterItem.operator) {
          return null;
        }
        const filterValue = filterItem.value as FilterValue;

        return (value, row, column, apiRef) => {
          let pass = true;
          if (filterValue.grade) {
            if (row.student_group?.grade !== filterValue.grade) pass = false;
          }
          if (filterValue.student_group_name) {
            if (row.student_group_name !== filterValue.student_group_name)
              pass = false;
          }
          return pass;
        };
      },
    };

  const columns: TypeSafeColDef[] = [
    {
      field: "mobile_column",
      headerName: "",
      valueGetter: (_, row) => row,
      flex: 1,
      display: "flex",
      renderCell: (args) => {
        const value =
          args.value as StudentInStudentGroupsWithStudentAndStudentGroup;
        return (
          <Stack sx={{ p: 1.5, gap: 1, width: "100%" }}>
            <Stack
              sx={{
                flexDirection: "row",
                justifyContent: "space-between",
                width: "100%",
              }}
            >
              <Typography sx={{ fontWeight: 600 }}>Tingkatan</Typography>
              <Typography>{value.student_group?.grade}</Typography>
            </Stack>
            <Stack
              sx={{
                flexDirection: "row",
                justifyContent: "space-between",
                width: "100%",
              }}
            >
              <Typography sx={{ fontWeight: 600 }}>Nama Kelas</Typography>
              <Typography>{value?.student_group_name}</Typography>
            </Stack>
            <Stack
              sx={{
                flexDirection: "row",
                justifyContent: "space-between",
                width: "100%",
              }}
            >
              <Typography sx={{ fontWeight: 600 }}>Nama Murid</Typography>
              <Typography>{value.student_name}</Typography>
            </Stack>
            <Stack
              sx={{
                flexDirection: "row",
                justifyContent: "flex-end",
                gap: 1,
                width: "100%",
              }}
            >
              <UpdateStudentGroupsStudentsButton
                student_group_id={value.student_group_id}
                renderTrigger={(onClick) => <EditIcon onClick={onClick} />}
              />
              <DeleteStudentInStudenrGroupButton
                student_group_id={value.student_group_id}
                student_id={value.student_id}
                renderTrigger={(onClick) => <DeleteIcon onClick={onClick} />}
              />
            </Stack>
          </Stack>
        );
      },
    },
    {
      field: "grade",
      headerName: "Tingkatan",
      display: "flex",
      width: 120,
      valueGetter: (_, row: StudentInStudentGroupsWithStudentAndStudentGroup) =>
        row.student_group?.grade,
      renderCell: ({ value }) => <Stack sx={{ py: 1.3 }}>{value}</Stack>,
    },
    {
      field: "student_group_name",
      headerName: "Nama Kelas",
      display: "flex",
      flex: 1,
      renderCell: ({ value }) => <Stack sx={{ py: 1.3 }}>{value}</Stack>,
    },
    {
      field: "student_name",
      headerName: "Nama Murid",
      display: "flex",
      flex: 1,
      renderCell: ({ value }) => <Stack sx={{ py: 1.3 }}>{value}</Stack>,
    },
    {
      field: "action",
      headerName: "Aksi",
      display: "flex",
      sortable: false,
      renderCell: ({
        row,
      }: {
        row: StudentInStudentGroupsWithStudentAndStudentGroup;
      }) => (
        <Stack
          sx={{ py: 1.3, flexDirection: "row", alignItems: "center", gap: 1 }}
        >
          <UpdateStudentGroupsStudentsButton
            student_group_id={row.student_group_id}
            renderTrigger={(onClick) => <EditIcon onClick={onClick} />}
          />
          <DeleteStudentInStudenrGroupButton
            student_group_id={row.student_group_id}
            student_id={row.student_id}
            renderTrigger={(onClick) => <DeleteIcon onClick={onClick} />}
          />
        </Stack>
      ),
    },
    {
      field: filter_field,
      filterOperators: [customOperator],
    },
  ];

  const columnVisibilityModel = Object.fromEntries(
    columns
      .map((column) => column.field)
      .filter((field) =>
        isMobile ? field !== "mobile_column" : field === "mobile_column"
      )
      .map((field) => [field, false])
  );
  columnVisibilityModel[filter_field] = false;

  return (
    <DataGrid
      sx={{
        "& .MuiDataGrid-cell:focus": {
          outline: " none",
        },
        border: "none",
      }}
      getRowId={(row: StudentInStudentGroupsWithStudentAndStudentGroup) =>
        `${row.student_id} - ${row.student_group_id}`
      }
      disableRowSelectionOnClick
      className="bg-white grow"
      rows={studentInStudentGroups}
      columns={columns}
      initialState={{ pagination: { paginationModel: { pageSize: 20 } } }}
      pageSizeOptions={[10, 20, 50]}
      getRowClassName={(params) =>
        params.indexRelativeToCurrentPage % 2 === 0
          ? "bg-neutral-50"
          : "bg-neutral-100"
      }
      slots={{
        toolbar: CustomToolbar,
      }}
      getRowHeight={() => "auto"}
      columnVisibilityModel={columnVisibilityModel}
      columnHeaderHeight={isMobile ? 0 : undefined}
      disableColumnMenu
      disableColumnFilter
    />
  );
}

function CustomToolbar() {
  const api = useGridApiContext();
  const [open, { toggle }] = useToggle(false);

  const { control, handleSubmit, reset, setValue, watch } = useForm<{
    field: Field | "";
    sortDirection: "asc" | "desc";
    quickFilter: string;
    [field.filter1]: string;
    [field.filter2]: string;
  }>({
    defaultValues: {
      field: "",
      sortDirection: "asc",
      quickFilter: "",
      [field.filter1]: "",
      [field.filter2]: "",
    },
  });

  const rows = Array.from(api.current.getRowModels()).map(
    ([key, value]) => value
  ) as StudentInStudentGroupsWithStudentAndStudentGroup[];
  const grades = rows
    .map((row) => row.student_group?.grade)
    .filter((item) => item)
    .filter(onlyUnique);
  const studentGroupNames = rows
    .map((row) => row.student_group_name)
    .filter((item) => item)
    .filter(onlyUnique);

  // apply filter
  const filter1 = watch(field.filter1);
  const filter2 = watch(field.filter2);
  useEffect(() => {
    const value: FilterValue = {
      [field.filter1]: filter1,
      [field.filter2]: filter2,
    };
    api.current.upsertFilterItem({
      field: filter_field,
      operator: "customOperator",
      value,
    });
  }, [api, filter1, filter2]);

  const sortSelectOptions: { label: string; id: Field }[] = [
    { label: "Tingkatan", id: "grade" },
    { label: "Nama Kelas", id: "student_group_name" },
    { label: "Nama Murid", id: "student_name" },
  ];

  const quickFilter = watch("quickFilter");
  useEffect(() => {
    api.current.setQuickFilterValues(
      quickFilter.split(" ").filter((s) => s !== "")
    );
  }, [api, quickFilter]);

  return (
    <div className="p-4 flex flex-col gap-2 border-b border-0 border-solid border-neutral-200">
      <div className=" flex justify-between gap-4 items-center thick-scrollbar">
        <Stack
          sx={{
            overflow: {
              xs: "auto",
              xl: "initial",
            },
            py: {
              xs: 1,
              xl: 0,
            },
          }}
        >
          <Stack sx={{ flexDirection: "row", gap: 2 }}>
            <TextFieldElement
              control={control}
              name="quickFilter"
              placeholder="Cari"
              size="small"
              sx={{
                minWidth: 150,
              }}
              slotProps={{
                input: {
                  endAdornment: (
                    <InputAdornment position="end">
                      {quickFilter ? (
                        <IconButton
                          size="small"
                          onClick={() => setValue("quickFilter", "")}
                        >
                          <Clear />
                        </IconButton>
                      ) : (
                        <Search />
                      )}
                    </InputAdornment>
                  ),
                },
              }}
            />
            <AutocompleteElement
              control={control}
              name={field.filter1}
              matchId
              options={grades.map((item) => ({
                id: item,
                label: item,
              }))}
              label="Tingkatan"
              autocompleteProps={{
                size: "small",
                sx: { minWidth: "180px" },
              }}
            />
            <AutocompleteElement
              control={control}
              name={field.filter2}
              matchId
              options={studentGroupNames.map((item) => ({
                id: item,
                label: item,
              }))}
              label="Nama Kelas"
              autocompleteProps={{
                size: "small",
                sx: { minWidth: "180px" },
              }}
            />
          </Stack>
        </Stack>
        <Stack
          sx={{
            minWidth: 110,
          }}
        >
          <UpdateStudentGroupsStudentsButton />
        </Stack>
      </div>
      <Button
        startIcon={<Sort />}
        variant="outlined"
        onClick={toggle}
        sx={{
          display: { xs: "flex", lg: "none" },
        }}
      >
        Urutkan Data
      </Button>
      <Dialog open={open} onClose={toggle}>
        <DialogTitle>Urutkan</DialogTitle>
        <DialogContent
          sx={{
            width: "250px",
            display: "flex",
            flexDirection: "column",
            gap: 2,
            overflow: "initial",
          }}
        >
          <SelectElement
            control={control}
            name="field"
            options={sortSelectOptions}
            label="Pilih Kolom"
            slotProps={{
              input: {
                endAdornment: watch("field") && (
                  <InputAdornment position="end">
                    <IconButton onClick={() => setValue("field", "")}>
                      <Clear />
                    </IconButton>
                  </InputAdornment>
                ),
              },
            }}
          />
          <SelectElement
            control={control}
            name="sortDirection"
            options={[
              { label: "A-Z", id: "asc" },
              { label: "Z-A", id: "desc" },
            ]}
            label="Urutan"
          />
        </DialogContent>
        <DialogActions sx={{ pb: 2, px: 3 }}>
          <Button
            variant="outlined"
            onClick={() => {
              reset();
              toggle();
            }}
          >
            Batal
          </Button>
          <Button
            variant="contained"
            onClick={handleSubmit((value) => {
              if (value.field) {
                api.current.sortColumn(value.field, value.sortDirection);
              } else {
                api.current.sortColumn("name", null);
              }
              toggle();
            })}
          >
            Simpan
          </Button>
        </DialogActions>
      </Dialog>
    </div>
  );
}
