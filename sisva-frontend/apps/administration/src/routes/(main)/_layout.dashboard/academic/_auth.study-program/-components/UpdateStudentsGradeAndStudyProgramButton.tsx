import { yupResolver } from "@hookform/resolvers/yup";
import { Add } from "@mui/icons-material";
import {
  <PERSON><PERSON>,
  Button,
  Divider,
  Modal,
  Paper,
  Stack,
  Typography,
} from "@mui/material";
import { useStudentsStudentGroup } from "@sisva/hooks/query/academic/useStudentGroups";
import {
  useStudyProgram,
  useStudyPrograms,
} from "@sisva/hooks/query/academic/useStudyPrograms";
import {
  useStudents,
  useUpdateStudent,
} from "@sisva/hooks/query/user/useStudents";
import { useIsStudentsStudyProgramAndGradeCanBeEdited } from "@sisva/hooks/utils";
import type { Grade } from "@sisva/types/apiTypes";
import { useToggle } from "ahooks";
import { type ReactNode, useEffect } from "react";
import {
  AutocompleteElement,
  SelectElement,
  useForm,
} from "react-hook-form-mui";
import toast from "react-hot-toast";
import { number, object, string } from "yup";

/**
 * this component has 3 modes:
 * 1. add study_program_id and grade to student, no props
 * 2. edit student's grade and study_program_id, by passing student_id
 * 3. delete student's grade and study_program_id, by passing student_id and deleteMode = true
 */
export default function UpdateStudentsGradeAndStudyProgramButton({
  student_id,
  renderTrigger,
  deleteMode,
}: {
  student_id?: string;
  renderTrigger?: (onClick: () => void) => ReactNode;
  deleteMode?: boolean;
}) {
  const { data: students = [] } = useStudents();
  // in 'add' mode, we only want display students without grade
  const studentsWithoutGrade = students.filter(
    (student) => !student.detail.grade
  );
  let student = students.find((student) => student.id === student_id);

  const { data: studyPrograms = [] } = useStudyPrograms();
  // only display study_programs with grades
  const studyProgramsWithGrades = studyPrograms.filter(
    (studyProgram) => studyProgram.grades?.length
  );

  const [visible, { toggle }] = useToggle(false);
  const { control, handleSubmit, reset, watch, setValue } = useForm({
    values: student_id
      ? {
          student_id: student_id ?? "",
          study_program_id: student?.detail.study_program_id ?? 0,
          grade: student?.detail?.grade ?? "",
        }
      : undefined,
    resolver: yupResolver(
      object({
        student_id: string().required("Wajib diisi."),
        study_program_id: number().required("Wajib diisi."),
        grade: deleteMode ? string() : string().required("Wajib diisi."),
      })
    ),
  });

  if (!student_id) {
    student = students.find((student) => student.id === watch("student_id"));
  }

  const studentCanBeEdited = useIsStudentsStudyProgramAndGradeCanBeEdited(
    watch("student_id")
  );
  const { data: studentGroup } = useStudentsStudentGroup(watch("student_id"));

  const { data: studyProgram, isFetching: isStudyProgramFetching } =
    useStudyProgram(watch("study_program_id"));

  const { mutate: updateStudent } = useUpdateStudent(watch("student_id"), {
    onSuccess: () => {
      toggle();
      reset();
      toast.success("Siswa berhasil diperbarui");
    },
    onError: () => {
      toast.error("Siswa gagal diperbarui");
    },
  });

  // reset grade if study_program_id changed
  useEffect(() => {
    if (
      !studyProgram?.grades?.includes(watch("grade") as Grade) &&
      !isStudyProgramFetching
    )
      setValue("grade", "");
  }, [isStudyProgramFetching, setValue, studyProgram?.grades, watch]);

  return (
    <>
      {renderTrigger ? (
        renderTrigger(toggle)
      ) : (
        <Button variant="contained" startIcon={<Add />} onClick={toggle}>
          Tambah
        </Button>
      )}
      <Modal open={visible} onClose={toggle}>
        <Stack
          sx={{
            width: 1,
            height: 1,
            justifyContent: "center",
            alignItems: "center",
            p: 2,
          }}
        >
          <Paper
            component={"form"}
            sx={{
              py: 2,
              width: 1,
              maxWidth: "ms",
              gap: 2,
              display: "flex",
              flexDirection: "column",
            }}
            onSubmit={handleSubmit((value) => {
              if (deleteMode) {
                setValue("grade", "");
                setValue("study_program_id", 0);
              }
              updateStudent({
                name: student?.name ?? "",
                grade: value.grade as Grade,
                study_program_id: value.study_program_id,
              });
            })}
          >
            <Typography sx={{ fontWeight: 600, px: 2 }}>
              {(() => {
                if (deleteMode) return "Hapus Data Siswa";
                if (student_id) return "Edit Siswa";
                return "Tambah Siswa";
              })()}
            </Typography>
            <Divider />
            <Stack sx={{ gap: 2, px: 2 }}>
              {!studentCanBeEdited && (
                <Alert severity="warning">
                  Data siswa tidak dapat diubah karena siswa ini adalah anggota
                  kelas{" "}
                  <span className="font-medium">{studentGroup?.name}</span>.
                </Alert>
              )}
              {!deleteMode && (
                <>
                  <AutocompleteElement
                    name="student_id"
                    label="Nama Siswa"
                    control={control}
                    matchId
                    autocompleteProps={{
                      readOnly: !!student_id,
                    }}
                    options={
                      student_id
                        ? students.map((item) => ({
                            id: item.id,
                            label: item.name,
                          }))
                        : studentsWithoutGrade.map((item) => ({
                            id: item.id,
                            label: item.name,
                          }))
                    }
                  />
                  <SelectElement
                    name="study_program_id"
                    control={control}
                    label="Program Study"
                    options={studyProgramsWithGrades.map((item) => ({
                      id: item.id,
                      label: item.name,
                    }))}
                    disabled={!studentCanBeEdited}
                  />
                  <SelectElement
                    name="grade"
                    control={control}
                    label="Tingkatan"
                    disabled={!watch("study_program_id") || !studentCanBeEdited}
                    options={studyProgram?.grades?.map((item) => ({
                      id: item,
                      label: item,
                    }))}
                  />
                </>
              )}

              {deleteMode && (
                <>
                  <Stack
                    sx={{
                      flexDirection: "row",
                      justifyContent: "space-between",
                    }}
                  >
                    <Typography sx={{ fontWeight: 600 }}>
                      Program Studi
                    </Typography>
                    <Typography>{studyProgram?.name}</Typography>
                  </Stack>
                  <Stack
                    sx={{
                      flexDirection: "row",
                      justifyContent: "space-between",
                    }}
                  >
                    <Typography sx={{ fontWeight: 600 }}>Tingkatan</Typography>
                    <Typography>{student?.detail.grade}</Typography>
                  </Stack>
                </>
              )}
            </Stack>
            <Divider />
            <Stack sx={{ gap: 2, px: 2, flexDirection: "row" }}>
              <Button variant="outlined" fullWidth onClick={toggle}>
                Batal
              </Button>
              <Button
                type="submit"
                variant="contained"
                fullWidth
                color={deleteMode ? "error" : "primary"}
                disabled={!studentCanBeEdited}
              >
                {deleteMode ? "Hapus" : "Simpan"}
              </Button>
            </Stack>
          </Paper>
        </Stack>
      </Modal>
    </>
  );
}
