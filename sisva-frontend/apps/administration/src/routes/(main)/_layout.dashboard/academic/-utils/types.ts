import type { Grade } from "@sisva/types/apiTypes";
import type { DayText, SubjectType } from "@sisva/types/types";

// Sheets structure
export type Sheet =
  | "Program Studi"
  | "Program Studi Siswa"
  | "Kurikulum dan Mata <PERSON>elajar<PERSON>"
  | "Tingkatan dan Silabus"
  | "Periode"
  | "Periode dan Kurikulum"
  | "Guru"
  | "Kelas"
  | "Murid"
  | "Ekstrakulikuler"
  | "Anggota"
  | "Jam Sekolah"
  | "Jadwal Kelas"
  | "Aktivitas Non KBM";

export type ProgramStudiInputData = [
  string, // 0 - name
  string, // 1 - code
  boolean, // 2 - tingkatan I
  boolean, // 3 - tingkatan II
  boolean, // 4 - tingkatan III
  boolean, // 5 - tingkatan IV
  boolean, // 6 - tingkatan V
  boolean, // 7 - tingkatan VI
  boolean, // 8 - tingkatan VII
  boolean, // 9 - tingkatan VIII
  boolean, // 10 - tingkatan IX
  boolean, // 11 - tingkatan X
  boolean, // 12 - tingkatan XI
  boolean, // 13 - tingkatan XII
][];

export type ProgramStudiSiswaInputData = [
  string, // 0 - name
  string, // 1 - username (opsional)
  string, // 2 - Nama Program Studi
  Grade, // 3 - Tingkatan
][];

export type KurikulumDanMataPelajaranInputData = [
  string, // 0 - nama kurikulum
  string, // 1 - nama program studi
  string, // 2 - nama mata pelajaran
  SubjectType, // 3 - type mata pelajaran
][];

export type TingkatanDanSilabusInputData = [
  string, // 0 - nama mata pelajaran
  Grade, // 1 - tingkatan
  string, // 2 - uri silabus
][];

export type PeriodeInputData = [
  string, // 0 - nama periode
  number, // 1 - tanggal mulai, Excel serial date format
  number, // 2 - tanggal selesai, Excel serial date format
][];

export type PeriodeDanKurikulumInputData = [
  string, // 0 - nama periode
  string, // 1 - nama kurikulum
  string, // 2 - nama program studi
  Grade, // 3 - tingkatan
][];

export type GuruInputData = [
  string, // 0 - nama guru
  string, // 1 - username (opsional)
  string, // 2 - nama program studi
  Grade, // 3 - tingkatan
][];

export type KelasInputData = [
  string, // 0 - nama kelas
  string, // 1 - nama wali kelas
  string, // 2 - username wali kelas
  string, // 3 - nama periode
  string, // 4 - nama program studi
  Grade, // 5 - tingkatan
][];

export type MuridInputData = [
  string, // 0 - nama kelas
  string, // 1 - nama siswa
  string, // 2 - username siswa
][];

export type EkstrakulikulerInputData = [
  string, // 0 - nama ekstrakulikuler
  string, // 1 - nama pembina
  string, // 2 - username pembina
][];

export type AnggotaInputData = [
  string, // 0 - nama ekstrakulikuler
  string, // 1 - nama anggota
  string, // 2 - username anggota
][];

export type JamSekolahInputData = [
  string, // 0 - nama periode
  string, // 1 - nama program studi
  Grade, // 2 - tingkatan
  DayText, // 3 - hari
  number, // 4 - jam mulai, OADate format
  number, // 5 - jam selesai, OADate format
][];

export type JadwalKelasInputData = [
  string, // 0 - nama periode
  string, // 1 - nama program studi
  Grade, // 2 - tingkatan
  DayText, // 3 - hari
  string, // 4 - nama kelas
  string, // 5 - mata pelajaran
  string, // 6 - nama guru
  string, // 7 - username guru
  number, // 8 - jam mulai, OADate format
  number, // 9 - jam selesai, OADate format
][];

export type AktivitasNonKBMInputData = [
  string, // 0 - nama aktivitas
  string, // 1 - nama periode
  string, // 2 - nama program studi
  Grade, // 3 - tingkatan
  DayText, // 4 - hari
  number, // 5 - jam mulai, OADate format
  number, // 6 - jam selesai, OADate format
][];
