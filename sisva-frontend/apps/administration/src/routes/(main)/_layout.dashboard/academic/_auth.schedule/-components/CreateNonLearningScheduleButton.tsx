import { valibotResolver } from "@hookform/resolvers/valibot";
import { Add } from "@mui/icons-material";
import {
  <PERSON><PERSON>,
  Button,
  Divider,
  Modal,
  Paper,
  Stack,
  Typography,
} from "@mui/material";
import {
  useCreateNonLearningSchedule,
  useDeleteNonLearningSchedules,
  useNonLearningSchedule,
  useUpdateNonLearningSchedule,
} from "@sisva/hooks/query/academic/useNonLearningSchedules";
import { usePeriod } from "@sisva/hooks/query/academic/usePeriods";
import {
  useSchoolSchedule,
  useSchoolSchedules,
} from "@sisva/hooks/query/academic/useSchoolSchedules";
import {
  useStudyProgram,
  useStudyPrograms,
} from "@sisva/hooks/query/academic/useStudyPrograms";
import { getDayText } from "@sisva/types/types";
import { isScheduleConflictError } from "@sisva/utils";
import { useToggle } from "ahooks";
import type { Dayjs } from "dayjs";
import dayjs from "dayjs";
import { sort } from "fast-sort";
import { type ReactNode, useEffect, useRef } from "react";
import {
  AutocompleteElement,
  TextFieldElement,
  useForm,
} from "react-hook-form-mui";
import { TimePickerElement } from "react-hook-form-mui/date-pickers";
import toast from "react-hot-toast";
import {
  custom,
  type InferOutput,
  nonEmpty,
  number,
  object,
  pipe,
  string,
} from "valibot";

import { toastScheduleError } from "./ScheduleErrorToast";

export default function CreateNonLearningScheduleButton({
  non_learning_schedule_id, //if passed, this component will be in edit mode
  study_program_id, //if passed, the form will preselect study_program_id with this value
  grade, // if passed, the form will preselect grade with this value
  period_id, // required
  deleteMode, // if true, this component will be in delete mode
  renderTrigger,
}: {
  non_learning_schedule_id?: number | null;
  study_program_id?: number | null;
  grade?: string | null;
  period_id?: number | null;
  deleteMode?: boolean;
  renderTrigger?: (onClick: () => void) => ReactNode;
}) {
  const deleteModeRef = useRef(deleteMode ?? false);

  const { data: nonLearningSchedule } = useNonLearningSchedule({
    non_learning_schedule_id,
    period_id,
  });

  const { data: schoolScheduleFromProp } = useSchoolSchedule({
    period_id,
    school_schedule_id: nonLearningSchedule?.school_schedule_id,
  });

  function dayjsSchema() {
    return custom<Dayjs>((input) => {
      try {
        //@ts-expect-error check if valid dayjs object
        return input.isValid();
      } catch {
        return false;
      }
    }, "Jam harus dipilih");
  }

  const [visible, { toggle }] = useToggle(false);

  const schema = object({
    period_id: number(),
    study_program_id: number("Program studi harus dipilih"),
    grade: pipe(
      string("Tingkatan harus diisi"),
      nonEmpty("Tingkatan harus diisi")
    ),
    name: pipe(string("Nama harus diisi"), nonEmpty("Nama harus diisi")),
    school_schedule_id: number("Hari harus dipilih"),
    start_time_dayjs: dayjsSchema(),
    end_time_dayjs: dayjsSchema(),
  });
  type Schema = InferOutput<typeof schema>;
  const {
    control,
    handleSubmit,
    reset,
    watch,
    formState: { isValid },
    setValue,
  } = useForm<Schema>({
    resolver: valibotResolver(schema),
    values: {
      //@ts-expect-error allow null
      period_id: schoolScheduleFromProp?.period_id ?? period_id ?? null,
      //@ts-expect-error allow null
      study_program_id:
        schoolScheduleFromProp?.study_program_id ?? study_program_id ?? null,
      grade: schoolScheduleFromProp?.grade ?? grade ?? "",
      name: nonLearningSchedule?.name ?? "",
      //@ts-expect-error allow null
      school_schedule_id: nonLearningSchedule?.school_schedule_id ?? null,
      //@ts-expect-error allow null
      start_time_dayjs: nonLearningSchedule?.start_time
        ? dayjs(nonLearningSchedule.start_time, "h:mm A Z")
        : null,
      //@ts-expect-error allow null
      end_time_dayjs: nonLearningSchedule?.start_time
        ? dayjs(nonLearningSchedule.end_time, "h:mm A Z")
        : null,
    },
  });

  const period_id_form = watch("period_id");
  const study_program_id_form = watch("study_program_id");
  const grade_form = watch("grade");
  const start_time_dayjs = watch("start_time_dayjs");
  const end_time_dayjs = watch("end_time_dayjs");
  const school_schedule_id = watch("school_schedule_id");

  const { data: schoolSchedules = [] } = useSchoolSchedules(period_id);
  const fitleredSchoolSchedules = schoolSchedules.filter(
    (item) =>
      item.period_id === period_id_form &&
      item.study_program_id === study_program_id_form &&
      item.grade === grade_form
  );

  const { data: period } = usePeriod(period_id_form);
  const studyProgramIds = period?.study_programs?.map((item) => item.id) ?? [];

  const { data: studyPrograms = [] } = useStudyPrograms();
  const fitleredStudyPrograms = studyPrograms.filter((item) =>
    studyProgramIds.includes(item.id)
  );

  const { data: studyProgram } = useStudyProgram(study_program_id_form);
  const grades = studyProgram?.grades ?? [];

  const { data: schoolSchedule } = useSchoolSchedule({
    period_id,
    school_schedule_id,
  });
  const schoolScheduleEndTime = schoolSchedule
    ? dayjs(schoolSchedule.end_time, "h:mm A Z")
    : null;
  const schoolScheduleStartTime = schoolSchedule
    ? dayjs(schoolSchedule.start_time, "h:mm A Z")
    : null;

  // //  ─────────────────────────── check dependent ───────────────────────────
  // ...
  // //  ─────────────────────────── check dependent ───────────────────────────
  const canBeDeleted = true;

  const { mutate: createNonLearningSchedule, isPending: P1 } =
    useCreateNonLearningSchedule({
      onSuccess: () => {
        toggle();
        reset();
        toast.success("Jadwal Non KBM berhasil ditambahkan");
      },
      onError: (error) => {
        const conflictError = isScheduleConflictError(error);
        if (conflictError) {
          return toastScheduleError(conflictError);
        }
        toast.error("Jadwal Non KBM gagal ditambahkan");
      },
    });

  const { mutate: updateNonLearningSchedule, isPending: P2 } =
    useUpdateNonLearningSchedule({
      onSuccess: () => {
        toggle();
        reset();
        toast.success("Jadwal Non KBM berhasil diperbarui");
      },
      onError: (error) => {
        const conflictError = isScheduleConflictError(error);
        if (conflictError) {
          return toastScheduleError(conflictError);
        }
        toast.error("Jadwal Non KBM gagal diperbarui");
      },
    });

  const { mutate: deleteNonLearningSchedule, isPending: P3 } =
    useDeleteNonLearningSchedules({
      onSuccess: () => {
        toggle();
        reset();
        toast.success("Jadwal Non KBM berhasil dihapus");
      },
      onError: () => {
        toast.error("Jadwal Non KBM gagal dihapus");
      },
    });

  // reset grade whenever study_program_id changed
  useEffect(() => {
    if (!non_learning_schedule_id) setValue("grade", "");
  }, [non_learning_schedule_id, setValue, study_program_id_form]);

  // reset school_schedule_id whenever grade changed
  useEffect(() => {
    //@ts-expect-error allow null
    if (!non_learning_schedule_id) setValue("school_schedule_id", null);
  }, [grade_form, non_learning_schedule_id, setValue]);

  // reset start_time_dayjs and end_time_dayjs whenever school_schedule_id changed
  useEffect(() => {
    if (!non_learning_schedule_id) {
      //@ts-expect-error allow null
      setValue("start_time_dayjs", null);
      //@ts-expect-error allow null
      setValue("end_time_dayjs", null);
    }
  }, [non_learning_schedule_id, school_schedule_id, setValue]);

  return (
    <>
      {renderTrigger ? (
        renderTrigger(toggle)
      ) : (
        <Button variant="contained" startIcon={<Add />} onClick={toggle}>
          Tambah
        </Button>
      )}
      <Modal open={visible} onClose={toggle}>
        <Stack
          sx={{
            width: 1,
            height: 1,
            justifyContent: "center",
            alignItems: "center",
            p: 2,
          }}
        >
          <Paper
            component={"form"}
            sx={{
              py: 2,
              width: 1,
              maxWidth: "ms",
              gap: 2,
              display: "flex",
              flexDirection: "column",
            }}
            onSubmit={handleSubmit((value) => {
              if (non_learning_schedule_id) {
                if (deleteMode || deleteModeRef.current) {
                  deleteModeRef.current = false;
                  return deleteNonLearningSchedule(non_learning_schedule_id);
                }
                updateNonLearningSchedule({
                  ...value,
                  start_time: value.start_time_dayjs.format("h:mm A Z"),
                  end_time: value.end_time_dayjs.format("h:mm A Z"),
                  non_learning_schedule_id,
                });
              } else {
                createNonLearningSchedule({
                  ...value,
                  start_time: value.start_time_dayjs.format("h:mm A Z"),
                  end_time: value.end_time_dayjs.format("h:mm A Z"),
                });
              }
            })}
          >
            <Typography sx={{ fontWeight: 600, px: 2 }}>
              {(() => {
                if (non_learning_schedule_id) {
                  if (deleteMode) return "Hapus Jadwal Non KBM";
                  return "Edit Jadwal Non KBM";
                }
                return "Tambah Jadwal Non KBM";
              })()}
            </Typography>
            <Divider />
            <Stack sx={{ gap: 2, px: 2 }}>
              {!canBeDeleted && deleteMode && (
                <Alert severity="warning">
                  Jam sekolah ini sedang digunakan
                </Alert>
              )}
              <TextFieldElement
                name="name"
                label="Nama Jadwal"
                control={control}
              />
              <AutocompleteElement
                name="study_program_id"
                label="Program Studi"
                control={control}
                matchId
                options={fitleredStudyPrograms.map((item) => ({
                  id: item.id,
                  label: item.name,
                }))}
                autocompleteProps={{
                  disabled: deleteMode || !!non_learning_schedule_id,
                }}
              />
              <AutocompleteElement
                name="grade"
                label="Tingkatan"
                control={control}
                matchId
                options={grades.map((item) => ({
                  id: item,
                  label: item,
                }))}
                autocompleteProps={{
                  disabled:
                    deleteMode ||
                    !study_program_id_form ||
                    !!non_learning_schedule_id,
                }}
              />
              <AutocompleteElement
                name="school_schedule_id"
                label="Hari"
                control={control}
                matchId
                options={sort(fitleredSchoolSchedules)
                  .asc((item) => item.day)
                  .map((item) => ({
                    id: item.id,
                    label: getDayText(item.day),
                  }))}
                autocompleteProps={{
                  disabled:
                    deleteMode ||
                    !grade_form ||
                    !study_program_id_form ||
                    !!non_learning_schedule_id,
                }}
              />
              <TimePickerElement
                label="Jam mulai"
                name="start_time_dayjs"
                control={control}
                disabled={deleteMode || !school_schedule_id}
                minTime={schoolScheduleStartTime ?? undefined}
                maxTime={end_time_dayjs ?? schoolScheduleEndTime ?? undefined}
              />
              <TimePickerElement
                label="Jam selesai"
                name="end_time_dayjs"
                control={control}
                disabled={deleteMode || !school_schedule_id}
                minTime={
                  start_time_dayjs ?? schoolScheduleStartTime ?? undefined
                }
                maxTime={schoolScheduleEndTime ?? undefined}
              />
            </Stack>
            <Divider />
            <Stack sx={{ gap: 2, px: 2, flexDirection: "row" }}>
              {non_learning_schedule_id && (
                <Button
                  type="submit"
                  variant="contained"
                  fullWidth
                  color="error"
                  disabled={!canBeDeleted || P1 || P2 || P3}
                  onClick={() => {
                    deleteModeRef.current = true;
                  }}
                >
                  Hapus
                </Button>
              )}
              <Button variant="outlined" fullWidth onClick={toggle}>
                Batal
              </Button>
              <Button
                type="submit"
                variant="contained"
                fullWidth
                color={deleteMode ? "error" : undefined}
                disabled={(!canBeDeleted && deleteMode) || P1 || P2 || P3}
              >
                {deleteMode ? "Hapus" : "Simpan"}
              </Button>
            </Stack>
          </Paper>
        </Stack>
      </Modal>
    </>
  );
}
