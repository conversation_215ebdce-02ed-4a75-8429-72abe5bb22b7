import { useToggle } from "ahooks";
import { useState } from "react";
import toast from "react-hot-toast";

import ExcelButton from "#/routes/(main)/-components/ExcelButton";

import ImportXLSXAlert from "../../-components/ImportXLSXAlert";
import handleXLSXUploadAcademic from "../-utils/handleXLSXUploadAcademic";

export default function ImportExcelAcademicButton() {
  const [open, { toggle }] = useToggle(false);
  const [importReport, setImportReport] = useState<string[]>([]);
  const [title, setTitle] = useState("");
  return (
    <>
      <ExcelButton
        showImport
        downloadTemplateLink="/templates/template-penambahan-akademik.xlsx"
        importConfirmOnClick={(file) => {
          const toastId = toast.loading("Sedang menambahkan...");
          handleXLSXUploadAcademic({
            file: file,
            onSuccess: (importReport) => {
              setTitle("File import berhasil");
              setImportReport(
                Object.values(importReport).filter((text) => text)
              );
              toast.remove(toastId);
              toggle();
            },
            onError: (importReport) => {
              setTitle("File import bermasalah");
              setImportReport(
                Object.values(importReport).filter((text) => text)
              );
              toast.remove(toastId);
              toggle();
            },
          });
        }}
      />
      <ImportXLSXAlert
        title={title}
        open={open}
        handleClose={() => toggle()}
        importReport={importReport}
      />
    </>
  );
}
