import { yupResolver } from "@hookform/resolvers/yup";
import { Add } from "@mui/icons-material";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Divider,
  Modal,
  Paper,
  Stack,
  Typography,
} from "@mui/material";
import {
  useCreateStudyProgram,
  useStudyProgram,
  useStudyPrograms,
  useUpdateStudyProgram,
} from "@sisva/hooks/query/academic/useStudyPrograms";
import { useIsStudyProgramGradeCanBeEdited } from "@sisva/hooks/utils";
import type { Grade } from "@sisva/types/apiTypes";
import { useToggle } from "ahooks";
import type { ReactNode } from "react";
import {
  MultiSelectElement,
  SelectElement,
  TextFieldElement,
  useForm,
} from "react-hook-form-mui";
import toast from "react-hot-toast";
import { array, object, string } from "yup";

/**
 * this component has 4 modes:
 * 1. create new study program, no props
 * 2. edit study program, by passing study_program_id
 * 3. edit grade, by passing editGrades = true
 * 4. edit grade and preselect, by passing editGrades = true and study_program_id
 */
export default function CreateStudyProgramButton({
  // if study_program_id provided, then it's edit mode
  study_program_id,
  renderTrigger,
  editGrades,
}: {
  study_program_id?: number;
  renderTrigger?: (onClick: () => void) => ReactNode;
  editGrades?: boolean;
}) {
  const { data: studyPrograms = [] } = useStudyPrograms();

  const [visible, { toggle }] = useToggle(false);

  // select study_program_id from SelectElement if editGrades true
  const { control: control2, watch: watch2 } = useForm({
    values: {
      // preselect study_program_id if provided
      study_program_id: study_program_id ?? "",
    },
  });
  if (editGrades) {
    study_program_id = Number(watch2("study_program_id"));
  }
  const gradesCanBeEdited = useIsStudyProgramGradeCanBeEdited(
    study_program_id ?? 0
  );

  const { data: studyProgram } = useStudyProgram(study_program_id);

  const { control, handleSubmit, reset } = useForm({
    values: {
      name: studyProgram?.name ?? "",
      code: studyProgram?.code ?? "",
      grades: studyProgram?.grades ?? [],
    },
    resolver: yupResolver(
      object({
        name: string().required("Wajib diisi."),
        code: string().required("Wajib diisi."),
        grades: array()
          .of(string().required("Wajib diisi."))
          .required("Wajib diisi."),
      })
    ),
  });

  const { mutate: createStudyProgram } = useCreateStudyProgram({
    onSuccess: () => {
      toggle();
      reset();
      toast.success("Program Studi berhasil ditambahkan");
    },
    onError: () => {
      toast.error("Program Studi gagal ditambahkan");
    },
  });

  const { mutate: updateStudyProgram } = useUpdateStudyProgram({
    onSuccess: () => {
      toggle();
      reset();
      toast.success("Program Studi berhasil diperbarui");
    },
    onError: () => {
      toast.error("Program Studi gagal diperbarui");
    },
  });

  return (
    <>
      {renderTrigger ? (
        renderTrigger(toggle)
      ) : (
        <Button variant="contained" startIcon={<Add />} onClick={toggle}>
          Tambah
        </Button>
      )}
      <Modal open={visible} onClose={toggle}>
        <Stack
          sx={{
            width: 1,
            height: 1,
            justifyContent: "center",
            alignItems: "center",
            p: 2,
          }}
        >
          <Paper
            component={"form"}
            sx={{
              py: 2,
              width: 1,
              maxWidth: "ms",
              gap: 2,
              display: "flex",
              flexDirection: "column",
            }}
            onSubmit={handleSubmit((value) => {
              if (study_program_id) {
                updateStudyProgram({
                  id: study_program_id,
                  code: value.code,
                  name: value.name,
                  grades: value.grades as Grade[],
                });
              } else {
                createStudyProgram(value);
              }
            })}
          >
            <Typography sx={{ fontWeight: 600, px: 2 }}>
              {(() => {
                if (editGrades) return "Tambah Tingkatan";
                if (study_program_id) {
                  return "Edit Program Studi";
                } else {
                  return "Tambah Program Studi";
                }
              })()}
            </Typography>
            <Divider />
            <Stack sx={{ gap: 2, px: 2 }}>
              {editGrades && !gradesCanBeEdited && (
                <Alert severity="warning">
                  Tingkatan Program Studi tidak dapat diubah karena Program
                  Studi ini sedang digunakan.
                </Alert>
              )}
              {editGrades && (
                <>
                  <SelectElement
                    control={control2}
                    label="Program Studi"
                    name="study_program_id"
                    options={studyPrograms.map((item) => ({
                      id: item.id,
                      label: item.name,
                    }))}
                  />
                  <MultiSelectElement
                    label="Tingkatan"
                    name="grades"
                    control={control}
                    disabled={editGrades && !gradesCanBeEdited}
                    options={[
                      "I",
                      "II",
                      "III",
                      "IV",
                      "V",
                      "VI",
                      "VII",
                      "VIII",
                      "IX",
                      "X",
                      "XI",
                      "XII",
                    ]}
                    showChips
                  />
                </>
              )}
              {!editGrades && (
                <>
                  <TextFieldElement
                    name="name"
                    label="Program Studi"
                    control={control}
                  />
                  <TextFieldElement
                    name="code"
                    label="Kode"
                    control={control}
                  />
                </>
              )}
            </Stack>
            <Divider />
            <Stack sx={{ gap: 2, px: 2, flexDirection: "row" }}>
              <Button variant="outlined" fullWidth onClick={toggle}>
                Batal
              </Button>
              <Button
                type="submit"
                variant="contained"
                fullWidth
                disabled={editGrades && !gradesCanBeEdited}
              >
                Simpan
              </Button>
            </Stack>
          </Paper>
        </Stack>
      </Modal>
    </>
  );
}
