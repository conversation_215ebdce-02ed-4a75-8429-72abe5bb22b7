import { yupResolver } from "@hookform/resolvers/yup";
import { Add } from "@mui/icons-material";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Divider,
  Modal,
  Paper,
  Stack,
  Typography,
} from "@mui/material";
import { useCurriculums } from "@sisva/hooks/query/academic/useCurriculums";
import {
  useAddCurriculumInPeriod,
  usePeriod,
  usePeriodCurriculum,
  usePeriodCurriculums,
  usePeriods,
  useUpdateCurriculumInPeriod,
} from "@sisva/hooks/query/academic/usePeriods";
import { useStudentGroups } from "@sisva/hooks/query/academic/useStudentGroups";
import {
  useStudyProgram,
  useStudyPrograms,
} from "@sisva/hooks/query/academic/useStudyPrograms";
import type { Grade } from "@sisva/types/apiTypes";
import { useToggle } from "ahooks";
import { sort } from "fast-sort";
import { type ReactNode, useEffect } from "react";
import { AutocompleteElement, useForm } from "react-hook-form-mui";
import toast from "react-hot-toast";
import { number, object, string } from "yup";

/**
 * this component has 2 modes:
 * 1. create new periodCurriculum, no props
 * 2. edit periodCurriculum, by passing periodCurriculumId
 */
export default function CreatePeriodCurriculumButton({
  // if periodCurriculumId provided, then it's edit mode
  periodCurriculumId,
  renderTrigger,
}: {
  periodCurriculumId?: {
    curriculum_id: number;
    period_id: number;
    study_program_id: number;
    grade: Grade;
  };
  renderTrigger?: (onClick: () => void) => ReactNode;
}) {
  const [visible, { toggle }] = useToggle(false);
  const { data: periodCurriculums = [] } = usePeriodCurriculums();

  const { data: periods = [] } = usePeriods();
  const { data: curriculums = [] } = useCurriculums();
  const { data: studyPrograms = [] } = useStudyPrograms();

  const { control, handleSubmit, reset, watch, setValue } = useForm({
    values: {
      curriculum_id: periodCurriculumId?.curriculum_id ?? 0,
      period_id: periodCurriculumId?.period_id ?? 0,
      study_program_id: periodCurriculumId?.study_program_id ?? 0,
      grade: periodCurriculumId?.grade ?? "",
    },
    resolver: yupResolver(
      object({
        curriculum_id: number()
          .required("Kurikulum wajib dipilih")
          .notOneOf([0], "Kurikulum wajib dipilih"),
        period_id: number()
          .required("Periode wajib dipilih")
          .notOneOf([0], "Periode wajib dipilih"),
        study_program_id: number()
          .required("Program Studi wajib dipilih")
          .notOneOf([0], "Program Studi wajib dipilih"),
        grade: string().required("Tingkatan wajib dipilih"),
      })
    ),
  });

  const curriculum_id = watch("curriculum_id");
  const period_id = watch("period_id");
  const study_program_id = watch("study_program_id");
  const grade = watch("grade");

  const filteredPeriodCurriculums = periodCurriculums.filter(
    (item) =>
      item.curriculum_id === curriculum_id &&
      item.period_id === period_id &&
      item.study_program_id === study_program_id
  );
  const unavailableGrades = filteredPeriodCurriculums.map((item) => item.grade);
  const { data: studyProgram } = useStudyProgram(watch("study_program_id"));
  const availableGrades =
    studyProgram?.grades?.filter((grade) => {
      return (
        grade === periodCurriculumId?.grade ||
        !unavailableGrades.includes(grade)
      );
    }) ?? [];

  const gradeAllowed = availableGrades.includes(grade as Grade);
  useEffect(() => {
    if (!gradeAllowed) setValue("grade", "");
  }, [gradeAllowed, setValue]);

  //  ──────────────── logic to decide if this can be modified ────────────────
  const { data: periodCurriculum } = usePeriodCurriculum({
    period_id: periodCurriculumId?.period_id,
    curriculum_id: periodCurriculumId?.curriculum_id,
    study_program_id: periodCurriculumId?.study_program_id,
    grade: periodCurriculumId?.grade,
  });
  const { data: period } = usePeriod(periodCurriculum?.period_id);

  const { data: studentGroups = [] } = useStudentGroups();
  const filteredStudentGroups = studentGroups.filter(
    (studentGroup) =>
      studentGroup.period_id === periodCurriculumId?.period_id &&
      studentGroup.study_program_id === periodCurriculumId?.study_program_id &&
      studentGroup.grade === periodCurriculumId?.grade
  );

  const studentGroupsLength = filteredStudentGroups.length;
  const periodInactive = period?.status === "inactive";
  const disabled =
    periodCurriculumId && (!periodInactive || studentGroupsLength > 0);
  //  ──────────────── logic to decide if this can be modified ────────────────

  const { mutate: addCurriculumInPeriod } = useAddCurriculumInPeriod({
    onSuccess: () => {
      toggle();
      reset();
      toast.success("Tingkatan pada Kurikulum berhasil ditambahkan");
    },
    onError: () => {
      toast.error("Tingkatan pada Kurikulum gagal ditambahkan");
    },
  });

  const { mutate: updateCurriculumInPeriod } = useUpdateCurriculumInPeriod({
    onSuccess: () => {
      toggle();
      reset();
      toast.success("Tingkatan pada Kurikulum berhasil diperbarui");
    },
    onError: () => {
      toast.error("Tingkatan pada Kurikulum gagal diperbarui");
    },
  });

  return (
    <>
      {renderTrigger ? (
        renderTrigger(toggle)
      ) : (
        <Button variant="contained" startIcon={<Add />} onClick={toggle}>
          Tambah
        </Button>
      )}
      <Modal open={visible} onClose={toggle}>
        <Stack
          sx={{
            width: 1,
            height: 1,
            justifyContent: "center",
            alignItems: "center",
            p: 2,
          }}
        >
          <Paper
            component={"form"}
            sx={{
              py: 2,
              width: 1,
              maxWidth: "ms",
              gap: 2,
              display: "flex",
              flexDirection: "column",
            }}
            onSubmit={handleSubmit((value) => {
              if (periodCurriculumId) {
                updateCurriculumInPeriod({
                  curriculum_id: periodCurriculumId.curriculum_id,
                  period_id: periodCurriculumId.period_id,
                  study_program_id: periodCurriculumId.study_program_id,
                  grade: periodCurriculumId.grade,
                  new_curriculum_id: value.curriculum_id,
                  new_period_id: value.period_id,
                  new_study_program_id: value.study_program_id,
                  new_grade: value.grade as Grade,
                });
              } else {
                addCurriculumInPeriod({
                  ...value,
                  grade: value.grade as Grade,
                });
              }
            })}
          >
            <Typography sx={{ fontWeight: 600, px: 2 }}>
              {(() => {
                if (periodCurriculumId) {
                  return "Edit Tingkatan pada Kurikulum";
                } else {
                  return "Tambah Tingkatan pada Kurikulum";
                }
              })()}
            </Typography>
            <Divider />
            <Stack sx={{ gap: 2, px: 2 }}>
              {disabled && (
                <Alert severity="warning">
                  {!periodInactive &&
                    `Hanya dapat memperbarui Tingkatan pada Kurikulum yang Periodenya belum aktif. `}
                  {studentGroupsLength > 0 &&
                    `Terdapat ${studentGroupsLength} Kelas yang sedang menggunakan Kurikulum dengan Tingkatan ini`}
                </Alert>
              )}
              <AutocompleteElement
                control={control}
                name="curriculum_id"
                matchId
                options={sort(curriculums)
                  .desc((item) => item.id)
                  .map((item) => ({
                    id: item.id,
                    label: item.name,
                  }))}
                label="Kurikulum"
                autocompleteProps={{
                  disabled: disabled,
                }}
              />
              <AutocompleteElement
                control={control}
                name="period_id"
                matchId
                options={sort(periods)
                  .desc((item) => item.id)
                  .map((item) => ({
                    id: item.id,
                    label: item.name,
                  }))}
                label="Periode"
                autocompleteProps={{
                  disabled: disabled,
                }}
              />
              <AutocompleteElement
                control={control}
                name="study_program_id"
                matchId
                options={sort(studyPrograms)
                  .desc((item) => item.id)
                  .map((item) => ({
                    id: item.id,
                    label: item.name,
                  }))}
                label="Program Studi"
                autocompleteProps={{
                  disabled: disabled,
                }}
              />
              <AutocompleteElement
                control={control}
                name="grade"
                matchId
                options={availableGrades.map((item) => ({
                  id: item,
                  label: item,
                }))}
                label="Tingkatan"
                autocompleteProps={{
                  disabled: disabled,
                }}
              />
            </Stack>
            <Divider />
            <Stack sx={{ gap: 2, px: 2, flexDirection: "row" }}>
              <Button variant="outlined" fullWidth onClick={toggle}>
                Batal
              </Button>
              <Button
                type="submit"
                variant="contained"
                fullWidth
                disabled={disabled}
              >
                Simpan
              </Button>
            </Stack>
          </Paper>
        </Stack>
      </Modal>
    </>
  );
}
