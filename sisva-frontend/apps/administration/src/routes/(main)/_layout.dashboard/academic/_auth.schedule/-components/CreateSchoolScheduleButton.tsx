import { valibotResolver } from "@hookform/resolvers/valibot";
import { Add } from "@mui/icons-material";
import {
  <PERSON><PERSON>,
  Button,
  Divider,
  Modal,
  Paper,
  Stack,
  Typography,
} from "@mui/material";
import { useClassSchedules } from "@sisva/hooks/query/academic/useClassSchedules";
import { useNonLearningSchedules } from "@sisva/hooks/query/academic/useNonLearningSchedules";
import {
  useCreateSchoolSchedule,
  useDeleteSchoolSchedule,
  useSchoolSchedule,
  useSchoolSchedules,
  useUpdateSchoolSchedule,
} from "@sisva/hooks/query/academic/useSchoolSchedules";
import { dayOptions } from "@sisva/types/dropdownOptions";
import { useToggle } from "ahooks";
import type { Dayjs } from "dayjs";
import dayjs from "dayjs";
import type { ReactNode } from "react";
import { AutocompleteElement, useForm } from "react-hook-form-mui";
import { TimePickerElement } from "react-hook-form-mui/date-pickers";
import toast from "react-hot-toast";
import { custom, nonEmpty, number, object, pipe, string } from "valibot";

export default function CreateSchoolScheduleButton({
  school_schedule_id, // if passed this component will be in edit mode
  period_id, // required
  study_program_id, // required
  grade, // required
  deleteMode, // if true, this component will be in delete mode
  renderTrigger,
}: {
  school_schedule_id?: number | null;
  period_id?: number | null;
  study_program_id?: number | null;
  grade?: string | null;
  deleteMode?: boolean;
  renderTrigger?: (onClick: () => void) => ReactNode;
}) {
  const { data: schoolSchedule } = useSchoolSchedule({
    school_schedule_id,
    period_id,
  });

  function dayjsSchema() {
    return custom<Dayjs>((input) => {
      try {
        //@ts-expect-error check if valid dayjs object
        return input.isValid();
      } catch {
        return false;
      }
    }, "Jam harus dipilih");
  }

  const [visible, { toggle }] = useToggle(false);
  const {
    control,
    handleSubmit,
    reset,
    watch,
    formState: { isValid },
  } = useForm({
    resolver: valibotResolver(
      pipe(
        object({
          period_id: number(),
          study_program_id: number(),
          grade: pipe(string(), nonEmpty("Tinkatan harus diisi")),
          day: number("Hari harus dipilih"),
          start_time_dayjs: dayjsSchema(),
          end_time_dayjs: dayjsSchema(),
        })
      )
    ),
    values: {
      //@ts-expect-error allow null
      day: schoolSchedule?.day ?? null,
      //@ts-expect-error allow null
      period_id: schoolSchedule?.period_id ?? period_id ?? null,
      //@ts-expect-error allow null
      study_program_id:
        schoolSchedule?.study_program_id ?? study_program_id ?? null,
      grade: schoolSchedule?.grade ?? grade ?? "",
      //@ts-expect-error allow null
      start_time_dayjs: schoolSchedule?.start_time
        ? dayjs(schoolSchedule.start_time, "h:mm A Z")
        : null,
      //@ts-expect-error allow null
      end_time_dayjs: schoolSchedule?.start_time
        ? dayjs(schoolSchedule.end_time, "h:mm A Z")
        : null,
    },
  });

  const period_id_form = watch("period_id");
  const study_program_id_form = watch("study_program_id");
  const grade_form = watch("grade");
  const start_time_dayjs = watch("start_time_dayjs");
  const end_time_dayjs = watch("end_time_dayjs");

  const { data: schoolSchedules = [] } = useSchoolSchedules(period_id);
  const fitleredSchoolSchedules = schoolSchedules.filter(
    (item) =>
      item.period_id === period_id_form &&
      item.study_program_id === study_program_id_form &&
      item.grade === grade_form
  );
  const usedDays = fitleredSchoolSchedules.map((item) => item.day);
  const filteredDayOptions = dayOptions.filter(
    (item) =>
      !usedDays.includes(item.value) || schoolSchedule?.day === item.value
  );

  //  ─────────────────────────── check dependent ───────────────────────────
  const { data: classSchedules = [] } = useClassSchedules(period_id_form);
  const { data: nonLearningSchedules = [] } =
    useNonLearningSchedules(period_id_form);
  const filteredClassSchedules = classSchedules.filter(
    (item) => item.school_schedule_id === school_schedule_id
  );
  const filteredNonLearningChedules = nonLearningSchedules.filter(
    (item) => item.school_schedule_id === school_schedule_id
  );
  const canBeDeleted =
    filteredClassSchedules.length === 0 &&
    filteredNonLearningChedules.length === 0;
  //  ─────────────────────────── check dependent ───────────────────────────

  const { mutate: createSchoolSchedule } = useCreateSchoolSchedule({
    onSuccess: () => {
      toggle();
      reset();
      toast.success("Jam sekolah berhasil ditambahkan");
    },
    onError: () => {
      toast.error("Jam sekolah gagal ditambahkan");
    },
  });

  const { mutate: updateSchoolSchedule } = useUpdateSchoolSchedule({
    onSuccess: () => {
      toggle();
      reset();
      toast.success("Jam sekolah berhasil diperbarui");
    },
    onError: () => {
      toast.error("Jam sekolah gagal diperbarui");
    },
  });

  const { mutate: deleteSchoolSchedule } = useDeleteSchoolSchedule({
    onSuccess: () => {
      toggle();
      reset();
      toast.success("Jam sekolah berhasil dihapus");
    },
    onError: () => {
      toast.error("Jam sekolah gagal dihapus");
    },
  });

  return (
    <>
      {renderTrigger ? (
        renderTrigger(toggle)
      ) : (
        <Button variant="contained" startIcon={<Add />} onClick={toggle}>
          Tambah
        </Button>
      )}
      <Modal open={visible} onClose={toggle}>
        <Stack
          sx={{
            width: 1,
            height: 1,
            justifyContent: "center",
            alignItems: "center",
            p: 2,
          }}
        >
          <Paper
            component={"form"}
            sx={{
              py: 2,
              width: 1,
              maxWidth: "ms",
              gap: 2,
              display: "flex",
              flexDirection: "column",
            }}
            onSubmit={handleSubmit((value) => {
              if (school_schedule_id) {
                if (deleteMode) return deleteSchoolSchedule(school_schedule_id);
                updateSchoolSchedule({
                  ...value,
                  start_time: value.start_time_dayjs.format("h:mm A Z"),
                  end_time: value.end_time_dayjs.format("h:mm A Z"),
                  school_schedule_id: school_schedule_id,
                });
              } else {
                createSchoolSchedule({
                  ...value,
                  start_time: value.start_time_dayjs.format("h:mm A Z"),
                  end_time: value.end_time_dayjs.format("h:mm A Z"),
                });
              }
            })}
          >
            <Typography sx={{ fontWeight: 600, px: 2 }}>
              {(() => {
                if (school_schedule_id) {
                  if (deleteMode) return "Hapus Jam Sekolah";
                  return "Edit Jam Sekolah";
                }
                return "Tambah Jam Sekolah";
              })()}
            </Typography>
            <Divider />
            <Stack sx={{ gap: 2, px: 2 }}>
              {!canBeDeleted && deleteMode && (
                <Alert severity="warning">
                  Jam sekolah ini sedang digunakan
                </Alert>
              )}
              <AutocompleteElement
                name="day"
                label="Hari"
                control={control}
                matchId
                options={filteredDayOptions.map((item) => ({
                  id: item.value,
                  label: item.label,
                }))}
                autocompleteProps={{
                  disabled: deleteMode,
                }}
              />
              <TimePickerElement
                label="Jam mulai"
                name="start_time_dayjs"
                control={control}
                disabled={deleteMode}
                maxTime={end_time_dayjs ?? undefined}
              />
              <TimePickerElement
                label="Jam selesai"
                name="end_time_dayjs"
                control={control}
                disabled={deleteMode}
                minTime={start_time_dayjs ?? undefined}
              />
            </Stack>
            <Divider />
            <Stack sx={{ gap: 2, px: 2, flexDirection: "row" }}>
              <Button variant="outlined" fullWidth onClick={toggle}>
                Batal
              </Button>
              <Button
                type="submit"
                variant="contained"
                fullWidth
                color={deleteMode ? "error" : undefined}
                disabled={!canBeDeleted && deleteMode}
              >
                {deleteMode ? "Hapus" : "Simpan"}
              </Button>
            </Stack>
          </Paper>
        </Stack>
      </Modal>
    </>
  );
}
