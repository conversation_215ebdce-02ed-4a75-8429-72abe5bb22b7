import AcademicAPI from "@sisva/api/academic";
import type {
  NonLearningSchedules,
  Period,
  SchoolSchedules,
  StudyProgram,
} from "@sisva/types/apiTypes";
import { getDay } from "@sisva/types/types";
import { checkScheduleConflict } from "@sisva/utils";
import { dateFromOADate } from "@sisva/utils";
import dayjs from "dayjs";
import customParseFormat from "dayjs/plugin/customParseFormat";

import type { AktivitasNonKBMInputData } from "./types";

dayjs.extend(customParseFormat);

function getStudyProgram(studyPrograms: StudyProgram[], name: string) {
  return studyPrograms.find((programStudi) => programStudi.name === name);
}

function getPeriod(periods: Period[], name: string) {
  return periods.find((period) => period.name === name);
}

export default async function handleAktivitasNonKBM(
  data: AktivitasNonKBMInputData
) {
  // periods
  const periods: Period[] = (await AcademicAPI.getAllPeriod()).data.data;
  const periodNames = periods.map((period) => period.name);
  const periodIds = periods.map((period) => period.id);

  // school schedules
  const schoolSchedules: SchoolSchedules[] = await (async () => {
    const arrayOfSchoolSchedules: SchoolSchedules[][] = [];
    for (const id of periodIds) {
      arrayOfSchoolSchedules.push(
        (await AcademicAPI.getAllSchoolSchedules({ period_id: id })).data.data
      );
    }
    return arrayOfSchoolSchedules.flat();
  })();

  // study programs
  const studyPrograms: StudyProgram[] = (await AcademicAPI.getAllProdi()).data
    .data;
  const studyProgramNames = studyPrograms.map(
    (studyProgram) => studyProgram.name
  );

  const nonLearningSchedules: NonLearningSchedules[] = await (async () => {
    const arrayOfClassSchedules: NonLearningSchedules[][] = [];
    for (const id of periodIds) {
      arrayOfClassSchedules.push(
        (await AcademicAPI.getAllNonLearningSchedules({ period_id: id })).data
          .data
      );
    }
    return arrayOfClassSchedules.flat();
  })();

  const dataObjects = data
    .map((row) => {
      return {
        id: crypto.randomUUID(),
        nama_aktivitas: row[0],
        nama_periode: row[1],
        nama_program_studi: row[2],
        grade: row[3],
        day: getDay(row[4]),
        jam_mulai: dayjs(dateFromOADate(row[5])).format("h:mm A Z"),
        jam_selesai: dayjs(dateFromOADate(row[6])).format("h:mm A Z"),
      };
    })
    .filter((data) => {
      const startTime = dayjs(data.jam_mulai, "h:mm A Z");
      const endTime = dayjs(data.jam_selesai, "h:mm A Z");
      return startTime.isBefore(endTime);
    });

  let createObjects = dataObjects.filter((data) => {
    const schoolSchedule = schoolSchedules.find(
      (schoolSchedule) =>
        schoolSchedule.period_id ===
          getPeriod(periods, data.nama_periode)?.id &&
        schoolSchedule.study_program_id ===
          getStudyProgram(studyPrograms, data.nama_program_studi)?.id &&
        schoolSchedule.grade === data.grade &&
        schoolSchedule.day === data.day
    );

    return (
      schoolSchedule &&
      !nonLearningSchedules.some(
        (nonLearningSchedule) =>
          nonLearningSchedule.name === data.nama_aktivitas &&
          nonLearningSchedule.school_schedule_id === schoolSchedule?.id &&
          nonLearningSchedule.start_time === data.jam_mulai &&
          nonLearningSchedule.end_time === data.jam_selesai
      ) &&
      periodNames.includes(data.nama_periode) &&
      studyProgramNames.includes(data.nama_program_studi)
    );
  });

  const createObjectWithConflictIndex: number[] = [];

  for (const [i, data] of createObjects.entries()) {
    const schoolSchedule = schoolSchedules.find(
      (schoolSchedule) =>
        schoolSchedule.period_id ===
          getPeriod(periods, data.nama_periode)?.id &&
        schoolSchedule.study_program_id ===
          getStudyProgram(studyPrograms, data.nama_program_studi)?.id &&
        schoolSchedule.grade === data.grade &&
        schoolSchedule.day === data.day
    );

    const { hasConflict } = await checkScheduleConflict({
      schedule: {
        id: data.id,
        school_schedule_id: schoolSchedule!.id,
        start_time: data.jam_mulai,
        end_time: data.jam_selesai,
      },
      otherSchedules: createObjects.map((data) => {
        const schoolSchedule = schoolSchedules.find(
          (schoolSchedule) =>
            schoolSchedule.period_id ===
              getPeriod(periods, data.nama_periode)?.id &&
            schoolSchedule.study_program_id ===
              getStudyProgram(studyPrograms, data.nama_program_studi)?.id &&
            schoolSchedule.grade === data.grade &&
            schoolSchedule.day === data.day
        );
        return {
          id: data.id,
          school_schedule_id: schoolSchedule!.id,
          start_time: data.jam_mulai,
          end_time: data.jam_selesai,
        };
      }),
    });

    if (hasConflict) createObjectWithConflictIndex.push(i);
  }

  createObjects = createObjects.filter(
    (_, i) => !createObjectWithConflictIndex.includes(i)
  );

  const promisesCreate = createObjects.map((data) => {
    const school_schedule_id = schoolSchedules.find(
      (schoolSchedule) =>
        schoolSchedule.period_id ===
          getPeriod(periods, data.nama_periode)?.id &&
        schoolSchedule.study_program_id ===
          getStudyProgram(studyPrograms, data.nama_program_studi)?.id &&
        schoolSchedule.grade === data.grade &&
        schoolSchedule.day === data.day
    )?.id;

    const payload = {
      name: data.nama_aktivitas,
      school_schedule_id,
      start_time: data.jam_mulai,
      end_time: data.jam_selesai,
    };

    return AcademicAPI.createNonLearningSchedule(payload);
  });

  const res = await Promise.all(promisesCreate);
  const reportText: string[] = [];
  if (promisesCreate.length)
    reportText.push(
      `${promisesCreate.length} baris Aktivitas Non KBM berhasil ditambahkan`
    );
  if (createObjectWithConflictIndex.length)
    reportText.push(
      `${createObjectWithConflictIndex.length} baris Aktivitas Non KBM konflik dan diabaikan`
    );
  return reportText.join(", ");
}
