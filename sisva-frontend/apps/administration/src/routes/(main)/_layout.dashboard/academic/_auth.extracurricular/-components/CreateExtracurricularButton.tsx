import { yupResolver } from "@hookform/resolvers/yup";
import { Add } from "@mui/icons-material";
import {
  Button,
  Divider,
  Modal,
  Paper,
  Stack,
  Typography,
} from "@mui/material";
import {
  useCreateExtracurricular,
  useExtracurricular,
  useUpdateExtracurricular,
} from "@sisva/hooks/query/academic/useExtracurriculars";
import { useTeachers } from "@sisva/hooks/query/user/useTeachers";
import { useToggle } from "ahooks";
import type { ReactNode } from "react";
import {
  AutocompleteElement,
  TextFieldElement,
  useForm,
} from "react-hook-form-mui";
import toast from "react-hot-toast";
import { object, string } from "yup";

export default function CreateExtracurricularButton({
  // if extracurricular_id provided, then it's edit mode
  extracurricular_id,
  renderTrigger,
}: {
  extracurricular_id?: number;
  renderTrigger?: (onClick: () => void) => ReactNode;
}) {
  const { data: teachers = [] } = useTeachers();
  const { data: extracurricular } = useExtracurricular(extracurricular_id);

  const [visible, { toggle }] = useToggle(false);
  const { control, handleSubmit, reset } = useForm({
    values: {
      name: extracurricular?.name ?? "",
      teacher_id: extracurricular?.teacher_id ?? "",
    },
    resolver: yupResolver(
      object({
        name: string().required("Wajib diisi."),
        teacher_id: string().required("Wajib diisi."),
      })
    ),
  });

  const { mutate: createExtracurricular } = useCreateExtracurricular({
    onSuccess: () => {
      toggle();
      reset();
      toast.success("Ekstrakurikuler berhasil ditambahkan");
    },
    onError: () => {
      toast.error("Ekstrakurikuler gagal ditambahkan");
    },
  });

  const { mutate: updateExtracurricular } = useUpdateExtracurricular({
    onSuccess: () => {
      toggle();
      reset();
      toast.success("Ekstrakurikuler berhasil diperbarui");
    },
    onError: () => {
      toast.error("Ekstrakurikuler gagal diperbarui");
    },
  });

  return (
    <>
      {renderTrigger ? (
        renderTrigger(toggle)
      ) : (
        <Button variant="contained" startIcon={<Add />} onClick={toggle}>
          Tambah
        </Button>
      )}
      <Modal open={visible} onClose={toggle}>
        <Stack
          sx={{
            width: 1,
            height: 1,
            justifyContent: "center",
            alignItems: "center",
            p: 2,
          }}
        >
          <Paper
            component={"form"}
            sx={{
              py: 2,
              width: 1,
              maxWidth: "ms",
              gap: 2,
              display: "flex",
              flexDirection: "column",
            }}
            onSubmit={handleSubmit((value) => {
              if (extracurricular_id) {
                updateExtracurricular({
                  extracurricular_id,
                  name: value.name,
                  teacher_id: value.teacher_id,
                });
              } else {
                createExtracurricular(value);
              }
            })}
          >
            <Typography sx={{ fontWeight: 600, px: 2 }}>
              {extracurricular
                ? "Edit Ekstrakurikuler"
                : "Tambah Eksrakurikuler"}
            </Typography>
            <Divider />
            <Stack sx={{ gap: 2, px: 2 }}>
              <TextFieldElement
                name="name"
                label="Nama Ekstrakurikuler"
                control={control}
              />
              <AutocompleteElement
                name="teacher_id"
                label="Pembina"
                control={control}
                matchId
                options={teachers.map((item) => ({
                  id: item.id,
                  label: item.name,
                }))}
              />
            </Stack>
            <Divider />
            <Stack sx={{ gap: 2, px: 2, flexDirection: "row" }}>
              <Button variant="outlined" fullWidth onClick={toggle}>
                Batal
              </Button>
              <Button type="submit" variant="contained" fullWidth>
                Simpan
              </Button>
            </Stack>
          </Paper>
        </Stack>
      </Modal>
    </>
  );
}
