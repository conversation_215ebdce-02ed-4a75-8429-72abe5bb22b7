import { valibotResolver } from "@hookform/resolvers/valibot";
import { Add } from "@mui/icons-material";
import {
  <PERSON><PERSON>,
  Button,
  Divider,
  Modal,
  Paper,
  Stack,
  Typography,
} from "@mui/material";
import {
  useClasses,
  useClassWithSubjectAndTeacherAndStudentGroup,
  useCreateClass,
} from "@sisva/hooks/query/academic/useClasses";
import {
  useClassSchedule,
  useCreateClassSchedule,
  useDeleteClassSchedules,
  useUpdateClassSchedule,
} from "@sisva/hooks/query/academic/useClassSchedules";
import { useCredit } from "@sisva/hooks/query/academic/useCredits";
import {
  usePeriod,
  usePeriodCurriculums,
} from "@sisva/hooks/query/academic/usePeriods";
import {
  useSchoolSchedule,
  useSchoolSchedules,
} from "@sisva/hooks/query/academic/useSchoolSchedules";
import { useStudentGroups } from "@sisva/hooks/query/academic/useStudentGroups";
import {
  useStudyProgram,
  useStudyPrograms,
} from "@sisva/hooks/query/academic/useStudyPrograms";
import { useSubjects } from "@sisva/hooks/query/academic/useSubjects";
import { useSubjectTeachers } from "@sisva/hooks/query/academic/useSubjectTeachers";
import { getDayText } from "@sisva/types/types";
import { isScheduleConflictError } from "@sisva/utils";
import { useToggle } from "ahooks";
import type { Dayjs } from "dayjs";
import dayjs from "dayjs";
import { sort } from "fast-sort";
import { type ReactNode, useEffect, useMemo, useRef } from "react";
import { AutocompleteElement, useForm } from "react-hook-form-mui";
import { TimePickerElement } from "react-hook-form-mui/date-pickers";
import toast from "react-hot-toast";
import {
  custom,
  type InferOutput,
  nonEmpty,
  number,
  object,
  pipe,
  string,
} from "valibot";

import { toastScheduleError } from "./ScheduleErrorToast";

export default function CreateClassScheduleButton({
  class_schedule_id, // if passed this component will be in edit mode
  study_program_id, // if passed, the form will preselect study_program_id with this value
  period_id, // required
  grade, // if passed, the form will preselect grade with this value
  student_group_id, // if passed, the form will preselect student_group_id with this value
  deleteMode, // if true, this component will be in delete mode
  renderTrigger,
}: {
  class_schedule_id?: number | null;
  study_program_id?: number | null;
  period_id?: number | null;
  grade?: string | null;
  student_group_id?: number | null;
  deleteMode?: boolean;
  renderTrigger?: (onClick: () => void) => ReactNode;
}) {
  const deleteModeRef = useRef(deleteMode ?? false);

  const { data: credit } = useCredit();
  const enableAutoSetStartTimeRef = useRef(true);
  const enableAutoSetEndTimeRef = useRef(true);

  const { data: classSchedule } = useClassSchedule({
    class_schedule_id,
    period_id,
  });

  const { data: schoolScheduleFromProps } = useSchoolSchedule({
    period_id,
    school_schedule_id: classSchedule?.school_schedule_id,
  });

  const { data: class_ } = useClassWithSubjectAndTeacherAndStudentGroup(
    classSchedule?.class_id
  );

  function dayjsSchema() {
    return custom<Dayjs>((input) => {
      try {
        //@ts-expect-error check if valid dayjs object
        return input.isValid();
      } catch {
        return false;
      }
    }, "Jam harus dipilih");
  }

  const [visible, { toggle }] = useToggle(false);

  const schema = object({
    period_id: number("Periode harus dipilih"),
    study_program_id: number("Program studi harus dipilih"),
    grade: pipe(
      string("Tingkatan harus diisi"),
      nonEmpty("Tingkatan harus diisi")
    ),
    student_group_id: number("Kelas harus dipilih"),
    subject_id: number("Mata pelajaran harus dipilih"),
    teacher_id: pipe(
      string("Guru harus dipilih"),
      nonEmpty("Guru harus dipilih")
    ),
    class_id: number(),
    school_schedule_id: number("Hari harus dipilih"),
    start_time_dayjs: dayjsSchema(),
    end_time_dayjs: dayjsSchema(),
  });
  type Schema = InferOutput<typeof schema>;
  const {
    control,
    handleSubmit,
    reset,
    watch,
    formState: { isValid },
    setValue,
  } = useForm<Schema>({
    resolver: valibotResolver(schema),
    values: {
      //@ts-expect-error allow null
      period_id: schoolScheduleFromProps?.period_id ?? period_id ?? null,
      //@ts-expect-error allow null
      study_program_id:
        schoolScheduleFromProps?.study_program_id ?? study_program_id ?? null,
      grade: schoolScheduleFromProps?.grade ?? grade ?? "",
      //@ts-expect-error allow null
      student_group_id: class_?.student_group_id ?? student_group_id ?? null,
      //@ts-expect-error allow null
      subject_id: class_?.subject_id ?? null,
      teacher_id: class_?.teacher_id ?? "",
      //@ts-expect-error allow null
      school_schedule_id: classSchedule?.school_schedule_id ?? null,
      //@ts-expect-error allow null
      start_time_dayjs: classSchedule?.start_time
        ? dayjs(classSchedule.start_time, "h:mm A Z")
        : null,
      //@ts-expect-error allow null
      end_time_dayjs: classSchedule?.start_time
        ? dayjs(classSchedule.end_time, "h:mm A Z")
        : null,
    },
  });

  const period_id_form = watch("period_id");
  const study_program_id_form = watch("study_program_id");
  const grade_form = watch("grade");
  const student_group_id_form = watch("student_group_id");
  const subject_id_form = watch("subject_id");
  const teacher_id_form = watch("teacher_id");
  const start_time_dayjs = watch("start_time_dayjs");
  const end_time_dayjs = watch("end_time_dayjs");
  const school_schedule_id_form = watch("school_schedule_id");
  const class_id_form = watch("class_id");

  const { data: schoolSchedules = [] } = useSchoolSchedules(period_id);
  const fitleredSchoolSchedules = schoolSchedules.filter(
    (item) =>
      item.period_id === period_id_form &&
      item.study_program_id === study_program_id_form &&
      item.grade === grade_form
  );

  const { data: period } = usePeriod(period_id_form);
  const studyProgramIds = period?.study_programs?.map((item) => item.id) ?? [];

  const { data: studyPrograms = [] } = useStudyPrograms();
  const fitleredStudyPrograms = studyPrograms.filter((item) =>
    studyProgramIds.includes(item.id)
  );

  const { data: studyProgram } = useStudyProgram(study_program_id_form);
  const grades = studyProgram?.grades ?? [];

  const { data: studentGroups = [] } = useStudentGroups();
  const filteredStudentGroups = studentGroups.filter(
    (item) =>
      item.study_program_id === study_program_id_form &&
      item.grade === grade_form &&
      item.period_id === period_id_form
  );

  const { data: periodCurriculums = [] } = usePeriodCurriculums();
  const filteredPeriodCurriculums = periodCurriculums.filter(
    (item) =>
      item.period_id === period_id_form &&
      item.study_program_id === study_program_id_form &&
      item.grade === grade_form
  );
  const curriculumIds = filteredPeriodCurriculums.map(
    (item) => item.curriculum_id
  );

  const { data: subjects = [] } = useSubjects();
  const fitleredSubjects = subjects.filter(
    (item) =>
      item.study_program_id === study_program_id_form &&
      curriculumIds.includes(item.curriculum_id)
  );

  const { data: subjectTeachers = [] } = useSubjectTeachers();
  const filteredSubjectTeachers = subjectTeachers.filter(
    (item) => item.subject_id === subject_id_form && item.grade === grade_form
  );

  const { data: schoolSchedule } = useSchoolSchedule({
    period_id,
    school_schedule_id: school_schedule_id_form,
  });
  const schoolScheduleEndTime = useMemo(() => {
    return schoolSchedule ? dayjs(schoolSchedule.end_time, "h:mm A Z") : null;
  }, [schoolSchedule]);
  const schoolScheduleStartTime = useMemo(() => {
    return schoolSchedule ? dayjs(schoolSchedule.start_time, "h:mm A Z") : null;
  }, [schoolSchedule]);

  const { mutateAsync: createClass } = useCreateClass({
    onSuccess: () => {
      console.info("class berhasil dibuat");
    },
    onError: () => {
      console.error("class gagal dibuat");
    },
  });

  const { data: classes = [], isLoading: L1 } = useClasses();
  // set class_id, create new class if doesn't exist
  useEffect(() => {
    if (student_group_id_form && subject_id_form && teacher_id_form && !L1) {
      const class_ = classes.find(
        (item) =>
          item.student_group_id === student_group_id_form &&
          item.subject_id === subject_id_form &&
          item.teacher_id === teacher_id_form
      );
      if (class_) {
        setValue("class_id", class_.id);
      } else {
        createClass({
          name: `${subject_id_form} - ${teacher_id_form} - ${student_group_id_form}`,
          student_group_id: student_group_id_form,
          teacher_id: teacher_id_form,
          subject_id: subject_id_form,
        }).then((class_id) => setValue("class_id", class_id));
      }
    }
  }, [
    L1,
    classes,
    createClass,
    setValue,
    student_group_id_form,
    subject_id_form,
    teacher_id_form,
  ]);

  // autoset start_time_dayjs whenever end_time_dayjs changed
  useEffect(() => {
    if (
      credit?.duration_minutes &&
      enableAutoSetStartTimeRef.current &&
      end_time_dayjs &&
      schoolScheduleStartTime &&
      !class_schedule_id
    ) {
      const newStartTime = end_time_dayjs.subtract(
        credit.duration_minutes,
        "minutes"
      );
      if (schoolScheduleStartTime.isBefore(newStartTime)) {
        enableAutoSetEndTimeRef.current = false; // prevent infinite loop
        setValue("start_time_dayjs", newStartTime);
      }
    }
  }, [
    class_schedule_id,
    credit,
    end_time_dayjs,
    schoolScheduleStartTime,
    setValue,
  ]);

  // autoset end_time_dayjs whenever start_time_dayjs changed
  useEffect(() => {
    if (
      credit?.duration_minutes &&
      enableAutoSetEndTimeRef.current &&
      start_time_dayjs &&
      schoolScheduleEndTime &&
      !class_schedule_id
    ) {
      const newEndTime = start_time_dayjs.add(
        credit.duration_minutes,
        "minutes"
      );
      if (schoolScheduleEndTime.isAfter(newEndTime)) {
        enableAutoSetStartTimeRef.current = false; // prevent infinite loop
        setValue("end_time_dayjs", newEndTime);
      }
    }
  }, [
    class_schedule_id,
    credit,
    schoolScheduleEndTime,
    setValue,
    start_time_dayjs,
  ]);

  // reset grade whenever study_program_id changed
  useEffect(() => {
    if (!class_schedule_id) setValue("grade", "");
  }, [class_schedule_id, setValue, study_program_id_form]);

  // reset student_group_id whenever grade changed
  useEffect(() => {
    //@ts-expect-error allow null
    if (!class_schedule_id) setValue("student_group_id", null);
  }, [class_schedule_id, grade_form, setValue]);

  // reset subject_id whenever student_group_id changed
  useEffect(() => {
    //@ts-expect-error allow null
    if (!class_schedule_id) setValue("subject_id", null);
  }, [class_schedule_id, setValue, student_group_id_form]);

  // reset teacher_id whenever subject_id changed
  useEffect(() => {
    if (!class_schedule_id) setValue("teacher_id", "");
  }, [class_schedule_id, setValue, subject_id_form]);

  // reset start_time_dayjs and end_time_dayjs whenever school_schedule_id changed
  useEffect(() => {
    if (!class_schedule_id) {
      //@ts-expect-error allow null
      setValue("start_time_dayjs", null);
      //@ts-expect-error allow null
      setValue("end_time_dayjs", null);
      enableAutoSetStartTimeRef.current = true;
      enableAutoSetEndTimeRef.current = true;
    }
  }, [class_schedule_id, setValue, school_schedule_id_form]);

  // //  ─────────────────────────── check dependent ───────────────────────────
  // //  ─────────────────────────── check dependent ───────────────────────────
  const canBeDeleted = true;

  const { mutate: createClassSchedule, isPending: P1 } = useCreateClassSchedule(
    {
      class_id: class_id_form,
      school_schedule_id: school_schedule_id_form,
      onSuccess: () => {
        toggle();
        reset();
        enableAutoSetStartTimeRef.current = true;
        enableAutoSetEndTimeRef.current = true;
        toast.success("Jadwal kelas berhasil ditambahkan");
      },
      onError: (error) => {
        const conflictError = isScheduleConflictError(error);
        if (conflictError) {
          return toastScheduleError(conflictError);
        }
        toast.error("Jadwal kelas gagal ditambahkan");
      },
    }
  );

  const { mutate: updateClassSchedule, isPending: P2 } = useUpdateClassSchedule(
    {
      class_id: class_id_form,
      school_schedule_id: school_schedule_id_form,
      onSuccess: () => {
        toggle();
        reset();
        toast.success("Jadwal kelas berhasil diperbarui");
      },
      onError: (error) => {
        const conflictError = isScheduleConflictError(error);
        if (conflictError) {
          return toastScheduleError(conflictError);
        }
        toast.error("Jadwal kelas gagal diperbarui");
      },
    }
  );

  const { mutate: deleteClassSchedule, isPending: P3 } =
    useDeleteClassSchedules({
      onSuccess: () => {
        toggle();
        reset();
        toast.success("Jadwal kelas berhasil dihapus");
      },
      onError: () => {
        toast.error("Jadwal kelas gagal dihapus");
      },
    });

  return (
    <>
      {renderTrigger ? (
        renderTrigger(toggle)
      ) : (
        <Button variant="contained" startIcon={<Add />} onClick={toggle}>
          Tambah
        </Button>
      )}
      <Modal open={visible} onClose={toggle}>
        <Stack
          sx={{
            width: 1,
            height: 1,
            justifyContent: "center",
            alignItems: "center",
            p: 2,
          }}
        >
          <Paper
            component={"form"}
            sx={{
              py: 2,
              width: 1,
              maxWidth: "ms",
              gap: 2,
              display: "flex",
              flexDirection: "column",
            }}
            onSubmit={handleSubmit((value) => {
              if (class_schedule_id) {
                if (deleteMode || deleteModeRef.current) {
                  deleteModeRef.current = false;
                  return deleteClassSchedule(class_schedule_id);
                }
                updateClassSchedule({
                  ...value,
                  start_time: value.start_time_dayjs.format("h:mm A Z"),
                  end_time: value.end_time_dayjs.format("h:mm A Z"),
                  class_schedule_id,
                });
              } else {
                createClassSchedule({
                  ...value,
                  start_time: value.start_time_dayjs.format("h:mm A Z"),
                  end_time: value.end_time_dayjs.format("h:mm A Z"),
                });
              }
            })}
          >
            <Typography sx={{ fontWeight: 600, px: 2 }}>
              {(() => {
                if (class_schedule_id) {
                  if (deleteMode) return "Hapus Jadwal Kelas";
                  return "Edit Jadwal Kelas";
                }
                return "Tambah Jadwal Kelas";
              })()}
            </Typography>
            <Divider />
            <Stack sx={{ gap: 2, px: 2 }}>
              {!canBeDeleted && deleteMode && (
                <Alert severity="warning">
                  Jam sekolah ini sedang digunakan
                </Alert>
              )}

              <AutocompleteElement
                name="study_program_id"
                label="Program Studi"
                control={control}
                matchId
                options={fitleredStudyPrograms.map((item) => ({
                  id: item.id,
                  label: item.name,
                }))}
                autocompleteProps={{
                  disabled: deleteMode || !!class_schedule_id,
                }}
              />
              <AutocompleteElement
                name="grade"
                label="Tingkatan"
                control={control}
                matchId
                options={grades.map((item) => ({
                  id: item,
                  label: item,
                }))}
                autocompleteProps={{
                  disabled:
                    deleteMode || !study_program_id_form || !!class_schedule_id,
                }}
              />
              <AutocompleteElement
                name="student_group_id"
                label="Kelas"
                control={control}
                matchId
                options={filteredStudentGroups.map((item) => ({
                  id: item.id,
                  label: item.name,
                }))}
                autocompleteProps={{
                  disabled: deleteMode || !grade_form || !!class_schedule_id,
                }}
              />
              <AutocompleteElement
                name="subject_id"
                label="Mata pelajaran"
                control={control}
                matchId
                options={fitleredSubjects.map((item) => ({
                  id: item.id,
                  label: item.name,
                }))}
                autocompleteProps={{
                  disabled:
                    deleteMode || !student_group_id_form || !!class_schedule_id,
                }}
              />
              <AutocompleteElement
                name="teacher_id"
                label="Guru"
                control={control}
                matchId
                options={filteredSubjectTeachers.map((item) => ({
                  id: item.teacher_id,
                  label: item.teacher_name,
                }))}
                autocompleteProps={{
                  disabled:
                    deleteMode || !subject_id_form || !!class_schedule_id,
                }}
              />
              <AutocompleteElement
                name="school_schedule_id"
                label="Hari"
                control={control}
                matchId
                options={sort(fitleredSchoolSchedules)
                  .asc((item) => item.day)
                  .map((item) => ({
                    id: item.id,
                    label: getDayText(item.day),
                  }))}
                autocompleteProps={{
                  disabled:
                    deleteMode ||
                    !grade_form ||
                    !study_program_id_form ||
                    !!class_schedule_id,
                }}
              />
              <TimePickerElement
                label="Jam mulai"
                name="start_time_dayjs"
                control={control}
                disabled={deleteMode || !school_schedule_id_form}
                minTime={schoolScheduleStartTime ?? undefined}
                maxTime={end_time_dayjs ?? schoolScheduleEndTime ?? undefined}
                onChange={() => {
                  enableAutoSetStartTimeRef.current = false;
                }}
              />
              <TimePickerElement
                label="Jam selesai"
                name="end_time_dayjs"
                control={control}
                disabled={deleteMode || !school_schedule_id_form}
                minTime={
                  start_time_dayjs ?? schoolScheduleStartTime ?? undefined
                }
                maxTime={schoolScheduleEndTime ?? undefined}
                onChange={() => {
                  enableAutoSetEndTimeRef.current = false;
                }}
              />
            </Stack>
            <Divider />
            <Stack sx={{ gap: 2, px: 2, flexDirection: "row" }}>
              {class_schedule_id && (
                <Button
                  type="submit"
                  variant="contained"
                  fullWidth
                  color="error"
                  disabled={!canBeDeleted || P1 || P2 || P3}
                  onClick={() => {
                    deleteModeRef.current = true;
                  }}
                >
                  Hapus
                </Button>
              )}
              <Button variant="outlined" fullWidth onClick={toggle}>
                Batal
              </Button>
              <Button
                type="submit"
                variant="contained"
                fullWidth
                color={deleteMode ? "error" : undefined}
                disabled={(!canBeDeleted && deleteMode) || P1 || P2 || P3}
              >
                {deleteMode ? "Hapus" : "Simpan"}
              </Button>
            </Stack>
          </Paper>
        </Stack>
      </Modal>
    </>
  );
}
