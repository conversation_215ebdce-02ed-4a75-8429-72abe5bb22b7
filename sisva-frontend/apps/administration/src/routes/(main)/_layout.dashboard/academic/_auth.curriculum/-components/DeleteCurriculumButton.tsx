import { Delete<PERSON>orever } from "@mui/icons-material";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Paper,
  Stack,
  Typography,
} from "@mui/material";
import {
  useCurriculum,
  useDeleteCurriculum,
} from "@sisva/hooks/query/academic/useCurriculums";
import { usePeriodCurriculums } from "@sisva/hooks/query/academic/usePeriods";
import { useSubjects } from "@sisva/hooks/query/academic/useSubjects";
import { useSyllabuses } from "@sisva/hooks/query/academic/useSyllabuses";
import { useTeachingMaterials } from "@sisva/hooks/query/classroom/useTeachingMaterials";
import { useToggle } from "ahooks";
import dayjs from "dayjs";
import id from "dayjs/locale/id";
import type { ReactNode } from "react";
import toast from "react-hot-toast";
dayjs.locale(id);

export default function DeleteCurriculumButton({
  curriculum_id,
  renderTrigger,
}: {
  curriculum_id: number;
  renderTrigger?: (onClick: () => void) => ReactNode;
}) {
  const [visible, { toggle }] = useToggle(false);
  const { data: curriculum } = useCurriculum(curriculum_id);

  //  ────────────────────────── check dependecies ──────────────────────────
  const { data: periodCurriculums = [] } = usePeriodCurriculums();
  const { data: subjects = [] } = useSubjects();
  const { data: teachingMaterials = [] } = useTeachingMaterials();
  const { data: syllabuses = [] } = useSyllabuses();

  const filteredPeriodCurriculums = periodCurriculums.filter(
    (item) => item.curriculum_id === curriculum_id
  );
  const filteredSubjects = subjects.filter(
    (item) => item.curriculum_id === curriculum_id
  );
  const filteredTeachingMaterials = teachingMaterials.filter(
    (item) => item.curriculum_id === curriculum_id
  );
  const filteredSyllabuses = syllabuses.filter(
    (item) => item.curriculum_id === curriculum_id
  );

  const canBeDeleted =
    filteredPeriodCurriculums.length === 0 &&
    filteredSubjects.length === 0 &&
    filteredTeachingMaterials.length === 0 &&
    filteredSyllabuses.length === 0;
  //  ────────────────────────── check dependecies ──────────────────────────

  const { mutate: deleteCurriculum } = useDeleteCurriculum({
    onSuccess: () => {
      toggle();
      toast.success("Kurikulum berhasil dihapus");
    },
    onError: () => {
      toast.error("Kurikulum gagal dihapus");
    },
  });

  return (
    <>
      {renderTrigger ? (
        renderTrigger(toggle)
      ) : (
        <Button
          variant="contained"
          startIcon={<DeleteForever />}
          color="error"
          onClick={toggle}
        >
          Hapus
        </Button>
      )}
      <Modal open={visible} onClose={toggle}>
        <Stack
          sx={{
            width: 1,
            height: 1,
            justifyContent: "center",
            alignItems: "center",
            p: 2,
          }}
        >
          <Paper
            component="form"
            sx={{
              py: 2,
              width: 1,
              maxWidth: "ms",
              gap: 2,
              display: "flex",
              flexDirection: "column",
            }}
            onSubmit={(e) => {
              e.preventDefault();
              deleteCurriculum(curriculum_id);
            }}
          >
            <Typography sx={{ fontWeight: 600, px: 2 }}>
              Hapus Kurikulum
            </Typography>
            <Divider />
            <Stack sx={{ gap: 2, px: 2 }}>
              {!canBeDeleted && (
                <Alert severity="warning">
                  Hanya dapat menghapus Kurikulum yang belum digunakan
                </Alert>
              )}
              <Stack
                sx={{ flexDirection: "row", justifyContent: "space-between" }}
              >
                <Typography sx={{ fontWeight: 600 }}>Nama Kurikulum</Typography>
                <Typography>{curriculum?.name}</Typography>
              </Stack>
            </Stack>
            <Divider />
            <Stack sx={{ gap: 2, px: 2, flexDirection: "row" }}>
              <Button variant="outlined" fullWidth onClick={toggle}>
                Batal
              </Button>
              <Button
                type="submit"
                variant="contained"
                fullWidth
                color="error"
                disabled={!canBeDeleted}
              >
                Hapus
              </Button>
            </Stack>
          </Paper>
        </Stack>
      </Modal>
    </>
  );
}
