import { DeleteForever } from "@mui/icons-material";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  Stack,
  Typography,
} from "@mui/material";
import {
  useDeletePeriod,
  usePeriod,
  usePeriodCurriculums,
} from "@sisva/hooks/query/academic/usePeriods";
import { useSchoolSchedules } from "@sisva/hooks/query/academic/useSchoolSchedules";
import { useStudentGroups } from "@sisva/hooks/query/academic/useStudentGroups";
import { getPeriodStatusText } from "@sisva/types/types";
import { useToggle } from "ahooks";
import dayjs from "dayjs";
import id from "dayjs/locale/id";
import type { ReactNode } from "react";
import toast from "react-hot-toast";
dayjs.locale(id);

export default function DeletePeriodButton({
  period_id,
  renderTrigger,
}: {
  period_id: number;
  renderTrigger?: (onClick: () => void) => ReactNode;
}) {
  const [visible, { toggle }] = useToggle(false);
  const { data: period } = usePeriod(period_id);

  //  ────────────────────────── check dependents ──────────────────────────
  const { data: periodCurriculums = [] } = usePeriodCurriculums();
  const { data: studentGroups = [] } = useStudentGroups();
  const { data: schoolSchedules = [] } = useSchoolSchedules(period_id);

  const fitleredPeriodCurriculums = periodCurriculums.filter(
    (item) => item.period_id === period_id
  );
  const filteredStudentGroups = studentGroups.filter(
    (item) => item.period_id === period_id
  );

  const canBeDeleted =
    period?.status !== "active" &&
    fitleredPeriodCurriculums.length === 0 &&
    filteredStudentGroups.length === 0 &&
    schoolSchedules.length === 0;
  //  ────────────────────────── check dependents ──────────────────────────

  const { mutate: deletePeriod } = useDeletePeriod({
    onSuccess: () => {
      toggle();
      toast.success("Periode berhasil dihapus");
    },
    onError: () => {
      toast.error("Periode gagal dihapus");
    },
  });

  return (
    <>
      {renderTrigger ? (
        renderTrigger(toggle)
      ) : (
        <Button
          variant="contained"
          startIcon={<DeleteForever />}
          color="error"
          onClick={toggle}
        >
          Hapus
        </Button>
      )}
      <Modal open={visible} onClose={toggle}>
        <Stack
          sx={{
            width: 1,
            height: 1,
            justifyContent: "center",
            alignItems: "center",
            p: 2,
          }}
        >
          <Paper
            component="form"
            sx={{
              py: 2,
              width: 1,
              maxWidth: "ms",
              gap: 2,
              display: "flex",
              flexDirection: "column",
            }}
            onSubmit={(e) => {
              e.preventDefault();
              deletePeriod(period_id);
            }}
          >
            <Typography sx={{ fontWeight: 600, px: 2 }}>
              Hapus Periode
            </Typography>
            <Divider />
            <Stack sx={{ gap: 2, px: 2 }}>
              {!canBeDeleted && (
                <Alert severity="warning">
                  Tidak dapat menghapus Periode yang sedang Aktif atau sedang
                  digunakan
                </Alert>
              )}
              <Stack
                sx={{ flexDirection: "row", justifyContent: "space-between" }}
              >
                <Typography sx={{ fontWeight: 600 }}>Nama Periode</Typography>
                <Typography>{period?.name}</Typography>
              </Stack>
              <Stack
                sx={{ flexDirection: "row", justifyContent: "space-between" }}
              >
                <Typography sx={{ fontWeight: 600 }}>Rentang Waktu</Typography>
                <Typography sx={{ textAlign: "right" }}>
                  {dayjs(period?.start_time, "DD/MM/YYYY h:mm A Z").format(
                    "MMMM YYYY"
                  )}{" "}
                  -{" "}
                  {dayjs(period?.end_time, "DD/MM/YYYY h:mm A Z").format(
                    "MMMM YYYY"
                  )}
                </Typography>
              </Stack>
              <Stack
                sx={{ flexDirection: "row", justifyContent: "space-between" }}
              >
                <Typography sx={{ fontWeight: 600 }}>Status</Typography>
                <Typography>
                  {period?.status && getPeriodStatusText(period?.status)}
                </Typography>
              </Stack>
            </Stack>
            <Divider />
            <Stack sx={{ gap: 2, px: 2, flexDirection: "row" }}>
              <Button variant="outlined" fullWidth onClick={toggle}>
                Batal
              </Button>
              <Button
                type="submit"
                variant="contained"
                fullWidth
                color="error"
                disabled={!canBeDeleted}
              >
                Hapus
              </Button>
            </Stack>
          </Paper>
        </Stack>
      </Modal>
    </>
  );
}
