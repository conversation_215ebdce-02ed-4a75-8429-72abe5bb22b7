import { yupResolver } from "@hookform/resolvers/yup";
import { Add } from "@mui/icons-material";
import {
  <PERSON><PERSON>,
  Divider,
  Modal,
  Paper,
  Stack,
  Typography,
} from "@mui/material";
import { useCurriculums } from "@sisva/hooks/query/academic/useCurriculums";
import { useStudyPrograms } from "@sisva/hooks/query/academic/useStudyPrograms";
import {
  useCreateSubject,
  useSubject,
  useUpdateSubject,
} from "@sisva/hooks/query/academic/useSubjects";
import { useToggle } from "ahooks";
import { type ReactNode } from "react";
import {
  AutocompleteElement,
  TextFieldElement,
  useForm,
} from "react-hook-form-mui";
import toast from "react-hot-toast";
import { number, object, string } from "yup";

/**
 * this component has 2 modes:
 * 1. create new subject, no props
 * 2. edit subject, by passing subject_id
 */
export default function CreateSubjectButton({
  // if subject_id provided, then it's edit mode
  subject_id,
  renderTrigger,
}: {
  subject_id?: number;
  renderTrigger?: (onClick: () => void) => ReactNode;
}) {
  const [visible, { toggle }] = useToggle(false);

  const { data: subject } = useSubject(subject_id);
  const { data: curriculums = [] } = useCurriculums();
  const { data: studyPrograms = [] } = useStudyPrograms();

  const { control, handleSubmit, reset, watch, setValue } = useForm({
    values: {
      name: subject?.name ?? "",
      curriculum_id: subject?.curriculum_id ?? 0,
      study_program_id: subject?.study_program_id ?? 0,
      type: subject?.type ?? "mandatory",
    },
    resolver: yupResolver(
      object({
        curriculum_id: number()
          .required("Kurikulum wajib dipilih")
          .notOneOf([0], "Kurikulum wajib dipilih"),
        study_program_id: number()
          .required("Program Studi wajib dipilih")
          .notOneOf([0], "Program Studi wajib dipilih"),
        name: string().required("Nama Mata Pelajaran wajib diisi"),
        type: string().required("Tipe Mata Pelajaran wajib diisi"),
      })
    ),
  });

  const { mutate: createSubject } = useCreateSubject({
    onSuccess: () => {
      toggle();
      reset();
      toast.success("Mata Pelajaran berhasil dibuat");
    },
    onError: () => {
      toast.error("Mata Pelajaran gagal dibuat");
    },
  });

  const { mutate: updateSubject } = useUpdateSubject({
    onSuccess: () => {
      toggle();
      reset();
      toast.success("Mata Pelajaran berhasil diperbarui");
    },
    onError: () => {
      toast.error("Mata Pelajaran gagal diperbarui");
    },
  });

  return (
    <>
      {renderTrigger ? (
        renderTrigger(toggle)
      ) : (
        <Button variant="contained" startIcon={<Add />} onClick={toggle}>
          Tambah
        </Button>
      )}
      <Modal open={visible} onClose={toggle}>
        <Stack
          sx={{
            width: 1,
            height: 1,
            justifyContent: "center",
            alignItems: "center",
            p: 2,
          }}
        >
          <Paper
            component={"form"}
            sx={{
              py: 2,
              width: 1,
              maxWidth: "ms",
              gap: 2,
              display: "flex",
              flexDirection: "column",
            }}
            onSubmit={handleSubmit((value) => {
              if (subject_id) {
                updateSubject({
                  subject_id,
                  ...value,
                });
              } else {
                createSubject(value);
              }
            })}
          >
            <Typography sx={{ fontWeight: 600, px: 2 }}>
              {(() => {
                if (subject_id) {
                  return "Edit Mata Pelajaran";
                } else {
                  return "Tambah Mata Pelajaran";
                }
              })()}
            </Typography>
            <Divider />
            <Stack sx={{ gap: 2, px: 2 }}>
              <AutocompleteElement
                control={control}
                name="curriculum_id"
                matchId
                options={curriculums.map((item) => ({
                  id: item.id,
                  label: item.name,
                }))}
                label="Kurikulum"
              />
              <AutocompleteElement
                control={control}
                name="study_program_id"
                matchId
                options={studyPrograms.map((item) => ({
                  id: item.id,
                  label: item.name,
                }))}
                label="Program Studi"
              />
              <TextFieldElement
                name="name"
                label="Nama Mata Pelajaran"
                control={control}
              />
            </Stack>
            <Divider />
            <Stack sx={{ gap: 2, px: 2, flexDirection: "row" }}>
              <Button variant="outlined" fullWidth onClick={toggle}>
                Batal
              </Button>
              <Button
                type="submit"
                variant="contained"
                fullWidth
                color="primary"
              >
                Simpan
              </Button>
            </Stack>
          </Paper>
        </Stack>
      </Modal>
    </>
  );
}
