import { valibotResolver } from "@hookform/resolvers/valibot";
import { Typography } from "@mui/material";
import {
  useCredit,
  useUpdateCredit,
} from "@sisva/hooks/query/academic/useCredits";
import { useDebounce } from "ahooks";
import { useEffect, useRef } from "react";
import { TextFieldElement, useForm } from "react-hook-form-mui";
import toast from "react-hot-toast";
import { nullable, number, object } from "valibot";

export default function CreditForm() {
  const { data: credit } = useCredit();
  const enabledRef = useRef(false);

  const schema = object({
    duration_minutes: nullable(number("Harus diisi")),
  });

  const {
    control,
    watch,
    handleSubmit,
    formState: { errors },
  } = useForm({
    resolver: valibotResolver(schema),
    values: {
      duration_minutes: credit?.duration_minutes ?? null,
    },
  });

  const duration_minutes = useDebounce(watch("duration_minutes"), {
    wait: 1500,
  });

  const { mutate: updateCredit } = useUpdateCredit({
    onSuccess: () => {
      toast.success("Bobot SKS berhasil diperbarui");
    },
    onError: () => {
      toast.error("Bobot SKS gagal diperbarui");
    },
  });

  useEffect(() => {
    if (!enabledRef.current) return;
    handleSubmit((values) => {
      if (values.duration_minutes)
        updateCredit({ duration_minutes: values.duration_minutes });
    })();
  }, [duration_minutes, handleSubmit, updateCredit]);

  return (
    <div className="p-4 flex gap-2 items-center">
      <TextFieldElement
        size="small"
        label="Bobot SKS"
        control={control}
        name="duration_minutes"
        type="number"
        sx={{
          width: "100px",
        }}
        onChange={() => {
          enabledRef.current = true;
        }}
      />
      <Typography>Menit</Typography>
    </div>
  );
}
