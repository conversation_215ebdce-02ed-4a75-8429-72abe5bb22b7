import { Clear, Search, Sort } from "@mui/icons-material";
import {
  <PERSON>ton,
  <PERSON>alog,
  DialogActions,
  DialogContent,
  DialogTitle,
  IconButton,
  InputAdornment,
  Stack,
  Typography,
  useMediaQuery,
} from "@mui/material";
import type { GridColDef, GridFilterOperator } from "@mui/x-data-grid";
import { DataGrid, useGridApiContext } from "@mui/x-data-grid";
import type { StudentGroupWithStudents } from "@sisva/hooks/query/academic/useStudentGroups";
import { useStudentGroupsWithStudents } from "@sisva/hooks/query/academic/useStudentGroups";
import { onlyUnique } from "@sisva/utils";
import { useToggle } from "ahooks";
import { useEffect } from "react";
import { useForm } from "react-hook-form";
import {
  AutocompleteElement,
  SelectElement,
  TextFieldElement,
} from "react-hook-form-mui";

import DeleteIcon from "#/routes/(main)/-components/DeleteIcon";
import EditIcon from "#/routes/(main)/-components/EditIcon";

import CreateStudentGroupButton from "../CreateStudentGroupButton";
import DeleteStudentGroupButton from "../DeleteStudentGroupButton";

type Field =
  | "mobile_column"
  | keyof StudentGroupWithStudents
  | typeof filter_field
  | "homeroom_teacher"
  | "student_count"
  | "action";
type TypeSafeColDef = GridColDef & { field: Field };

const filter_field = "filter_field";
const field = {
  filter1: "grade",
  filter2: "name",
  filter3: "homeroom_teacher",
} satisfies Record<string, Field>;

type FilterValue = Record<(typeof field)[keyof typeof field], string>;

export default function KelasTab() {
  const isMobile = useMediaQuery((theme) => theme.breakpoints.down("lg"));
  const { data: studentGroups = [] } = useStudentGroupsWithStudents();

  const customOperator: GridFilterOperator<StudentGroupWithStudents> = {
    label: "Custom Operator",
    value: "customOperator",
    getApplyFilterFn: (filterItem, column) => {
      if (!filterItem.field || !filterItem.value || !filterItem.operator) {
        return null;
      }
      const filterValue = filterItem.value as FilterValue;

      return (value, row, column, apiRef) => {
        let pass = true;
        if (filterValue.grade) {
          if (row.grade !== filterValue.grade) pass = false;
        }
        if (filterValue.name) {
          if (row.name !== filterValue.name) pass = false;
        }
        if (filterValue.homeroom_teacher) {
          if (row.detail.homeroom_teacher_name !== filterValue.homeroom_teacher)
            pass = false;
        }

        return pass;
      };
    },
  };

  const columns: TypeSafeColDef[] = [
    {
      field: "mobile_column",
      headerName: "",
      valueGetter: (_, row) => row,
      flex: 1,
      display: "flex",
      renderCell: (args) => {
        const value = args.value as StudentGroupWithStudents;
        return (
          <Stack sx={{ p: 1.5, gap: 1, width: "100%" }}>
            <Stack
              sx={{
                flexDirection: "row",
                justifyContent: "space-between",
                width: "100%",
              }}
            >
              <Typography sx={{ fontWeight: 600 }}>Tingkatan</Typography>
              <Typography>{value.grade}</Typography>
            </Stack>
            <Stack
              sx={{
                flexDirection: "row",
                justifyContent: "space-between",
                width: "100%",
              }}
            >
              <Typography sx={{ fontWeight: 600 }}>Nama Kelas</Typography>
              <Typography>{value.name}</Typography>
            </Stack>
            <Stack
              sx={{
                flexDirection: "row",
                justifyContent: "space-between",
                width: "100%",
              }}
            >
              <Typography sx={{ fontWeight: 600 }}>Periode</Typography>
              <Typography>{value.period_name}</Typography>
            </Stack>
            <Stack
              sx={{
                flexDirection: "row",
                justifyContent: "space-between",
                width: "100%",
              }}
            >
              <Typography sx={{ fontWeight: 600 }}>Program Studi</Typography>
              <Typography>{value.study_program_name}</Typography>
            </Stack>
            <Stack
              sx={{
                flexDirection: "row",
                justifyContent: "space-between",
                width: "100%",
              }}
            >
              <Typography sx={{ fontWeight: 600 }}>Wali Kelas</Typography>
              <Typography>{value.detail.homeroom_teacher_name}</Typography>
            </Stack>
            <Stack
              sx={{
                flexDirection: "row",
                justifyContent: "space-between",
                width: "100%",
              }}
            >
              <Typography sx={{ fontWeight: 600 }}>Jumlah Murid</Typography>
              <Typography>{value.students.length}</Typography>
            </Stack>
            <Stack
              sx={{
                flexDirection: "row",
                justifyContent: "flex-end",
                gap: 1,
                width: "100%",
              }}
            >
              <CreateStudentGroupButton
                student_group_id={value.id}
                renderTrigger={(onClick) => <EditIcon onClick={onClick} />}
              />
              <DeleteStudentGroupButton
                student_group_id={value.id}
                renderTrigger={(onClick) => <DeleteIcon onClick={onClick} />}
              />
            </Stack>
          </Stack>
        );
      },
    },
    {
      field: "grade",
      headerName: "Tingkatan",
      display: "flex",
      width: 120,
      renderCell: ({ value }) => <Stack sx={{ py: 1.3 }}>{value}</Stack>,
    },
    {
      field: "name",
      headerName: "Nama Kelas",
      display: "flex",
      flex: 1,
      renderCell: ({ value }) => <Stack sx={{ py: 1.3 }}>{value}</Stack>,
    },
    {
      field: "period_name",
      headerName: "Periode",
      display: "flex",
      width: 120,
      renderCell: ({ value }) => <Stack sx={{ py: 1.3 }}>{value}</Stack>,
    },
    {
      field: "study_program_name",
      headerName: "Program Studi",
      display: "flex",
      flex: 1,
      renderCell: ({ value }) => <Stack sx={{ py: 1.3 }}>{value}</Stack>,
    },
    {
      field: "homeroom_teacher",
      headerName: "Wali Kelas",
      display: "flex",
      flex: 1,
      valueGetter: (_, row: StudentGroupWithStudents) =>
        row.detail.homeroom_teacher_name,
      renderCell: ({ value }) => (
        <Stack
          sx={{ py: 1.3, flexDirection: "row", alignItems: "center", gap: 2 }}
        >
          {value}
        </Stack>
      ),
    },
    {
      field: "student_count",
      headerName: "Jumlah Murid",
      valueGetter: (_, row: StudentGroupWithStudents) => row.students.length,
      display: "flex",
      width: 120,
      renderCell: ({ value }) => {
        return (
          <Stack
            sx={{ py: 1.3, flexDirection: "row", alignItems: "center", gap: 2 }}
          >
            {value}
          </Stack>
        );
      },
    },
    {
      field: "action",
      headerName: "Aksi",
      display: "flex",
      sortable: false,
      renderCell: ({ row }) => (
        <Stack
          sx={{ py: 1.3, flexDirection: "row", alignItems: "center", gap: 1 }}
        >
          <CreateStudentGroupButton
            student_group_id={row.id}
            renderTrigger={(onClick) => <EditIcon onClick={onClick} />}
          />
          <DeleteStudentGroupButton
            student_group_id={row.id}
            renderTrigger={(onClick) => <DeleteIcon onClick={onClick} />}
          />
        </Stack>
      ),
    },
    {
      field: filter_field,
      filterOperators: [customOperator],
    },
  ];

  const columnVisibilityModel = Object.fromEntries(
    columns
      .map((column) => column.field)
      .filter((field) =>
        isMobile ? field !== "mobile_column" : field === "mobile_column"
      )
      .map((field) => [field, false])
  );
  columnVisibilityModel[filter_field] = false;

  return (
    <DataGrid
      sx={{
        "& .MuiDataGrid-cell:focus": {
          outline: " none",
        },
        border: "none",
      }}
      getRowId={(row: StudentGroupWithStudents) => `${row.id}`}
      disableRowSelectionOnClick
      className="bg-white grow"
      rows={studentGroups}
      columns={columns}
      initialState={{ pagination: { paginationModel: { pageSize: 20 } } }}
      pageSizeOptions={[10, 20, 50]}
      getRowClassName={(params) =>
        params.indexRelativeToCurrentPage % 2 === 0
          ? "bg-neutral-50"
          : "bg-neutral-100"
      }
      slots={{
        toolbar: CustomToolbar,
      }}
      getRowHeight={() => "auto"}
      columnVisibilityModel={columnVisibilityModel}
      columnHeaderHeight={isMobile ? 0 : undefined}
      disableColumnMenu
      disableColumnFilter
    />
  );
}

function CustomToolbar() {
  const api = useGridApiContext();
  const [open, { toggle }] = useToggle(false);

  const { control, handleSubmit, reset, setValue, watch } = useForm<{
    field: Field | "";
    sortDirection: "asc" | "desc";
    quickFilter: string;
    [field.filter1]: string;
    [field.filter2]: string;
    [field.filter3]: string;
  }>({
    defaultValues: {
      field: "",
      sortDirection: "asc",
      quickFilter: "",
      [field.filter1]: "",
      [field.filter2]: "",
      [field.filter3]: "",
    },
  });

  const rows = Array.from(api.current.getRowModels()).map(
    ([key, value]) => value
  ) as StudentGroupWithStudents[];
  const grades = rows
    .map((row) => row.grade)
    .filter((item) => item)
    .filter(onlyUnique);
  const studentGroupNames = rows
    .map((row) => row.name)
    .filter((item) => item)
    .filter(onlyUnique);
  const homeroomTeacherNames = rows
    .map((row) => row.detail.homeroom_teacher_name)
    .filter((item) => item)
    .filter(onlyUnique);

  // apply filter
  const filter1 = watch(field.filter1);
  const filter2 = watch(field.filter2);
  const filter3 = watch(field.filter3);
  useEffect(() => {
    const value: FilterValue = {
      [field.filter1]: filter1,
      [field.filter2]: filter2,
      [field.filter3]: filter3,
    };
    api.current.upsertFilterItem({
      field: filter_field,
      operator: "customOperator",
      value,
    });
  }, [api, filter1, filter2, filter3]);

  const sortSelectOptions: { label: string; id: Field }[] = [
    { label: "Tingkatan", id: "grade" },
    { label: "Nama Kelas", id: "name" },
    { label: "Periode", id: "period_name" },
    { label: "Program Studi", id: "study_program_name" },
    { label: "Wali Kelas", id: "homeroom_teacher" },
    { label: "Jumlah Murid", id: "student_count" },
  ];

  const quickFilter = watch("quickFilter");
  useEffect(() => {
    api.current.setQuickFilterValues(
      quickFilter.split(" ").filter((s) => s !== "")
    );
  }, [api, quickFilter]);

  return (
    <div className="p-4 flex flex-col gap-2 border-b border-0 border-solid border-neutral-200">
      <div className=" flex justify-between gap-4 thick-scrollbar items-center">
        <Stack
          sx={{
            overflow: {
              xs: "auto",
              xl: "initial",
            },
            py: {
              xs: 1,
              xl: 0,
            },
          }}
        >
          <Stack sx={{ flexDirection: "row", gap: 2 }}>
            <TextFieldElement
              control={control}
              name="quickFilter"
              placeholder="Cari"
              size="small"
              sx={{
                minWidth: "150px",
              }}
              slotProps={{
                input: {
                  endAdornment: (
                    <InputAdornment position="end">
                      {quickFilter ? (
                        <IconButton
                          size="small"
                          onClick={() => setValue("quickFilter", "")}
                        >
                          <Clear />
                        </IconButton>
                      ) : (
                        <Search />
                      )}
                    </InputAdornment>
                  ),
                },
              }}
            />
            <SelectElement
              size="small"
              sx={{ minWidth: "150px" }}
              control={control}
              name={field.filter1}
              options={grades.map((item) => ({
                id: item,
                label: item,
              }))}
              label="Tingkatan"
              slotProps={{
                input: {
                  endAdornment: watch(field.filter1) && (
                    <InputAdornment position="end">
                      <IconButton
                        onClick={() => setValue(field.filter1, "")}
                        size="small"
                      >
                        <Clear />
                      </IconButton>
                    </InputAdornment>
                  ),
                },
              }}
            />
            <AutocompleteElement
              control={control}
              name={field.filter2}
              matchId
              options={studentGroupNames.map((item) => ({
                id: item,
                label: item,
              }))}
              label="Nama Kelas"
              autocompleteProps={{
                size: "small",
                sx: { minWidth: "180px" },
              }}
            />
            <AutocompleteElement
              control={control}
              name={field.filter3}
              matchId
              options={homeroomTeacherNames.map((item) => ({
                id: item,
                label: item,
              }))}
              label="Wali Kelas"
              autocompleteProps={{
                size: "small",
                sx: { minWidth: "180px" },
              }}
            />
          </Stack>
        </Stack>
        <Stack sx={{ minWidth: 110 }}>
          <CreateStudentGroupButton />
        </Stack>
      </div>
      <Button
        startIcon={<Sort />}
        variant="outlined"
        onClick={toggle}
        sx={{
          display: { xs: "flex", lg: "none" },
        }}
      >
        Urutkan Data
      </Button>
      <Dialog open={open} onClose={toggle}>
        <DialogTitle>Urutkan</DialogTitle>
        <DialogContent
          sx={{
            width: "250px",
            display: "flex",
            flexDirection: "column",
            gap: 2,
            overflow: "initial",
          }}
        >
          <SelectElement
            control={control}
            name="field"
            options={sortSelectOptions}
            label="Pilih Kolom"
            slotProps={{
              input: {
                endAdornment: watch("field") && (
                  <InputAdornment position="end">
                    <IconButton onClick={() => setValue("field", "")}>
                      <Clear />
                    </IconButton>
                  </InputAdornment>
                ),
              },
            }}
          />
          <SelectElement
            control={control}
            name="sortDirection"
            options={[
              { label: "A-Z", id: "asc" },
              { label: "Z-A", id: "desc" },
            ]}
            label="Urutan"
          />
        </DialogContent>
        <DialogActions sx={{ pb: 2, px: 3 }}>
          <Button
            variant="outlined"
            onClick={() => {
              reset();
              toggle();
            }}
          >
            Batal
          </Button>
          <Button
            variant="contained"
            onClick={handleSubmit((value) => {
              if (value.field) {
                api.current.sortColumn(value.field, value.sortDirection);
              } else {
                api.current.sortColumn("name", null);
              }
              toggle();
            })}
          >
            Simpan
          </Button>
        </DialogActions>
      </Dialog>
    </div>
  );
}
