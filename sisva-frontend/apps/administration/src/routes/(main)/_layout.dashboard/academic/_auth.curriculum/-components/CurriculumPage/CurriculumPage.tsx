import {
  Paper,
  Stack,
  Tab,
  Tabs,
  Typography,
  useMediaQuery,
} from "@mui/material";
import type { SyntheticEvent } from "react";
import { useState } from "react";

import ImportExcelAcademicButton from "../../../-components/ImportExcelAcademicButton";
import KurikulumTab from "./KurikulumTab";
import MataPelajaranTab from "./MataPelajaranTab";
import SilabusTab from "./SilabusTab";

export default function CurriculumPage() {
  const isMobile = useMediaQuery((theme) => theme.breakpoints.down("lg"));

  const [tabIndex, setTabIndex] = useState(0);
  return (
    <Stack
      sx={{
        height: "100%",
        width: "100%",
        p: { xs: 0, lg: 4 },
        gap: 2,
      }}
    >
      <Stack
        sx={{
          flexDirection: "row",
          justifyContent: "space-between",
          display: {
            xs: "none",
            lg: "flex",
          },
        }}
      >
        <Typography
          sx={{
            fontWeight: 700,
            fontSize: 20,
          }}
        >
          Kurikulum
        </Typography>
        <ImportExcelAcademicButton />
      </Stack>
      <Paper
        variant="outlined"
        sx={{
          flex: 1,
          display: "flex",
          flexDirection: "column",
          overflow: "auto",
        }}
      >
        <Tabs
          value={tabIndex}
          onChange={(event: SyntheticEvent, newValue: number) => {
            setTabIndex(newValue);
          }}
          sx={{
            borderBottom: 1,
            borderColor: "divider",
          }}
          variant={isMobile ? "fullWidth" : "standard"}
        >
          <Tab
            label="Kurikulum"
            sx={{ fontWeight: 600, textTransform: "none", fontSize: 14 }}
          />
          <Tab
            label="Mata Pelajaran"
            sx={{ fontWeight: 600, textTransform: "none", fontSize: 14 }}
          />
          <Tab
            label="Silabus"
            sx={{ fontWeight: 600, textTransform: "none", fontSize: 14 }}
          />
        </Tabs>
        {(() => {
          switch (tabIndex) {
            case 0:
              return <KurikulumTab />;
            case 1:
              return <MataPelajaranTab />;
            case 2:
              return <SilabusTab />;
          }
        })()}
      </Paper>
    </Stack>
  );
}
