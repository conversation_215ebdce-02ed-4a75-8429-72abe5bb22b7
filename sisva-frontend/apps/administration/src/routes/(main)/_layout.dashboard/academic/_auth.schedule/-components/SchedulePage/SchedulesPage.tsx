import {
  Paper,
  Stack,
  Tab,
  Tabs,
  Typography,
  useMediaQuery,
} from "@mui/material";
import type { SyntheticEvent } from "react";
import { useState } from "react";

import ImportExcelAcademicButton from "../../../-components/ImportExcelAcademicButton";
import CreditForm from "../CreditForm";
import JadwalGuruTab from "./JadwalGuruTab";
import JadwalKelasTab from "./JadwalKelasTab";
import JadwalKeseluruhanTab from "./JadwalKeseluruhanTab";
import EkstrakurikulerTab from "./JamSekolahTab";

export default function SchedulesPage() {
  const isMobile = useMediaQuery((theme) => theme.breakpoints.down("lg"));

  const [tabIndex, setTabIndex] = useState(0);
  return (
    <Stack
      sx={{
        height: "100%",
        width: "100%",
        p: { xs: 0, lg: 4 },
        gap: 2,
      }}
    >
      <Stack
        sx={{
          flexDirection: "row",
          justifyContent: "space-between",
          display: {
            xs: "none",
            lg: "flex",
          },
        }}
      >
        <Typography
          sx={{
            fontWeight: 700,
            fontSize: 20,
            display: {
              xs: "none",
              lg: "flex",
            },
          }}
        >
          Jadwal Pelajaran
        </Typography>
        <ImportExcelAcademicButton />
      </Stack>

      <Paper
        variant="outlined"
        sx={{
          flex: 1,
          display: "flex",
          flexDirection: "column",
          overflow: "auto",
        }}
        className="thick-scrollbar"
      >
        <Tabs
          value={tabIndex}
          onChange={(event: SyntheticEvent, newValue: number) => {
            setTabIndex(newValue);
          }}
          sx={{
            borderBottom: 1,
            borderColor: "divider",
          }}
          variant={isMobile ? "fullWidth" : "standard"}
        >
          <Tab
            label="Jam Sekolah"
            sx={{ fontWeight: 600, textTransform: "none", fontSize: 14 }}
          />
          <Tab
            label="Jadwal Keseluruhan"
            sx={{ fontWeight: 600, textTransform: "none", fontSize: 14 }}
          />
          <Tab
            label="Jadwal Kelas"
            sx={{ fontWeight: 600, textTransform: "none", fontSize: 14 }}
          />
          <Tab
            label="Jadwal Guru"
            sx={{ fontWeight: 600, textTransform: "none", fontSize: 14 }}
          />
        </Tabs>
        {(() => {
          switch (tabIndex) {
            case 0:
              return <EkstrakurikulerTab />;
            case 1:
              return <JadwalKeseluruhanTab />;
            case 2:
              return <JadwalKelasTab />;
            case 3:
              return <JadwalGuruTab />;
          }
        })()}
      </Paper>
      {tabIndex === 0 && (
        <Paper variant="outlined">
          <CreditForm />
        </Paper>
      )}
    </Stack>
  );
}
