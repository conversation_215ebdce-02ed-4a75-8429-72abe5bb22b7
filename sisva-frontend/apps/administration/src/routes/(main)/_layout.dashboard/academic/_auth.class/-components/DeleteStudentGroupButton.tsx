import { Delete<PERSON>orever } from "@mui/icons-material";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Di<PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  Stack,
  Typography,
} from "@mui/material";
import { useClasses } from "@sisva/hooks/query/academic/useClasses";
import { usePeriod } from "@sisva/hooks/query/academic/usePeriods";
import {
  useDeleteStudentGroup,
  useStudentGroupWithStudents,
} from "@sisva/hooks/query/academic/useStudentGroups";
import { useToggle } from "ahooks";
import type { ReactNode } from "react";
import toast from "react-hot-toast";

export default function DeleteStudentGroupButton({
  student_group_id,
  renderTrigger,
}: {
  student_group_id: number;
  renderTrigger?: (onClick: () => void) => ReactNode;
}) {
  const { data: studentGroup } = useStudentGroupWithStudents(student_group_id);
  const { data: period } = usePeriod(studentGroup?.period_id);
  const { data: classes = [] } = useClasses();
  const studentGroupsIds = classes.map((class_) => class_.student_group_id);
  const canBeDeleted = (() => {
    if (period?.status === "active" || period?.status === "finished")
      return false;
    if (studentGroupsIds.includes(student_group_id)) return false;
    return true;
  })();

  const [visible, { toggle }] = useToggle(false);

  const { mutate: deleteStudentGroup } = useDeleteStudentGroup({
    onSuccess: () => {
      toggle();
      toast.success("Kelas berhasil dihapus");
    },
    onError: () => {
      toast.error("Kelas gagal dihapus");
    },
  });

  return (
    <>
      {renderTrigger ? (
        renderTrigger(toggle)
      ) : (
        <Button
          variant="contained"
          startIcon={<DeleteForever />}
          color="error"
          onClick={toggle}
        >
          Hapus
        </Button>
      )}
      <Modal open={visible} onClose={toggle}>
        <Stack
          sx={{
            width: 1,
            height: 1,
            justifyContent: "center",
            alignItems: "center",
            p: 2,
          }}
        >
          <Paper
            component="form"
            sx={{
              py: 2,
              width: 1,
              maxWidth: "ms",
              gap: 2,
              display: "flex",
              flexDirection: "column",
            }}
            onSubmit={(e) => {
              e.preventDefault();
              deleteStudentGroup(student_group_id);
            }}
          >
            <Typography sx={{ fontWeight: 600, px: 2 }}>Hapus Kelas</Typography>
            <Divider />
            <Stack sx={{ gap: 2, px: 2 }}>
              {!canBeDeleted && (
                <Alert severity="warning">
                  Kelas tidak dapat dihapus karena Kelas ini sedang digunakan
                </Alert>
              )}
              <Stack
                sx={{ flexDirection: "row", justifyContent: "space-between" }}
              >
                <Typography sx={{ fontWeight: 600 }}>Tingkatan</Typography>
                <Typography>{studentGroup?.grade}</Typography>
              </Stack>
              <Stack
                sx={{ flexDirection: "row", justifyContent: "space-between" }}
              >
                <Typography sx={{ fontWeight: 600 }}>Nama Kelas</Typography>
                <Typography>{studentGroup?.name}</Typography>
              </Stack>
              <Stack
                sx={{ flexDirection: "row", justifyContent: "space-between" }}
              >
                <Typography sx={{ fontWeight: 600 }}>Wali Kelas</Typography>
                <Typography>
                  {studentGroup?.detail?.homeroom_teacher_name}
                </Typography>
              </Stack>
              <Stack
                sx={{ flexDirection: "row", justifyContent: "space-between" }}
              >
                <Typography sx={{ fontWeight: 600 }}>Jumlah Murid</Typography>
                <Typography>{studentGroup?.students.length}</Typography>
              </Stack>
            </Stack>
            <Divider />
            <Stack sx={{ gap: 2, px: 2, flexDirection: "row" }}>
              <Button variant="outlined" fullWidth onClick={toggle}>
                Batal
              </Button>
              <Button
                type="submit"
                variant="contained"
                fullWidth
                color="error"
                disabled={!canBeDeleted}
              >
                Hapus
              </Button>
            </Stack>
          </Paper>
        </Stack>
      </Modal>
    </>
  );
}
