import { Clear, Search, Sort } from "@mui/icons-material";
import {
  Button,
  <PERSON>alog,
  <PERSON>alogActions,
  <PERSON>alogContent,
  DialogTitle,
  IconButton,
  InputAdornment,
  Stack,
  Typography,
  useMediaQuery,
} from "@mui/material";
import type { GridColDef } from "@mui/x-data-grid";
import { DataGrid, useGridApiContext } from "@mui/x-data-grid";
import { useStudyPrograms } from "@sisva/hooks/query/academic/useStudyPrograms";
import type { StudyProgram } from "@sisva/types/apiTypes";
import { onlyUnique } from "@sisva/utils";
import { useToggle } from "ahooks";
import { useEffect } from "react";
import { useForm } from "react-hook-form";
import { SelectElement, TextFieldElement } from "react-hook-form-mui";

import EditIcon from "#/routes/(main)/-components/EditIcon";

import CreateStudyProgramButton from "../CreateStudyProgramButton";

type Field = "mobile_column" | keyof StudyProgram | "grade" | "action";
type TypeSafeColDef = GridColDef & { field: Field };
type StudyProgramPerGrade = Omit<StudyProgram, "id"> & {
  grade: string;
  id: string;
  study_program_id: number;
};

export default function ProgramStudiTab() {
  const isMobile = useMediaQuery((theme) => theme.breakpoints.down("lg"));
  const { data: studyPrograms = [] } = useStudyPrograms();
  const studyProgramsPerGrade: StudyProgramPerGrade[] = [];
  studyPrograms.forEach((studyProgram) => {
    studyProgram.grades?.forEach((grade) => {
      studyProgramsPerGrade.push({
        ...studyProgram,
        id: studyProgram.code + "-" + grade,
        grade: grade,
        study_program_id: studyProgram.id,
      });
    });
  });

  const columns: TypeSafeColDef[] = [
    {
      field: "mobile_column",
      headerName: "",
      valueGetter: (_, row) => row,
      flex: 1,
      display: "flex",
      renderCell: (args) => {
        const value = args.value as StudyProgramPerGrade;
        return (
          <Stack sx={{ p: 1.5, gap: 2, width: "100%" }}>
            <Stack
              sx={{
                flexDirection: "row",
                justifyContent: "space-between",
                width: "100%",
              }}
            >
              <Typography sx={{ fontWeight: 600 }}>Program Studi</Typography>
              <Typography>{value.name}</Typography>
            </Stack>
            <Stack
              sx={{
                flexDirection: "row",
                justifyContent: "space-between",
                width: "100%",
              }}
            >
              <Typography sx={{ fontWeight: 600 }}>Kode</Typography>
              <Typography>{value.code}</Typography>
            </Stack>
            <Stack
              sx={{
                flexDirection: "row",
                justifyContent: "space-between",
                width: "100%",
              }}
            >
              <Typography sx={{ fontWeight: 600 }}>Tingkatan</Typography>
              <Typography>{value.grade}</Typography>
            </Stack>
            <Stack
              sx={{ flexDirection: "row", gap: 0.5, justifyContent: "end" }}
            >
              <CreateStudyProgramButton
                editGrades
                study_program_id={value.study_program_id}
                renderTrigger={(onClick) => <EditIcon onClick={onClick} />}
              />
            </Stack>
          </Stack>
        );
      },
    },
    {
      field: "name",
      headerName: "Program Studi",
      display: "flex",
      flex: 1,
      renderCell: ({ value }) => <Stack sx={{ py: 1.3 }}>{value}</Stack>,
    },
    {
      field: "code",
      headerName: "Kode",
      display: "flex",
      flex: 1,
      renderCell: ({ value }) => <Stack sx={{ py: 1.3 }}>{value}</Stack>,
    },
    {
      field: "grade",
      headerName: "Tingkatan",
      width: 150,
      display: "flex",
      renderCell: ({ row }: { row: StudyProgramPerGrade }) => {
        return (
          <Stack sx={{ py: 1.3, flexDirection: "row", gap: 0.5 }}>
            {row.grade}
          </Stack>
        );
      },
    },
    {
      field: "action",
      headerName: "Aksi",
      width: 128,
      display: "flex",
      renderCell: ({ row }: { row: StudyProgramPerGrade }) => (
        <Stack sx={{ flexDirection: "row", gap: 0.5, py: 1.5 }}>
          <CreateStudyProgramButton
            editGrades
            study_program_id={row.study_program_id}
            renderTrigger={(onClick) => <EditIcon onClick={onClick} />}
          />
        </Stack>
      ),
      sortable: false,
    },
  ];

  const columnVisibilityModel = Object.fromEntries(
    columns
      .map((column) => column.field)
      .filter((field) =>
        isMobile ? field !== "mobile_column" : field === "mobile_column"
      )
      .map((field) => [field, false])
  );

  return (
    <DataGrid
      sx={{
        "& .MuiDataGrid-cell:focus": {
          outline: " none",
        },
        border: "none",
      }}
      getRowId={(row: StudyProgramPerGrade) => `${row.id}`}
      disableRowSelectionOnClick
      className="bg-white grow"
      rows={studyProgramsPerGrade}
      columns={columns}
      initialState={{ pagination: { paginationModel: { pageSize: 20 } } }}
      pageSizeOptions={[10, 20, 50]}
      getRowClassName={(params) =>
        params.indexRelativeToCurrentPage % 2 === 0
          ? "bg-neutral-50"
          : "bg-neutral-100"
      }
      slots={{
        toolbar: CustomToolbar,
      }}
      getRowHeight={() => "auto"}
      columnVisibilityModel={columnVisibilityModel}
      columnHeaderHeight={isMobile ? 0 : undefined}
      disableColumnMenu
      disableColumnFilter
    />
  );
}

function CustomToolbar() {
  const api = useGridApiContext();
  const [open, { toggle }] = useToggle(false);

  const filter_1 = "study_program_name";
  const { control, handleSubmit, reset, setValue, watch } = useForm<{
    field: Field | "";
    sortDirection: "asc" | "desc";
    quickFilter: string;
    [filter_1]: string;
  }>({
    defaultValues: {
      field: "",
      sortDirection: "asc",
      quickFilter: "",
      [filter_1]: "",
    },
  });
  const rows = Array.from(api.current.getRowModels()).map(
    ([key, value]) => value
  ) as StudyProgramPerGrade[];
  const studyProgramNames = rows.map((row) => row.name).filter(onlyUnique);

  const sortSelectOptions: { label: string; id: Field }[] = [
    { label: "Program Studi", id: "name" },
    { label: "Kode", id: "code" },
    { label: "Tingkatan", id: "grade" },
  ];

  const studyProgramNameFilter = watch(filter_1);

  // apply filter
  useEffect(() => {
    const field: Field = "name";
    api.current.upsertFilterItem({
      field,
      // import getGridStringOperators from mui to see more operator options
      operator: "equals",
      value: studyProgramNameFilter,
    });
  }, [api, studyProgramNameFilter]);

  const quickFilter = watch("quickFilter");
  useEffect(() => {
    api.current.setQuickFilterValues(
      quickFilter.split(" ").filter((s) => s !== "")
    );
  }, [api, quickFilter]);

  return (
    <div className="p-4 flex flex-col gap-2 border-b border-0 border-solid border-neutral-200">
      <div className=" flex justify-between ">
        <Stack sx={{ flexDirection: "row", gap: 2 }}>
          <TextFieldElement
            control={control}
            name="quickFilter"
            placeholder="Cari Tingkatan"
            size="small"
            slotProps={{
              input: {
                endAdornment: (
                  <InputAdornment position="end">
                    {quickFilter ? (
                      <IconButton
                        size="small"
                        onClick={() => setValue("quickFilter", "")}
                      >
                        <Clear />
                      </IconButton>
                    ) : (
                      <Search />
                    )}
                  </InputAdornment>
                ),
              },
            }}
          />
          <SelectElement
            size="small"
            sx={{ width: "150px" }}
            control={control}
            name={filter_1}
            options={studyProgramNames.map((item) => ({
              id: item,
              label: item,
            }))}
            label="Program Studi"
            slotProps={{
              input: {
                endAdornment: watch(filter_1) && (
                  <InputAdornment position="end">
                    <IconButton
                      onClick={() => setValue(filter_1, "")}
                      size="small"
                    >
                      <Clear />
                    </IconButton>
                  </InputAdornment>
                ),
              },
            }}
          />
        </Stack>
        <CreateStudyProgramButton editGrades />
      </div>
      <Button
        startIcon={<Sort />}
        variant="outlined"
        onClick={toggle}
        sx={{
          display: { xs: "flex", lg: "none" },
        }}
      >
        Urutkan Data
      </Button>
      <Dialog open={open} onClose={toggle}>
        <DialogTitle>Urutkan</DialogTitle>
        <DialogContent
          sx={{
            width: "250px",
            display: "flex",
            flexDirection: "column",
            gap: 2,
            overflow: "initial",
          }}
        >
          <SelectElement
            control={control}
            name="field"
            options={sortSelectOptions}
            label="Pilih Kolom"
            slotProps={{
              input: {
                endAdornment: watch("field") && (
                  <InputAdornment position="end">
                    <IconButton onClick={() => setValue("field", "")}>
                      <Clear />
                    </IconButton>
                  </InputAdornment>
                ),
              },
            }}
          />
          <SelectElement
            control={control}
            name="sortDirection"
            options={[
              { label: "A-Z", id: "asc" },
              { label: "Z-A", id: "desc" },
            ]}
            label="Urutan"
          />
        </DialogContent>
        <DialogActions sx={{ pb: 2, px: 3 }}>
          <Button
            variant="outlined"
            onClick={() => {
              reset();
              toggle();
            }}
          >
            Batal
          </Button>
          <Button
            variant="contained"
            onClick={handleSubmit((value) => {
              if (value.field) {
                api.current.sortColumn(value.field, value.sortDirection);
              } else {
                api.current.sortColumn("name", null);
              }
              toggle();
            })}
          >
            Simpan
          </Button>
        </DialogActions>
      </Dialog>
    </div>
  );
}
