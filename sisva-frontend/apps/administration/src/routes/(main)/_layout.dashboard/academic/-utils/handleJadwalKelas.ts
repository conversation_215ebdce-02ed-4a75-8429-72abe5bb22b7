import AcademicAPI from "@sisva/api/academic";
import UsersAP<PERSON> from "@sisva/api/users";
import type {
  Class,
  ClassSchedule,
  Period,
  SchoolSchedules,
  StudentGroup,
  StudyProgram,
  Subject,
  User,
} from "@sisva/types/apiTypes";
import { getDay } from "@sisva/types/types";
import { checkScheduleConflict } from "@sisva/utils";
import { dateFromOADate } from "@sisva/utils";
import dayjs from "dayjs";
import customParseFormat from "dayjs/plugin/customParseFormat";

import type { JadwalKelasInputData } from "./types";

dayjs.extend(customParseFormat);

function getStudyProgram(studyPrograms: StudyProgram[], name: string) {
  return studyPrograms.find((programStudi) => programStudi.name === name);
}

function getPeriod(periods: Period[], name: string) {
  return periods.find((period) => period.name === name);
}

function getUserByName(users: User[], name: string) {
  return users.find((user) => user.name === name);
}

function getUserByUsername(users: User[], username: string) {
  return users.find((user) => user.username === username);
}

function getUser(users: User[], user: { name: string; username: string }) {
  if (user.username) return getUserByUsername(users, user.username);
  return getUserByName(users, user.name);
}

function getStudentGroup(
  studentGroups: StudentGroup[],
  name: string,
  period_id: number
) {
  return studentGroups.find(
    (studentGroup) =>
      studentGroup.name === name && studentGroup.period_id === period_id
  );
}

function getSubject(subjects: Subject[], name: string) {
  return subjects.find((subject) => subject.name === name);
}

export default async function handleJadwalKelas(data: JadwalKelasInputData) {
  // periods
  const periods: Period[] = (await AcademicAPI.getAllPeriod()).data.data;
  const periodNames = periods.map((period) => period.name);
  const periodIds = periods.map((period) => period.id);

  // school schedules
  const schoolSchedules: SchoolSchedules[] = await (async () => {
    const arrayOfSchoolSchedules: SchoolSchedules[][] = [];
    for (const id of periodIds) {
      arrayOfSchoolSchedules.push(
        (await AcademicAPI.getAllSchoolSchedules({ period_id: id })).data.data
      );
    }
    return arrayOfSchoolSchedules.flat();
  })();

  const classSchedules: ClassSchedule[] = await (async () => {
    const arrayOfClassSchedules: ClassSchedule[][] = [];
    for (const id of periodIds) {
      arrayOfClassSchedules.push(
        (await AcademicAPI.getAllClassSchedules({ period_id: id })).data.data
      );
    }
    return arrayOfClassSchedules.flat();
  })();

  // study programs
  const studyPrograms: StudyProgram[] = (await AcademicAPI.getAllProdi()).data
    .data;
  const studyProgramNames = studyPrograms.map(
    (studyProgram) => studyProgram.name
  );

  // class
  let classes: Class[] = (await AcademicAPI.getAllClasses()).data.data;

  // teachers
  const teachers: User[] = (
    await UsersAPI.getAllUsers("teacher")
  ).data.data.filter((teacher: User) => teacher.status === "active");
  const teacherNames = teachers.map((teacher) => teacher.name);
  const teacherUsernames = teachers.map((teacher) => teacher.username);

  // student groups
  const studentGroups: StudentGroup[] = (await AcademicAPI.getAllStudentGroup())
    .data.data;
  const studentGroupNames = studentGroups.map(
    (studentGroup) => studentGroup.name
  );

  // subjects
  const subjects: Subject[] = (await AcademicAPI.getAllSubject()).data.data;
  const subjectNames = subjects.map((subject) => subject.name);

  const dataObjects = data
    .map((row) => {
      return {
        id: crypto.randomUUID(),
        nama_periode: row[0],
        nama_program_studi: row[1],
        grade: row[2],
        day: getDay(row[3]),
        nama_kelas: row[4],
        nama_mata_pelajaran: row[5],
        nama_guru: row[6],
        username_guru: row[7],
        jam_mulai: dayjs(dateFromOADate(row[8])).format("h:mm A Z"),
        jam_selesai: dayjs(dateFromOADate(row[9])).format("h:mm A Z"),
      };
    })
    .filter((data) => {
      const startTime = dayjs(data.jam_mulai, "h:mm A Z");
      const endTime = dayjs(data.jam_selesai, "h:mm A Z");
      return startTime.isBefore(endTime);
    });

  let createObjects = dataObjects.filter((data) => {
    const schoolSchedule = schoolSchedules.find(
      (schoolSchedule) =>
        schoolSchedule.period_id ===
          getPeriod(periods, data.nama_periode)?.id &&
        schoolSchedule.study_program_id ===
          getStudyProgram(studyPrograms, data.nama_program_studi)?.id &&
        schoolSchedule.grade === data.grade &&
        schoolSchedule.day === data.day
    );

    const class_ = classes.find(
      (class_) =>
        class_.subject_name === data.nama_mata_pelajaran &&
        class_.teacher_id ===
          getUser(teachers, {
            name: data.nama_guru,
            username: data.username_guru,
          })?.id &&
        class_.student_group_name === data.nama_kelas
    );

    const studentGroup = getStudentGroup(
      studentGroups,
      data.nama_kelas,
      getPeriod(periods, data.nama_periode)!.id
    );

    return (
      schoolSchedule &&
      studentGroup &&
      !classSchedules.some(
        (classSchedule) =>
          classSchedule.school_schedule_id === schoolSchedule?.id &&
          classSchedule.class_id === class_?.id &&
          classSchedule.start_time === data.jam_mulai &&
          classSchedule.end_time === data.jam_selesai
      ) &&
      periodNames.includes(data.nama_periode) &&
      studyProgramNames.includes(data.nama_program_studi) &&
      teacherNames.includes(data.nama_guru) &&
      (!data.username_guru || teacherUsernames.includes(data.username_guru)) &&
      subjectNames.includes(data.nama_mata_pelajaran)
    );
  });

  const createObjectWithConflictIndex: number[] = [];

  for (const [i, data] of createObjects.entries()) {
    const studentGroup = getStudentGroup(
      studentGroups,
      data.nama_kelas,
      getPeriod(periods, data.nama_periode)!.id
    );
    const teacher = getUser(teachers, {
      name: data.nama_guru,
      username: data.username_guru,
    });

    const { hasConflict } = await checkScheduleConflict({
      schedule: {
        id: data.id,
        student_group_id: studentGroup!.id,
        period_id: studentGroup!.period_id,
        teacher_id: teacher!.id,
        day: data.day,
        start_time: data.jam_mulai,
        end_time: data.jam_selesai,
      },
      otherSchedules: createObjects.map((data) => {
        const studentGroup = getStudentGroup(
          studentGroups,
          data.nama_kelas,
          getPeriod(periods, data.nama_periode)!.id
        );
        const teacher = getUser(teachers, {
          name: data.nama_guru,
          username: data.username_guru,
        });

        return {
          id: data.id,
          student_group_id: studentGroup!.id,
          period_id: studentGroup!.period_id,
          teacher_id: teacher!.id,
          day: data.day,
          start_time: data.jam_mulai,
          end_time: data.jam_selesai,
        };
      }),
    });

    if (hasConflict) createObjectWithConflictIndex.push(i);
  }

  createObjects = createObjects.filter(
    (_, i) => !createObjectWithConflictIndex.includes(i)
  );

  // create classes if they don't exist
  for (const data of createObjects) {
    const class_ = classes.find(
      (class_) =>
        class_.subject_name === data.nama_mata_pelajaran &&
        class_.teacher_id ===
          getUser(teachers, {
            name: data.nama_guru,
            username: "",
          })?.id &&
        class_.student_group_name === data.nama_kelas
    );

    if (!class_) {
      const studentGroup = getStudentGroup(
        studentGroups,
        data.nama_kelas,
        getPeriod(periods, data.nama_periode)!.id
      );
      const subject = getSubject(subjects, data.nama_mata_pelajaran);
      const teacher = getUser(teachers, {
        name: data.nama_guru,
        username: data.username_guru,
      });

      await AcademicAPI.createClass({
        name: `${subject?.id} - ${teacher?.id} - ${studentGroup?.id}`,
        student_group_id: studentGroup?.id,
        subject_id: subject?.id,
        teacher_id: teacher?.id,
      });

      // refetch classes
      classes = (await AcademicAPI.getAllClasses()).data.data;
    }
  }

  const promisesCreate = createObjects.map((data) => {
    const school_schedule_id = schoolSchedules.find(
      (schoolSchedule) =>
        schoolSchedule.period_id ===
          getPeriod(periods, data.nama_periode)?.id &&
        schoolSchedule.study_program_id ===
          getStudyProgram(studyPrograms, data.nama_program_studi)?.id &&
        schoolSchedule.grade === data.grade &&
        schoolSchedule.day === data.day
    )?.id;

    const class_id = classes.find(
      (class_) =>
        class_.subject_name === data.nama_mata_pelajaran &&
        class_.teacher_id ===
          getUser(teachers, {
            name: data.nama_guru,
            username: data.username_guru,
          })?.id &&
        class_.student_group_name === data.nama_kelas
    )?.id;

    const payload = {
      class_id,
      school_schedule_id,
      start_time: data.jam_mulai,
      end_time: data.jam_selesai,
    };

    return AcademicAPI.createClassSchedule(payload);
  });

  const res = await Promise.all(promisesCreate);
  const reportText: string[] = [];
  if (promisesCreate.length)
    reportText.push(
      `${promisesCreate.length} baris Jadwal Kelas berhasil ditambahkan`
    );
  if (createObjectWithConflictIndex.length)
    reportText.push(
      `${createObjectWithConflictIndex.length} baris Jadwal Kelas konflik dan diabaikan`
    );
  return reportText.join(", ");
}
