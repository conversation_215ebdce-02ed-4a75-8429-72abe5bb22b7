import { Add, Clear, Search, Sort } from "@mui/icons-material";
import {
  <PERSON><PERSON>,
  <PERSON>ton,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  IconButton,
  InputAdornment,
  Stack,
  Typography,
  useMediaQuery,
} from "@mui/material";
import type { GridColDef } from "@mui/x-data-grid";
import { DataGrid, useGridApiContext } from "@mui/x-data-grid";
import {
  usePeriod,
  usePeriods,
  useSelectedPeriod,
} from "@sisva/hooks/query/academic/usePeriods";
import { useSchoolSchedules } from "@sisva/hooks/query/academic/useSchoolSchedules";
import {
  useStudyProgram,
  useStudyPrograms,
} from "@sisva/hooks/query/academic/useStudyPrograms";
import type { SchoolSchedules } from "@sisva/types/apiTypes";
import { getDayText } from "@sisva/types/types";
import { cn } from "@sisva/utils";
import { useToggle } from "ahooks";
import dayjs from "dayjs";
import { useEffect } from "react";
import {
  AutocompleteElement,
  SelectElement,
  TextFieldElement,
  useForm,
} from "react-hook-form-mui";

import DeleteIcon from "#/routes/(main)/-components/DeleteIcon";
import EditIcon from "#/routes/(main)/-components/EditIcon";

import CreateSchoolScheduleButton from "../CreateSchoolScheduleButton";

type Field = "mobile_column" | keyof SchoolSchedules | "action";
type TypeSafeColDef = GridColDef & { field: Field };

export default function JamSekolahTab() {
  const isMobile = useMediaQuery((theme) => theme.breakpoints.down("lg"));
  const { data: periods = [] } = usePeriods();
  const { data: selectedPeriod } = useSelectedPeriod();
  const { data: studyPrograms = [] } = useStudyPrograms();

  const { control, watch, setValue } = useForm({
    values: {
      period_id: selectedPeriod?.id ?? null,
      study_program_id: null,
      grade: "",
    },
  });

  const period_id = watch("period_id");
  const study_program_id = watch("study_program_id");
  const grade = watch("grade");
  const disabled = !period_id || !study_program_id || !grade;

  const { data: period } = usePeriod(period_id);
  const studyProgramIds = [
    ...new Set(period?.study_programs?.map((item) => item.id) ?? []),
  ];
  const fitleredStudyPrograms = studyPrograms.filter((item) =>
    studyProgramIds.includes(item.id)
  );

  const { data: studyProgram } = useStudyProgram(study_program_id);
  const grades = studyProgram?.grades ?? [];

  const { data: schoolSchedules = [] } = useSchoolSchedules(period_id);
  const fitleredSchoolSchedules = schoolSchedules
    .filter((item) => (period_id ? item.period_id === period_id : true))
    .filter((item) =>
      study_program_id ? item.study_program_id === study_program_id : true
    )
    .filter((item) => (grade ? item.grade === grade : true));

  // reset grade when study_program_id changed
  useEffect(() => {
    setValue("grade", "");
  }, [setValue, study_program_id]);
  // reset study_program_id when period_id changed
  useEffect(() => {
    setValue("study_program_id", null);
  }, [period_id, setValue]);

  const columns: TypeSafeColDef[] = [
    {
      field: "mobile_column",
      headerName: "",
      valueGetter: (_, row) => row,
      flex: 1,
      display: "flex",
      renderCell: (args) => {
        const value = args.value as SchoolSchedules;
        return (
          <Stack sx={{ p: 1.5, gap: 2, width: "100%" }}>
            <Stack
              sx={{
                flexDirection: "row",
                justifyContent: "space-between",
                width: "100%",
              }}
            >
              <Typography sx={{ fontWeight: 600 }}>Hari</Typography>
              <Typography>{getDayText(value.day)}</Typography>
            </Stack>
            <Stack
              sx={{
                flexDirection: "row",
                justifyContent: "space-between",
                width: "100%",
              }}
            >
              <Typography sx={{ fontWeight: 600 }}>Jam Mulai</Typography>
              <Typography>
                {dayjs(value.start_time, "h:mm A Z").format("h:mm A")}
              </Typography>
            </Stack>
            <Stack
              sx={{
                flexDirection: "row",
                justifyContent: "space-between",
                width: "100%",
              }}
            >
              <Typography sx={{ fontWeight: 600 }}>Jam Selesai</Typography>
              <Typography>
                {dayjs(value.end_time, "h:mm A Z").format("h:mm A")}
              </Typography>
            </Stack>
            <Stack sx={{ flexDirection: "row", gap: 1, justifyContent: "end" }}>
              <CreateSchoolScheduleButton
                school_schedule_id={value.id}
                period_id={period_id}
                renderTrigger={(onClick) => {
                  return <EditIcon onClick={onClick} />;
                }}
              />
              <CreateSchoolScheduleButton
                school_schedule_id={value.id}
                period_id={period_id}
                deleteMode
                renderTrigger={(onClick) => {
                  return <DeleteIcon onClick={onClick} />;
                }}
              />
            </Stack>
          </Stack>
        );
      },
    },
    {
      field: "day",
      headerName: "Hari",
      display: "flex",
      flex: 1,
      valueGetter: (_, row: SchoolSchedules) => getDayText(row.day),
      renderCell: ({ value }) => <Stack sx={{ py: 1.5 }}>{value}</Stack>,
    },
    {
      field: "start_time",
      headerName: "Jam Mulai",
      display: "flex",
      flex: 1,
      valueGetter: (_, row: SchoolSchedules) =>
        dayjs(row.start_time, "h:mm A Z").toDate(),
      renderCell: ({ value }) => (
        <Stack sx={{ py: 1.5 }}>{dayjs(value).format("h:mm A")}</Stack>
      ),
    },
    {
      field: "end_time",
      headerName: "Jam Selesai",
      display: "flex",
      flex: 1,
      valueGetter: (_, row: SchoolSchedules) =>
        dayjs(row.end_time, "h:mm A Z").toDate(),
      renderCell: ({ value }) => (
        <Stack sx={{ py: 1.5 }}>{dayjs(value).format("h:mm A")}</Stack>
      ),
    },

    {
      field: "action",
      headerName: "Aksi",
      width: 128,
      display: "flex",
      renderCell: ({ row }: { row: SchoolSchedules }) => (
        <Stack sx={{ flexDirection: "row", gap: 1, py: 1.5 }}>
          <CreateSchoolScheduleButton
            school_schedule_id={row.id}
            period_id={period_id}
            renderTrigger={(onClick) => {
              return <EditIcon onClick={onClick} />;
            }}
          />
          <CreateSchoolScheduleButton
            school_schedule_id={row.id}
            period_id={period_id}
            deleteMode
            renderTrigger={(onClick) => {
              return <DeleteIcon onClick={onClick} />;
            }}
          />
        </Stack>
      ),
      sortable: false,
    },
  ];

  const columnVisibilityModel = Object.fromEntries(
    columns
      .map((column) => column.field)
      .filter((field) =>
        isMobile ? field !== "mobile_column" : field === "mobile_column"
      )
      .map((field) => [field, false])
  );

  return (
    <>
      <div className="flex gap-4 p-4 border-b border-0 border-solid border-neutral-200 justify-between thick-scrollbar items-center">
        <div className="flex gap-2 overflow-auto lg:overflow-visible py-2 lg:py-0">
          <AutocompleteElement
            control={control}
            name="period_id"
            matchId
            options={periods.map((item) => ({ id: item.id, label: item.name }))}
            label="Periode"
            autocompleteProps={{
              size: "small",
              sx: { minWidth: "180px" },
            }}
          />
          <AutocompleteElement
            control={control}
            name="study_program_id"
            matchId
            options={fitleredStudyPrograms.map((item) => ({
              id: item.id,
              label: item.name,
            }))}
            label="Program Studi"
            autocompleteProps={{
              disabled: !period_id,
              size: "small",
              sx: { minWidth: "180px" },
            }}
          />
          <AutocompleteElement
            control={control}
            name="grade"
            matchId
            options={grades.map((item) => ({
              id: item,
              label: item,
            }))}
            label="Tingkatan"
            autocompleteProps={{
              disabled: !study_program_id,
              size: "small",
              sx: { minWidth: "150px" },
            }}
          />
        </div>
        <div className="min-w-28">
          <CreateSchoolScheduleButton
            period_id={period_id}
            study_program_id={study_program_id}
            grade={grade}
            renderTrigger={(onClick) => {
              return (
                <Button
                  variant="contained"
                  startIcon={<Add />}
                  onClick={onClick}
                  disabled={disabled}
                  sx={{ width: "100%" }}
                >
                  Tambah
                </Button>
              );
            }}
          />
        </div>
      </div>
      {disabled && (
        <div className="p-4 border-b border-0 border-solid border-neutral-200 ">
          <Alert severity="warning">
            Lengkapi ketiga filter terlebih dahulu
          </Alert>
        </div>
      )}
      <DataGrid
        sx={{
          "& .MuiDataGrid-cell:focus": {
            outline: " none",
          },
          border: "none",
        }}
        disableRowSelectionOnClick
        className="bg-white grow"
        rows={disabled ? [] : fitleredSchoolSchedules}
        columns={columns}
        initialState={{ pagination: { paginationModel: { pageSize: 20 } } }}
        pageSizeOptions={[10, 20, 50]}
        getRowClassName={(params) =>
          params.indexRelativeToCurrentPage % 2 === 0
            ? "bg-neutral-50"
            : "bg-neutral-100"
        }
        slots={{
          toolbar: CustomToolbar,
        }}
        getRowHeight={() => "auto"}
        columnVisibilityModel={columnVisibilityModel}
        columnHeaderHeight={isMobile ? 0 : undefined}
        disableColumnMenu
      />
    </>
  );
}

function CustomToolbar() {
  const api = useGridApiContext();
  const [open, { toggle }] = useToggle(false);
  const { control, handleSubmit, reset, setValue, watch } = useForm<{
    field: Field | "";
    sortDirection: "asc" | "desc";
    quickFilter: string;
  }>({
    defaultValues: {
      field: "",
      sortDirection: "asc",
      quickFilter: "",
    },
  });

  const sortSelectOptions: { label: string; id: Field }[] = [
    { label: "Hari", id: "day" },
    { label: "Jam Mulai", id: "start_time" },
    { label: "Jam Selesai", id: "end_time" },
  ];

  const quickFilter = watch("quickFilter");
  useEffect(() => {
    api.current.setQuickFilterValues(
      quickFilter.split(" ").filter((s) => s !== "")
    );
  }, [api, quickFilter]);

  return (
    <div
      className={cn(
        "p-4 flex flex-col gap-2 border-b border-0 border-solid border-neutral-200",
        {
          hidden: true,
        }
      )}
    >
      <div className=" flex justify-between ">
        <TextFieldElement
          control={control}
          name="quickFilter"
          placeholder="Cari"
          size="small"
          slotProps={{
            input: {
              endAdornment: (
                <InputAdornment position="end">
                  {quickFilter ? (
                    <IconButton
                      size="small"
                      onClick={() => setValue("quickFilter", "")}
                    >
                      <Clear />
                    </IconButton>
                  ) : (
                    <Search />
                  )}
                </InputAdornment>
              ),
            },
          }}
        />
        {/* <CreateExtracurricularButton /> */}
      </div>
      <Button
        startIcon={<Sort />}
        variant="outlined"
        onClick={toggle}
        sx={{
          display: { xs: "flex", lg: "none" },
        }}
      >
        Urutkan Data
      </Button>
      <Dialog open={open} onClose={toggle}>
        <DialogTitle>Urutkan</DialogTitle>
        <DialogContent
          sx={{
            width: "250px",
            display: "flex",
            flexDirection: "column",
            gap: 2,
            overflow: "initial",
          }}
        >
          <SelectElement
            control={control}
            name="field"
            options={sortSelectOptions}
            label="Pilih Kolom"
            slotProps={{
              input: {
                endAdornment: watch("field") && (
                  <InputAdornment position="end">
                    <IconButton onClick={() => setValue("field", "")}>
                      <Clear />
                    </IconButton>
                  </InputAdornment>
                ),
              },
            }}
          />
          <SelectElement
            control={control}
            name="sortDirection"
            options={[
              { label: "A-Z", id: "asc" },
              { label: "Z-A", id: "desc" },
            ]}
            label="Urutan"
          />
        </DialogContent>
        <DialogActions sx={{ pb: 2, px: 3 }}>
          <Button
            variant="outlined"
            onClick={() => {
              reset();
              toggle();
            }}
          >
            Batal
          </Button>
          <Button
            variant="contained"
            onClick={handleSubmit((value) => {
              if (value.field) {
                api.current.sortColumn(value.field, value.sortDirection);
              } else {
                api.current.sortColumn("name", null);
              }
              toggle();
            })}
          >
            Simpan
          </Button>
        </DialogActions>
      </Dialog>
    </div>
  );
}
