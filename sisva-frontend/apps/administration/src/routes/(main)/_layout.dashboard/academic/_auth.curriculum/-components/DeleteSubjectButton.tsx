import { DeleteForever } from "@mui/icons-material";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Paper,
  Stack,
  Typography,
} from "@mui/material";
import { useClasses } from "@sisva/hooks/query/academic/useClasses";
import {
  useDeleteSubject,
  useSubject,
} from "@sisva/hooks/query/academic/useSubjects";
import { useSubjectTeachers } from "@sisva/hooks/query/academic/useSubjectTeachers";
import { useSyllabuses } from "@sisva/hooks/query/academic/useSyllabuses";
import { useTeachingMaterials } from "@sisva/hooks/query/classroom/useTeachingMaterials";
import { useToggle } from "ahooks";
import dayjs from "dayjs";
import id from "dayjs/locale/id";
import type { ReactNode } from "react";
import toast from "react-hot-toast";
dayjs.locale(id);

export default function DeleteSubjectButton({
  subject_id,
  renderTrigger,
}: {
  subject_id: number;
  renderTrigger?: (onClick: () => void) => ReactNode;
}) {
  const [visible, { toggle }] = useToggle(false);
  const { data: subject } = useSubject(subject_id);

  //  ────────────────────────── check dependent ──────────────────────────
  const { data: subjectTeachers = [] } = useSubjectTeachers();
  const { data: classes = [] } = useClasses();
  const { data: teachingMaterials = [] } = useTeachingMaterials();
  const { data: syllabuses = [] } = useSyllabuses();

  const filtered1 = subjectTeachers.filter(
    (item) => item.subject_id === subject_id
  );
  const filtered2 = classes.filter((item) => item.subject_id === subject_id);
  const filtered3 = teachingMaterials.filter(
    (item) => item.subject_id === subject_id
  );
  const filtered4 = syllabuses.filter((item) => item.subject_id === subject_id);

  const canBeDeleted =
    filtered1.length === 0 &&
    filtered2.length === 0 &&
    filtered3.length === 0 &&
    filtered4.length === 0;
  //  ────────────────────────── check dependent ──────────────────────────

  const { mutate: deleteSubject } = useDeleteSubject({
    onSuccess: () => {
      toggle();
      toast.success("Mata Pelajaran berhasil dihapus");
    },
    onError: () => {
      toast.error("Mata Pelajaran gagal dihapus");
    },
  });

  return (
    <>
      {renderTrigger ? (
        renderTrigger(toggle)
      ) : (
        <Button
          variant="contained"
          startIcon={<DeleteForever />}
          color="error"
          onClick={toggle}
        >
          Hapus
        </Button>
      )}
      <Modal open={visible} onClose={toggle}>
        <Stack
          sx={{
            width: 1,
            height: 1,
            justifyContent: "center",
            alignItems: "center",
            p: 2,
          }}
        >
          <Paper
            component="form"
            sx={{
              py: 2,
              width: 1,
              maxWidth: "ms",
              gap: 2,
              display: "flex",
              flexDirection: "column",
            }}
            onSubmit={(e) => {
              e.preventDefault();
              deleteSubject(subject_id);
            }}
          >
            <Typography sx={{ fontWeight: 600, px: 2 }}>
              Hapus Mata Pelajaran
            </Typography>
            <Divider />
            <Stack sx={{ gap: 2, px: 2 }}>
              {!canBeDeleted && (
                <Alert severity="warning">
                  Hanya dapat menghapus Mata Pelajaran yang belum digunakan
                </Alert>
              )}
              <Stack
                sx={{ flexDirection: "row", justifyContent: "space-between" }}
              >
                <Typography sx={{ fontWeight: 600 }}>Mata Pelajaran</Typography>
                <Typography>{subject?.name}</Typography>
              </Stack>
              <Stack
                sx={{ flexDirection: "row", justifyContent: "space-between" }}
              >
                <Typography sx={{ fontWeight: 600 }}>Kurikulum</Typography>
                <Typography>{subject?.curriculum_name}</Typography>
              </Stack>
              <Stack
                sx={{ flexDirection: "row", justifyContent: "space-between" }}
              >
                <Typography sx={{ fontWeight: 600 }}>Program Studi</Typography>
                <Typography>{subject?.study_program_name}</Typography>
              </Stack>
            </Stack>
            <Divider />
            <Stack sx={{ gap: 2, px: 2, flexDirection: "row" }}>
              <Button variant="outlined" fullWidth onClick={toggle}>
                Batal
              </Button>
              <Button
                type="submit"
                variant="contained"
                fullWidth
                color="error"
                disabled={!canBeDeleted}
              >
                Hapus
              </Button>
            </Stack>
          </Paper>
        </Stack>
      </Modal>
    </>
  );
}
