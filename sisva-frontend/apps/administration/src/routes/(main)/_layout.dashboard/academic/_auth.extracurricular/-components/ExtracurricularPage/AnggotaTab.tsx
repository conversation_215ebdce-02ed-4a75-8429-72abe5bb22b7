import { Clear, Search, Sort } from "@mui/icons-material";
import {
  Button,
  <PERSON>alog,
  DialogActions,
  DialogContent,
  DialogTitle,
  IconButton,
  InputAdornment,
  Stack,
  Typography,
  useMediaQuery,
} from "@mui/material";
import type { GridColDef } from "@mui/x-data-grid";
import { DataGrid, useGridApiContext } from "@mui/x-data-grid";
import { useExtracurricularMembers } from "@sisva/hooks/query/academic/useExtracurricularMembers";
import { useStudentInStudentGroups } from "@sisva/hooks/query/academic/useStudentGroups";
import type { ExtracurricularMember } from "@sisva/types/apiTypes";
import { onlyUnique } from "@sisva/utils";
import { useToggle } from "ahooks";
import { useEffect } from "react";
import { useForm } from "react-hook-form";
import { SelectElement, TextFieldElement } from "react-hook-form-mui";

import DeleteIcon from "#/routes/(main)/-components/DeleteIcon";
import EditIcon from "#/routes/(main)/-components/EditIcon";

import AddExtracurricularMemberButton from "../AddExtracurricularMemberButton";
import DeleteExtracurricularMemberButton from "../DeleteExtracurricularMemberButton";

type Field =
  | "mobile_column"
  | keyof ExtracurricularMember
  | "student_group_name"
  | "action";
type TypeSafeColDef = GridColDef & { field: Field };

export default function AnggotaTab() {
  const isMobile = useMediaQuery((theme) => theme.breakpoints.down("lg"));
  const { data: extracurricularMembers = [] } = useExtracurricularMembers();
  const { data: studentInStudentGroups = [] } = useStudentInStudentGroups();

  const columns: TypeSafeColDef[] = [
    {
      field: "mobile_column",
      headerName: "",
      valueGetter: (_, row) => row,
      flex: 1,
      display: "flex",
      renderCell: (args) => {
        const value = args.value as ExtracurricularMember;
        return (
          <Stack sx={{ p: 1.5, gap: 2, width: "100%" }}>
            <Stack
              sx={{
                flexDirection: "row",
                justifyContent: "space-between",
                width: "100%",
              }}
            >
              <Typography sx={{ fontWeight: 600 }}>Ekstrakurikuler</Typography>
              <Typography>{value.extracurricular_name}</Typography>
            </Stack>
            <Stack
              sx={{
                flexDirection: "row",
                justifyContent: "space-between",
                width: "100%",
              }}
            >
              <Typography sx={{ fontWeight: 600 }}>Kelas</Typography>
              <Typography>
                {
                  studentInStudentGroups.find(
                    (studentInStudentGroup) =>
                      studentInStudentGroup.student_id === value.student_id
                  )?.student_group_name
                }
              </Typography>
            </Stack>
            <Stack
              sx={{
                flexDirection: "row",
                justifyContent: "space-between",
                width: "100%",
              }}
            >
              <Typography sx={{ fontWeight: 600 }}>Murid</Typography>
              <Typography>{value.student_name}</Typography>
            </Stack>
            <Stack sx={{ flexDirection: "row", gap: 1, justifyContent: "end" }}>
              <AddExtracurricularMemberButton
                old_extracurricular_id={value.extracurricular_id}
                old_student_id={value.student_id}
                renderTrigger={(onClick) => <EditIcon onClick={onClick} />}
              />
              <DeleteExtracurricularMemberButton
                extracurricular_id={value.extracurricular_id}
                student_id={value.student_id}
                renderTrigger={(onClick) => {
                  return <DeleteIcon onClick={onClick} />;
                }}
              />
            </Stack>
          </Stack>
        );
      },
    },
    {
      field: "extracurricular_name",
      headerName: "Ekstrakurikuler",
      display: "flex",
      flex: 1,
      renderCell: ({ value }) => <Stack sx={{ py: 1.3 }}>{value}</Stack>,
    },
    {
      field: "student_name",
      headerName: "Murid",
      display: "flex",
      flex: 1,
      renderCell: ({ value }) => <Stack sx={{ py: 1.3 }}>{value}</Stack>,
    },
    {
      field: "student_group_name",
      headerName: "Kelas",
      width: 150,
      display: "flex",
      valueGetter: (_, row: ExtracurricularMember) =>
        studentInStudentGroups.find(
          (studentInStudentGroup) =>
            studentInStudentGroup.student_id === row.student_id
        )?.student_group_name,
      renderCell: ({ value }) => {
        return (
          <Stack sx={{ py: 1.3, flexDirection: "row", gap: 0.5 }}>
            {value}
          </Stack>
        );
      },
    },
    {
      field: "action",
      headerName: "Aksi",
      width: 128,
      display: "flex",
      renderCell: ({ row }: { row: ExtracurricularMember }) => (
        <Stack sx={{ flexDirection: "row", gap: 1, py: 1.5 }}>
          <AddExtracurricularMemberButton
            old_extracurricular_id={row.extracurricular_id}
            old_student_id={row.student_id}
            renderTrigger={(onClick) => <EditIcon onClick={onClick} />}
          />
          <DeleteExtracurricularMemberButton
            extracurricular_id={row.extracurricular_id}
            student_id={row.student_id}
            renderTrigger={(onClick) => {
              return <DeleteIcon onClick={onClick} />;
            }}
          />
        </Stack>
      ),
      sortable: false,
    },
  ];

  const columnVisibilityModel = Object.fromEntries(
    columns
      .map((column) => column.field)
      .filter((field) =>
        isMobile ? field !== "mobile_column" : field === "mobile_column"
      )
      .map((field) => [field, false])
  );

  return (
    <DataGrid
      sx={{
        "& .MuiDataGrid-cell:focus": {
          outline: " none",
        },
        border: "none",
      }}
      getRowId={(row: ExtracurricularMember) =>
        `${row.student_id} - ${row.extracurricular_id}`
      }
      disableRowSelectionOnClick
      className="bg-white grow"
      rows={extracurricularMembers}
      columns={columns}
      initialState={{ pagination: { paginationModel: { pageSize: 20 } } }}
      pageSizeOptions={[10, 20, 50]}
      getRowClassName={(params) =>
        params.indexRelativeToCurrentPage % 2 === 0
          ? "bg-neutral-50"
          : "bg-neutral-100"
      }
      slots={{
        toolbar: CustomToolbar,
      }}
      getRowHeight={() => "auto"}
      columnVisibilityModel={columnVisibilityModel}
      columnHeaderHeight={isMobile ? 0 : undefined}
      disableColumnMenu
      disableColumnFilter
    />
  );
}

function CustomToolbar() {
  const api = useGridApiContext();
  const [open, { toggle }] = useToggle(false);

  const filter_1 = "extracurricular_name";
  const { control, handleSubmit, reset, setValue, watch } = useForm<{
    field: Field | "";
    sortDirection: "asc" | "desc";
    quickFilter: string;
    [filter_1]: string;
  }>({
    defaultValues: {
      field: "",
      sortDirection: "asc",
      quickFilter: "",
      [filter_1]: "",
    },
  });
  const rows = Array.from(api.current.getRowModels()).map(
    ([key, value]) => value
  ) as ExtracurricularMember[];
  const extracurricularNames = rows
    .map((row) => row.extracurricular_name)
    .filter(onlyUnique);

  const sortSelectOptions: { label: string; id: Field }[] = [
    { label: "Ekstrakurikuler", id: "extracurricular_name" },
    { label: "Murid", id: "student_name" },
    { label: "Kelas", id: "student_group_name" },
  ];

  const extracurricularNemeFilter = watch(filter_1);

  // apply filter
  useEffect(() => {
    const field: Field = "extracurricular_name";
    api.current.upsertFilterItem({
      field,
      // import getGridStringOperators from mui to see more operator options
      operator: "equals",
      value: extracurricularNemeFilter,
    });
  }, [api, extracurricularNemeFilter]);

  const quickFilter = watch("quickFilter");
  useEffect(() => {
    api.current.setQuickFilterValues(
      quickFilter.split(" ").filter((s) => s !== "")
    );
  }, [api, quickFilter]);

  return (
    <div className="p-4 flex flex-col gap-2 border-b border-0 border-solid border-neutral-200">
      <div className=" flex justify-between ">
        <Stack sx={{ flexDirection: "row", gap: 2 }}>
          <TextFieldElement
            control={control}
            name="quickFilter"
            placeholder="Cari"
            size="small"
            slotProps={{
              input: {
                endAdornment: (
                  <InputAdornment position="end">
                    {quickFilter ? (
                      <IconButton
                        size="small"
                        onClick={() => setValue("quickFilter", "")}
                      >
                        <Clear />
                      </IconButton>
                    ) : (
                      <Search />
                    )}
                  </InputAdornment>
                ),
              },
            }}
          />
          <SelectElement
            size="small"
            sx={{ width: "150px" }}
            control={control}
            name={filter_1}
            options={extracurricularNames.map((item) => ({
              id: item,
              label: item,
            }))}
            label="Ekstrakurikuler"
            slotProps={{
              input: {
                endAdornment: watch(filter_1) && (
                  <InputAdornment position="end">
                    <IconButton
                      onClick={() => setValue(filter_1, "")}
                      size="small"
                    >
                      <Clear />
                    </IconButton>
                  </InputAdornment>
                ),
              },
            }}
          />
        </Stack>

        <AddExtracurricularMemberButton />
      </div>
      <Button
        startIcon={<Sort />}
        variant="outlined"
        onClick={toggle}
        sx={{
          display: { xs: "flex", lg: "none" },
        }}
      >
        Urutkan Data
      </Button>
      <Dialog open={open} onClose={toggle}>
        <DialogTitle>Urutkan</DialogTitle>
        <DialogContent
          sx={{
            width: "250px",
            display: "flex",
            flexDirection: "column",
            gap: 2,
            overflow: "initial",
          }}
        >
          <SelectElement
            control={control}
            name="field"
            options={sortSelectOptions}
            label="Pilih Kolom"
            slotProps={{
              input: {
                endAdornment: watch("field") && (
                  <InputAdornment position="end">
                    <IconButton onClick={() => setValue("field", "")}>
                      <Clear />
                    </IconButton>
                  </InputAdornment>
                ),
              },
            }}
          />
          <SelectElement
            control={control}
            name="sortDirection"
            options={[
              { label: "A-Z", id: "asc" },
              { label: "Z-A", id: "desc" },
            ]}
            label="Urutan"
          />
        </DialogContent>
        <DialogActions sx={{ pb: 2, px: 3 }}>
          <Button
            variant="outlined"
            onClick={() => {
              reset();
              toggle();
            }}
          >
            Batal
          </Button>
          <Button
            variant="contained"
            onClick={handleSubmit((value) => {
              if (value.field) {
                api.current.sortColumn(value.field, value.sortDirection);
              } else {
                api.current.sortColumn("name", null);
              }
              toggle();
            })}
          >
            Simpan
          </Button>
        </DialogActions>
      </Dialog>
    </div>
  );
}
