import { valibotResolver } from "@hookform/resolvers/valibot";
import { Add } from "@mui/icons-material";
import {
  Button,
  Divider,
  Modal,
  Paper,
  Stack,
  Typography,
} from "@mui/material";
import {
  useCreateCurriculum,
  useCurriculum,
  useUpdateCurriculum,
} from "@sisva/hooks/query/academic/useCurriculums";
import { useToggle } from "ahooks";
import { type ReactNode } from "react";
import { TextFieldElement, useForm } from "react-hook-form-mui";
import toast from "react-hot-toast";
import {
  maxValue,
  message,
  minLength,
  minValue,
  number,
  object,
  pipe,
  string,
} from "valibot";

/**
 * this component has 2 modes:
 * 1. create new curriculum, no props
 * 2. edit curriculum, by passing curriculum_id
 */
export default function CreateCurriculumButton({
  // if curriculum_id provided, then it's edit mode
  curriculum_id,
  renderTrigger,
}: {
  curriculum_id?: number;
  renderTrigger?: (onClick: () => void) => ReactNode;
}) {
  const [visible, { toggle }] = useToggle(false);

  const { data: curriculum } = useCurriculum(curriculum_id);

  const schema = object({
    name: message(pipe(string(), minLength(1)), "Nama Kurikulum wajib diisi"),
    passing_grade: message(
      pipe(number(), minValue(0), maxValue(100)),
      "KKM harus antara 0 sampai 100"
    ),
  });

  const { control, handleSubmit, reset, watch, setValue } = useForm({
    values: {
      name: curriculum?.name ?? "",
      passing_grade: curriculum?.passing_grade ?? 0,
    },
    resolver: valibotResolver(schema),
  });

  const { mutate: createCurriculum } = useCreateCurriculum({
    onSuccess: () => {
      toggle();
      reset();
      toast.success("Kurikulum berhasil dibuat");
    },
    onError: () => {
      toast.error("Kurikulum gagal dibuat");
    },
  });

  const { mutate: updateCurriculum } = useUpdateCurriculum({
    onSuccess: () => {
      toggle();
      reset();
      toast.success("Kurikulum berhasil diperbarui");
    },
    onError: () => {
      toast.error("Kurikulum gagal diperbarui");
    },
  });

  return (
    <>
      {renderTrigger ? (
        renderTrigger(toggle)
      ) : (
        <Button variant="contained" startIcon={<Add />} onClick={toggle}>
          Tambah
        </Button>
      )}
      <Modal open={visible} onClose={toggle}>
        <Stack
          sx={{
            width: 1,
            height: 1,
            justifyContent: "center",
            alignItems: "center",
            p: 2,
          }}
        >
          <Paper
            component={"form"}
            sx={{
              py: 2,
              width: 1,
              maxWidth: "ms",
              gap: 2,
              display: "flex",
              flexDirection: "column",
            }}
            onSubmit={handleSubmit((value) => {
              if (curriculum_id) {
                updateCurriculum({
                  curriculum_id,
                  ...value,
                });
              } else {
                createCurriculum(value);
              }
            })}
          >
            <Typography sx={{ fontWeight: 600, px: 2 }}>
              {(() => {
                if (curriculum_id) {
                  return "Edit Kurikulum";
                } else {
                  return "Tambah Kurikulum";
                }
              })()}
            </Typography>
            <Divider />
            <Stack sx={{ gap: 2, px: 2 }}>
              <TextFieldElement
                name="name"
                label="Nama Kurikulum"
                control={control}
              />
              <TextFieldElement
                type="number"
                name="passing_grade"
                label="KKM"
                control={control}
                slotProps={{
                  htmlInput: {
                    min: 0,
                    max: 100,
                  },
                }}
              />
            </Stack>
            <Divider />
            <Stack sx={{ gap: 2, px: 2, flexDirection: "row" }}>
              <Button variant="outlined" fullWidth onClick={toggle}>
                Batal
              </Button>
              <Button
                type="submit"
                variant="contained"
                fullWidth
                color="primary"
              >
                Simpan
              </Button>
            </Stack>
          </Paper>
        </Stack>
      </Modal>
    </>
  );
}
