import { Add, DeleteForever } from "@mui/icons-material";
import {
  Button,
  Divider,
  Modal,
  Paper,
  Stack,
  Typography,
} from "@mui/material";
import {
  useDeleteExtracurricular,
  useExtracurricular,
} from "@sisva/hooks/query/academic/useExtracurriculars";
import { useToggle } from "ahooks";
import type { ReactNode } from "react";
import toast from "react-hot-toast";

export default function DeleteExtracurricularButton({
  extracurricular_id,
  renderTrigger,
}: {
  extracurricular_id: number;
  renderTrigger?: (onClick: () => void) => ReactNode;
}) {
  const { data: extracurricular } = useExtracurricular(extracurricular_id);

  const [visible, { toggle }] = useToggle(false);

  const { mutate: deleteExtracurricular } = useDeleteExtracurricular({
    onSuccess: () => {
      toggle();
      toast.success("Ekstrakurikuler berhasil dihapus");
    },
    onError: () => {
      toast.error("Ekstrakurikuler gagal dihapus");
    },
  });

  return (
    <>
      {renderTrigger ? (
        renderTrigger(toggle)
      ) : (
        <Button
          variant="contained"
          startIcon={<DeleteForever />}
          color="error"
          onClick={toggle}
        >
          Hapus
        </Button>
      )}
      <Modal open={visible} onClose={toggle}>
        <Stack
          sx={{
            width: 1,
            height: 1,
            justifyContent: "center",
            alignItems: "center",
            p: 2,
          }}
        >
          <Paper
            component="form"
            sx={{
              py: 2,
              width: 1,
              maxWidth: "ms",
              gap: 2,
              display: "flex",
              flexDirection: "column",
            }}
            onSubmit={(e) => {
              e.preventDefault();
              deleteExtracurricular(extracurricular_id);
            }}
          >
            <Typography sx={{ fontWeight: 600, px: 2 }}>
              Hapus Ekstrakurikuler
            </Typography>
            <Divider />
            <Stack sx={{ gap: 2, px: 2 }}>
              <Stack
                sx={{ flexDirection: "row", justifyContent: "space-between" }}
              >
                <Typography sx={{ fontWeight: 600 }}>Ektrakurikuler</Typography>
                <Typography>{extracurricular?.name}</Typography>
              </Stack>
              <Stack
                sx={{ flexDirection: "row", justifyContent: "space-between" }}
              >
                <Typography sx={{ fontWeight: 600 }}>Pembina</Typography>
                <Typography>{extracurricular?.teacher_name}</Typography>
              </Stack>
            </Stack>
            <Divider />
            <Stack sx={{ gap: 2, px: 2, flexDirection: "row" }}>
              <Button variant="outlined" fullWidth onClick={toggle}>
                Batal
              </Button>
              <Button type="submit" variant="contained" fullWidth color="error">
                Hapus
              </Button>
            </Stack>
          </Paper>
        </Stack>
      </Modal>
    </>
  );
}
