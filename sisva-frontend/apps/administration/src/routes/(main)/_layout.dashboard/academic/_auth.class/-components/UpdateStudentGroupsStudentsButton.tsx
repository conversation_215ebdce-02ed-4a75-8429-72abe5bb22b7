import { yupResolver } from "@hookform/resolvers/yup";
import { Add } from "@mui/icons-material";
import {
  Button,
  Divider,
  Modal,
  Paper,
  Stack,
  Typography,
} from "@mui/material";
import { usePeriods } from "@sisva/hooks/query/academic/usePeriods";
import {
  useStudentGroupsWithStudents,
  useStudentGroupWithStudents,
  useUpdateStudentGroupsStudents,
} from "@sisva/hooks/query/academic/useStudentGroups";
import { useStudentsWithoutStudentGroup } from "@sisva/hooks/query/user/useStudents";
import { useToggle } from "ahooks";
import { sort } from "fast-sort";
import { type ReactNode, useEffect } from "react";
import { AutocompleteElement, useForm } from "react-hook-form-mui";
import toast from "react-hot-toast";
import { array, number, object, string } from "yup";

export default function UpdateStudentGroupsStudentsButton({
  student_group_id,
  renderTrigger,
}: {
  student_group_id?: number;
  renderTrigger?: (onClick: () => void) => ReactNode;
}) {
  const { data: students = [] } = useStudentsWithoutStudentGroup();
  const { data: studentGroups = [] } = useStudentGroupsWithStudents();
  const { data: periods = [] } = usePeriods();

  const [visible, { toggle }] = useToggle(false);
  const { control, handleSubmit, reset, watch, setValue } = useForm({
    values: {
      period_id: 0,
      student_group_id: student_group_id ?? 0,
      student_ids: [],
    },
    resolver: yupResolver(
      object({
        period_id: number().nullable(),
        student_group_id: number()
          .required("Kelas wajib dipilih")
          .notOneOf([0], "Kelas wajib dipilih"),
        student_ids: array()
          .of(string().required("Murid wajib dipilih"))
          .required("Murid wajib dipilih"),
      })
    ),
  });

  const period_id = watch("period_id");
  const filteredStudentGroups = studentGroups.filter((studentGroup) => {
    if (period_id) {
      return studentGroup.period_id === period_id;
    }
    return true;
  });

  student_group_id = watch("student_group_id");
  const { data: studentGroup } = useStudentGroupWithStudents(student_group_id);

  const studentGroupIdAvailable = filteredStudentGroups
    .map((studentGroup) => studentGroup.id)
    .includes(student_group_id);

  useEffect(() => {
    // reset student_ids everytime studetGroup changes
    setValue(
      "student_ids",
      studentGroup?.students.map((student) => student.id) ?? []
    );

    // reset student_group_id everytime period changes and studentGroupIdAvailable is false
    if (!studentGroupIdAvailable) setValue("student_group_id", 0);
  }, [setValue, studentGroup?.students, studentGroupIdAvailable]);

  const availableStudents = students
    .filter(
      (student) =>
        student.detail.grade && student.detail.grade === studentGroup?.grade
    )
    .concat(studentGroup?.students ?? []);

  const { mutateAsync: updateStudentGroupsStudents, isPending } =
    useUpdateStudentGroupsStudents({
      student_group_id: watch("student_group_id"),
      onSuccess: ({
        studentToBeUpdatedCount,
        isError,
        deletedStudentCount,
        insertedStudentCount,
      }) => {
        if (isError) {
          toast.error("Terjadi masalah saat memperbarui Kelas Siswa");
          toast.success(
            `${deletedStudentCount} baris Kelas Siswa telah dihapus`
          );
          toast.success(
            `${insertedStudentCount} baris Kelas Siswa telah ditambahkan`
          );
          return;
        }
        toggle();
        toast.success(`${studentToBeUpdatedCount} Siswa berhasil diperbarui`);
      },
      onError: () => {
        toast.error("Siswa gagal diperbarui");
      },
    });

  return (
    <>
      {renderTrigger ? (
        renderTrigger(toggle)
      ) : (
        <Button variant="contained" startIcon={<Add />} onClick={toggle}>
          Tambah
        </Button>
      )}
      <Modal open={visible} onClose={toggle}>
        <Stack
          sx={{
            width: 1,
            height: 1,
            justifyContent: "center",
            alignItems: "center",
            p: 2,
          }}
        >
          <Paper
            component={"form"}
            sx={{
              py: 2,
              width: 1,
              maxWidth: "ml",
              maxHeight: "80svh",
              gap: 2,
              display: "flex",
              flexDirection: "column",
              overflow: "auto",
            }}
            onSubmit={handleSubmit(async (value) => {
              const toast_id = toast.loading("Sedang memperbarui...");
              await updateStudentGroupsStudents(value);
              toast.dismiss(toast_id);
            })}
          >
            <Typography sx={{ fontWeight: 600, px: 2 }}>
              Tambah Murid
            </Typography>
            <Divider />
            <Stack sx={{ gap: 2, px: 2 }}>
              <AutocompleteElement
                control={control}
                name="period_id"
                matchId
                options={sort(periods)
                  .desc((item) => item.id)
                  .map((item) => ({
                    id: item.id,
                    label: `${item.name}`,
                  }))}
                label="Periode"
              />
              <AutocompleteElement
                control={control}
                name="student_group_id"
                matchId
                options={filteredStudentGroups.map((item) => ({
                  id: item.id,
                  label: `${item.name}`,
                }))}
                label="Nama Kelas *"
              />
              <AutocompleteElement
                control={control}
                name="student_ids"
                multiple
                matchId
                showCheckbox
                options={availableStudents.map((item) => ({
                  id: item.id,
                  label: `${item.name}`,
                }))}
                label="Nama Siswa"
              />
            </Stack>
            <Divider />
            <Stack sx={{ gap: 2, px: 2, flexDirection: "row" }}>
              <Button variant="outlined" fullWidth onClick={toggle}>
                Batal
              </Button>
              <Button
                type="submit"
                variant="contained"
                fullWidth
                disabled={isPending}
              >
                Simpan
              </Button>
            </Stack>
          </Paper>
        </Stack>
      </Modal>
    </>
  );
}
