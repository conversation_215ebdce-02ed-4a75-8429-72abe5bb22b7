import { ErrorMessage } from "@hookform/error-message";
import { yupResolver } from "@hookform/resolvers/yup";
import { Add, Article } from "@mui/icons-material";
import {
  Button,
  Divider,
  Modal,
  Paper,
  Stack,
  Typography,
} from "@mui/material";
import { useCurriculums } from "@sisva/hooks/query/academic/useCurriculums";
import {
  useStudyProgram,
  useStudyPrograms,
} from "@sisva/hooks/query/academic/useStudyPrograms";
import { useSubjects } from "@sisva/hooks/query/academic/useSubjects";
import {
  useCreateSyllabus,
  useSyllabus,
  useUpdateSyllabus,
} from "@sisva/hooks/query/academic/useSyllabuses";
import { useUploadFile } from "@sisva/hooks/query/file/useFiles";
import { useGetFileUrl } from "@sisva/hooks/utils";
import type { Grade } from "@sisva/types/apiTypes";
import { useToggle } from "ahooks";
import { type ReactNode } from "react";
import { AutocompleteElement, useForm } from "react-hook-form-mui";
import toast from "react-hot-toast";
import { number, object, string } from "yup";

/**
 * this component has 2 modes:
 * 1. create new syllabus, no props
 * 2. edit syllabus, by passing syllabys_id
 */
export default function CreateSyllabusButton({
  // if syllabys_id provided, then it's edit mode
  syllabus_id,
  renderTrigger,
}: {
  syllabus_id?: number;
  renderTrigger?: (onClick: () => void) => ReactNode;
}) {
  const [visible, { toggle }] = useToggle(false);

  const getUrl = useGetFileUrl();
  const { mutateAsync: uploadFile } = useUploadFile();

  const { data: syllabus } = useSyllabus(syllabus_id);
  const { data: curriculums = [] } = useCurriculums();
  const { data: studyPrograms = [] } = useStudyPrograms();
  const { data: subjects = [] } = useSubjects();

  const {
    control,
    handleSubmit,
    reset,
    watch,
    setValue,
    formState,
    clearErrors,
  } = useForm({
    values: {
      curriculum_id: syllabus?.curriculum_id ?? 0,
      study_program_id: syllabus?.study_program_id ?? 0,
      subject_id: syllabus?.subject_id ?? 0,
      grade: syllabus?.grade ?? "",
      file_uri: syllabus?.file_uri ?? "",
    },
    resolver: yupResolver(
      object({
        curriculum_id: number()
          .required("Kurikulum wajib dipilih")
          .notOneOf([0], "Kurikulum wajib dipilih"),
        study_program_id: number()
          .required("Program Studi wajib dipilih")
          .notOneOf([0], "Program Studi wajib dipilih"),
        subject_id: number()
          .required("Mata Pelajaran wajib dipilih")
          .notOneOf([0], "Mata Pelajaran wajib dipilih"),
        grade: string().required("Tingkatan wajib dipilih"),
        file_uri: string().required("Silabus wajib dipilih"),
      })
    ),
  });

  const curriculum_id = watch("curriculum_id");
  const study_program_id = watch("study_program_id");
  const filteredSubjects = subjects.filter(
    (subject) =>
      subject.curriculum_id === curriculum_id &&
      subject.study_program_id === study_program_id
  );

  const { data: studyProgram } = useStudyProgram(study_program_id);
  const grades = studyProgram?.grades ?? [];

  const { mutate: createSyllabus } = useCreateSyllabus({
    onSuccess: () => {
      toggle();
      reset();
      toast.success("Silabus berhasil dibuat");
    },
    onError: () => {
      toast.error("Silabus gagal dibuat");
    },
  });

  const { mutate: updateSyllabus } = useUpdateSyllabus({
    onSuccess: () => {
      toggle();
      reset();
      toast.success("Silabus berhasil diperbarui");
    },
    onError: () => {
      toast.error("Silabus gagal diperbarui");
    },
  });

  const file_uri = watch("file_uri");
  const url = getUrl(file_uri);

  return (
    <>
      {renderTrigger ? (
        renderTrigger(toggle)
      ) : (
        <Button variant="contained" startIcon={<Add />} onClick={toggle}>
          Tambah
        </Button>
      )}
      <Modal open={visible} onClose={toggle}>
        <Stack
          sx={{
            width: 1,
            height: 1,
            justifyContent: "center",
            alignItems: "center",
            p: 2,
          }}
        >
          <Paper
            component={"form"}
            sx={{
              py: 2,
              width: 1,
              maxWidth: "ms",
              gap: 2,
              display: "flex",
              flexDirection: "column",
            }}
            onSubmit={handleSubmit((value) => {
              if (syllabus_id) {
                updateSyllabus({
                  ...value,
                  syllabus_id,
                  grade: value.grade as Grade,
                });
              } else {
                createSyllabus({
                  ...value,
                  grade: value.grade as Grade,
                });
              }
            })}
          >
            <Typography sx={{ fontWeight: 600, px: 2 }}>
              {(() => {
                if (syllabus_id) {
                  return "Edit Mata Pelajaran";
                } else {
                  return "Tambah Mata Pelajaran";
                }
              })()}
            </Typography>
            <Divider />
            <Stack sx={{ gap: 2, px: 2 }}>
              <AutocompleteElement
                control={control}
                name="curriculum_id"
                matchId
                options={curriculums.map((item) => ({
                  id: item.id,
                  label: item.name,
                }))}
                label="Kurikulum"
              />
              <AutocompleteElement
                control={control}
                name="study_program_id"
                matchId
                options={studyPrograms.map((item) => ({
                  id: item.id,
                  label: item.name,
                }))}
                label="Program Studi"
              />
              <AutocompleteElement
                control={control}
                name="subject_id"
                matchId
                options={filteredSubjects.map((item) => ({
                  id: item.id,
                  label: item.name,
                }))}
                label="Mata Pelajaran"
                autocompleteProps={{
                  disabled: !curriculum_id || !study_program_id,
                }}
              />
              <AutocompleteElement
                control={control}
                name="grade"
                matchId
                options={grades.map((item) => ({
                  id: item,
                  label: item,
                }))}
                label="Tingkatan"
                autocompleteProps={{
                  disabled: !curriculum_id || !study_program_id,
                }}
              />
              <Stack
                sx={{
                  flexDirection: "row",
                  gap: 2,
                }}
              >
                <Stack
                  sx={{
                    gap: 2,
                    flex: 1,
                    justifyContent: "space-between",
                  }}
                >
                  <ErrorMessage
                    errors={formState.errors}
                    name="file_uri"
                    render={({ message }) => (
                      <Typography
                        color="error"
                        sx={{
                          fontSize: 12,
                          textAlign: "center",
                          backgroundColor: "grey.200",
                          p: 1,
                          borderRadius: 1,
                        }}
                      >
                        {message}
                      </Typography>
                    )}
                  />
                  {file_uri && (
                    <Stack
                      sx={{
                        fontSize: 12,
                        backgroundColor: "grey.200",
                        p: 1,
                        borderRadius: 1,
                        flexDirection: "row",
                        gap: 1,
                        alignItems: "center",
                      }}
                    >
                      <Article color="primary" sx={{ fontSize: 16 }} />
                      <Typography sx={{ fontSize: 12 }}>{file_uri}</Typography>
                    </Stack>
                  )}
                  <Stack sx={{ gap: 1 }}>
                    <Button component="label" variant="contained" size="small">
                      {url ? "Ubah Silabus" : "Upload Silabus"}
                      <input
                        accept="application/pdf"
                        className="hidden"
                        type="file"
                        onChange={async (event) => {
                          if (!event.target.files?.[0]) return;
                          const formData = new FormData();
                          formData.append("file", event.target.files[0]);
                          const uri = await uploadFile(formData);
                          setValue("file_uri", uri);
                          clearErrors("file_uri");
                          event.target.value = "";
                        }}
                        multiple
                      />
                    </Button>
                    <Button
                      variant="outlined"
                      size="small"
                      color="error"
                      onClick={() => {
                        setValue("file_uri", "");
                      }}
                    >
                      Hapus
                    </Button>
                  </Stack>
                </Stack>
              </Stack>
            </Stack>
            <Divider />
            <Stack sx={{ gap: 2, px: 2, flexDirection: "row" }}>
              <Button variant="outlined" fullWidth onClick={toggle}>
                Batal
              </Button>
              <Button
                type="submit"
                variant="contained"
                fullWidth
                color="primary"
              >
                Simpan
              </Button>
            </Stack>
          </Paper>
        </Stack>
      </Modal>
    </>
  );
}
