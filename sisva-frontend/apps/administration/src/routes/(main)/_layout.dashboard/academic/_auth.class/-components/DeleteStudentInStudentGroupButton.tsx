import { DeleteForever } from "@mui/icons-material";
import {
  Button,
  Divider,
  Modal,
  Paper,
  Stack,
  Typography,
} from "@mui/material";
import {
  useDeleteStudentInStudentGroup,
  useStudentGroup,
} from "@sisva/hooks/query/academic/useStudentGroups";
import { useUser } from "@sisva/hooks/query/user/useUsers";
import { useToggle } from "ahooks";
import type { ReactNode } from "react";
import toast from "react-hot-toast";

export default function DeleteStudentInStudenrGroupButton({
  student_group_id,
  student_id,
  renderTrigger,
}: {
  student_group_id: number;
  student_id: string;
  renderTrigger?: (onClick: () => void) => ReactNode;
}) {
  const { data: studentGroup } = useStudentGroup(student_group_id);
  const { data: student } = useUser(student_id);

  const [visible, { toggle }] = useToggle(false);

  const { mutate: deleteStudentInStudentGroup } =
    useDeleteStudentInStudentGroup({
      onSuccess: () => {
        toggle();
        toast.success("Siswa berhasil dihapus dari kelas");
      },
      onError: () => {
        toast.error("Siswa gagal dihapus dari kelas");
      },
    });

  return (
    <>
      {renderTrigger ? (
        renderTrigger(toggle)
      ) : (
        <Button
          variant="contained"
          startIcon={<DeleteForever />}
          color="error"
          onClick={toggle}
        >
          Hapus
        </Button>
      )}
      <Modal open={visible} onClose={toggle}>
        <Stack
          sx={{
            width: 1,
            height: 1,
            justifyContent: "center",
            alignItems: "center",
            p: 2,
          }}
        >
          <Paper
            component="form"
            sx={{
              py: 2,
              width: 1,
              maxWidth: "ms",
              gap: 2,
              display: "flex",
              flexDirection: "column",
            }}
            onSubmit={(e) => {
              e.preventDefault();
              deleteStudentInStudentGroup({
                student_group_id,
                student_id,
              });
            }}
          >
            <Typography sx={{ fontWeight: 600, px: 2 }}>
              Hapus Murid dari Kelas
            </Typography>
            <Divider />
            <Stack sx={{ gap: 2, px: 2 }}>
              <Stack
                sx={{ flexDirection: "row", justifyContent: "space-between" }}
              >
                <Typography sx={{ fontWeight: 600 }}>Nama Kelas</Typography>
                <Typography>{studentGroup?.name}</Typography>
              </Stack>
              <Stack
                sx={{ flexDirection: "row", justifyContent: "space-between" }}
              >
                <Typography sx={{ fontWeight: 600 }}>Nama Siswa</Typography>
                <Typography>{student?.name}</Typography>
              </Stack>
              <Stack
                sx={{ flexDirection: "row", justifyContent: "space-between" }}
              >
                <Typography sx={{ fontWeight: 600 }}>Tingkatan</Typography>
                <Typography>{studentGroup?.grade}</Typography>
              </Stack>
            </Stack>
            <Divider />
            <Stack sx={{ gap: 2, px: 2, flexDirection: "row" }}>
              <Button variant="outlined" fullWidth onClick={toggle}>
                Batal
              </Button>
              <Button type="submit" variant="contained" fullWidth color="error">
                Hapus
              </Button>
            </Stack>
          </Paper>
        </Stack>
      </Modal>
    </>
  );
}
