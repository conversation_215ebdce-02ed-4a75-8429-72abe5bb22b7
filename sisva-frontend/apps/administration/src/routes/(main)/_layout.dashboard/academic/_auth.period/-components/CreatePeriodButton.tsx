import { yupResolver } from "@hookform/resolvers/yup";
import { Add } from "@mui/icons-material";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Divider,
  Modal,
  Paper,
  Stack,
  Typography,
} from "@mui/material";
import {
  useCreatePeriod,
  usePeriod,
  useUpdatePeriod,
} from "@sisva/hooks/query/academic/usePeriods";
import { periodStatusOptions } from "@sisva/types/dropdownOptions";
import type { PeriodStatus } from "@sisva/types/types";
import { useToggle } from "ahooks";
import axios from "axios";
import dayjs from "dayjs";
import { type ReactNode } from "react";
import {
  PasswordElement,
  SelectElement,
  TextFieldElement,
  useForm,
} from "react-hook-form-mui";
import { DatePickerElement } from "react-hook-form-mui/date-pickers";
import toast from "react-hot-toast";
import { object, string } from "yup";

/**
 * this component has 2 modes:
 * 1. create new period, no props
 * 2. edit period, by passing period_id
 */
export default function CreatePeriodButton({
  // if period_id provided, then it's edit mode
  period_id,
  renderTrigger,
}: {
  period_id?: number;
  renderTrigger?: (onClick: () => void) => ReactNode;
}) {
  const [visible, { toggle }] = useToggle(false);

  const { data: period } = usePeriod(period_id);

  const { control, handleSubmit, reset, watch, setValue } = useForm({
    values: {
      name: period?.name ?? "",
      start_time: period?.start_time ?? "",
      end_time: period?.end_time ?? "",
      status: period?.status ?? "",
    },
    resolver: yupResolver(
      object({
        name: string().required("Nama periode wajib diisi"),
        start_time: string().required("Waktu mulai wajib diisi"),
        end_time: string().required("Waktu selesai wajib diisi"),
        status: period_id
          ? string().required("Status wajib dipilih")
          : string().nullable(),
        password: period_id
          ? string().required("Password wajib diisi")
          : string().nullable(),
      }).test(
        "end-time-has-been-passed-if-status-is-finished",
        (value, { createError }) => {
          const status = value.status as PeriodStatus;
          if (
            status === "finished" &&
            dayjs(value.end_time, "DD/MM/YYYY h:mm A Z").isAfter(dayjs())
          ) {
            return createError({
              path: "status",
              message:
                "Tidak dapat mengakhiri periode jika Waktu Selesai belum terlewati",
            });
          }
          return true;
        }
      )
    ),
  });

  const { mutate: createPeriod } = useCreatePeriod({
    onSuccess: () => {
      toggle();
      reset();
      toast.success("Periode berhasil dibuat");
    },
    onError: () => {
      toast.error("Periode gagal dibuat");
    },
  });

  const { mutate: updatePeriod } = useUpdatePeriod({
    onSuccess: () => {
      toggle();
      reset();
      toast.success("Periode berhasil diperbarui");
    },
    onError: (error) => {
      if (axios.isAxiosError(error)) {
        try {
          if (error.response?.data?.errors.includes("INVALID_PASSWORD")) {
            return toast.error("Password salah");
          }
        } catch (error) {}
      }
      toast.error("Periode gagal diperbarui");
    },
  });

  const periodInactive = period?.status === "inactive";
  const periodActive = period?.status === "active";
  const periodFinished = period?.status === "finished";
  const status = watch("status") as PeriodStatus;

  return (
    <>
      {renderTrigger ? (
        renderTrigger(toggle)
      ) : (
        <Button variant="contained" startIcon={<Add />} onClick={toggle}>
          Tambah
        </Button>
      )}
      <Modal open={visible} onClose={toggle}>
        <Stack
          sx={{
            width: 1,
            height: 1,
            justifyContent: "center",
            alignItems: "center",
            p: 2,
          }}
        >
          <Paper
            component={"form"}
            sx={{
              py: 2,
              width: 1,
              maxWidth: "ms",
              gap: 2,
              display: "flex",
              flexDirection: "column",
            }}
            onSubmit={handleSubmit((value) => {
              if (period_id) {
                updatePeriod({
                  period_id,
                  name: value.name,
                  start_time: value.start_time,
                  end_time: value.end_time,
                  status: value.status! as PeriodStatus,
                  password: value.password!,
                });
              } else {
                createPeriod(value);
              }
            })}
          >
            <Typography sx={{ fontWeight: 600, px: 2 }}>
              {(() => {
                if (period_id) {
                  return "Edit Periode";
                } else {
                  return "Buat Periode";
                }
              })()}
            </Typography>
            <Divider />
            <Stack sx={{ gap: 2, px: 2 }}>
              {periodFinished && (
                <Alert severity="warning">
                  Tidak dapat mengubah Periode yang sudah selesai
                </Alert>
              )}
              <TextFieldElement
                name="name"
                label="Nama Periode"
                control={control}
                slotProps={{
                  input: {
                    readOnly: periodFinished,
                  },
                }}
              />
              <DatePickerElement
                views={["month", "year"]}
                transform={{
                  input(dateString) {
                    if (!dateString) return null;
                    return dayjs(dateString, "DD/MM/YYYY h:mm A Z");
                  },
                  output(date) {
                    return (
                      date?.startOf("month").format("DD/MM/YYYY h:mm A Z") ?? ""
                    );
                  },
                }}
                label="Waktu Mulai"
                control={control}
                name="start_time"
                format="DD MMMM YYYY"
                readOnly={periodFinished || periodActive}
              />
              <DatePickerElement
                views={["month", "year"]}
                transform={{
                  input(dateString) {
                    if (!dateString) return null;
                    return dayjs(dateString, "DD/MM/YYYY h:mm A Z");
                  },
                  output(date) {
                    return (
                      date?.endOf("month").format("DD/MM/YYYY h:mm A Z") ?? ""
                    );
                  },
                }}
                label="Waktu Selesai"
                control={control}
                name="end_time"
                format="DD MMMM YYYY"
                readOnly={periodFinished}
              />
              {period_id && (
                <SelectElement
                  control={control}
                  name="status"
                  options={periodStatusOptions
                    .filter((item) => {
                      // don't show finished option if period inactive
                      if (periodInactive) return item.value !== "finished";
                      // don't show inactive option if period active
                      if (periodActive) return item.value !== "inactive";
                      return true;
                    })
                    .map((option) => ({
                      id: option.value,
                      label: option.label,
                    }))}
                  label="Status"
                  slotProps={{
                    input: {
                      readOnly: periodFinished,
                    },
                  }}
                />
              )}
              {period_id && !periodFinished && (
                <PasswordElement
                  control={control}
                  name="password"
                  label="Password"
                  slotProps={{
                    input: {
                      readOnly: periodFinished,
                    },
                  }}
                />
              )}
            </Stack>
            <Divider />
            <Stack sx={{ gap: 2, px: 2, flexDirection: "row" }}>
              <Button variant="outlined" fullWidth onClick={toggle}>
                Batal
              </Button>
              <Button
                type="submit"
                variant="contained"
                fullWidth
                color={status === "finished" ? "error" : "primary"}
                disabled={periodFinished}
              >
                {status === "finished" ? "Akhiri Periode" : "Simpan"}
              </Button>
            </Stack>
          </Paper>
        </Stack>
      </Modal>
    </>
  );
}
