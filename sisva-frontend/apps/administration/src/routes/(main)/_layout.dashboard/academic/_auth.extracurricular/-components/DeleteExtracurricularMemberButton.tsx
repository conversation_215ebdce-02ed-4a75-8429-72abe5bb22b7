import { DeleteForever } from "@mui/icons-material";
import {
  Button,
  Divider,
  Modal,
  Paper,
  Stack,
  Typography,
} from "@mui/material";
import {
  useDeleteExtracurricularMember,
  useExtracurricularMembers,
} from "@sisva/hooks/query/academic/useExtracurricularMembers";
import { useToggle } from "ahooks";
import type { ReactNode } from "react";
import toast from "react-hot-toast";

export default function DeleteExtracurricularMemberButton({
  extracurricular_id,
  student_id,
  renderTrigger,
}: {
  extracurricular_id: number;
  student_id: string;
  renderTrigger?: (onClick: () => void) => ReactNode;
}) {
  const { data: extracurricularMembers = [] } = useExtracurricularMembers();

  const [visible, { toggle }] = useToggle(false);

  const { mutate: deleteExtracurricularMember } =
    useDeleteExtracurricularMember({
      onSuccess: () => {
        toggle();
        toast.success("Anggota berhasil dihapus");
      },
      onError: () => {
        toast.error("Anggota gagal dihapus");
      },
    });

  return (
    <>
      {renderTrigger ? (
        renderTrigger(toggle)
      ) : (
        <Button
          variant="contained"
          startIcon={<DeleteForever />}
          color="error"
          onClick={toggle}
        >
          Hapus
        </Button>
      )}
      <Modal open={visible} onClose={toggle}>
        <Stack
          sx={{
            width: 1,
            height: 1,
            justifyContent: "center",
            alignItems: "center",
            p: 2,
          }}
        >
          <Paper
            component="form"
            sx={{
              py: 2,
              width: 1,
              maxWidth: "ms",
              gap: 2,
              display: "flex",
              flexDirection: "column",
            }}
            onSubmit={(e) => {
              e.preventDefault();
              deleteExtracurricularMember({
                extracurricular_id: extracurricular_id,
                student_id: student_id,
              });
            }}
          >
            <Typography sx={{ fontWeight: 600, px: 2 }}>
              Hapus Anggota
            </Typography>
            <Divider />
            <Stack sx={{ gap: 2, px: 2 }}>
              <Stack
                sx={{ flexDirection: "row", justifyContent: "space-between" }}
              >
                <Typography sx={{ fontWeight: 600 }}>Ektrakurikuler</Typography>
                <Typography>
                  {
                    extracurricularMembers.find((item) => {
                      return (
                        item.extracurricular_id === extracurricular_id &&
                        item.student_id === student_id
                      );
                    })?.extracurricular_name
                  }
                </Typography>
              </Stack>
              <Stack
                sx={{ flexDirection: "row", justifyContent: "space-between" }}
              >
                <Typography sx={{ fontWeight: 600 }}>Anggota</Typography>
                <Typography>
                  {
                    extracurricularMembers.find((item) => {
                      return (
                        item.extracurricular_id === extracurricular_id &&
                        item.student_id === student_id
                      );
                    })?.student_name
                  }
                </Typography>
              </Stack>
            </Stack>
            <Divider />
            <Stack sx={{ gap: 2, px: 2, flexDirection: "row" }}>
              <Button variant="outlined" fullWidth onClick={toggle}>
                Batal
              </Button>
              <Button type="submit" variant="contained" fullWidth color="error">
                Hapus
              </Button>
            </Stack>
          </Paper>
        </Stack>
      </Modal>
    </>
  );
}
