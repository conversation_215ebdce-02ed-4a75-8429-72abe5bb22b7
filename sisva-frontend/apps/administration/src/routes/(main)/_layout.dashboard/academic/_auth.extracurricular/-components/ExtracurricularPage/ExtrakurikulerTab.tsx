import { Clear, Search, Sort } from "@mui/icons-material";
import {
  <PERSON>ton,
  <PERSON>alog,
  DialogActions,
  DialogContent,
  DialogTitle,
  IconButton,
  InputAdornment,
  Stack,
  Typography,
  useMediaQuery,
} from "@mui/material";
import type { GridColDef } from "@mui/x-data-grid";
import { DataGrid, useGridApiContext } from "@mui/x-data-grid";
import { useExtracurricularMembers } from "@sisva/hooks/query/academic/useExtracurricularMembers";
import { useExtracurriculars } from "@sisva/hooks/query/academic/useExtracurriculars";
import type { Extracurricular } from "@sisva/types/apiTypes";
import { useToggle } from "ahooks";
import { useEffect } from "react";
import { useForm } from "react-hook-form";
import { SelectElement, TextFieldElement } from "react-hook-form-mui";

import DeleteIcon from "#/routes/(main)/-components/DeleteIcon";
import EditIcon from "#/routes/(main)/-components/EditIcon";

import CreateExtracurricularButton from "../CreateExtracurricularButton";
import DeleteExtracurricularButton from "../DeleteExtracurricularButton";

type Field =
  | "mobile_column"
  | keyof Extracurricular
  | "member_count"
  | "action";
type TypeSafeColDef = GridColDef & { field: Field };

export default function ExtrakurikulerTab() {
  const isMobile = useMediaQuery((theme) => theme.breakpoints.down("lg"));
  const { data: extracurriculars = [] } = useExtracurriculars();
  const { data: extracurricularMembers = [] } = useExtracurricularMembers();

  const columns: TypeSafeColDef[] = [
    {
      field: "mobile_column",
      headerName: "",
      valueGetter: (_, row) => row,
      flex: 1,
      display: "flex",
      renderCell: (args) => {
        const value = args.value as Extracurricular;
        return (
          <Stack sx={{ p: 1.5, gap: 2, width: "100%" }}>
            <Stack
              sx={{
                flexDirection: "row",
                justifyContent: "space-between",
                width: "100%",
              }}
            >
              <Typography sx={{ fontWeight: 600 }}>Ekstrakurikuler</Typography>
              <Typography>{value.name}</Typography>
            </Stack>
            <Stack
              sx={{
                flexDirection: "row",
                justifyContent: "space-between",
                width: "100%",
              }}
            >
              <Typography sx={{ fontWeight: 600 }}>Pembina</Typography>
              <Typography>{value.teacher_name}</Typography>
            </Stack>
            <Stack
              sx={{
                flexDirection: "row",
                justifyContent: "space-between",
                width: "100%",
              }}
            >
              <Typography sx={{ fontWeight: 600 }}>Jumlah Anggota</Typography>
              <Typography>
                {extracurricularMembers.reduce((acc, curr) => {
                  return curr.extracurricular_id === value.id ? acc + 1 : acc;
                }, 0)}
              </Typography>
            </Stack>
            <Stack sx={{ flexDirection: "row", gap: 1, justifyContent: "end" }}>
              <CreateExtracurricularButton
                extracurricular_id={value.id}
                renderTrigger={(onClick) => <EditIcon onClick={onClick} />}
              />
              <DeleteExtracurricularButton
                extracurricular_id={value.id}
                renderTrigger={(onClick) => {
                  return <DeleteIcon onClick={onClick} />;
                }}
              />
            </Stack>
          </Stack>
        );
      },
    },
    {
      field: "name",
      headerName: "Ekstrakurikuler",
      display: "flex",
      flex: 1,
      renderCell: ({ value }) => <Stack sx={{ py: 1.5 }}>{value}</Stack>,
    },
    {
      field: "teacher_name",
      headerName: "Pembina",
      display: "flex",
      flex: 1,
      renderCell: ({ value }) => <Stack sx={{ py: 1.5 }}>{value}</Stack>,
    },
    {
      field: "member_count",
      headerName: "Jumlah Anggota",
      width: 150,
      display: "flex",
      valueGetter: (_, row: Extracurricular) =>
        extracurricularMembers.reduce((acc, curr) => {
          return curr.extracurricular_id === row.id ? acc + 1 : acc;
        }, 0),
      renderCell: ({ value }) => {
        return (
          <Stack sx={{ py: 1.5, flexDirection: "row", gap: 0.5 }}>
            {value}
          </Stack>
        );
      },
    },
    {
      field: "action",
      headerName: "Aksi",
      width: 128,
      display: "flex",
      renderCell: ({ row }: { row: Extracurricular }) => (
        <Stack sx={{ flexDirection: "row", gap: 1, py: 1.5 }}>
          <CreateExtracurricularButton
            extracurricular_id={row.id}
            renderTrigger={(onClick) => {
              return <EditIcon onClick={onClick} />;
            }}
          />
          <DeleteExtracurricularButton
            extracurricular_id={row.id}
            renderTrigger={(onClick) => {
              return <DeleteIcon onClick={onClick} />;
            }}
          />
        </Stack>
      ),
      sortable: false,
    },
  ];

  const columnVisibilityModel = Object.fromEntries(
    columns
      .map((column) => column.field)
      .filter((field) =>
        isMobile ? field !== "mobile_column" : field === "mobile_column"
      )
      .map((field) => [field, false])
  );

  return (
    <DataGrid
      sx={{
        "& .MuiDataGrid-cell:focus": {
          outline: " none",
        },
        border: "none",
      }}
      disableRowSelectionOnClick
      className="bg-white grow"
      rows={extracurriculars}
      columns={columns}
      initialState={{ pagination: { paginationModel: { pageSize: 20 } } }}
      pageSizeOptions={[10, 20, 50]}
      getRowClassName={(params) =>
        params.indexRelativeToCurrentPage % 2 === 0
          ? "bg-neutral-50"
          : "bg-neutral-100"
      }
      slots={{
        toolbar: CustomToolbar,
      }}
      getRowHeight={() => "auto"}
      columnVisibilityModel={columnVisibilityModel}
      columnHeaderHeight={isMobile ? 0 : undefined}
      disableColumnMenu
    />
  );
}

function CustomToolbar() {
  const api = useGridApiContext();
  const [open, { toggle }] = useToggle(false);
  const { control, handleSubmit, reset, setValue, watch } = useForm<{
    field: Field | "";
    sortDirection: "asc" | "desc";
    quickFilter: string;
  }>({
    defaultValues: {
      field: "",
      sortDirection: "asc",
      quickFilter: "",
    },
  });

  const sortSelectOptions: { label: string; id: Field }[] = [
    { label: "Ekstrakurikuler", id: "name" },
    { label: "Pembina", id: "teacher_name" },
    { label: "Jumlah Anggota", id: "member_count" },
  ];

  const quickFilter = watch("quickFilter");
  useEffect(() => {
    api.current.setQuickFilterValues(
      quickFilter.split(" ").filter((s) => s !== "")
    );
  }, [api, quickFilter]);

  return (
    <div className="p-4 flex flex-col gap-2 border-b border-0 border-solid border-neutral-200">
      <div className=" flex justify-between ">
        <TextFieldElement
          control={control}
          name="quickFilter"
          placeholder="Cari"
          size="small"
          slotProps={{
            input: {
              endAdornment: (
                <InputAdornment position="end">
                  {quickFilter ? (
                    <IconButton
                      size="small"
                      onClick={() => setValue("quickFilter", "")}
                    >
                      <Clear />
                    </IconButton>
                  ) : (
                    <Search />
                  )}
                </InputAdornment>
              ),
            },
          }}
        />
        <CreateExtracurricularButton />
      </div>
      <Button
        startIcon={<Sort />}
        variant="outlined"
        onClick={toggle}
        sx={{
          display: { xs: "flex", lg: "none" },
        }}
      >
        Urutkan Data
      </Button>
      <Dialog open={open} onClose={toggle}>
        <DialogTitle>Urutkan</DialogTitle>
        <DialogContent
          sx={{
            width: "250px",
            display: "flex",
            flexDirection: "column",
            gap: 2,
            overflow: "initial",
          }}
        >
          <SelectElement
            control={control}
            name="field"
            options={sortSelectOptions}
            label="Pilih Kolom"
            slotProps={{
              input: {
                endAdornment: watch("field") && (
                  <InputAdornment position="end">
                    <IconButton onClick={() => setValue("field", "")}>
                      <Clear />
                    </IconButton>
                  </InputAdornment>
                ),
              },
            }}
          />
          <SelectElement
            control={control}
            name="sortDirection"
            options={[
              { label: "A-Z", id: "asc" },
              { label: "Z-A", id: "desc" },
            ]}
            label="Urutan"
          />
        </DialogContent>
        <DialogActions sx={{ pb: 2, px: 3 }}>
          <Button
            variant="outlined"
            onClick={() => {
              reset();
              toggle();
            }}
          >
            Batal
          </Button>
          <Button
            variant="contained"
            onClick={handleSubmit((value) => {
              if (value.field) {
                api.current.sortColumn(value.field, value.sortDirection);
              } else {
                api.current.sortColumn("name", null);
              }
              toggle();
            })}
          >
            Simpan
          </Button>
        </DialogActions>
      </Dialog>
    </div>
  );
}
