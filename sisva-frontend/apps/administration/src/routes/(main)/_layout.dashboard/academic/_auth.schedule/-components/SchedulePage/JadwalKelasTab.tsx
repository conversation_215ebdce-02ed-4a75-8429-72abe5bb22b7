import { <PERSON><PERSON>, Divider } from "@mui/material";
import {
  usePeriod,
  usePeriods,
  useSelectedPeriod,
} from "@sisva/hooks/query/academic/usePeriods";
import {
  useStudentGroup,
  useStudentGroups,
} from "@sisva/hooks/query/academic/useStudentGroups";
import {
  useStudyProgram,
  useStudyPrograms,
} from "@sisva/hooks/query/academic/useStudyPrograms";
import { SisvaScheduleComponent } from "@sisva/ui";
import { useEffect } from "react";
import { AutocompleteElement, useForm } from "react-hook-form-mui";

export default function JadwalKelasTab() {
  const { data: periods = [] } = usePeriods();
  const { data: selectedPeriod } = useSelectedPeriod();
  const { data: studyPrograms = [] } = useStudyPrograms();

  const { control, watch, setValue } = useForm<{
    period_id: number | null;
    study_program_id: number | null;
    grade: string;
    student_group_id: number | null;
  }>({
    values: {
      period_id: selectedPeriod?.id ?? null,
      study_program_id: null,
      grade: "",
      student_group_id: null,
    },
  });

  const period_id = watch("period_id");
  const study_program_id = watch("study_program_id");
  const grade = watch("grade");
  const student_group_id = watch("student_group_id");
  const disabled = !period_id || !student_group_id;

  const { data: studentGroup } = useStudentGroup(student_group_id);

  const { data: period } = usePeriod(period_id);
  const studyProgramIds = [
    ...new Set(period?.study_programs?.map((item) => item.id) ?? []),
  ];
  const fitleredStudyPrograms = studyPrograms.filter((item) =>
    studyProgramIds.includes(item.id)
  );

  const { data: studyProgram } = useStudyProgram(study_program_id);
  const grades = studyProgram?.grades ?? [];

  const { data: studentGroups = [] } = useStudentGroups();
  const filteredStudentGroupsOptions = studentGroups.filter(
    (item) =>
      (!period_id || item.period_id === period_id) &&
      (!study_program_id || item.study_program_id === study_program_id) &&
      (!grade || item.grade === grade)
  );

  const filteredStudentGroups = filteredStudentGroupsOptions.filter(
    (item) => !student_group_id || item.id === student_group_id
  );

  // set student_group_id and grade when studentGroup changed
  useEffect(() => {
    if (studentGroup) {
      setValue("study_program_id", studentGroup.study_program_id);
      setValue("grade", studentGroup.grade);
    }
  }, [setValue, studentGroup]);

  return (
    <>
      <div className="flex gap-4 p-4 border-b border-0 border-solid border-neutral-200 justify-between thick-scrollbar items-center">
        <div className="flex gap-2 overflow-auto py-2">
          <AutocompleteElement
            control={control}
            name="period_id"
            matchId
            options={periods.map((item) => ({ id: item.id, label: item.name }))}
            label="Periode"
            autocompleteProps={{
              size: "small",
              sx: { minWidth: "180px" },
              onChange() {
                setValue("study_program_id", null);
                setValue("grade", "");
                setValue("student_group_id", null);
              },
            }}
          />
          <AutocompleteElement
            control={control}
            name="study_program_id"
            matchId
            options={fitleredStudyPrograms.map((item) => ({
              id: item.id,
              label: item.name,
            }))}
            label="Program Studi"
            autocompleteProps={{
              disabled: !period_id,
              size: "small",
              sx: { minWidth: "180px" },
              onChange() {
                setValue("grade", "");
                setValue("student_group_id", null);
              },
            }}
          />
          <AutocompleteElement
            control={control}
            name="grade"
            matchId
            options={grades.map((item) => ({
              id: item,
              label: item,
            }))}
            label="Tingkatan"
            autocompleteProps={{
              disabled: !study_program_id,
              size: "small",
              sx: { minWidth: "120px" },
              onChange() {
                setValue("student_group_id", null);
              },
            }}
          />
          <AutocompleteElement
            control={control}
            name="student_group_id"
            matchId
            options={filteredStudentGroupsOptions.map((item) => ({
              id: item.id,
              label: item.name,
            }))}
            label="Kelas"
            autocompleteProps={{
              disabled: !period_id,
              size: "small",
              sx: { minWidth: "180px" },
            }}
          />
        </div>
        <div className="flex gap-4">{/* buttons */}</div>
      </div>
      {disabled && (
        <div className="p-4 border-b border-0 border-solid border-neutral-200 ">
          <Alert severity="warning">
            Lengkapi filter <span className="font-medium">Periode</span> dan{" "}
            <span className="font-medium">Kelas</span> terlebih dahulu
          </Alert>
        </div>
      )}
      <div className="overflow-auto p-4 gap-4 flex flex-col flex-1">
        {(() => {
          if (disabled) return null;
          if (filteredStudentGroups.length === 0) {
            return (
              <div className="size-full flex flex-col justify-center items-center">
                Kelas belum tersedia
              </div>
            );
          }

          return filteredStudentGroups.map((studentGroup) => (
            <div
              key={studentGroup.id}
              className="flex flex-col gap-2 w-[1600px] flex-1"
            >
              <div className="font-bold text-lg border-[0] border-b border-solid border-neutral-300">
                {studentGroup.name}
              </div>
              <SisvaScheduleComponent student_group_id={studentGroup.id} />
              <Divider className="py-4" />
            </div>
          ));
        })()}
      </div>
    </>
  );
}
