import { Alert } from "@mui/material";
import { useClassSchedules } from "@sisva/hooks/query/academic/useClassSchedules";
import {
  usePeriods,
  useSelectedPeriod,
} from "@sisva/hooks/query/academic/usePeriods";
import { useTeachers } from "@sisva/hooks/query/user/useTeachers";
import { SisvaScheduleComponent } from "@sisva/ui";
import { useEffect } from "react";
import { AutocompleteElement, useForm } from "react-hook-form-mui";

export default function JadwalGuruTab() {
  const { data: periods = [] } = usePeriods();
  const { data: selectedPeriod } = useSelectedPeriod();

  const { control, watch, setValue } = useForm<{
    period_id: number | null;
    teacher_id: string | null;
  }>({
    values: {
      period_id: selectedPeriod?.id ?? null,
      teacher_id: null,
    },
  });

  const period_id = watch("period_id");
  const teacher_id = watch("teacher_id");
  const disabled = !period_id || !teacher_id;

  const { data: classSchedules = [] } = useClassSchedules(period_id);
  const teacher_ids = [
    ...new Set(classSchedules.map((item) => item.teacher_id)),
  ];
  const { data: teachers = [] } = useTeachers();
  const fitleredTeachers = teachers.filter((item) =>
    teacher_ids.includes(item.id)
  );

  //  reset teacher_id when period_id changed
  useEffect(() => {
    setValue("teacher_id", "");
  }, [setValue, period_id]);

  return (
    <>
      <div className="flex gap-4 p-4 border-b border-0 border-solid border-neutral-200 justify-between thick-scrollbar items-center">
        <div className="flex gap-2 overflow-auto py-2">
          <AutocompleteElement
            control={control}
            name="period_id"
            matchId
            options={periods.map((item) => ({ id: item.id, label: item.name }))}
            label="Periode"
            autocompleteProps={{
              size: "small",
              sx: { minWidth: "180px" },
            }}
          />
          <AutocompleteElement
            control={control}
            name="teacher_id"
            matchId
            options={fitleredTeachers.map((item) => ({
              id: item.id,
              label: item.name,
            }))}
            label="Guru"
            autocompleteProps={{
              size: "small",
              sx: { minWidth: "200px" },
            }}
          />
        </div>
        <div className="flex gap-4">{/* buttons */}</div>
      </div>
      {disabled && (
        <div className="p-4 border-b border-0 border-solid border-neutral-200 ">
          <Alert severity="warning">
            Lengkapi filter <span className="font-medium">Periode</span> dan{" "}
            <span className="font-medium">Guru</span> terlebih dahulu
          </Alert>
        </div>
      )}
      <div className="overflow-auto p-4 gap-4 flex flex-col flex-1">
        {(() => {
          if (disabled) return null;
          return (
            <div className="flex flex-col gap-2 w-[1600px] flex-1">
              <SisvaScheduleComponent
                user_id={teacher_id ?? undefined}
                period_id={period_id}
              />
            </div>
          );
        })()}
      </div>
    </>
  );
}
