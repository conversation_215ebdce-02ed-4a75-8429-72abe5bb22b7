import { Clear, Search, Sort } from "@mui/icons-material";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  DialogActions,
  Dialog<PERSON>ontent,
  DialogTitle,
  IconButton,
  InputAdornment,
  Stack,
  Typography,
  useMediaQuery,
} from "@mui/material";
import type { GridColDef, GridFilterOperator } from "@mui/x-data-grid";
import { DataGrid, useGridApiContext } from "@mui/x-data-grid";
import { usePeriodCurriculums } from "@sisva/hooks/query/academic/usePeriods";
import type { PeriodCurriculum } from "@sisva/types/apiTypes";
import { onlyUnique } from "@sisva/utils";
import { useToggle } from "ahooks";
import { sort } from "fast-sort";
import { useEffect } from "react";
import { useForm } from "react-hook-form";
import {
  AutocompleteElement,
  SelectElement,
  TextFieldElement,
} from "react-hook-form-mui";

import DeleteIcon from "#/routes/(main)/-components/DeleteIcon";
import EditIcon from "#/routes/(main)/-components/EditIcon";

import CreatePeriodCurriculumButton from "../CreatePeriodCurriculumButton";
import DeletePeriodCurriculumButton from "../DeletePeriodCurriculumButton";

type Field =
  | "mobile_column"
  | keyof PeriodCurriculum
  | typeof filter_field
  | "action";
type TypeSafeColDef = GridColDef & { field: Field };

const filter_field = "filter_field";
const field = {
  filter1: "period_name",
} satisfies Record<string, Field>;

type FilterValue = Record<(typeof field)[keyof typeof field], string>;

export default function KurikulumTab() {
  const isMobile = useMediaQuery((theme) => theme.breakpoints.down("lg"));
  const { data: periodCurriculums = [] } = usePeriodCurriculums();

  const customOperator: GridFilterOperator<PeriodCurriculum> = {
    label: "Custom Operator",
    value: "customOperator",
    getApplyFilterFn: (filterItem, column) => {
      if (!filterItem.field || !filterItem.value || !filterItem.operator) {
        return null;
      }
      const filterValue = filterItem.value as FilterValue;

      return (value, row, column, apiRef) => {
        let pass = true;
        if (filterValue.period_name) {
          if (row.period_name !== filterValue.period_name) pass = false;
        }
        return pass;
      };
    },
  };

  const columns: TypeSafeColDef[] = [
    {
      field: "mobile_column",
      headerName: "",
      valueGetter: (_, row) => row,
      flex: 1,
      display: "flex",
      renderCell: (args) => {
        const value = args.value as PeriodCurriculum;
        return (
          <Stack sx={{ p: 1.5, gap: 1, width: "100%" }}>
            <Stack
              sx={{
                flexDirection: "row",
                justifyContent: "space-between",
                width: "100%",
              }}
            >
              <Typography sx={{ fontWeight: 600 }}>Kurikulum</Typography>
              <Typography>{value.curriculum_name}</Typography>
            </Stack>
            <Stack
              sx={{
                flexDirection: "row",
                justifyContent: "space-between",
                width: "100%",
              }}
            >
              <Typography sx={{ fontWeight: 600 }}>Periode</Typography>
              <Typography>{value.period_name}</Typography>
            </Stack>
            <Stack
              sx={{
                flexDirection: "row",
                justifyContent: "space-between",
                width: "100%",
              }}
            >
              <Typography sx={{ fontWeight: 600 }}>Program Studi</Typography>
              <Typography>{value?.study_program_name}</Typography>
            </Stack>
            <Stack
              sx={{
                flexDirection: "row",
                justifyContent: "space-between",
                width: "100%",
              }}
            >
              <Typography sx={{ fontWeight: 600 }}>Tingkatan</Typography>
              <Typography>{value.grade}</Typography>
            </Stack>
            <Stack
              sx={{
                flexDirection: "row",
                justifyContent: "flex-end",
                gap: 1,
                width: "100%",
              }}
            >
              <CreatePeriodCurriculumButton
                periodCurriculumId={{
                  curriculum_id: value.curriculum_id,
                  period_id: value.period_id,
                  study_program_id: value.study_program_id,
                  grade: value.grade,
                }}
                renderTrigger={(onClick) => <EditIcon onClick={onClick} />}
              />
              <DeletePeriodCurriculumButton
                periodCurriculumId={{
                  curriculum_id: value.curriculum_id,
                  period_id: value.period_id,
                  study_program_id: value.study_program_id,
                  grade: value.grade,
                }}
                renderTrigger={(onClick) => <DeleteIcon onClick={onClick} />}
              />
            </Stack>
          </Stack>
        );
      },
    },
    {
      field: "curriculum_name",
      headerName: "Kurikulum",
      display: "flex",
      flex: 1,
      renderCell: ({ value }) => <Stack sx={{ py: 1.3 }}>{value}</Stack>,
    },
    {
      field: "period_name",
      headerName: "Periode",
      display: "flex",
      flex: 1,
      renderCell: ({ value }) => <Stack sx={{ py: 1.3 }}>{value}</Stack>,
    },
    {
      field: "study_program_name",
      headerName: "Program Studi",
      display: "flex",
      flex: 1,
      renderCell: ({ value }) => <Stack sx={{ py: 1.3 }}>{value}</Stack>,
    },
    {
      field: "grade",
      headerName: "Tingkatan",
      display: "flex",
      flex: 1,
      renderCell: ({ value }) => <Stack sx={{ py: 1.3 }}>{value}</Stack>,
    },

    {
      field: "action",
      headerName: "Aksi",
      display: "flex",
      sortable: false,
      renderCell: ({ row }: { row: PeriodCurriculum }) => (
        <Stack
          sx={{ py: 1.3, flexDirection: "row", alignItems: "center", gap: 1 }}
        >
          <CreatePeriodCurriculumButton
            periodCurriculumId={{
              curriculum_id: row.curriculum_id,
              period_id: row.period_id,
              study_program_id: row.study_program_id,
              grade: row.grade,
            }}
            renderTrigger={(onClick) => <EditIcon onClick={onClick} />}
          />
          <DeletePeriodCurriculumButton
            periodCurriculumId={{
              curriculum_id: row.curriculum_id,
              period_id: row.period_id,
              study_program_id: row.study_program_id,
              grade: row.grade,
            }}
            renderTrigger={(onClick) => <DeleteIcon onClick={onClick} />}
          />
        </Stack>
      ),
    },
    {
      field: filter_field,
      filterOperators: [customOperator],
    },
  ];

  const columnVisibilityModel = Object.fromEntries(
    columns
      .map((column) => column.field)
      .filter((field) =>
        isMobile ? field !== "mobile_column" : field === "mobile_column"
      )
      .map((field) => [field, false])
  );
  columnVisibilityModel[filter_field] = false;

  return (
    <DataGrid
      sx={{
        "& .MuiDataGrid-cell:focus": {
          outline: " none",
        },
        border: "none",
      }}
      getRowId={(row: PeriodCurriculum) =>
        `${row.period_id} - ${row.study_program_id} - ${row.curriculum_id} - ${row.grade}`
      }
      disableRowSelectionOnClick
      className="bg-white grow"
      rows={periodCurriculums}
      columns={columns}
      initialState={{ pagination: { paginationModel: { pageSize: 20 } } }}
      pageSizeOptions={[10, 20, 50]}
      getRowClassName={(params) =>
        params.indexRelativeToCurrentPage % 2 === 0
          ? "bg-neutral-50"
          : "bg-neutral-100"
      }
      slots={{
        toolbar: CustomToolbar,
      }}
      getRowHeight={() => "auto"}
      columnVisibilityModel={columnVisibilityModel}
      columnHeaderHeight={isMobile ? 0 : undefined}
      disableColumnMenu
      disableColumnFilter
    />
  );
}

function CustomToolbar() {
  const api = useGridApiContext();
  const [open, { toggle }] = useToggle(false);

  const { control, handleSubmit, reset, setValue, watch } = useForm<{
    field: Field | "";
    sortDirection: "asc" | "desc";
    quickFilter: string;
    [field.filter1]: string;
  }>({
    defaultValues: {
      field: "",
      sortDirection: "asc",
      quickFilter: "",
      [field.filter1]: "",
    },
  });

  const rows = Array.from(api.current.getRowModels()).map(
    ([key, value]) => value
  ) as PeriodCurriculum[];
  const periodNames = sort(rows)
    .desc((r) => r.period_id)
    .map((row) => row.period_name)
    .filter((item) => item)
    .filter(onlyUnique);

  // apply filter
  const filter1 = watch(field.filter1);
  useEffect(() => {
    const value: FilterValue = {
      [field.filter1]: filter1,
    };
    api.current.upsertFilterItem({
      field: filter_field,
      operator: "customOperator",
      value,
    });
  }, [api, filter1]);

  const sortSelectOptions: { label: string; id: Field }[] = [
    { label: "Periode", id: "period_name" },
    { label: "Program Studi", id: "study_program_name" },
    { label: "Tingkatan", id: "grade" },
    { label: "Kurikulum", id: "curriculum_name" },
  ];

  const quickFilter = watch("quickFilter");
  useEffect(() => {
    api.current.setQuickFilterValues(
      quickFilter.split(" ").filter((s) => s !== "")
    );
  }, [api, quickFilter]);

  return (
    <div className="p-4 flex flex-col gap-2 border-b border-0 border-solid border-neutral-200">
      <div className=" flex justify-between gap-4 items-center thick-scrollbar">
        <Stack
          sx={{
            overflow: {
              xs: "auto",
              lg: "initial",
            },
            py: {
              xs: 1,
              lg: 0,
            },
          }}
        >
          <Stack sx={{ flexDirection: "row", gap: 2 }}>
            <TextFieldElement
              control={control}
              name="quickFilter"
              placeholder="Cari"
              size="small"
              sx={{
                minWidth: 150,
              }}
              slotProps={{
                input: {
                  endAdornment: (
                    <InputAdornment position="end">
                      {quickFilter ? (
                        <IconButton
                          size="small"
                          onClick={() => setValue("quickFilter", "")}
                        >
                          <Clear />
                        </IconButton>
                      ) : (
                        <Search />
                      )}
                    </InputAdornment>
                  ),
                },
              }}
            />
            <AutocompleteElement
              control={control}
              name={field.filter1}
              matchId
              options={periodNames.map((item) => ({
                id: item,
                label: item,
              }))}
              label="Periode"
              autocompleteProps={{
                size: "small",
                sx: { minWidth: "180px" },
              }}
            />
          </Stack>
        </Stack>
        <Stack
          sx={{
            minWidth: 110,
          }}
        >
          <CreatePeriodCurriculumButton />
        </Stack>
      </div>
      <Button
        startIcon={<Sort />}
        variant="outlined"
        onClick={toggle}
        sx={{
          display: { xs: "flex", lg: "none" },
        }}
      >
        Urutkan Data
      </Button>
      <Dialog open={open} onClose={toggle}>
        <DialogTitle>Urutkan</DialogTitle>
        <DialogContent
          sx={{
            width: "250px",
            display: "flex",
            flexDirection: "column",
            gap: 2,
            overflow: "initial",
          }}
        >
          <SelectElement
            control={control}
            name="field"
            options={sortSelectOptions}
            label="Pilih Kolom"
            slotProps={{
              input: {
                endAdornment: watch("field") && (
                  <InputAdornment position="end">
                    <IconButton onClick={() => setValue("field", "")}>
                      <Clear />
                    </IconButton>
                  </InputAdornment>
                ),
              },
            }}
          />
          <SelectElement
            control={control}
            name="sortDirection"
            options={[
              { label: "A-Z", id: "asc" },
              { label: "Z-A", id: "desc" },
            ]}
            label="Urutan"
          />
        </DialogContent>
        <DialogActions sx={{ pb: 2, px: 3 }}>
          <Button
            variant="outlined"
            onClick={() => {
              reset();
              toggle();
            }}
          >
            Batal
          </Button>
          <Button
            variant="contained"
            onClick={handleSubmit((value) => {
              if (value.field) {
                api.current.sortColumn(value.field, value.sortDirection);
              } else {
                api.current.sortColumn("name", null);
              }
              toggle();
            })}
          >
            Simpan
          </Button>
        </DialogActions>
      </Dialog>
    </div>
  );
}
