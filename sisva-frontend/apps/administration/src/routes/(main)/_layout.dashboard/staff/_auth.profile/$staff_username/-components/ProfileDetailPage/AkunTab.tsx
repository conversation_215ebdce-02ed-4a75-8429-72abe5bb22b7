import { yup<PERSON>esolver } from "@hookform/resolvers/yup";
import { BorderColorRounded } from "@mui/icons-material";
import { <PERSON><PERSON>, Chip, Stack, TextField } from "@mui/material";
import { invalidateCurrentUser } from "@sisva/hooks/query/useAuth";
import { useUpdateStaff } from "@sisva/hooks/query/user/useStaffTeachers";
import { useUser } from "@sisva/hooks/query/user/useUsers";
import { useCurrentUser } from "@sisva/providers";
import { permissionsOptions } from "@sisva/types/dropdownOptions";
import { updateStaffSchema } from "@sisva/types/formTypes";
import type { Permission } from "@sisva/types/types";
import { getPermmissionText, getUserTypeText } from "@sisva/types/types";
import { useQueryClient } from "@tanstack/react-query";
import { useRouter } from "@tanstack/react-router";
import { useToggle } from "ahooks";
import { MultiSelectElement, TextFieldElement } from "react-hook-form-mui";
import { useForm } from "react-hook-form-mui";
import toast from "react-hot-toast";

export default function AkunTab({ user_id }: { user_id: string }) {
  const router = useRouter();
  const currentUser = useCurrentUser();
  const { data: user } = useUser(user_id);
  const [edit, { toggle }] = useToggle(false);
  const { control, handleSubmit, reset } = useForm({
    values: {
      name: user?.name ?? "",
      permissions: user?.permissions ?? [],
    },
    resolver: yupResolver(updateStaffSchema),
  });
  const queryClient = useQueryClient();

  const { mutate: updateStaff } = useUpdateStaff({
    user_id,
    async onSuccess() {
      if (user_id === currentUser.id) {
        invalidateCurrentUser(queryClient);
        await router.invalidate();
      }
      toggle();
      toast.success("Karyawan berhasil diperbarui");
    },
    onError() {
      toast.error("Karyawan gagal diperbarui");
    },
  });

  return (
    <Stack
      sx={{ p: 2, gap: 2 }}
      component={"form"}
      onSubmit={handleSubmit((value) => {
        updateStaff(value);
      })}
    >
      <TextFieldElement
        name="name"
        label="Nama"
        control={control}
        variant={edit ? undefined : "standard"}
        slotProps={{
          input: {
            readOnly: !edit,
            ...(edit ? {} : { disableUnderline: true }),
          },
          inputLabel: {
            sx: {
              fontSize: 16,
            },
          },
        }}
      />
      <MultiSelectElement
        itemKey="value"
        itemLabel="label"
        control={control}
        name="permissions"
        label="Akses"
        options={permissionsOptions}
        inputLabelProps={{
          sx: {
            fontSize: 16,
          },
        }}
        IconComponent={edit ? undefined : () => null}
        variant={edit ? undefined : "standard"}
        slotProps={{
          input: {
            readOnly: !edit,
          },
        }}
        {...(edit ? {} : { disableUnderline: true })}
        showCheckbox
        //@ts-expect-error unknown value
        renderValue={(selected: Permission[]) => (
          <div className="flex size-full flex-wrap gap-1">
            {selected.map((value) => (
              <Chip key={value} label={getPermmissionText(value)} />
            ))}
          </div>
        )}
      />
      <TextField
        label="Tipe"
        sx={{
          display: edit ? "none" : undefined,
        }}
        variant={edit ? undefined : "standard"}
        value={user?.type ? getUserTypeText(user?.type) : ""}
        slotProps={{
          input: {
            readOnly: !edit,
            ...(edit ? {} : { disableUnderline: true }),
          },
          inputLabel: {
            sx: {
              fontSize: 16,
            },
          },
        }}
      />

      <Stack
        sx={{
          flexDirection: "row",
          justifyContent: "end",
          gap: 2,
        }}
      >
        <Button
          onClick={() => {
            toggle();
            reset();
          }}
          startIcon={edit ? undefined : <BorderColorRounded />}
          variant="outlined"
        >
          {edit ? "Batal" : "Edit"}
        </Button>
        <Button
          type="submit"
          variant="contained"
          sx={{ display: edit ? undefined : "none" }}
        >
          Simpan
        </Button>
      </Stack>
    </Stack>
  );
}
