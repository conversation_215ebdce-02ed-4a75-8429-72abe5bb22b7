import { yupResolver } from "@hookform/resolvers/yup";
import { Add } from "@mui/icons-material";
import {
  Button,
  Divider,
  Modal,
  Paper,
  Stack,
  Typography,
} from "@mui/material";
import { useCreateStaff } from "@sisva/hooks/query/user/useStaffTeachers";
import { permissionsOptions, roleOptions } from "@sisva/types/dropdownOptions";
import { createStaffSchema } from "@sisva/types/formTypes";
import { useToggle } from "ahooks";
import {
  MultiSelectElement,
  PasswordElement,
  SelectElement,
  TextFieldElement,
  useForm,
} from "react-hook-form-mui";
import toast from "react-hot-toast";

export default function CreateStaffButton() {
  const [visible, { toggle }] = useToggle(false);
  const { control, handleSubmit, reset } = useForm({
    resolver: yupResolver(createStaffSchema),
  });
  const { mutate: createStaff } = useCreateStaff({
    onSuccess: () => {
      toggle();
      reset();
      toast.success("Karyawan berhasil ditambahkan");
    },
    onError: () => {
      toast.error("Karyawan gagal ditambahkan");
    },
  });

  return (
    <>
      <Button variant="contained" startIcon={<Add />} onClick={toggle}>
        Tambah
      </Button>
      <Modal open={visible} onClose={toggle}>
        <Stack
          sx={{
            width: 1,
            height: 1,
            justifyContent: "center",
            alignItems: "center",
            p: 2,
          }}
        >
          <Paper
            component={"form"}
            sx={{
              py: 2,
              width: 1,
              maxWidth: "ms",
              gap: 2,
              display: "flex",
              flexDirection: "column",
            }}
            onSubmit={handleSubmit((value) => {
              createStaff(value);
            })}
          >
            <Typography sx={{ fontWeight: 600, px: 2 }}>
              Tambah Karyawan
            </Typography>
            <Divider />
            <Stack sx={{ gap: 2, px: 2 }}>
              <TextFieldElement control={control} name="name" label="Nama" />
              <SelectElement
                control={control}
                name="type"
                label="Tipe"
                options={roleOptions
                  .filter(
                    (item) => item.value === "staff" || item.value === "teacher"
                  )
                  .map((item) => ({
                    id: item.value,
                    label: item.label,
                  }))}
              />
              <MultiSelectElement
                itemKey="value"
                itemLabel="label"
                control={control}
                name="permissions"
                label="Akses"
                options={permissionsOptions}
                showChips
              />
              <PasswordElement
                control={control}
                name="password"
                label="Password"
              />
              <PasswordElement
                control={control}
                name="confirm_password"
                label="Konfirmasi Password"
              />
            </Stack>
            <Divider />
            <Stack sx={{ gap: 2, px: 2, flexDirection: "row" }}>
              <Button variant="outlined" fullWidth onClick={toggle}>
                Batal
              </Button>
              <Button type="submit" variant="contained" fullWidth>
                Simpan
              </Button>
            </Stack>
          </Paper>
        </Stack>
      </Modal>
    </>
  );
}
