import { Clear, Search, Sort } from "@mui/icons-material";
import type { ChipOwnProps } from "@mui/material";
import {
  Button,
  Chip,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  IconButton,
  InputAdornment,
  Stack,
  Typography,
  useMediaQuery,
} from "@mui/material";
import type { GridColDef, GridFilterOperator } from "@mui/x-data-grid";
import { DataGrid, useGridApiContext } from "@mui/x-data-grid";
import { DatePicker } from "@mui/x-date-pickers";
import {
  type StaffTeachersWithAttendance,
  useStaffTeachersWithAttendance,
} from "@sisva/hooks/query/user/useStaffTeachers";
import type { Attendance } from "@sisva/types/types";
import { getAttendanceText } from "@sisva/types/types";
import { AvatarWithAcronym, AvatarWithAcronymByID } from "@sisva/ui";
import { onlyUnique } from "@sisva/utils";
import { useToggle } from "ahooks";
import dayjs from "dayjs";
import { atom, useAtom } from "jotai";
import { useEffect } from "react";
import { useForm } from "react-hook-form";
import { SelectElement, TextFieldElement } from "react-hook-form-mui";

import EditIcon from "#/routes/(main)/-components/EditIcon";

import UpdateStaffAttendanceButton from "../UpdateStaffAttendanceButton";

type Field =
  | "mobile_column"
  | "attendance"
  | keyof StaffTeachersWithAttendance
  | typeof filter_field
  | "action";
type TypeSafeColDef = GridColDef & { field: Field };

const filter_field = "filter_field";
const field = {
  filter1: "attendance",
} satisfies Record<string, Field>;

type FilterValue = Record<(typeof field)[keyof typeof field], string>;

const selectedDateIdAtom = atom(dayjs().format("YYYYMMDD"));

export default function KehadiranTab() {
  const isMobile = useMediaQuery((theme) => theme.breakpoints.down("lg"));

  const [selectedDateId, setSelectedDateId] = useAtom(selectedDateIdAtom);
  const { data: staffTeachers = [] } = useStaffTeachersWithAttendance({
    date_id: selectedDateId,
  });

  const customOperator: GridFilterOperator<StaffTeachersWithAttendance> = {
    label: "Custom Operator",
    value: "customOperator",
    getApplyFilterFn: (filterItem, column) => {
      if (!filterItem.field || !filterItem.value || !filterItem.operator) {
        return null;
      }
      const filterValue = filterItem.value as FilterValue;

      return (value, row, column, apiRef) => {
        let pass = true;
        if (filterValue.attendance) {
          if (
            getAttendanceText(row.attendance?.status ?? "present") !==
            filterValue.attendance
          )
            pass = false;
        }
        return pass;
      };
    },
  };

  const columns: TypeSafeColDef[] = [
    {
      field: "mobile_column",
      headerName: "",
      valueGetter: (_, row) => row,
      flex: 1,
      display: "flex",
      renderCell: (args) => {
        const value = args.value as StaffTeachersWithAttendance;
        return (
          <Stack sx={{ p: 1.5, gap: 2, width: "100%" }}>
            <Stack
              sx={{
                flexDirection: "row",
                justifyContent: "space-between",
                width: "100%",
              }}
            >
              <Stack
                sx={{ flexDirection: "row", gap: 1, alignItems: "center" }}
              >
                <AvatarWithAcronym
                  uri={value.profile_image_uri}
                  name={value.name}
                />
                <Typography sx={{ fontSize: 17 }}>{value.name}</Typography>
              </Stack>
              <Stack sx={{ gap: 1, flexDirection: "row", alignItems: "start" }}>
                <UpdateStaffAttendanceButton
                  staff_id={value.id}
                  date_id={Number(selectedDateId)}
                  renderTrigger={(onClick) => <EditIcon onClick={onClick} />}
                />
              </Stack>
            </Stack>
            <Stack
              sx={{
                flexDirection: "row",
                justifyContent: "space-between",
                width: "100%",
                alignItems: "center",
              }}
            >
              <Stack>
                <Typography color="textSecondary">Username</Typography>
                <Typography>{value.username}</Typography>
              </Stack>
              {value.attendance?.status ? (
                <AttenanceChip status={value.attendance?.status} />
              ) : null}
            </Stack>
          </Stack>
        );
      },
    },
    {
      field: "name",
      headerName: "Nama",
      display: "flex",
      flex: 2,
      renderCell: ({ row }: { row: StaffTeachersWithAttendance }) => (
        <Stack
          sx={{ py: 1.3, flexDirection: "row", alignItems: "center", gap: 2 }}
        >
          <AvatarWithAcronymByID user_id={row.id} size={40} />
          {row.name}
        </Stack>
      ),
    },
    {
      field: "username",
      valueGetter: (_, row: StaffTeachersWithAttendance) => row.username,
      headerName: "Username",
      flex: 1,
      display: "flex",
    },
    {
      field: "attendance",
      valueGetter: (_, row: StaffTeachersWithAttendance) =>
        row.attendance?.status ? getAttendanceText(row.attendance?.status) : "",
      headerName: "Status",
      renderCell: ({ row }: { row: StaffTeachersWithAttendance }) => (
        <Stack sx={{ py: 1.3 }}>
          {row.attendance?.status ? (
            <AttenanceChip status={row.attendance?.status} />
          ) : null}
        </Stack>
      ),
      display: "flex",
    },
    {
      field: "action",
      headerName: "Aksi",
      renderCell: ({ row }: { row: StaffTeachersWithAttendance }) => (
        <div className="flex gap-2 justify-center items-center size-full">
          <UpdateStaffAttendanceButton
            staff_id={row.id}
            date_id={Number(selectedDateId)}
            renderTrigger={(onClick) => <EditIcon onClick={onClick} />}
          />
        </div>
      ),
      sortable: false,
    },
    {
      field: filter_field,
      filterOperators: [customOperator],
    },
  ];

  const columnVisibilityModel = Object.fromEntries(
    columns
      .map((column) => column.field)
      .filter((field) =>
        isMobile ? field !== "mobile_column" : field === "mobile_column"
      )
      .map((field) => [field, false])
  );
  columnVisibilityModel[filter_field] = false;

  return (
    <DataGrid
      sx={{
        "& .MuiDataGrid-cell:focus": {
          outline: " none",
        },
        border: "none",
      }}
      disableRowSelectionOnClick
      className="bg-white grow"
      rows={staffTeachers}
      columns={columns}
      initialState={{ pagination: { paginationModel: { pageSize: 20 } } }}
      pageSizeOptions={[10, 20, 50]}
      getRowClassName={(params) =>
        params.indexRelativeToCurrentPage % 2 === 0
          ? "bg-neutral-50"
          : "bg-neutral-100"
      }
      slots={{
        toolbar: CustomToolbar,
      }}
      getRowHeight={() => "auto"}
      columnVisibilityModel={columnVisibilityModel}
      columnHeaderHeight={isMobile ? 0 : undefined}
      disableColumnMenu
    />
  );
}

function CustomToolbar() {
  const api = useGridApiContext();
  const [open, { toggle }] = useToggle(false);
  const [selectedDateId, setSelectedDateId] = useAtom(selectedDateIdAtom);

  const { control, handleSubmit, reset, setValue, watch } = useForm<{
    field: Field | "";
    sortDirection: "asc" | "desc";
    quickFilter: string;
    [field.filter1]: string;
  }>({
    defaultValues: {
      field: "",
      sortDirection: "asc",
      quickFilter: "",
      [field.filter1]: "",
    },
  });
  const filter1 = watch(field.filter1);

  const rows = Array.from(api.current.getRowModels()).map(
    ([key, value]) => value
  ) as StaffTeachersWithAttendance[];

  const attendanceStatuses = rows
    .map((row) => getAttendanceText(row.attendance?.status ?? "present"))
    .filter((item) => item)
    .filter(onlyUnique);

  const sortSelectOptions: { label: string; id: Field }[] = [
    { label: "Nama", id: "name" },
  ];

  // apply filter
  useEffect(() => {
    const value: FilterValue = {
      [field.filter1]: filter1,
    };
    api.current.upsertFilterItem({
      field: filter_field,
      operator: "customOperator",
      value,
    });
  }, [api, filter1]);

  const quickFilter = watch("quickFilter");
  useEffect(() => {
    api.current.setQuickFilterValues(
      quickFilter.split(" ").filter((s) => s !== "")
    );
  }, [api, quickFilter]);

  return (
    <div className="p-4 flex flex-col gap-2 border-b border-0 border-solid border-neutral-200">
      <div className=" flex justify-between gap-4 items-center thick-scrollbar">
        <Stack
          sx={{
            overflow: {
              xs: "auto",
              ["2xl"]: "initial",
            },
            py: {
              xs: 1,
              ["2xl"]: 0,
            },
          }}
        >
          <Stack sx={{ flexDirection: "row", gap: 2 }}>
            <TextFieldElement
              control={control}
              name="quickFilter"
              placeholder="Cari Karyawan"
              size="small"
              sx={{
                minWidth: 150,
              }}
              slotProps={{
                input: {
                  endAdornment: (
                    <InputAdornment position="end">
                      {quickFilter ? (
                        <IconButton
                          size="small"
                          onClick={() => setValue("quickFilter", "")}
                        >
                          <Clear />
                        </IconButton>
                      ) : (
                        <Search />
                      )}
                    </InputAdornment>
                  ),
                },
              }}
            />
            <DatePicker
              label="Pilih Tanggal"
              slotProps={{ textField: { size: "small" } }}
              value={dayjs(selectedDateId, "YYYYMMDD")}
              onChange={(value) => {
                setSelectedDateId(
                  value?.format("YYYYMMDD") ?? dayjs().format("YYYYMMDD")
                );
              }}
              sx={{
                minWidth: 180,
              }}
            />

            <SelectElement
              size="small"
              sx={{ minWidth: "180px" }}
              control={control}
              name={field.filter1}
              options={attendanceStatuses.map((item) => ({
                id: item,
                label: item,
              }))}
              label="Status"
              slotProps={{
                input: {
                  endAdornment: watch(field.filter1) && (
                    <InputAdornment position="end">
                      <IconButton
                        onClick={() => setValue(field.filter1, "")}
                        size="small"
                      >
                        <Clear />
                      </IconButton>
                    </InputAdornment>
                  ),
                },
              }}
            />
          </Stack>
        </Stack>
        <Stack
          sx={{
            minWidth: 110,
            // remove this line if you want to place a button inside this Stack
            display: "none",
          }}
        ></Stack>
      </div>
      <Button
        startIcon={<Sort />}
        variant="outlined"
        onClick={toggle}
        sx={{
          display: { xs: "flex", lg: "none" },
        }}
      >
        Urutkan Data
      </Button>
      <Dialog open={open} onClose={toggle}>
        <DialogTitle>Urutkan</DialogTitle>
        <DialogContent
          sx={{
            width: "250px",
            display: "flex",
            flexDirection: "column",
            gap: 2,
            overflow: "initial",
          }}
        >
          <SelectElement
            control={control}
            name="field"
            options={sortSelectOptions}
            label="Pilih Kolom"
            slotProps={{
              input: {
                endAdornment: watch("field") && (
                  <InputAdornment position="end">
                    <IconButton onClick={() => setValue("field", "")}>
                      <Clear />
                    </IconButton>
                  </InputAdornment>
                ),
              },
            }}
          />
          <SelectElement
            control={control}
            name="sortDirection"
            options={[
              { label: "A-Z", id: "asc" },
              { label: "Z-A", id: "desc" },
            ]}
            label="Urutan"
          />
        </DialogContent>
        <DialogActions sx={{ pb: 2, px: 3 }}>
          <Button
            variant="outlined"
            onClick={() => {
              reset();
              toggle();
            }}
          >
            Batal
          </Button>
          <Button
            variant="contained"
            onClick={handleSubmit((value) => {
              if (value.field) {
                api.current.sortColumn(value.field, value.sortDirection);
              } else {
                api.current.sortColumn("name", null);
              }
              toggle();
            })}
          >
            Simpan
          </Button>
        </DialogActions>
      </Dialog>
    </div>
  );
}

function AttenanceChip({ status }: { status: Attendance }) {
  function getColor(): ChipOwnProps["color"] {
    switch (status) {
      case "absent":
        return "error";
      case "present":
        return "success";
      case "leave":
        return "info";
      case "sick":
        return "warning";
    }
  }
  return <Chip label={getAttendanceText(status)} color={getColor()} />;
}
