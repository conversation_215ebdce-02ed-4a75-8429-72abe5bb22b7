import {
  Button,
  Divider,
  Modal,
  Paper,
  Stack,
  Typography,
} from "@mui/material";
import { useDeleteUser, useUser } from "@sisva/hooks/query/user/useUsers";
import { AvatarWithAcronym } from "@sisva/ui";
import { useToggle } from "ahooks";
import toast from "react-hot-toast";

import DeleteIcon from "#/routes/(main)/-components/DeleteIcon";

export default function DeleteStaffButton({ staff_id }: { staff_id: string }) {
  const [visible, { toggle }] = useToggle(false);
  const { mutate: deleteUser } = useDeleteUser({
    onSuccess() {
      toggle();
      toast.success("Karyawan berhasil dihapus");
    },
    onError() {
      toast.error("Karyawan gagal dihapus");
    },
  });

  return (
    <>
      <DeleteIcon onClick={toggle} />
      <Modal open={visible} onClose={toggle}>
        <div className="flex items-center justify-center size-full px-4">
          <Paper className="flex flex-col gap-4 w-full max-w-sm py-4">
            <div className="px-4 font-semibold"><PERSON><PERSON></div>
            <Divider />
            <Stack className="px-4 gap-2">
              <Typography>Anda akan menghapus karayawan berikut:</Typography>
              <Preview staff_id={staff_id} />
            </Stack>
            <Divider />
            <div className="flex gap-4 px-4">
              <Button variant="outlined" fullWidth onClick={toggle}>
                Batal
              </Button>
              <Button
                variant="contained"
                color="error"
                fullWidth
                onClick={() => deleteUser(staff_id)}
              >
                Hapus
              </Button>
            </div>
          </Paper>
        </div>
      </Modal>
    </>
  );
}

function Preview({ staff_id }: { staff_id: string }) {
  const { data: user } = useUser(staff_id);
  return (
    <div className="flex items-center gap-2 bg-neutral-200 rounded-md p-3">
      <AvatarWithAcronym uri={user?.profile_image_uri} name={user?.name} />
      <Stack>
        <div className="font-semibold">{user?.name}</div>
        <div className="text-sm">{user?.username}</div>
      </Stack>
    </div>
  );
}
