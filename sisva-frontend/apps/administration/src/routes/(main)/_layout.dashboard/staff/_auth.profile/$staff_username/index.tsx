import { usersQueryOptions } from "@sisva/hooks/query/user/useUsers";
import { createFileRoute, notFound } from "@tanstack/react-router";

import ProfileDetailPage from "./-components/ProfileDetailPage";

export const Route = createFileRoute(
  "/(main)/_layout/dashboard/staff/_auth/profile/$staff_username/"
)({
  async beforeLoad({ context: { queryClient }, params: { staff_username } }) {
    const users = await queryClient.fetchQuery(usersQueryOptions);
    const user = users.find((user) => user.username === staff_username);
    if (!user || (user.type !== "staff" && user.type !== "teacher")) {
      throw notFound();
    }
  },
  head: () => ({
    meta: [
      {
        title: "Profil Karyawan | Sisva",
      },
    ],
  }),
  component: RouteComponent,
});

function RouteComponent() {
  const { staff_username } = Route.useParams();
  return <ProfileDetailPage username={staff_username} />;
}
