import { Clear, Search, Sort } from "@mui/icons-material";
import {
  <PERSON>ton,
  Chip,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  IconButton,
  InputAdornment,
  Stack,
  Typography,
  useMediaQuery,
} from "@mui/material";
import type { GridColDef, GridFilterOperator } from "@mui/x-data-grid";
import { DataGrid, useGridApiContext } from "@mui/x-data-grid";
import { useStaffTeachers } from "@sisva/hooks/query/user/useStaffTeachers";
import type { User } from "@sisva/types/apiTypes";
import { permissionsOptions, roleOptions } from "@sisva/types/dropdownOptions";
import type { Permission } from "@sisva/types/types";
import { getPermmissionText, getUserTypeText } from "@sisva/types/types";
import { AvatarWithAcronym, AvatarWithAcronymByID } from "@sisva/ui";
import { useToggle } from "ahooks";
import { useEffect } from "react";
import { useForm } from "react-hook-form";
import {
  MultiSelectElement,
  SelectElement,
  TextFieldElement,
} from "react-hook-form-mui";

import CreateStaffButton from "../CreateStaffButton";
import DeleteStaffButton from "../DeleteStaffButton";
import DetailStaffButton from "../DetailStaffButton";

type Field = "mobile_column" | keyof User | "action" | typeof filter_field;

const filter_field = "filter_field";
const field = {
  filter1: "type",
  filter2: "permissions",
} satisfies Record<string, Field | "start_date" | "end_date">;

type FilterValue = Record<
  (typeof field)[keyof typeof field],
  string | string[]
>;

type TypeSafeColDef = GridColDef & { field: Field };

export default function KaryawanTab() {
  const isMobile = useMediaQuery((theme) => theme.breakpoints.down("lg"));

  const { data: staffTeachers = [] } = useStaffTeachers();

  const customOperator: GridFilterOperator<User> = {
    label: "Custom Operator",
    value: "customOperator",
    getApplyFilterFn: (filterItem, column) => {
      if (!filterItem.field || !filterItem.value || !filterItem.operator) {
        return null;
      }
      const filterValue = filterItem.value as FilterValue;
      const permissionFilter = filterValue.permissions as Permission[];

      return (value, row, column, apiRef) => {
        let pass = true;
        if (filterValue.type) {
          if (row.type !== filterValue.type) pass = false;
        }
        if (filterValue.permissions?.length) {
          if (
            !permissionFilter.every((permission) =>
              row.permissions.includes(permission)
            )
          )
            pass = false;
        }
        return pass;
      };
    },
  };

  const columns: TypeSafeColDef[] = [
    {
      field: "mobile_column",
      headerName: "",
      valueGetter: (_, row) => row,
      flex: 1,
      display: "flex",
      renderCell: (args) => {
        const value = args.value as User;
        return (
          <Stack sx={{ p: 1.5, gap: 2, width: "100%" }}>
            <Stack
              sx={{
                flexDirection: "row",
                justifyContent: "space-between",
                width: "100%",
              }}
            >
              <Stack
                sx={{ flexDirection: "row", gap: 1, alignItems: "center" }}
              >
                <AvatarWithAcronym
                  uri={value.profile_image_uri}
                  name={value.name}
                />
                <Typography sx={{ fontSize: 17 }}>{value.name}</Typography>
              </Stack>
              <Stack sx={{ gap: 1, flexDirection: "row", alignItems: "start" }}>
                <DetailStaffButton staff_username={value.username} />
                <DeleteStaffButton staff_id={value.id} />
              </Stack>
            </Stack>
            <Stack
              sx={{ flexDirection: "row", justifyContent: "space-between" }}
            >
              <Stack>
                <Typography color="textSecondary">Username</Typography>
                <Typography>{value.username}</Typography>
              </Stack>
              <Stack>
                <Typography color="textSecondary">Tipe</Typography>
                <Typography>{getUserTypeText(value.type)}</Typography>
              </Stack>
            </Stack>
            <Stack
              sx={{ flexDirection: "row", justifyContent: "space-between" }}
            >
              <Stack sx={{ gap: 0.5 }}>
                <Typography color="textSecondary">Akses</Typography>
                <Stack
                  sx={{ flexDirection: "row", flexWrap: "wrap", gap: 0.5 }}
                >
                  {value.permissions.map((permission) => {
                    return (
                      <Chip
                        key={permission}
                        label={getPermmissionText(permission)}
                        color="primary"
                      />
                    );
                  })}
                </Stack>
              </Stack>
            </Stack>
          </Stack>
        );
      },
    },
    {
      field: "name",
      headerName: "Nama",
      display: "flex",
      flex: 1,
      renderCell: ({ row }: { row: User }) => (
        <Stack
          sx={{ py: 1.3, flexDirection: "row", alignItems: "center", gap: 2 }}
        >
          <AvatarWithAcronymByID user_id={row.id} size={40} />
          {row.name}
        </Stack>
      ),
    },
    {
      field: "username",
      headerName: "Username",
      display: "flex",
    },
    {
      field: "type",
      headerName: "Tipe",
      valueGetter: (_, row) => getUserTypeText(row.type),
      display: "flex",
    },
    {
      field: "permissions",
      headerName: "Akses",
      sortable: false,
      flex: 1.5,
      display: "flex",
      renderCell: ({ row }: { row: User }) => (
        <Stack
          sx={{ py: 1.3, flexDirection: "row", alignItems: "center", gap: 2 }}
        >
          <Stack sx={{ flexDirection: "row", flexWrap: "wrap", gap: 0.5 }}>
            {row.permissions.map((permission) => {
              return (
                <Chip
                  key={permission}
                  label={getPermmissionText(permission)}
                  color="primary"
                />
              );
            })}
          </Stack>
        </Stack>
      ),
    },
    {
      field: "action",
      headerName: "Aksi",
      width: 128,
      renderCell: ({ row }: { row: User }) => (
        <div className="flex gap-2 justify-center items-center size-full">
          <DetailStaffButton staff_username={row.username} />
          <DeleteStaffButton staff_id={row.id} />
        </div>
      ),
      sortable: false,
    },
    {
      field: filter_field,
      filterOperators: [customOperator],
    },
  ];

  const columnVisibilityModel = Object.fromEntries(
    columns
      .map((column) => column.field)
      .filter((field) =>
        isMobile ? field !== "mobile_column" : field === "mobile_column"
      )
      .map((field) => [field, false])
  );
  columnVisibilityModel[filter_field] = false;

  return (
    <DataGrid
      sx={{
        "& .MuiDataGrid-cell:focus": {
          outline: " none",
        },
        border: "none",
      }}
      disableRowSelectionOnClick
      className="bg-white grow"
      rows={staffTeachers}
      columns={columns}
      initialState={{ pagination: { paginationModel: { pageSize: 20 } } }}
      pageSizeOptions={[10, 20, 50]}
      getRowClassName={(params) =>
        params.indexRelativeToCurrentPage % 2 === 0
          ? "bg-neutral-50"
          : "bg-neutral-100"
      }
      slots={{
        toolbar: CustomToolbar,
      }}
      getRowHeight={() => "auto"}
      columnVisibilityModel={columnVisibilityModel}
      columnHeaderHeight={isMobile ? 0 : undefined}
      disableColumnMenu
    />
  );
}

function CustomToolbar() {
  const api = useGridApiContext();
  const [open, { toggle }] = useToggle(false);

  const { control, handleSubmit, reset, setValue, watch } = useForm<{
    field: Field | "";
    sortDirection: "asc" | "desc";
    quickFilter: string;
    [field.filter1]: string;
    [field.filter2]: string[];
  }>({
    defaultValues: {
      field: "",
      sortDirection: "asc",
      quickFilter: "",
      [field.filter1]: "",
      [field.filter2]: [],
    },
  });

  // apply filter
  const filter1 = watch(field.filter1);
  const filter2 = watch(field.filter2);
  useEffect(() => {
    const value: FilterValue = {
      [field.filter1]: filter1,
      [field.filter2]: filter2,
    };
    api.current.upsertFilterItem({
      field: filter_field,
      operator: "customOperator",
      value,
    });
  }, [api, filter1, filter2]);

  const sortSelectOptions: { label: string; id: Field }[] = [
    { label: "Nama", id: "name" },
    { label: "Username", id: "username" },
  ];

  const quickFilter = watch("quickFilter");
  useEffect(() => {
    api.current.setQuickFilterValues(
      quickFilter.split(" ").filter((s) => s !== "")
    );
  }, [api, quickFilter]);

  return (
    <div className="p-4 flex flex-col gap-2 border-b border-0 border-solid border-neutral-200">
      <div className=" flex justify-between gap-4 items-center thick-scrollbar">
        <Stack
          sx={{
            overflow: {
              xs: "auto",
              xl: "initial",
            },
            py: {
              xs: 1,
              xl: 0,
            },
          }}
        >
          <Stack sx={{ flexDirection: "row", gap: 2 }}>
            <TextFieldElement
              control={control}
              name="quickFilter"
              placeholder="Cari Karyawan"
              size="small"
              sx={{
                minWidth: 150,
              }}
              slotProps={{
                input: {
                  endAdornment: (
                    <InputAdornment position="end">
                      {quickFilter ? (
                        <IconButton
                          size="small"
                          onClick={() => setValue("quickFilter", "")}
                        >
                          <Clear />
                        </IconButton>
                      ) : (
                        <Search />
                      )}
                    </InputAdornment>
                  ),
                },
              }}
            />
            <SelectElement
              size="small"
              sx={{ minWidth: "180px" }}
              control={control}
              name={field.filter1}
              options={roleOptions
                .filter(
                  (option) =>
                    option.value === "teacher" || option.value === "staff"
                )
                .map((option) => ({
                  id: option.value,
                  label: option.label,
                }))}
              label="Tipe"
              slotProps={{
                input: {
                  endAdornment: watch(field.filter1) && (
                    <InputAdornment position="end">
                      <IconButton
                        onClick={() => setValue(field.filter1, "")}
                        size="small"
                      >
                        <Clear />
                      </IconButton>
                    </InputAdornment>
                  ),
                },
              }}
            />
            <MultiSelectElement
              control={control}
              sx={{ minWidth: "180px" }}
              name={field.filter2}
              options={permissionsOptions.map((option) => ({
                id: option.value,
                label: option.label,
              }))}
              label="Akses"
              size="small"
              showCheckbox
            />
          </Stack>
        </Stack>
        <Stack
          sx={{
            minWidth: 110,
          }}
        >
          <CreateStaffButton />
        </Stack>
      </div>
      <Button
        startIcon={<Sort />}
        variant="outlined"
        onClick={toggle}
        sx={{
          display: { xs: "flex", lg: "none" },
        }}
      >
        Urutkan Data
      </Button>
      <Dialog open={open} onClose={toggle}>
        <DialogTitle>Urutkan</DialogTitle>
        <DialogContent
          sx={{
            width: "250px",
            display: "flex",
            flexDirection: "column",
            gap: 2,
            overflow: "initial",
          }}
        >
          <SelectElement
            control={control}
            name="field"
            options={sortSelectOptions}
            label="Pilih Kolom"
            slotProps={{
              input: {
                endAdornment: watch("field") && (
                  <InputAdornment position="end">
                    <IconButton onClick={() => setValue("field", "")}>
                      <Clear />
                    </IconButton>
                  </InputAdornment>
                ),
              },
            }}
          />
          <SelectElement
            control={control}
            name="sortDirection"
            options={[
              { label: "A-Z", id: "asc" },
              { label: "Z-A", id: "desc" },
            ]}
            label="Urutan"
          />
        </DialogContent>
        <DialogActions sx={{ pb: 2, px: 3 }}>
          <Button
            variant="outlined"
            onClick={() => {
              reset();
              toggle();
            }}
          >
            Batal
          </Button>
          <Button
            variant="contained"
            onClick={handleSubmit((value) => {
              if (value.field) {
                api.current.sortColumn(value.field, value.sortDirection);
              } else {
                api.current.sortColumn("name", null);
              }
              toggle();
            })}
          >
            Simpan
          </Button>
        </DialogActions>
      </Dialog>
    </div>
  );
}
