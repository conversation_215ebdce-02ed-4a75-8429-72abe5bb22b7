import { Add } from "@mui/icons-material";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Paper,
  Stack,
  Typography,
} from "@mui/material";
import { useToggle } from "ahooks";
import dayjs from "dayjs";
import id from "dayjs/locale/id";
import { type ReactNode } from "react";
import { SelectElement, useForm } from "react-hook-form-mui";
import toast from "react-hot-toast";

dayjs.locale(id);

import {
  useStaffAttendance,
  useUpdateStaffAttendance,
} from "@sisva/hooks/query/attendance/useAttendance";
import { useUser } from "@sisva/hooks/query/user/useUsers";
import { attendanceStatusOptions } from "@sisva/types/dropdownOptions";
import type { Attendance } from "@sisva/types/types";
import { AvatarWithAcronymByID } from "@sisva/ui";

export default function UpdateStaffAttendanceButton({
  staff_id,
  date_id,
  renderTrigger,
}: {
  staff_id: string;
  date_id: number;
  renderTrigger?: (onClick: () => void) => ReactNode;
}) {
  const [visible, { toggle }] = useToggle(false);

  const { data: user } = useUser(staff_id);
  const { data: staffAttendance } = useStaffAttendance({
    date_id,
    staff_id,
  });

  const { control, handleSubmit, reset, watch, setValue } = useForm<{
    date_id: number;
    staff_id: string;
    status: Attendance;
  }>({
    values: {
      date_id: date_id,
      staff_id: staff_id,
      status: staffAttendance?.status ?? "present",
    },
  });

  const { mutate: updateStaffAttendance } = useUpdateStaffAttendance({
    onSuccess: () => {
      toggle();
      reset();
      toast.success("Kehadiran berhasil diperbarui");
    },
    onError: () => {
      toast.error("Kehadiran gagal diperbarui");
    },
  });

  return (
    <>
      {renderTrigger ? (
        renderTrigger(toggle)
      ) : (
        <Button variant="contained" startIcon={<Add />} onClick={toggle}>
          Tambah
        </Button>
      )}
      <Modal open={visible} onClose={toggle}>
        <Stack
          sx={{
            width: 1,
            height: 1,
            justifyContent: "center",
            alignItems: "center",
            p: 2,
          }}
        >
          <Paper
            component={"form"}
            sx={{
              py: 2,
              width: 1,
              maxWidth: "ms",
              gap: 2,
              display: "flex",
              flexDirection: "column",
            }}
            onSubmit={handleSubmit((value) => {
              updateStaffAttendance({
                ...value,
              });
            })}
          >
            <Typography sx={{ fontWeight: 600, px: 2 }}>Absensi</Typography>
            <Divider />
            <Stack sx={{ gap: 2, px: 2 }}>
              <Typography sx={{ fontSize: 14 }}>
                {dayjs(String(date_id), "YYYYMMDD").format(
                  "dddd, DD MMMM YYYY"
                )}
              </Typography>
              <Stack
                sx={{
                  flexDirection: "row",
                  alignItems: "center",
                  gap: 1,
                  p: 1,
                  borderRadius: 2,
                  backgroundColor: "grey.200",
                }}
              >
                <AvatarWithAcronymByID user_id={staff_id} />
                <Stack
                  sx={{
                    justifyContent: "center",
                  }}
                >
                  <Typography
                    sx={{
                      fontWeight: 600,
                    }}
                  >
                    {user?.name}
                  </Typography>
                  <Typography sx={{ fontSize: 14 }}>
                    {user?.username}
                  </Typography>
                </Stack>
              </Stack>
              <SelectElement
                name="status"
                control={control}
                options={attendanceStatusOptions.map((item) => ({
                  id: item.value,
                  label: item.label,
                }))}
              />
            </Stack>
            <Divider />
            <Stack sx={{ gap: 2, px: 2, flexDirection: "row" }}>
              <Button variant="outlined" fullWidth onClick={toggle}>
                Batal
              </Button>
              <Button
                type="submit"
                variant="contained"
                fullWidth
                color="primary"
              >
                Simpan
              </Button>
            </Stack>
          </Paper>
        </Stack>
      </Modal>
    </>
  );
}
