import { Paper, Stack, Tab, Tabs, Typography } from "@mui/material";
import { useUserByUsername } from "@sisva/hooks/query/user/useUsers";
import { AvatarWithAcronymByID } from "@sisva/ui";
import type { SyntheticEvent } from "react";
import { useState } from "react";

import AkunTab from "./AkunTab";
import BiodataTab from "./BiodataTab";
import PasswordTab from "./PasswordTab";

export default function ProfileDetailPage({ username }: { username: string }) {
  const { data: user } = useUserByUsername(username);
  const user_id = user?.id ?? "";
  const [tabIndex, setTabIndex] = useState(0);

  return (
    <Stack
      sx={{
        height: "100%",
        width: "100%",
        p: {
          xs: 2,
          lg: 4,
        },
        gap: 2,
      }}
    >
      <Paper
        variant="outlined"
        sx={{
          display: "flex",
          flexDirection: "row",
          px: {
            xs: 2,
            lg: 4,
          },
          py: 2,
          gap: 2,
          alignItems: "center",
        }}
      >
        <AvatarWithAcronymByID user_id={user_id} size={64} />
        <Stack sx={{}}>
          <Typography
            sx={{
              fontSize: 18,
              fontWeight: 600,
            }}
          >
            {user?.name}
          </Typography>
          <Typography
            sx={{
              fontSize: 14,
            }}
          >
            {user?.username}
          </Typography>
        </Stack>
      </Paper>
      <Paper
        variant="outlined"
        sx={{
          flex: 1,
          overflow: "auto",
        }}
      >
        <Tabs
          value={tabIndex}
          onChange={(event: SyntheticEvent, newValue: number) => {
            setTabIndex(newValue);
          }}
          sx={{
            borderBottom: 1,
            borderColor: "divider",
          }}
        >
          <Tab
            label="Akun"
            sx={{ fontWeight: 600, textTransform: "none", fontSize: 14 }}
          />
          <Tab
            label="Biodata"
            sx={{ fontWeight: 600, textTransform: "none", fontSize: 14 }}
          />
          <Tab
            label="Password"
            sx={{ fontWeight: 600, textTransform: "none", fontSize: 14 }}
          />
        </Tabs>
        {(() => {
          switch (tabIndex) {
            case 0:
              return <AkunTab user_id={user_id} />;
            case 1:
              return <BiodataTab user_id={user_id} />;
            case 2:
              return <PasswordTab user_id={user_id} />;
          }
        })()}
      </Paper>
    </Stack>
  );
}
