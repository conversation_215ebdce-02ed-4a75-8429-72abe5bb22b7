import { useToggle } from "ahooks";
import { useState } from "react";
import toast from "react-hot-toast";

import ExcelButton from "#/routes/(main)/-components/ExcelButton";

import ImportXLSXAlert from "../../../-components/ImportXLSXAlert";
import ProgressAlert from "../../../-components/ProgressAlert";
import handleXLSXUploadStaffAttendance from "../-utils/handleXLSXUploadStaffAttendance";

export default function ImportExcelStaffAttendanceButton() {
  const [open, { toggle }] = useToggle(false);
  const [importReport, setImportReport] = useState<string[]>([]);
  const [title, setTitle] = useState("");

  const [openProgressAlert, toggleProgressAlert] = useState(false);
  const [progress, setProgress] = useState("");
  const [progressLog, setProgressLog] = useState("");
  return (
    <>
      <ExcelButton
        downloadTemplateLink="/templates/template-kehadiran-karyawan.xlsx"
        showImport
        importConfirmOnClick={(file) => {
          const toastId = toast.loading("Sedang menambahkan...");
          handleXLSXUploadStaffAttendance({
            file: file,
            onSuccess: (importReport) => {
              setTitle("File import berhasil");
              setImportReport(
                Object.values(importReport).filter((text) => text)
              );
              toast.remove(toastId);
              toggle();
            },
            onError: (importReport) => {
              setTitle("File import bermasalah");
              setImportReport(
                Object.values(importReport).filter((text) => text)
              );
              toast.remove(toastId);
              toggle();
            },
            setProgress,
            setProgressLog,
            toggleProgressAlert,
          });
        }}
      />
      <ImportXLSXAlert
        title={title}
        open={open}
        handleClose={() => toggle()}
        importReport={importReport}
      />
      <ProgressAlert
        open={openProgressAlert}
        title="Sedang Mengimport Data Kehadiran Karyawan"
        report={[progress, progressLog]}
      />
    </>
  );
}
