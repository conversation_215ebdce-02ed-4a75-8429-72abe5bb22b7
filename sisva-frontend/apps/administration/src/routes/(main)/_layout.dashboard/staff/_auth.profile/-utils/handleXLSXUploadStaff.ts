import AuthAPI from "@sisva/api/auth";
import UsersAP<PERSON> from "@sisva/api/users";
import type { User } from "@sisva/types/apiTypes";
import {
  type GenderText,
  getGender,
  getNationality,
  getPermissions,
  getReligion,
  getUserType,
  type NationalityText,
  type ReligionText,
  type UserTypeText,
} from "@sisva/types/types";
import * as XLSX from "xlsx";

/*
# Array structure

- index 0 - 2 is the header
- index 3 - MAX_ROW is the data
- set MAX_ROW to be 1000 max
- index in data array hold this information:
  0: name
  1: username
  2: type
  3: permissions "Sekolah"
  4: permissions "Karyawan"
  5: permissions "Akademik"
  6: permissions "Siswa"
  7: permissions "Rapot"
  8: permissions "Informasi"
  9: permissions "Keuangan"
  10: password
  11: email
  12: phone
  13: gender
  14: nationality
  15: personal_id
  16: education_id
  17: religion
  18: address
  19: profile_image_uri

*/

const MAX_ROW = 1000;

function getJsonText(data: Record<string, string | string[]>) {
  return JSON.stringify({
    email: data.email,
    phone: data.phone,
    gender: data.gender,
    nationality: data.nationality,
    personal_id: data.personal_id,
    education_id: data.education_id,
    religion: data.religion,
    address: data.address,
  });
}

function getUserByName(users: User[], name: string) {
  return users.find((user) => user.name === name);
}

function getUserByUsername(users: User[], username: string) {
  return users.find((user) => user.username === username);
}

function getUser(users: User[], user: { name: string; username: string }) {
  if (user.username) return getUserByUsername(users, user.username);
  return getUserByName(users, user.name);
}

export default function handleXLSXUploadStaff({
  file,
  onSuccess,
  onError,
}: {
  file: File;
  onSuccess: (reportText: string[]) => void;
  onError: (reportText: string[]) => void;
}) {
  const reader = new FileReader();
  const reportText: string[] = [];
  reader.onload = async (e) => {
    const file = e.target!.result;
    try {
      const users: User[] = (await UsersAPI.getAllUsers("staff,teacher")).data
        .data;

      const filteredData = users
        .map((user) => {
          const additionalJson = JSON.parse(user.detail.json_text);
          return { ...additionalJson, ...user };
        })
        .filter((user) => user.status == "active");
      const names = filteredData.map((user) => user.name) as string[];
      const usernames = filteredData.map((user) => user.username) as string[];

      const template = XLSX.read(file);
      const sheet = template.Sheets[template.SheetNames[0]!];
      const rawData = XLSX.utils.sheet_to_json(sheet!, {
        header: 1,
      }) as unknown[][];
      const rawDataWithoutHeader = rawData
        .slice(3, MAX_ROW)
        .filter((row) => row[0] && row.length !== 0);

      const dataObject = rawDataWithoutHeader.map((row) => {
        const permissions = getPermissions({
          manage_school: row[3] as boolean,
          manage_staff: row[4] as boolean,
          manage_academic: row[5] as boolean,
          manage_student: row[6] as boolean,
          manage_report: row[7] as boolean,
          manage_information: row[8] as boolean,
          manage_finance: row[9] as boolean,
        });

        return {
          name: row[0] as string,
          username: row[1] as string,
          type: getUserType(row[2] as UserTypeText),
          permissions: permissions,
          password: row[10] as string,
          email: row[11] as string,
          phone: row[12] as string,
          gender: getGender(row[13] as GenderText),
          nationality: getNationality(row[14] as NationalityText),
          personal_id: row[15] as string,
          education_id: row[16] as string,
          religion: getReligion(row[17] as ReligionText),
          address: row[18] as string,
          profile_image_uri: row[19] as string,
        };
      });

      const dataUpdate = dataObject.filter(
        (data) =>
          names.includes(data.name) &&
          (!data.username || usernames.includes(data.username))
      );
      const dataCreate = dataObject.filter(
        (user) => !names.includes(user.name)
      );

      let countCreateUser = 0;
      for (const data of dataCreate) {
        await new Promise((resolve) => setTimeout(resolve, 100));
        const payload = {
          user: {
            name: data.name,
            nik: data.personal_id ?? "",
            type: data.type,
            detail: {
              json_text: getJsonText(data),
            },
            profile_image_uri: data.profile_image_uri,
            roles: [data.type],
            permissions: data.permissions,
          },
          password: String(data.password),
        };
        await UsersAPI.createUser(payload);
        countCreateUser += 1;
      }
      if (countCreateUser > 0)
        reportText.push(`${countCreateUser} baris user berhasil ditambahkan`);

      const promisesUpdate = dataUpdate.map((data) => {
        const payload = {
          name: data.name,
          nik: data.personal_id,
          detail: {
            json_text: getJsonText(data),
          },
          profile_image_uri: data.profile_image_uri,
          roles: [data.type],
          permissions: data.permissions,
        };
        return UsersAPI.updateUserById(
          payload,
          getUser(filteredData, { name: data.name, username: data.username })!
            .id
        );
      });

      const promisesUpdatePassword = dataUpdate.map((data) => {
        const payload = {
          user_id: getUser(filteredData, {
            name: data.name,
            username: data.username,
          })!.id,
          new_password: String(data.password),
        };
        return AuthAPI.resetUserPass(payload);
      });

      const res = await Promise.all([
        ...promisesUpdate,
        ...promisesUpdatePassword,
      ]);

      if (promisesUpdate.length > 0)
        reportText.push(
          `${promisesUpdate.length} baris user berhasil diupdate`
        );
      if (promisesUpdatePassword.length > 0)
        reportText.push(
          `${promisesUpdatePassword.length} baris password berhasil diupdate`
        );
      onSuccess(reportText);
    } catch (error) {
      console.log(error);
      onError(reportText);
    }
  };
  reader.readAsArrayBuffer(file);
}
