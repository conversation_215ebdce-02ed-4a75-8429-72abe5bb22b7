import { Clear, Search, Sort } from "@mui/icons-material";
import {
  <PERSON>ton,
  Chip,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  IconButton,
  InputAdornment,
  Stack,
  Typography,
  useMediaQuery,
} from "@mui/material";
import type { GridColDef, GridFilterOperator } from "@mui/x-data-grid";
import { DataGrid, useGridApiContext } from "@mui/x-data-grid";
import { useAnnouncements } from "@sisva/hooks/query/academic/useAnnouncements";
import { useGetFileUrl } from "@sisva/hooks/utils";
import type { Announcement } from "@sisva/types/apiTypes";
import { roleOptions } from "@sisva/types/dropdownOptions";
import type { UserType } from "@sisva/types/types";
import { getUserTypeText } from "@sisva/types/types";
import { useToggle } from "ahooks";
import { Image } from "antd";
import dayjs from "dayjs";
import id from "dayjs/locale/id";
import { useEffect } from "react";
import { useForm } from "react-hook-form";
import { SelectElement, TextFieldElement } from "react-hook-form-mui";
dayjs.locale(id);

import fallback from "#/assets/images/fallback.png";
import DeleteIcon from "#/routes/(main)/-components/DeleteIcon";
import EditIcon from "#/routes/(main)/-components/EditIcon";

import CreateAnnouncementButton from "../CreateAnnouncementButton";
import DeleteAnnouncementButton from "../DeleteAnnouncementButton";

type Field =
  | "mobile_column"
  | keyof Announcement
  | typeof filter_field
  | "action";
type TypeSafeColDef = GridColDef & { field: Field };

const filter_field = "filter_field";
const field = {
  filter1: "user_type",
} satisfies Record<string, Field | "user_type">;

type FilterValue = Record<(typeof field)[keyof typeof field], string>;

export default function PengumumanTab() {
  const isMobile = useMediaQuery((theme) => theme.breakpoints.down("lg"));
  const { data: announcements = [] } = useAnnouncements();
  const getUrl = useGetFileUrl();

  const customOperator: GridFilterOperator<Announcement> = {
    label: "Custom Operator",
    value: "customOperator",
    getApplyFilterFn: (filterItem, column) => {
      if (!filterItem.field || !filterItem.value || !filterItem.operator) {
        return null;
      }
      const filterValue = filterItem.value as FilterValue;
      const userTypeFilter = filterValue.user_type as UserType;

      return (value, row, column, apiRef) => {
        let pass = true;
        if (userTypeFilter) {
          if (!row.target_user_types.includes(userTypeFilter)) pass = false;
        }

        return pass;
      };
    },
  };

  const columns: TypeSafeColDef[] = [
    {
      field: "mobile_column",
      headerName: "",
      valueGetter: (_, row) => row,
      flex: 1,
      display: "flex",
      renderCell: (args) => {
        const value = args.value as Announcement;
        return (
          <Stack sx={{ p: 1.5, gap: 1, width: "100%" }}>
            <Stack
              sx={{
                flexDirection: "row",
                alignItems: "center",
                gap: 2,
                width: "100%",
              }}
            >
              <Image
                src={
                  getUrl(value.image_uri) ? getUrl(value.image_uri) : fallback
                }
                alt={"Gambar Pengumuman"}
                width={80}
                height={80}
                className="min-w-20 object-cover"
                fallback={fallback}
                preview={!!getUrl(value.image_uri)}
              />
              <Typography sx={{ fontWeight: 600 }}>{value.title}</Typography>
            </Stack>
            <Stack
              sx={{
                flexDirection: "row",
                justifyContent: "space-between",
                width: "100%",
              }}
            >
              <Stack sx={{ flexDirection: "row", flexWrap: "wrap", gap: 0.5 }}>
                {value.text}
              </Stack>
            </Stack>

            <Stack
              sx={{
                flexDirection: "row",
                justifyContent: "space-between",
                width: "100%",
              }}
            >
              <Stack sx={{ gap: 0.5 }}>
                <Typography color="textSecondary">Target</Typography>
                <Stack sx={{ flexDirection: "row", gap: 1 }}>
                  {value.target_user_types.map((type) => (
                    <Chip
                      label={getUserTypeText(type)}
                      key={type}
                      color="primary"
                    />
                  ))}
                </Stack>
              </Stack>
              <Stack>
                <Typography color="textSecondary">Tanggal Post</Typography>
                <Typography>
                  {dayjs(value.create_time, "DD/MM/YYYY h:mm A Z").format(
                    "DD MMMM YYYY h:mm A"
                  )}
                </Typography>
              </Stack>
            </Stack>

            <Stack
              sx={{
                flexDirection: "row",
                justifyContent: "flex-end",
                gap: 1,
                width: "100%",
              }}
            >
              <CreateAnnouncementButton
                announcement_id={value.id}
                renderTrigger={(onClick) => <EditIcon onClick={onClick} />}
              />
              <DeleteAnnouncementButton
                announcement_id={value.id}
                renderTrigger={(onClick) => <DeleteIcon onClick={onClick} />}
              />
            </Stack>
          </Stack>
        );
      },
    },
    {
      field: "image_uri",
      headerName: "",
      sortable: false,
      display: "flex",
      renderCell: ({ value }) => (
        <Stack sx={{ py: 1.3 }}>
          <Image
            src={getUrl(value) ? getUrl(value) : fallback}
            alt={"Gambar Pengumuman"}
            width={80}
            height={80}
            className="min-w-20 object-cover"
            fallback={fallback}
            preview={!!getUrl(value)}
          />
        </Stack>
      ),
    },
    {
      field: "title",
      headerName: "Judul",
      display: "flex",
      minWidth: 180,
      renderCell: ({ value }) => <Stack sx={{ py: 1.3 }}>{value}</Stack>,
    },

    {
      field: "text",
      headerName: "Deskripsi",
      display: "flex",
      flex: 1,
      minWidth: 200,
      renderCell: ({ value }) => (
        <Stack sx={{ py: 1.3 }}>
          <Stack
            sx={{
              maxHeight: 150,
              overflowY: "auto",
              pr: 2,
            }}
          >
            {value}
          </Stack>
        </Stack>
      ),
    },
    {
      field: "target_user_types",
      headerName: "Target",
      display: "flex",
      minWidth: 130,
      sortable: false,
      renderCell: ({ row }: { row: Announcement }) => (
        <Stack sx={{ py: 1.3 }}>
          <Stack sx={{ flexDirection: "row", gap: 0.5, flexWrap: "wrap" }}>
            {row.target_user_types.map((type) => (
              <Chip label={getUserTypeText(type)} key={type} color="primary" />
            ))}
          </Stack>
        </Stack>
      ),
    },
    {
      field: "create_time",
      headerName: "Tanggal Post",
      display: "flex",
      minWidth: 130,
      valueGetter: (value) => dayjs(value, "DD/MM/YYYY h:mm A Z").toDate(),
      renderCell: ({ value }) => (
        <Stack sx={{ py: 1.3 }}>
          {dayjs(value).format("DD MMMM YYYY h:mm A")}
        </Stack>
      ),
    },

    {
      field: "action",
      headerName: "Aksi",
      display: "flex",
      sortable: false,
      renderCell: ({ row }) => (
        <Stack
          sx={{ py: 1.3, flexDirection: "row", alignItems: "center", gap: 1 }}
        >
          <CreateAnnouncementButton
            announcement_id={row.id}
            renderTrigger={(onClick) => <EditIcon onClick={onClick} />}
          />
          <DeleteAnnouncementButton
            announcement_id={row.id}
            renderTrigger={(onClick) => <DeleteIcon onClick={onClick} />}
          />
        </Stack>
      ),
    },
    {
      field: filter_field,
      filterOperators: [customOperator],
    },
  ];

  const columnVisibilityModel = Object.fromEntries(
    columns
      .map((column) => column.field)
      .filter((field) =>
        isMobile ? field !== "mobile_column" : field === "mobile_column"
      )
      .map((field) => [field, false])
  );
  columnVisibilityModel[filter_field] = false;

  return (
    <DataGrid
      sx={{
        "& .MuiDataGrid-cell:focus": {
          outline: " none",
        },
        border: "none",
      }}
      getRowId={(row: Announcement) => `${row.id}`}
      disableRowSelectionOnClick
      className="bg-white grow thick-scrollbar"
      rows={announcements}
      columns={columns}
      initialState={{ pagination: { paginationModel: { pageSize: 20 } } }}
      pageSizeOptions={[10, 20, 50]}
      getRowClassName={(params) =>
        params.indexRelativeToCurrentPage % 2 === 0
          ? "bg-neutral-50"
          : "bg-neutral-100"
      }
      slots={{
        toolbar: CustomToolbar,
      }}
      getRowHeight={() => "auto"}
      columnVisibilityModel={columnVisibilityModel}
      columnHeaderHeight={isMobile ? 0 : undefined}
      disableColumnMenu
      disableColumnFilter
    />
  );
}

function CustomToolbar() {
  const api = useGridApiContext();
  const [open, { toggle }] = useToggle(false);

  const { control, handleSubmit, reset, setValue, watch } = useForm<{
    field: Field | "";
    sortDirection: "asc" | "desc";
    quickFilter: string;
    [field.filter1]: string;
  }>({
    defaultValues: {
      field: "",
      sortDirection: "asc",
      quickFilter: "",
      [field.filter1]: "",
    },
  });

  const rows = Array.from(api.current.getRowModels()).map(
    ([key, value]) => value
  ) as Announcement[];

  // apply filter
  const filter1 = watch(field.filter1);
  useEffect(() => {
    const value: FilterValue = {
      [field.filter1]: filter1,
    };
    api.current.upsertFilterItem({
      field: filter_field,
      operator: "customOperator",
      value,
    });
  }, [api, filter1]);

  const sortSelectOptions: { label: string; id: Field }[] = [
    { label: "Judul", id: "title" },
    { label: "Deskripsi", id: "text" },
    { label: "Tanggal Post", id: "create_time" },
  ];

  const quickFilter = watch("quickFilter");
  useEffect(() => {
    api.current.setQuickFilterValues(
      quickFilter.split(" ").filter((s) => s !== "")
    );
  }, [api, quickFilter]);

  return (
    <div className="p-4 flex flex-col gap-2 border-b border-0 border-solid border-neutral-200">
      <div className=" flex justify-between gap-4 thick-scrollbar items-center">
        <Stack>
          <Stack sx={{ flexDirection: "row", gap: 2 }}>
            <TextFieldElement
              control={control}
              name="quickFilter"
              placeholder="Cari"
              size="small"
              sx={{
                minWidth: "150px",
              }}
              slotProps={{
                input: {
                  endAdornment: (
                    <InputAdornment position="end">
                      {quickFilter ? (
                        <IconButton
                          size="small"
                          onClick={() => setValue("quickFilter", "")}
                        >
                          <Clear />
                        </IconButton>
                      ) : (
                        <Search />
                      )}
                    </InputAdornment>
                  ),
                },
              }}
            />
            <SelectElement
              control={control}
              name={field.filter1}
              options={roleOptions
                .filter(
                  (option) =>
                    option.value === "staff" ||
                    option.value === "student" ||
                    option.value === "student_guardian"
                )
                .map((option) => {
                  return { id: option.value, label: option.label };
                })}
              label="Target"
              size="small"
              sx={{
                minWidth: "150px",
              }}
              slotProps={{
                input: {
                  endAdornment: filter1 && (
                    <InputAdornment position="end">
                      <IconButton onClick={() => setValue(field.filter1, "")}>
                        <Clear />
                      </IconButton>
                    </InputAdornment>
                  ),
                },
              }}
            />
          </Stack>
        </Stack>
        <Stack sx={{ minWidth: 110 }}>
          <CreateAnnouncementButton />
        </Stack>
      </div>
      <Button
        startIcon={<Sort />}
        variant="outlined"
        onClick={toggle}
        sx={{
          display: { xs: "flex", lg: "none" },
        }}
      >
        Urutkan Data
      </Button>
      <Dialog open={open} onClose={toggle}>
        <DialogTitle>Urutkan</DialogTitle>
        <DialogContent
          sx={{
            width: "250px",
            display: "flex",
            flexDirection: "column",
            gap: 2,
            overflow: "initial",
          }}
        >
          <SelectElement
            control={control}
            name="field"
            options={sortSelectOptions}
            label="Pilih Kolom"
            slotProps={{
              input: {
                endAdornment: watch("field") && (
                  <InputAdornment position="end">
                    <IconButton onClick={() => setValue("field", "")}>
                      <Clear />
                    </IconButton>
                  </InputAdornment>
                ),
              },
            }}
          />
          <SelectElement
            control={control}
            name="sortDirection"
            options={[
              { label: "A-Z", id: "asc" },
              { label: "Z-A", id: "desc" },
            ]}
            label="Urutan"
          />
        </DialogContent>
        <DialogActions sx={{ pb: 2, px: 3 }}>
          <Button
            variant="outlined"
            onClick={() => {
              reset();
              toggle();
            }}
          >
            Batal
          </Button>
          <Button
            variant="contained"
            onClick={handleSubmit((value) => {
              if (value.field) {
                api.current.sortColumn(value.field, value.sortDirection);
              } else {
                api.current.sortColumn("name", null);
              }
              toggle();
            })}
          >
            Simpan
          </Button>
        </DialogActions>
      </Dialog>
    </div>
  );
}
