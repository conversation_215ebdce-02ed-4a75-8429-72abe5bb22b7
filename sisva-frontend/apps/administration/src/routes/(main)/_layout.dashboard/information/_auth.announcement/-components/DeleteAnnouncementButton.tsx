import { DeleteForever } from "@mui/icons-material";
import {
  Button,
  Divider,
  <PERSON>dal,
  Paper,
  Stack,
  Typography,
} from "@mui/material";
import {
  useAnnouncement,
  useDeleteAnnouncement,
} from "@sisva/hooks/query/academic/useAnnouncements";
import { useGetFileUrl } from "@sisva/hooks/utils";
import { useToggle } from "ahooks";
import { Image } from "antd";
import dayjs from "dayjs";
import id from "dayjs/locale/id";
import type { ReactNode } from "react";
import toast from "react-hot-toast";

import fallback from "#/assets/images/fallback.png";
dayjs.locale(id);

export default function DeleteAnnouncementButton({
  announcement_id,
  renderTrigger,
}: {
  announcement_id: number;
  renderTrigger?: (onClick: () => void) => ReactNode;
}) {
  const [visible, { toggle }] = useToggle(false);
  const { data: announcement } = useAnnouncement(announcement_id);

  const getUrl = useGetFileUrl();

  const { mutate: deleteAnnouncement } = useDeleteAnnouncement({
    onSuccess: () => {
      toggle();
      toast.success("Pengumuman berhasil dihapus");
    },
    onError: () => {
      toast.error("Pengumuman gagal dihapus");
    },
  });

  const url = getUrl(announcement?.image_uri ?? "");

  return (
    <>
      {renderTrigger ? (
        renderTrigger(toggle)
      ) : (
        <Button
          variant="contained"
          startIcon={<DeleteForever />}
          color="error"
          onClick={toggle}
        >
          Hapus
        </Button>
      )}
      <Modal open={visible} onClose={toggle}>
        <Stack
          sx={{
            width: 1,
            height: 1,
            justifyContent: "center",
            alignItems: "center",
            p: 2,
          }}
        >
          <Paper
            component="form"
            sx={{
              py: 2,
              width: 1,
              maxWidth: "ms",
              gap: 2,
              display: "flex",
              flexDirection: "column",
            }}
            onSubmit={(e) => {
              e.preventDefault();
              deleteAnnouncement(announcement_id);
            }}
          >
            <Typography sx={{ fontWeight: 600, px: 2 }}>
              Hapus Pengumuman
            </Typography>
            <Divider />
            <Stack sx={{ gap: 2, px: 2 }}>
              <Stack
                sx={{ flexDirection: "row", justifyContent: "space-between" }}
              >
                <Typography sx={{ fontWeight: 600 }}>Judul</Typography>
                <Typography>{announcement?.title}</Typography>
              </Stack>
            </Stack>
            <Stack sx={{ flexDirection: "row", justifyContent: "center" }}>
              <Image
                src={url ? url : fallback}
                alt={"Gambar Pengumuman"}
                width={80}
                height={80}
                rootClassName="!z-[1310] relative"
                className="min-w-20 object-cover"
                fallback={fallback}
                preview={!!url}
              />
            </Stack>
            <Divider />
            <Stack sx={{ gap: 2, px: 2, flexDirection: "row" }}>
              <Button variant="outlined" fullWidth onClick={toggle}>
                Batal
              </Button>
              <Button type="submit" variant="contained" fullWidth color="error">
                Hapus
              </Button>
            </Stack>
          </Paper>
        </Stack>
      </Modal>
    </>
  );
}
