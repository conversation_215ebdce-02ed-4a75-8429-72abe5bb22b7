import { yupResolver } from "@hookform/resolvers/yup";
import { Add } from "@mui/icons-material";
import {
  Button,
  Divider,
  Modal,
  Paper,
  Stack,
  Typography,
} from "@mui/material";
import {
  useAnnouncement,
  useCreateAnnouncement,
  useUpdateAnnouncement,
} from "@sisva/hooks/query/academic/useAnnouncements";
import { useUploadFile } from "@sisva/hooks/query/file/useFiles";
import { useGetFileUrl } from "@sisva/hooks/utils";
import { roleOptions } from "@sisva/types/dropdownOptions";
import type { UserType } from "@sisva/types/types";
import { useToggle } from "ahooks";
import { Image } from "antd";
import { type ReactNode } from "react";
import {
  MultiSelectElement,
  TextareaAutosizeElement,
  TextFieldElement,
  useForm,
} from "react-hook-form-mui";
import toast from "react-hot-toast";
import { array, object, string } from "yup";

import fallback from "#/assets/images/fallback.png";

/**
 * this component has 2 modes:
 * 1. create new announcement, no props
 * 2. edit announcement, by passing announcement_id
 */
export default function CreateAnnouncementButton({
  // if annoucenemt_id provided, then it's edit mode
  announcement_id,
  renderTrigger,
}: {
  announcement_id?: number;
  renderTrigger?: (onClick: () => void) => ReactNode;
}) {
  const [visible, { toggle }] = useToggle(false);

  const { data: announcement } = useAnnouncement(announcement_id);

  const { mutateAsync: uploadFile } = useUploadFile();
  const getUrl = useGetFileUrl();

  const { control, handleSubmit, reset, watch, setValue } = useForm({
    values: {
      title: announcement?.title ?? "",
      text: announcement?.text ?? "",
      image_uri: announcement?.image_uri ?? "",
      target_user_types: announcement?.target_user_types ?? [],
    },
    resolver: yupResolver(
      object({
        title: string().required("Judul wajib diisi"),
        text: string().required("Deskripsi wajib diisi"),
        image_uri: string(),
        target_user_types: array()
          .of(string().required("Target wajib dipilih"))
          .required("Target wajib dipilih")
          .min(1, "Target wajib dipilih"),
      })
    ),
  });

  const { mutate: createAnnouncement } = useCreateAnnouncement({
    onSuccess: () => {
      toggle();
      reset();
      toast.success("Pengumuman berhasil dibuat");
    },
    onError: () => {
      toast.error("Pengumuman gagal dibuat");
    },
  });

  const { mutate: updateAnnouncement } = useUpdateAnnouncement({
    onSuccess: () => {
      toggle();
      reset();
      toast.success("Pengumuman berhasil diperbarui");
    },
    onError: () => {
      toast.error("Pengumuman gagal diperbarui");
    },
  });

  const url = getUrl(watch("image_uri") ?? "");

  return (
    <>
      {renderTrigger ? (
        renderTrigger(toggle)
      ) : (
        <Button variant="contained" startIcon={<Add />} onClick={toggle}>
          Tambah
        </Button>
      )}
      <Modal open={visible} onClose={toggle}>
        <Stack
          sx={{
            width: 1,
            height: 1,
            justifyContent: "center",
            alignItems: "center",
            p: 2,
          }}
        >
          <Paper
            component={"form"}
            sx={{
              py: 2,
              width: 1,
              maxWidth: "ms",
              gap: 2,
              display: "flex",
              flexDirection: "column",
            }}
            onSubmit={handleSubmit((value) => {
              if (announcement_id) {
                updateAnnouncement({
                  ...value,
                  id: announcement_id,
                  target_user_types: value.target_user_types as UserType[],
                });
              } else {
                createAnnouncement({
                  ...value,
                  target_user_types: value.target_user_types as UserType[],
                });
              }
            })}
          >
            <Typography sx={{ fontWeight: 600, px: 2 }}>
              {(() => {
                if (announcement_id) {
                  return "Edit Pengumuman";
                } else {
                  return "Buat Pengumuman";
                }
              })()}
            </Typography>
            <Divider />
            <Stack sx={{ gap: 2, px: 2 }}>
              <TextFieldElement name="title" label="Judul" control={control} />
              <TextareaAutosizeElement
                resizeStyle="vertical"
                name="text"
                label="Deskripsi"
                control={control}
                rows={5}
              />
              <MultiSelectElement
                control={control}
                name="target_user_types"
                options={roleOptions
                  .filter(
                    (option) =>
                      option.value === "staff" ||
                      option.value === "student" ||
                      option.value === "student_guardian"
                  )
                  .map((option) => {
                    return { id: option.value, label: option.label };
                  })}
                label="Target"
                showChips
              />
              <Stack
                sx={{
                  flexDirection: "row",
                  gap: 2,
                }}
              >
                <Image
                  src={url ? url : fallback}
                  alt={"Gambar Pengumuman"}
                  width={80}
                  height={80}
                  className="min-w-20 object-cover"
                  rootClassName="!z-[1310] relative"
                  fallback={fallback}
                  preview={!!url}
                />
                <Stack
                  sx={{
                    gap: 2,
                    flex: 1,
                    justifyContent: "space-between",
                  }}
                >
                  <Button component="label" variant="contained" size="small">
                    {url ? "Ubah Gambar" : "Upload Gambar"}
                    <input
                      accept="image/*"
                      className="hidden"
                      type="file"
                      onChange={async (event) => {
                        if (!event.target.files?.[0]) return;
                        const formData = new FormData();
                        formData.append("file", event.target.files[0]);
                        const uri = await uploadFile(formData);
                        setValue("image_uri", uri);
                        event.target.value = "";
                      }}
                      multiple
                    />
                  </Button>
                  <Button
                    variant="outlined"
                    size="small"
                    color="error"
                    onClick={() => {
                      setValue("image_uri", "");
                    }}
                  >
                    Hapus
                  </Button>
                </Stack>
              </Stack>
            </Stack>
            <Divider />
            <Stack sx={{ gap: 2, px: 2, flexDirection: "row" }}>
              <Button variant="outlined" fullWidth onClick={toggle}>
                Batal
              </Button>
              <Button
                type="submit"
                variant="contained"
                fullWidth
                color="primary"
              >
                Simpan
              </Button>
            </Stack>
          </Paper>
        </Stack>
      </Modal>
    </>
  );
}
