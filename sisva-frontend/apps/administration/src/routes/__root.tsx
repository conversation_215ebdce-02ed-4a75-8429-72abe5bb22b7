import {
  MUILocalizationProvider,
  type SchoolWithMoreData,
} from "@sisva/providers";
import type { User } from "@sisva/types/apiTypes";
import { NotFoundPage } from "@sisva/ui";
import { registerLicense } from "@syncfusion/ej2-base";
import { type QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { ReactQueryDevtools } from "@tanstack/react-query-devtools";
import {
  createRootRouteWithContext,
  HeadContent,
  Outlet,
} from "@tanstack/react-router";
import { TanStackRouterDevtools } from "@tanstack/router-devtools";
import { createPortal } from "react-dom";
import { Toaster } from "react-hot-toast";

import { env } from "#/env";

export const Route = createRootRouteWithContext<{
  queryClient: QueryClient;
  currentUser: User;
  currentSchool: SchoolWithMoreData;
}>()({
  head() {
    return {
      meta: [
        {
          title: "Demo Aplikasi | Sisva",
        },
      ],
    };
  },
  component: RootComponent,
  notFoundComponent: NotFoundPage,
});

function RootComponent() {
  const { queryClient } = Route.useRouteContext();

  registerLicense(env.VITE_EJ2_SYNCFUSION_LICENSE_KEY);

  return (
    <MUILocalizationProvider>
      <QueryClientProvider client={queryClient}>
        {createPortal(<HeadContent />, document.querySelector("head")!)}{" "}
        <Toaster position="bottom-left" />
        <Outlet />
        {env.DEV && (
          <>
            <ReactQueryDevtools buttonPosition="bottom-left" />
            <TanStackRouterDevtools position="bottom-right" />{" "}
          </>
        )}
      </QueryClientProvider>
    </MUILocalizationProvider>
  );
}
