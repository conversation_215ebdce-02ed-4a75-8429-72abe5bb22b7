/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { createFileRoute } from '@tanstack/react-router'

// Import Routes

import { Route as rootRoute } from './routes/__root'
import { Route as IndexImport } from './routes/index'
import { Route as mainLayoutImport } from './routes/(main)/_layout'
import { Route as authSigninIndexImport } from './routes/(auth)/signin/index'
import { Route as mainLayoutDashboardIndexImport } from './routes/(main)/_layout.dashboard/index'
import { Route as mainLayoutDashboardStudentAuthImport } from './routes/(main)/_layout.dashboard/student/_auth'
import { Route as mainLayoutDashboardStaffAuthImport } from './routes/(main)/_layout.dashboard/staff/_auth'
import { Route as mainLayoutDashboardSchoolAuthImport } from './routes/(main)/_layout.dashboard/school/_auth'
import { Route as mainLayoutDashboardReportAuthImport } from './routes/(main)/_layout.dashboard/report/_auth'
import { Route as mainLayoutDashboardInformationAuthImport } from './routes/(main)/_layout.dashboard/information/_auth'
import { Route as mainLayoutDashboardFinanceAuthImport } from './routes/(main)/_layout.dashboard/finance/_auth'
import { Route as mainLayoutDashboardAcademicAuthImport } from './routes/(main)/_layout.dashboard/academic/_auth'
import { Route as mainLayoutDashboardStudentAuthIndexImport } from './routes/(main)/_layout.dashboard/student/_auth.index'
import { Route as mainLayoutDashboardStaffAuthIndexImport } from './routes/(main)/_layout.dashboard/staff/_auth.index'
import { Route as mainLayoutDashboardSchoolAuthIndexImport } from './routes/(main)/_layout.dashboard/school/_auth.index'
import { Route as mainLayoutDashboardReportAuthIndexImport } from './routes/(main)/_layout.dashboard/report/_auth.index'
import { Route as mainLayoutDashboardInformationAuthIndexImport } from './routes/(main)/_layout.dashboard/information/_auth.index'
import { Route as mainLayoutDashboardFinanceAuthIndexImport } from './routes/(main)/_layout.dashboard/finance/_auth.index'
import { Route as mainLayoutDashboardAcademicAuthIndexImport } from './routes/(main)/_layout.dashboard/academic/_auth.index'
import { Route as mainLayoutDashboardStudentAuthProfileIndexImport } from './routes/(main)/_layout.dashboard/student/_auth.profile/index'
import { Route as mainLayoutDashboardStudentAuthGuardianIndexImport } from './routes/(main)/_layout.dashboard/student/_auth.guardian/index'
import { Route as mainLayoutDashboardStudentAuthAttendanceIndexImport } from './routes/(main)/_layout.dashboard/student/_auth.attendance/index'
import { Route as mainLayoutDashboardStaffAuthProfileIndexImport } from './routes/(main)/_layout.dashboard/staff/_auth.profile/index'
import { Route as mainLayoutDashboardStaffAuthAttendanceIndexImport } from './routes/(main)/_layout.dashboard/staff/_auth.attendance/index'
import { Route as mainLayoutDashboardSchoolAuthProfileIndexImport } from './routes/(main)/_layout.dashboard/school/_auth.profile/index'
import { Route as mainLayoutDashboardReportAuthViewIndexImport } from './routes/(main)/_layout.dashboard/report/_auth.view/index'
import { Route as mainLayoutDashboardReportAuthTemplateIndexImport } from './routes/(main)/_layout.dashboard/report/_auth.template/index'
import { Route as mainLayoutDashboardReportAuthPlaygroundIndexImport } from './routes/(main)/_layout.dashboard/report/_auth.playground/index'
import { Route as mainLayoutDashboardReportAuthListIndexImport } from './routes/(main)/_layout.dashboard/report/_auth.list/index'
import { Route as mainLayoutDashboardReportAuthFillingIndexImport } from './routes/(main)/_layout.dashboard/report/_auth.filling/index'
import { Route as mainLayoutDashboardInformationAuthAnnouncementIndexImport } from './routes/(main)/_layout.dashboard/information/_auth.announcement/index'
import { Route as mainLayoutDashboardFinanceAuthInvoiceIndexImport } from './routes/(main)/_layout.dashboard/finance/_auth.invoice/index'
import { Route as mainLayoutDashboardAcademicAuthTeacherIndexImport } from './routes/(main)/_layout.dashboard/academic/_auth.teacher/index'
import { Route as mainLayoutDashboardAcademicAuthStudyProgramIndexImport } from './routes/(main)/_layout.dashboard/academic/_auth.study-program/index'
import { Route as mainLayoutDashboardAcademicAuthScheduleIndexImport } from './routes/(main)/_layout.dashboard/academic/_auth.schedule/index'
import { Route as mainLayoutDashboardAcademicAuthPeriodIndexImport } from './routes/(main)/_layout.dashboard/academic/_auth.period/index'
import { Route as mainLayoutDashboardAcademicAuthExtracurricularIndexImport } from './routes/(main)/_layout.dashboard/academic/_auth.extracurricular/index'
import { Route as mainLayoutDashboardAcademicAuthCurriculumIndexImport } from './routes/(main)/_layout.dashboard/academic/_auth.curriculum/index'
import { Route as mainLayoutDashboardAcademicAuthClassIndexImport } from './routes/(main)/_layout.dashboard/academic/_auth.class/index'
import { Route as mainLayoutDashboardStudentAuthProfileStudentusernameIndexImport } from './routes/(main)/_layout.dashboard/student/_auth.profile/$student_username/index'
import { Route as mainLayoutDashboardStudentAuthGuardianGuardianusernameIndexImport } from './routes/(main)/_layout.dashboard/student/_auth.guardian/$guardian_username/index'
import { Route as mainLayoutDashboardStaffAuthProfileStaffusernameIndexImport } from './routes/(main)/_layout.dashboard/staff/_auth.profile/$staff_username/index'
import { Route as mainLayoutDashboardReportAuthViewReportidIndexImport } from './routes/(main)/_layout.dashboard/report/_auth.view/$report_id/index'
import { Route as mainLayoutDashboardReportAuthListNewIndexImport } from './routes/(main)/_layout.dashboard/report/_auth.list/new/index'
import { Route as mainLayoutDashboardReportAuthFillingReportidIndexImport } from './routes/(main)/_layout.dashboard/report/_auth.filling/$report_id/index'
import { Route as mainLayoutDashboardReportAuthListReportidEditIndexImport } from './routes/(main)/_layout.dashboard/report/_auth.list/$report_id/edit/index'

// Create Virtual Routes

const mainImport = createFileRoute('/(main)')()
const mainLayoutDashboardStudentImport = createFileRoute(
  '/(main)/_layout/dashboard/student',
)()
const mainLayoutDashboardStaffImport = createFileRoute(
  '/(main)/_layout/dashboard/staff',
)()
const mainLayoutDashboardSchoolImport = createFileRoute(
  '/(main)/_layout/dashboard/school',
)()
const mainLayoutDashboardReportImport = createFileRoute(
  '/(main)/_layout/dashboard/report',
)()
const mainLayoutDashboardInformationImport = createFileRoute(
  '/(main)/_layout/dashboard/information',
)()
const mainLayoutDashboardFinanceImport = createFileRoute(
  '/(main)/_layout/dashboard/finance',
)()
const mainLayoutDashboardAcademicImport = createFileRoute(
  '/(main)/_layout/dashboard/academic',
)()

// Create/Update Routes

const mainRoute = mainImport.update({
  id: '/(main)',
  getParentRoute: () => rootRoute,
} as any)

const IndexRoute = IndexImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRoute,
} as any)

const mainLayoutRoute = mainLayoutImport.update({
  id: '/_layout',
  getParentRoute: () => mainRoute,
} as any)

const authSigninIndexRoute = authSigninIndexImport.update({
  id: '/(auth)/signin/',
  path: '/signin/',
  getParentRoute: () => rootRoute,
} as any)

const mainLayoutDashboardStudentRoute = mainLayoutDashboardStudentImport.update(
  {
    id: '/dashboard/student',
    path: '/dashboard/student',
    getParentRoute: () => mainLayoutRoute,
  } as any,
)

const mainLayoutDashboardStaffRoute = mainLayoutDashboardStaffImport.update({
  id: '/dashboard/staff',
  path: '/dashboard/staff',
  getParentRoute: () => mainLayoutRoute,
} as any)

const mainLayoutDashboardSchoolRoute = mainLayoutDashboardSchoolImport.update({
  id: '/dashboard/school',
  path: '/dashboard/school',
  getParentRoute: () => mainLayoutRoute,
} as any)

const mainLayoutDashboardReportRoute = mainLayoutDashboardReportImport.update({
  id: '/dashboard/report',
  path: '/dashboard/report',
  getParentRoute: () => mainLayoutRoute,
} as any)

const mainLayoutDashboardInformationRoute =
  mainLayoutDashboardInformationImport.update({
    id: '/dashboard/information',
    path: '/dashboard/information',
    getParentRoute: () => mainLayoutRoute,
  } as any)

const mainLayoutDashboardFinanceRoute = mainLayoutDashboardFinanceImport.update(
  {
    id: '/dashboard/finance',
    path: '/dashboard/finance',
    getParentRoute: () => mainLayoutRoute,
  } as any,
)

const mainLayoutDashboardAcademicRoute =
  mainLayoutDashboardAcademicImport.update({
    id: '/dashboard/academic',
    path: '/dashboard/academic',
    getParentRoute: () => mainLayoutRoute,
  } as any)

const mainLayoutDashboardIndexRoute = mainLayoutDashboardIndexImport.update({
  id: '/dashboard/',
  path: '/dashboard/',
  getParentRoute: () => mainLayoutRoute,
} as any)

const mainLayoutDashboardStudentAuthRoute =
  mainLayoutDashboardStudentAuthImport.update({
    id: '/_auth',
    getParentRoute: () => mainLayoutDashboardStudentRoute,
  } as any)

const mainLayoutDashboardStaffAuthRoute =
  mainLayoutDashboardStaffAuthImport.update({
    id: '/_auth',
    getParentRoute: () => mainLayoutDashboardStaffRoute,
  } as any)

const mainLayoutDashboardSchoolAuthRoute =
  mainLayoutDashboardSchoolAuthImport.update({
    id: '/_auth',
    getParentRoute: () => mainLayoutDashboardSchoolRoute,
  } as any)

const mainLayoutDashboardReportAuthRoute =
  mainLayoutDashboardReportAuthImport.update({
    id: '/_auth',
    getParentRoute: () => mainLayoutDashboardReportRoute,
  } as any)

const mainLayoutDashboardInformationAuthRoute =
  mainLayoutDashboardInformationAuthImport.update({
    id: '/_auth',
    getParentRoute: () => mainLayoutDashboardInformationRoute,
  } as any)

const mainLayoutDashboardFinanceAuthRoute =
  mainLayoutDashboardFinanceAuthImport.update({
    id: '/_auth',
    getParentRoute: () => mainLayoutDashboardFinanceRoute,
  } as any)

const mainLayoutDashboardAcademicAuthRoute =
  mainLayoutDashboardAcademicAuthImport.update({
    id: '/_auth',
    getParentRoute: () => mainLayoutDashboardAcademicRoute,
  } as any)

const mainLayoutDashboardStudentAuthIndexRoute =
  mainLayoutDashboardStudentAuthIndexImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => mainLayoutDashboardStudentAuthRoute,
  } as any)

const mainLayoutDashboardStaffAuthIndexRoute =
  mainLayoutDashboardStaffAuthIndexImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => mainLayoutDashboardStaffAuthRoute,
  } as any)

const mainLayoutDashboardSchoolAuthIndexRoute =
  mainLayoutDashboardSchoolAuthIndexImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => mainLayoutDashboardSchoolAuthRoute,
  } as any)

const mainLayoutDashboardReportAuthIndexRoute =
  mainLayoutDashboardReportAuthIndexImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => mainLayoutDashboardReportAuthRoute,
  } as any)

const mainLayoutDashboardInformationAuthIndexRoute =
  mainLayoutDashboardInformationAuthIndexImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => mainLayoutDashboardInformationAuthRoute,
  } as any)

const mainLayoutDashboardFinanceAuthIndexRoute =
  mainLayoutDashboardFinanceAuthIndexImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => mainLayoutDashboardFinanceAuthRoute,
  } as any)

const mainLayoutDashboardAcademicAuthIndexRoute =
  mainLayoutDashboardAcademicAuthIndexImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => mainLayoutDashboardAcademicAuthRoute,
  } as any)

const mainLayoutDashboardStudentAuthProfileIndexRoute =
  mainLayoutDashboardStudentAuthProfileIndexImport.update({
    id: '/profile/',
    path: '/profile/',
    getParentRoute: () => mainLayoutDashboardStudentAuthRoute,
  } as any)

const mainLayoutDashboardStudentAuthGuardianIndexRoute =
  mainLayoutDashboardStudentAuthGuardianIndexImport.update({
    id: '/guardian/',
    path: '/guardian/',
    getParentRoute: () => mainLayoutDashboardStudentAuthRoute,
  } as any)

const mainLayoutDashboardStudentAuthAttendanceIndexRoute =
  mainLayoutDashboardStudentAuthAttendanceIndexImport.update({
    id: '/attendance/',
    path: '/attendance/',
    getParentRoute: () => mainLayoutDashboardStudentAuthRoute,
  } as any)

const mainLayoutDashboardStaffAuthProfileIndexRoute =
  mainLayoutDashboardStaffAuthProfileIndexImport.update({
    id: '/profile/',
    path: '/profile/',
    getParentRoute: () => mainLayoutDashboardStaffAuthRoute,
  } as any)

const mainLayoutDashboardStaffAuthAttendanceIndexRoute =
  mainLayoutDashboardStaffAuthAttendanceIndexImport.update({
    id: '/attendance/',
    path: '/attendance/',
    getParentRoute: () => mainLayoutDashboardStaffAuthRoute,
  } as any)

const mainLayoutDashboardSchoolAuthProfileIndexRoute =
  mainLayoutDashboardSchoolAuthProfileIndexImport.update({
    id: '/profile/',
    path: '/profile/',
    getParentRoute: () => mainLayoutDashboardSchoolAuthRoute,
  } as any)

const mainLayoutDashboardReportAuthViewIndexRoute =
  mainLayoutDashboardReportAuthViewIndexImport.update({
    id: '/view/',
    path: '/view/',
    getParentRoute: () => mainLayoutDashboardReportAuthRoute,
  } as any)

const mainLayoutDashboardReportAuthTemplateIndexRoute =
  mainLayoutDashboardReportAuthTemplateIndexImport.update({
    id: '/template/',
    path: '/template/',
    getParentRoute: () => mainLayoutDashboardReportAuthRoute,
  } as any)

const mainLayoutDashboardReportAuthPlaygroundIndexRoute =
  mainLayoutDashboardReportAuthPlaygroundIndexImport.update({
    id: '/playground/',
    path: '/playground/',
    getParentRoute: () => mainLayoutDashboardReportAuthRoute,
  } as any)

const mainLayoutDashboardReportAuthListIndexRoute =
  mainLayoutDashboardReportAuthListIndexImport.update({
    id: '/list/',
    path: '/list/',
    getParentRoute: () => mainLayoutDashboardReportAuthRoute,
  } as any)

const mainLayoutDashboardReportAuthFillingIndexRoute =
  mainLayoutDashboardReportAuthFillingIndexImport.update({
    id: '/filling/',
    path: '/filling/',
    getParentRoute: () => mainLayoutDashboardReportAuthRoute,
  } as any)

const mainLayoutDashboardInformationAuthAnnouncementIndexRoute =
  mainLayoutDashboardInformationAuthAnnouncementIndexImport.update({
    id: '/announcement/',
    path: '/announcement/',
    getParentRoute: () => mainLayoutDashboardInformationAuthRoute,
  } as any)

const mainLayoutDashboardFinanceAuthInvoiceIndexRoute =
  mainLayoutDashboardFinanceAuthInvoiceIndexImport.update({
    id: '/invoice/',
    path: '/invoice/',
    getParentRoute: () => mainLayoutDashboardFinanceAuthRoute,
  } as any)

const mainLayoutDashboardAcademicAuthTeacherIndexRoute =
  mainLayoutDashboardAcademicAuthTeacherIndexImport.update({
    id: '/teacher/',
    path: '/teacher/',
    getParentRoute: () => mainLayoutDashboardAcademicAuthRoute,
  } as any)

const mainLayoutDashboardAcademicAuthStudyProgramIndexRoute =
  mainLayoutDashboardAcademicAuthStudyProgramIndexImport.update({
    id: '/study-program/',
    path: '/study-program/',
    getParentRoute: () => mainLayoutDashboardAcademicAuthRoute,
  } as any)

const mainLayoutDashboardAcademicAuthScheduleIndexRoute =
  mainLayoutDashboardAcademicAuthScheduleIndexImport.update({
    id: '/schedule/',
    path: '/schedule/',
    getParentRoute: () => mainLayoutDashboardAcademicAuthRoute,
  } as any)

const mainLayoutDashboardAcademicAuthPeriodIndexRoute =
  mainLayoutDashboardAcademicAuthPeriodIndexImport.update({
    id: '/period/',
    path: '/period/',
    getParentRoute: () => mainLayoutDashboardAcademicAuthRoute,
  } as any)

const mainLayoutDashboardAcademicAuthExtracurricularIndexRoute =
  mainLayoutDashboardAcademicAuthExtracurricularIndexImport.update({
    id: '/extracurricular/',
    path: '/extracurricular/',
    getParentRoute: () => mainLayoutDashboardAcademicAuthRoute,
  } as any)

const mainLayoutDashboardAcademicAuthCurriculumIndexRoute =
  mainLayoutDashboardAcademicAuthCurriculumIndexImport.update({
    id: '/curriculum/',
    path: '/curriculum/',
    getParentRoute: () => mainLayoutDashboardAcademicAuthRoute,
  } as any)

const mainLayoutDashboardAcademicAuthClassIndexRoute =
  mainLayoutDashboardAcademicAuthClassIndexImport.update({
    id: '/class/',
    path: '/class/',
    getParentRoute: () => mainLayoutDashboardAcademicAuthRoute,
  } as any)

const mainLayoutDashboardStudentAuthProfileStudentusernameIndexRoute =
  mainLayoutDashboardStudentAuthProfileStudentusernameIndexImport.update({
    id: '/profile/$student_username/',
    path: '/profile/$student_username/',
    getParentRoute: () => mainLayoutDashboardStudentAuthRoute,
  } as any)

const mainLayoutDashboardStudentAuthGuardianGuardianusernameIndexRoute =
  mainLayoutDashboardStudentAuthGuardianGuardianusernameIndexImport.update({
    id: '/guardian/$guardian_username/',
    path: '/guardian/$guardian_username/',
    getParentRoute: () => mainLayoutDashboardStudentAuthRoute,
  } as any)

const mainLayoutDashboardStaffAuthProfileStaffusernameIndexRoute =
  mainLayoutDashboardStaffAuthProfileStaffusernameIndexImport.update({
    id: '/profile/$staff_username/',
    path: '/profile/$staff_username/',
    getParentRoute: () => mainLayoutDashboardStaffAuthRoute,
  } as any)

const mainLayoutDashboardReportAuthViewReportidIndexRoute =
  mainLayoutDashboardReportAuthViewReportidIndexImport.update({
    id: '/view/$report_id/',
    path: '/view/$report_id/',
    getParentRoute: () => mainLayoutDashboardReportAuthRoute,
  } as any)

const mainLayoutDashboardReportAuthListNewIndexRoute =
  mainLayoutDashboardReportAuthListNewIndexImport.update({
    id: '/list/new/',
    path: '/list/new/',
    getParentRoute: () => mainLayoutDashboardReportAuthRoute,
  } as any)

const mainLayoutDashboardReportAuthFillingReportidIndexRoute =
  mainLayoutDashboardReportAuthFillingReportidIndexImport.update({
    id: '/filling/$report_id/',
    path: '/filling/$report_id/',
    getParentRoute: () => mainLayoutDashboardReportAuthRoute,
  } as any)

const mainLayoutDashboardReportAuthListReportidEditIndexRoute =
  mainLayoutDashboardReportAuthListReportidEditIndexImport.update({
    id: '/list/$report_id/edit/',
    path: '/list/$report_id/edit/',
    getParentRoute: () => mainLayoutDashboardReportAuthRoute,
  } as any)

// Populate the FileRoutesByPath interface

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexImport
      parentRoute: typeof rootRoute
    }
    '/(main)': {
      id: '/(main)'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof mainImport
      parentRoute: typeof rootRoute
    }
    '/(main)/_layout': {
      id: '/(main)/_layout'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof mainLayoutImport
      parentRoute: typeof mainRoute
    }
    '/(auth)/signin/': {
      id: '/(auth)/signin/'
      path: '/signin'
      fullPath: '/signin'
      preLoaderRoute: typeof authSigninIndexImport
      parentRoute: typeof rootRoute
    }
    '/(main)/_layout/dashboard/': {
      id: '/(main)/_layout/dashboard/'
      path: '/dashboard'
      fullPath: '/dashboard'
      preLoaderRoute: typeof mainLayoutDashboardIndexImport
      parentRoute: typeof mainLayoutImport
    }
    '/(main)/_layout/dashboard/academic': {
      id: '/(main)/_layout/dashboard/academic'
      path: '/dashboard/academic'
      fullPath: '/dashboard/academic'
      preLoaderRoute: typeof mainLayoutDashboardAcademicImport
      parentRoute: typeof mainLayoutImport
    }
    '/(main)/_layout/dashboard/academic/_auth': {
      id: '/(main)/_layout/dashboard/academic/_auth'
      path: '/dashboard/academic'
      fullPath: '/dashboard/academic'
      preLoaderRoute: typeof mainLayoutDashboardAcademicAuthImport
      parentRoute: typeof mainLayoutDashboardAcademicRoute
    }
    '/(main)/_layout/dashboard/finance': {
      id: '/(main)/_layout/dashboard/finance'
      path: '/dashboard/finance'
      fullPath: '/dashboard/finance'
      preLoaderRoute: typeof mainLayoutDashboardFinanceImport
      parentRoute: typeof mainLayoutImport
    }
    '/(main)/_layout/dashboard/finance/_auth': {
      id: '/(main)/_layout/dashboard/finance/_auth'
      path: '/dashboard/finance'
      fullPath: '/dashboard/finance'
      preLoaderRoute: typeof mainLayoutDashboardFinanceAuthImport
      parentRoute: typeof mainLayoutDashboardFinanceRoute
    }
    '/(main)/_layout/dashboard/information': {
      id: '/(main)/_layout/dashboard/information'
      path: '/dashboard/information'
      fullPath: '/dashboard/information'
      preLoaderRoute: typeof mainLayoutDashboardInformationImport
      parentRoute: typeof mainLayoutImport
    }
    '/(main)/_layout/dashboard/information/_auth': {
      id: '/(main)/_layout/dashboard/information/_auth'
      path: '/dashboard/information'
      fullPath: '/dashboard/information'
      preLoaderRoute: typeof mainLayoutDashboardInformationAuthImport
      parentRoute: typeof mainLayoutDashboardInformationRoute
    }
    '/(main)/_layout/dashboard/report': {
      id: '/(main)/_layout/dashboard/report'
      path: '/dashboard/report'
      fullPath: '/dashboard/report'
      preLoaderRoute: typeof mainLayoutDashboardReportImport
      parentRoute: typeof mainLayoutImport
    }
    '/(main)/_layout/dashboard/report/_auth': {
      id: '/(main)/_layout/dashboard/report/_auth'
      path: '/dashboard/report'
      fullPath: '/dashboard/report'
      preLoaderRoute: typeof mainLayoutDashboardReportAuthImport
      parentRoute: typeof mainLayoutDashboardReportRoute
    }
    '/(main)/_layout/dashboard/school': {
      id: '/(main)/_layout/dashboard/school'
      path: '/dashboard/school'
      fullPath: '/dashboard/school'
      preLoaderRoute: typeof mainLayoutDashboardSchoolImport
      parentRoute: typeof mainLayoutImport
    }
    '/(main)/_layout/dashboard/school/_auth': {
      id: '/(main)/_layout/dashboard/school/_auth'
      path: '/dashboard/school'
      fullPath: '/dashboard/school'
      preLoaderRoute: typeof mainLayoutDashboardSchoolAuthImport
      parentRoute: typeof mainLayoutDashboardSchoolRoute
    }
    '/(main)/_layout/dashboard/staff': {
      id: '/(main)/_layout/dashboard/staff'
      path: '/dashboard/staff'
      fullPath: '/dashboard/staff'
      preLoaderRoute: typeof mainLayoutDashboardStaffImport
      parentRoute: typeof mainLayoutImport
    }
    '/(main)/_layout/dashboard/staff/_auth': {
      id: '/(main)/_layout/dashboard/staff/_auth'
      path: '/dashboard/staff'
      fullPath: '/dashboard/staff'
      preLoaderRoute: typeof mainLayoutDashboardStaffAuthImport
      parentRoute: typeof mainLayoutDashboardStaffRoute
    }
    '/(main)/_layout/dashboard/student': {
      id: '/(main)/_layout/dashboard/student'
      path: '/dashboard/student'
      fullPath: '/dashboard/student'
      preLoaderRoute: typeof mainLayoutDashboardStudentImport
      parentRoute: typeof mainLayoutImport
    }
    '/(main)/_layout/dashboard/student/_auth': {
      id: '/(main)/_layout/dashboard/student/_auth'
      path: '/dashboard/student'
      fullPath: '/dashboard/student'
      preLoaderRoute: typeof mainLayoutDashboardStudentAuthImport
      parentRoute: typeof mainLayoutDashboardStudentRoute
    }
    '/(main)/_layout/dashboard/academic/_auth/': {
      id: '/(main)/_layout/dashboard/academic/_auth/'
      path: '/'
      fullPath: '/dashboard/academic/'
      preLoaderRoute: typeof mainLayoutDashboardAcademicAuthIndexImport
      parentRoute: typeof mainLayoutDashboardAcademicAuthImport
    }
    '/(main)/_layout/dashboard/finance/_auth/': {
      id: '/(main)/_layout/dashboard/finance/_auth/'
      path: '/'
      fullPath: '/dashboard/finance/'
      preLoaderRoute: typeof mainLayoutDashboardFinanceAuthIndexImport
      parentRoute: typeof mainLayoutDashboardFinanceAuthImport
    }
    '/(main)/_layout/dashboard/information/_auth/': {
      id: '/(main)/_layout/dashboard/information/_auth/'
      path: '/'
      fullPath: '/dashboard/information/'
      preLoaderRoute: typeof mainLayoutDashboardInformationAuthIndexImport
      parentRoute: typeof mainLayoutDashboardInformationAuthImport
    }
    '/(main)/_layout/dashboard/report/_auth/': {
      id: '/(main)/_layout/dashboard/report/_auth/'
      path: '/'
      fullPath: '/dashboard/report/'
      preLoaderRoute: typeof mainLayoutDashboardReportAuthIndexImport
      parentRoute: typeof mainLayoutDashboardReportAuthImport
    }
    '/(main)/_layout/dashboard/school/_auth/': {
      id: '/(main)/_layout/dashboard/school/_auth/'
      path: '/'
      fullPath: '/dashboard/school/'
      preLoaderRoute: typeof mainLayoutDashboardSchoolAuthIndexImport
      parentRoute: typeof mainLayoutDashboardSchoolAuthImport
    }
    '/(main)/_layout/dashboard/staff/_auth/': {
      id: '/(main)/_layout/dashboard/staff/_auth/'
      path: '/'
      fullPath: '/dashboard/staff/'
      preLoaderRoute: typeof mainLayoutDashboardStaffAuthIndexImport
      parentRoute: typeof mainLayoutDashboardStaffAuthImport
    }
    '/(main)/_layout/dashboard/student/_auth/': {
      id: '/(main)/_layout/dashboard/student/_auth/'
      path: '/'
      fullPath: '/dashboard/student/'
      preLoaderRoute: typeof mainLayoutDashboardStudentAuthIndexImport
      parentRoute: typeof mainLayoutDashboardStudentAuthImport
    }
    '/(main)/_layout/dashboard/academic/_auth/class/': {
      id: '/(main)/_layout/dashboard/academic/_auth/class/'
      path: '/class'
      fullPath: '/dashboard/academic/class'
      preLoaderRoute: typeof mainLayoutDashboardAcademicAuthClassIndexImport
      parentRoute: typeof mainLayoutDashboardAcademicAuthImport
    }
    '/(main)/_layout/dashboard/academic/_auth/curriculum/': {
      id: '/(main)/_layout/dashboard/academic/_auth/curriculum/'
      path: '/curriculum'
      fullPath: '/dashboard/academic/curriculum'
      preLoaderRoute: typeof mainLayoutDashboardAcademicAuthCurriculumIndexImport
      parentRoute: typeof mainLayoutDashboardAcademicAuthImport
    }
    '/(main)/_layout/dashboard/academic/_auth/extracurricular/': {
      id: '/(main)/_layout/dashboard/academic/_auth/extracurricular/'
      path: '/extracurricular'
      fullPath: '/dashboard/academic/extracurricular'
      preLoaderRoute: typeof mainLayoutDashboardAcademicAuthExtracurricularIndexImport
      parentRoute: typeof mainLayoutDashboardAcademicAuthImport
    }
    '/(main)/_layout/dashboard/academic/_auth/period/': {
      id: '/(main)/_layout/dashboard/academic/_auth/period/'
      path: '/period'
      fullPath: '/dashboard/academic/period'
      preLoaderRoute: typeof mainLayoutDashboardAcademicAuthPeriodIndexImport
      parentRoute: typeof mainLayoutDashboardAcademicAuthImport
    }
    '/(main)/_layout/dashboard/academic/_auth/schedule/': {
      id: '/(main)/_layout/dashboard/academic/_auth/schedule/'
      path: '/schedule'
      fullPath: '/dashboard/academic/schedule'
      preLoaderRoute: typeof mainLayoutDashboardAcademicAuthScheduleIndexImport
      parentRoute: typeof mainLayoutDashboardAcademicAuthImport
    }
    '/(main)/_layout/dashboard/academic/_auth/study-program/': {
      id: '/(main)/_layout/dashboard/academic/_auth/study-program/'
      path: '/study-program'
      fullPath: '/dashboard/academic/study-program'
      preLoaderRoute: typeof mainLayoutDashboardAcademicAuthStudyProgramIndexImport
      parentRoute: typeof mainLayoutDashboardAcademicAuthImport
    }
    '/(main)/_layout/dashboard/academic/_auth/teacher/': {
      id: '/(main)/_layout/dashboard/academic/_auth/teacher/'
      path: '/teacher'
      fullPath: '/dashboard/academic/teacher'
      preLoaderRoute: typeof mainLayoutDashboardAcademicAuthTeacherIndexImport
      parentRoute: typeof mainLayoutDashboardAcademicAuthImport
    }
    '/(main)/_layout/dashboard/finance/_auth/invoice/': {
      id: '/(main)/_layout/dashboard/finance/_auth/invoice/'
      path: '/invoice'
      fullPath: '/dashboard/finance/invoice'
      preLoaderRoute: typeof mainLayoutDashboardFinanceAuthInvoiceIndexImport
      parentRoute: typeof mainLayoutDashboardFinanceAuthImport
    }
    '/(main)/_layout/dashboard/information/_auth/announcement/': {
      id: '/(main)/_layout/dashboard/information/_auth/announcement/'
      path: '/announcement'
      fullPath: '/dashboard/information/announcement'
      preLoaderRoute: typeof mainLayoutDashboardInformationAuthAnnouncementIndexImport
      parentRoute: typeof mainLayoutDashboardInformationAuthImport
    }
    '/(main)/_layout/dashboard/report/_auth/filling/': {
      id: '/(main)/_layout/dashboard/report/_auth/filling/'
      path: '/filling'
      fullPath: '/dashboard/report/filling'
      preLoaderRoute: typeof mainLayoutDashboardReportAuthFillingIndexImport
      parentRoute: typeof mainLayoutDashboardReportAuthImport
    }
    '/(main)/_layout/dashboard/report/_auth/list/': {
      id: '/(main)/_layout/dashboard/report/_auth/list/'
      path: '/list'
      fullPath: '/dashboard/report/list'
      preLoaderRoute: typeof mainLayoutDashboardReportAuthListIndexImport
      parentRoute: typeof mainLayoutDashboardReportAuthImport
    }
    '/(main)/_layout/dashboard/report/_auth/playground/': {
      id: '/(main)/_layout/dashboard/report/_auth/playground/'
      path: '/playground'
      fullPath: '/dashboard/report/playground'
      preLoaderRoute: typeof mainLayoutDashboardReportAuthPlaygroundIndexImport
      parentRoute: typeof mainLayoutDashboardReportAuthImport
    }
    '/(main)/_layout/dashboard/report/_auth/template/': {
      id: '/(main)/_layout/dashboard/report/_auth/template/'
      path: '/template'
      fullPath: '/dashboard/report/template'
      preLoaderRoute: typeof mainLayoutDashboardReportAuthTemplateIndexImport
      parentRoute: typeof mainLayoutDashboardReportAuthImport
    }
    '/(main)/_layout/dashboard/report/_auth/view/': {
      id: '/(main)/_layout/dashboard/report/_auth/view/'
      path: '/view'
      fullPath: '/dashboard/report/view'
      preLoaderRoute: typeof mainLayoutDashboardReportAuthViewIndexImport
      parentRoute: typeof mainLayoutDashboardReportAuthImport
    }
    '/(main)/_layout/dashboard/school/_auth/profile/': {
      id: '/(main)/_layout/dashboard/school/_auth/profile/'
      path: '/profile'
      fullPath: '/dashboard/school/profile'
      preLoaderRoute: typeof mainLayoutDashboardSchoolAuthProfileIndexImport
      parentRoute: typeof mainLayoutDashboardSchoolAuthImport
    }
    '/(main)/_layout/dashboard/staff/_auth/attendance/': {
      id: '/(main)/_layout/dashboard/staff/_auth/attendance/'
      path: '/attendance'
      fullPath: '/dashboard/staff/attendance'
      preLoaderRoute: typeof mainLayoutDashboardStaffAuthAttendanceIndexImport
      parentRoute: typeof mainLayoutDashboardStaffAuthImport
    }
    '/(main)/_layout/dashboard/staff/_auth/profile/': {
      id: '/(main)/_layout/dashboard/staff/_auth/profile/'
      path: '/profile'
      fullPath: '/dashboard/staff/profile'
      preLoaderRoute: typeof mainLayoutDashboardStaffAuthProfileIndexImport
      parentRoute: typeof mainLayoutDashboardStaffAuthImport
    }
    '/(main)/_layout/dashboard/student/_auth/attendance/': {
      id: '/(main)/_layout/dashboard/student/_auth/attendance/'
      path: '/attendance'
      fullPath: '/dashboard/student/attendance'
      preLoaderRoute: typeof mainLayoutDashboardStudentAuthAttendanceIndexImport
      parentRoute: typeof mainLayoutDashboardStudentAuthImport
    }
    '/(main)/_layout/dashboard/student/_auth/guardian/': {
      id: '/(main)/_layout/dashboard/student/_auth/guardian/'
      path: '/guardian'
      fullPath: '/dashboard/student/guardian'
      preLoaderRoute: typeof mainLayoutDashboardStudentAuthGuardianIndexImport
      parentRoute: typeof mainLayoutDashboardStudentAuthImport
    }
    '/(main)/_layout/dashboard/student/_auth/profile/': {
      id: '/(main)/_layout/dashboard/student/_auth/profile/'
      path: '/profile'
      fullPath: '/dashboard/student/profile'
      preLoaderRoute: typeof mainLayoutDashboardStudentAuthProfileIndexImport
      parentRoute: typeof mainLayoutDashboardStudentAuthImport
    }
    '/(main)/_layout/dashboard/report/_auth/filling/$report_id/': {
      id: '/(main)/_layout/dashboard/report/_auth/filling/$report_id/'
      path: '/filling/$report_id'
      fullPath: '/dashboard/report/filling/$report_id'
      preLoaderRoute: typeof mainLayoutDashboardReportAuthFillingReportidIndexImport
      parentRoute: typeof mainLayoutDashboardReportAuthImport
    }
    '/(main)/_layout/dashboard/report/_auth/list/new/': {
      id: '/(main)/_layout/dashboard/report/_auth/list/new/'
      path: '/list/new'
      fullPath: '/dashboard/report/list/new'
      preLoaderRoute: typeof mainLayoutDashboardReportAuthListNewIndexImport
      parentRoute: typeof mainLayoutDashboardReportAuthImport
    }
    '/(main)/_layout/dashboard/report/_auth/view/$report_id/': {
      id: '/(main)/_layout/dashboard/report/_auth/view/$report_id/'
      path: '/view/$report_id'
      fullPath: '/dashboard/report/view/$report_id'
      preLoaderRoute: typeof mainLayoutDashboardReportAuthViewReportidIndexImport
      parentRoute: typeof mainLayoutDashboardReportAuthImport
    }
    '/(main)/_layout/dashboard/staff/_auth/profile/$staff_username/': {
      id: '/(main)/_layout/dashboard/staff/_auth/profile/$staff_username/'
      path: '/profile/$staff_username'
      fullPath: '/dashboard/staff/profile/$staff_username'
      preLoaderRoute: typeof mainLayoutDashboardStaffAuthProfileStaffusernameIndexImport
      parentRoute: typeof mainLayoutDashboardStaffAuthImport
    }
    '/(main)/_layout/dashboard/student/_auth/guardian/$guardian_username/': {
      id: '/(main)/_layout/dashboard/student/_auth/guardian/$guardian_username/'
      path: '/guardian/$guardian_username'
      fullPath: '/dashboard/student/guardian/$guardian_username'
      preLoaderRoute: typeof mainLayoutDashboardStudentAuthGuardianGuardianusernameIndexImport
      parentRoute: typeof mainLayoutDashboardStudentAuthImport
    }
    '/(main)/_layout/dashboard/student/_auth/profile/$student_username/': {
      id: '/(main)/_layout/dashboard/student/_auth/profile/$student_username/'
      path: '/profile/$student_username'
      fullPath: '/dashboard/student/profile/$student_username'
      preLoaderRoute: typeof mainLayoutDashboardStudentAuthProfileStudentusernameIndexImport
      parentRoute: typeof mainLayoutDashboardStudentAuthImport
    }
    '/(main)/_layout/dashboard/report/_auth/list/$report_id/edit/': {
      id: '/(main)/_layout/dashboard/report/_auth/list/$report_id/edit/'
      path: '/list/$report_id/edit'
      fullPath: '/dashboard/report/list/$report_id/edit'
      preLoaderRoute: typeof mainLayoutDashboardReportAuthListReportidEditIndexImport
      parentRoute: typeof mainLayoutDashboardReportAuthImport
    }
  }
}

// Create and export the route tree

interface mainLayoutDashboardAcademicAuthRouteChildren {
  mainLayoutDashboardAcademicAuthIndexRoute: typeof mainLayoutDashboardAcademicAuthIndexRoute
  mainLayoutDashboardAcademicAuthClassIndexRoute: typeof mainLayoutDashboardAcademicAuthClassIndexRoute
  mainLayoutDashboardAcademicAuthCurriculumIndexRoute: typeof mainLayoutDashboardAcademicAuthCurriculumIndexRoute
  mainLayoutDashboardAcademicAuthExtracurricularIndexRoute: typeof mainLayoutDashboardAcademicAuthExtracurricularIndexRoute
  mainLayoutDashboardAcademicAuthPeriodIndexRoute: typeof mainLayoutDashboardAcademicAuthPeriodIndexRoute
  mainLayoutDashboardAcademicAuthScheduleIndexRoute: typeof mainLayoutDashboardAcademicAuthScheduleIndexRoute
  mainLayoutDashboardAcademicAuthStudyProgramIndexRoute: typeof mainLayoutDashboardAcademicAuthStudyProgramIndexRoute
  mainLayoutDashboardAcademicAuthTeacherIndexRoute: typeof mainLayoutDashboardAcademicAuthTeacherIndexRoute
}

const mainLayoutDashboardAcademicAuthRouteChildren: mainLayoutDashboardAcademicAuthRouteChildren =
  {
    mainLayoutDashboardAcademicAuthIndexRoute:
      mainLayoutDashboardAcademicAuthIndexRoute,
    mainLayoutDashboardAcademicAuthClassIndexRoute:
      mainLayoutDashboardAcademicAuthClassIndexRoute,
    mainLayoutDashboardAcademicAuthCurriculumIndexRoute:
      mainLayoutDashboardAcademicAuthCurriculumIndexRoute,
    mainLayoutDashboardAcademicAuthExtracurricularIndexRoute:
      mainLayoutDashboardAcademicAuthExtracurricularIndexRoute,
    mainLayoutDashboardAcademicAuthPeriodIndexRoute:
      mainLayoutDashboardAcademicAuthPeriodIndexRoute,
    mainLayoutDashboardAcademicAuthScheduleIndexRoute:
      mainLayoutDashboardAcademicAuthScheduleIndexRoute,
    mainLayoutDashboardAcademicAuthStudyProgramIndexRoute:
      mainLayoutDashboardAcademicAuthStudyProgramIndexRoute,
    mainLayoutDashboardAcademicAuthTeacherIndexRoute:
      mainLayoutDashboardAcademicAuthTeacherIndexRoute,
  }

const mainLayoutDashboardAcademicAuthRouteWithChildren =
  mainLayoutDashboardAcademicAuthRoute._addFileChildren(
    mainLayoutDashboardAcademicAuthRouteChildren,
  )

interface mainLayoutDashboardAcademicRouteChildren {
  mainLayoutDashboardAcademicAuthRoute: typeof mainLayoutDashboardAcademicAuthRouteWithChildren
}

const mainLayoutDashboardAcademicRouteChildren: mainLayoutDashboardAcademicRouteChildren =
  {
    mainLayoutDashboardAcademicAuthRoute:
      mainLayoutDashboardAcademicAuthRouteWithChildren,
  }

const mainLayoutDashboardAcademicRouteWithChildren =
  mainLayoutDashboardAcademicRoute._addFileChildren(
    mainLayoutDashboardAcademicRouteChildren,
  )

interface mainLayoutDashboardFinanceAuthRouteChildren {
  mainLayoutDashboardFinanceAuthIndexRoute: typeof mainLayoutDashboardFinanceAuthIndexRoute
  mainLayoutDashboardFinanceAuthInvoiceIndexRoute: typeof mainLayoutDashboardFinanceAuthInvoiceIndexRoute
}

const mainLayoutDashboardFinanceAuthRouteChildren: mainLayoutDashboardFinanceAuthRouteChildren =
  {
    mainLayoutDashboardFinanceAuthIndexRoute:
      mainLayoutDashboardFinanceAuthIndexRoute,
    mainLayoutDashboardFinanceAuthInvoiceIndexRoute:
      mainLayoutDashboardFinanceAuthInvoiceIndexRoute,
  }

const mainLayoutDashboardFinanceAuthRouteWithChildren =
  mainLayoutDashboardFinanceAuthRoute._addFileChildren(
    mainLayoutDashboardFinanceAuthRouteChildren,
  )

interface mainLayoutDashboardFinanceRouteChildren {
  mainLayoutDashboardFinanceAuthRoute: typeof mainLayoutDashboardFinanceAuthRouteWithChildren
}

const mainLayoutDashboardFinanceRouteChildren: mainLayoutDashboardFinanceRouteChildren =
  {
    mainLayoutDashboardFinanceAuthRoute:
      mainLayoutDashboardFinanceAuthRouteWithChildren,
  }

const mainLayoutDashboardFinanceRouteWithChildren =
  mainLayoutDashboardFinanceRoute._addFileChildren(
    mainLayoutDashboardFinanceRouteChildren,
  )

interface mainLayoutDashboardInformationAuthRouteChildren {
  mainLayoutDashboardInformationAuthIndexRoute: typeof mainLayoutDashboardInformationAuthIndexRoute
  mainLayoutDashboardInformationAuthAnnouncementIndexRoute: typeof mainLayoutDashboardInformationAuthAnnouncementIndexRoute
}

const mainLayoutDashboardInformationAuthRouteChildren: mainLayoutDashboardInformationAuthRouteChildren =
  {
    mainLayoutDashboardInformationAuthIndexRoute:
      mainLayoutDashboardInformationAuthIndexRoute,
    mainLayoutDashboardInformationAuthAnnouncementIndexRoute:
      mainLayoutDashboardInformationAuthAnnouncementIndexRoute,
  }

const mainLayoutDashboardInformationAuthRouteWithChildren =
  mainLayoutDashboardInformationAuthRoute._addFileChildren(
    mainLayoutDashboardInformationAuthRouteChildren,
  )

interface mainLayoutDashboardInformationRouteChildren {
  mainLayoutDashboardInformationAuthRoute: typeof mainLayoutDashboardInformationAuthRouteWithChildren
}

const mainLayoutDashboardInformationRouteChildren: mainLayoutDashboardInformationRouteChildren =
  {
    mainLayoutDashboardInformationAuthRoute:
      mainLayoutDashboardInformationAuthRouteWithChildren,
  }

const mainLayoutDashboardInformationRouteWithChildren =
  mainLayoutDashboardInformationRoute._addFileChildren(
    mainLayoutDashboardInformationRouteChildren,
  )

interface mainLayoutDashboardReportAuthRouteChildren {
  mainLayoutDashboardReportAuthIndexRoute: typeof mainLayoutDashboardReportAuthIndexRoute
  mainLayoutDashboardReportAuthFillingIndexRoute: typeof mainLayoutDashboardReportAuthFillingIndexRoute
  mainLayoutDashboardReportAuthListIndexRoute: typeof mainLayoutDashboardReportAuthListIndexRoute
  mainLayoutDashboardReportAuthPlaygroundIndexRoute: typeof mainLayoutDashboardReportAuthPlaygroundIndexRoute
  mainLayoutDashboardReportAuthTemplateIndexRoute: typeof mainLayoutDashboardReportAuthTemplateIndexRoute
  mainLayoutDashboardReportAuthViewIndexRoute: typeof mainLayoutDashboardReportAuthViewIndexRoute
  mainLayoutDashboardReportAuthFillingReportidIndexRoute: typeof mainLayoutDashboardReportAuthFillingReportidIndexRoute
  mainLayoutDashboardReportAuthListNewIndexRoute: typeof mainLayoutDashboardReportAuthListNewIndexRoute
  mainLayoutDashboardReportAuthViewReportidIndexRoute: typeof mainLayoutDashboardReportAuthViewReportidIndexRoute
  mainLayoutDashboardReportAuthListReportidEditIndexRoute: typeof mainLayoutDashboardReportAuthListReportidEditIndexRoute
}

const mainLayoutDashboardReportAuthRouteChildren: mainLayoutDashboardReportAuthRouteChildren =
  {
    mainLayoutDashboardReportAuthIndexRoute:
      mainLayoutDashboardReportAuthIndexRoute,
    mainLayoutDashboardReportAuthFillingIndexRoute:
      mainLayoutDashboardReportAuthFillingIndexRoute,
    mainLayoutDashboardReportAuthListIndexRoute:
      mainLayoutDashboardReportAuthListIndexRoute,
    mainLayoutDashboardReportAuthPlaygroundIndexRoute:
      mainLayoutDashboardReportAuthPlaygroundIndexRoute,
    mainLayoutDashboardReportAuthTemplateIndexRoute:
      mainLayoutDashboardReportAuthTemplateIndexRoute,
    mainLayoutDashboardReportAuthViewIndexRoute:
      mainLayoutDashboardReportAuthViewIndexRoute,
    mainLayoutDashboardReportAuthFillingReportidIndexRoute:
      mainLayoutDashboardReportAuthFillingReportidIndexRoute,
    mainLayoutDashboardReportAuthListNewIndexRoute:
      mainLayoutDashboardReportAuthListNewIndexRoute,
    mainLayoutDashboardReportAuthViewReportidIndexRoute:
      mainLayoutDashboardReportAuthViewReportidIndexRoute,
    mainLayoutDashboardReportAuthListReportidEditIndexRoute:
      mainLayoutDashboardReportAuthListReportidEditIndexRoute,
  }

const mainLayoutDashboardReportAuthRouteWithChildren =
  mainLayoutDashboardReportAuthRoute._addFileChildren(
    mainLayoutDashboardReportAuthRouteChildren,
  )

interface mainLayoutDashboardReportRouteChildren {
  mainLayoutDashboardReportAuthRoute: typeof mainLayoutDashboardReportAuthRouteWithChildren
}

const mainLayoutDashboardReportRouteChildren: mainLayoutDashboardReportRouteChildren =
  {
    mainLayoutDashboardReportAuthRoute:
      mainLayoutDashboardReportAuthRouteWithChildren,
  }

const mainLayoutDashboardReportRouteWithChildren =
  mainLayoutDashboardReportRoute._addFileChildren(
    mainLayoutDashboardReportRouteChildren,
  )

interface mainLayoutDashboardSchoolAuthRouteChildren {
  mainLayoutDashboardSchoolAuthIndexRoute: typeof mainLayoutDashboardSchoolAuthIndexRoute
  mainLayoutDashboardSchoolAuthProfileIndexRoute: typeof mainLayoutDashboardSchoolAuthProfileIndexRoute
}

const mainLayoutDashboardSchoolAuthRouteChildren: mainLayoutDashboardSchoolAuthRouteChildren =
  {
    mainLayoutDashboardSchoolAuthIndexRoute:
      mainLayoutDashboardSchoolAuthIndexRoute,
    mainLayoutDashboardSchoolAuthProfileIndexRoute:
      mainLayoutDashboardSchoolAuthProfileIndexRoute,
  }

const mainLayoutDashboardSchoolAuthRouteWithChildren =
  mainLayoutDashboardSchoolAuthRoute._addFileChildren(
    mainLayoutDashboardSchoolAuthRouteChildren,
  )

interface mainLayoutDashboardSchoolRouteChildren {
  mainLayoutDashboardSchoolAuthRoute: typeof mainLayoutDashboardSchoolAuthRouteWithChildren
}

const mainLayoutDashboardSchoolRouteChildren: mainLayoutDashboardSchoolRouteChildren =
  {
    mainLayoutDashboardSchoolAuthRoute:
      mainLayoutDashboardSchoolAuthRouteWithChildren,
  }

const mainLayoutDashboardSchoolRouteWithChildren =
  mainLayoutDashboardSchoolRoute._addFileChildren(
    mainLayoutDashboardSchoolRouteChildren,
  )

interface mainLayoutDashboardStaffAuthRouteChildren {
  mainLayoutDashboardStaffAuthIndexRoute: typeof mainLayoutDashboardStaffAuthIndexRoute
  mainLayoutDashboardStaffAuthAttendanceIndexRoute: typeof mainLayoutDashboardStaffAuthAttendanceIndexRoute
  mainLayoutDashboardStaffAuthProfileIndexRoute: typeof mainLayoutDashboardStaffAuthProfileIndexRoute
  mainLayoutDashboardStaffAuthProfileStaffusernameIndexRoute: typeof mainLayoutDashboardStaffAuthProfileStaffusernameIndexRoute
}

const mainLayoutDashboardStaffAuthRouteChildren: mainLayoutDashboardStaffAuthRouteChildren =
  {
    mainLayoutDashboardStaffAuthIndexRoute:
      mainLayoutDashboardStaffAuthIndexRoute,
    mainLayoutDashboardStaffAuthAttendanceIndexRoute:
      mainLayoutDashboardStaffAuthAttendanceIndexRoute,
    mainLayoutDashboardStaffAuthProfileIndexRoute:
      mainLayoutDashboardStaffAuthProfileIndexRoute,
    mainLayoutDashboardStaffAuthProfileStaffusernameIndexRoute:
      mainLayoutDashboardStaffAuthProfileStaffusernameIndexRoute,
  }

const mainLayoutDashboardStaffAuthRouteWithChildren =
  mainLayoutDashboardStaffAuthRoute._addFileChildren(
    mainLayoutDashboardStaffAuthRouteChildren,
  )

interface mainLayoutDashboardStaffRouteChildren {
  mainLayoutDashboardStaffAuthRoute: typeof mainLayoutDashboardStaffAuthRouteWithChildren
}

const mainLayoutDashboardStaffRouteChildren: mainLayoutDashboardStaffRouteChildren =
  {
    mainLayoutDashboardStaffAuthRoute:
      mainLayoutDashboardStaffAuthRouteWithChildren,
  }

const mainLayoutDashboardStaffRouteWithChildren =
  mainLayoutDashboardStaffRoute._addFileChildren(
    mainLayoutDashboardStaffRouteChildren,
  )

interface mainLayoutDashboardStudentAuthRouteChildren {
  mainLayoutDashboardStudentAuthIndexRoute: typeof mainLayoutDashboardStudentAuthIndexRoute
  mainLayoutDashboardStudentAuthAttendanceIndexRoute: typeof mainLayoutDashboardStudentAuthAttendanceIndexRoute
  mainLayoutDashboardStudentAuthGuardianIndexRoute: typeof mainLayoutDashboardStudentAuthGuardianIndexRoute
  mainLayoutDashboardStudentAuthProfileIndexRoute: typeof mainLayoutDashboardStudentAuthProfileIndexRoute
  mainLayoutDashboardStudentAuthGuardianGuardianusernameIndexRoute: typeof mainLayoutDashboardStudentAuthGuardianGuardianusernameIndexRoute
  mainLayoutDashboardStudentAuthProfileStudentusernameIndexRoute: typeof mainLayoutDashboardStudentAuthProfileStudentusernameIndexRoute
}

const mainLayoutDashboardStudentAuthRouteChildren: mainLayoutDashboardStudentAuthRouteChildren =
  {
    mainLayoutDashboardStudentAuthIndexRoute:
      mainLayoutDashboardStudentAuthIndexRoute,
    mainLayoutDashboardStudentAuthAttendanceIndexRoute:
      mainLayoutDashboardStudentAuthAttendanceIndexRoute,
    mainLayoutDashboardStudentAuthGuardianIndexRoute:
      mainLayoutDashboardStudentAuthGuardianIndexRoute,
    mainLayoutDashboardStudentAuthProfileIndexRoute:
      mainLayoutDashboardStudentAuthProfileIndexRoute,
    mainLayoutDashboardStudentAuthGuardianGuardianusernameIndexRoute:
      mainLayoutDashboardStudentAuthGuardianGuardianusernameIndexRoute,
    mainLayoutDashboardStudentAuthProfileStudentusernameIndexRoute:
      mainLayoutDashboardStudentAuthProfileStudentusernameIndexRoute,
  }

const mainLayoutDashboardStudentAuthRouteWithChildren =
  mainLayoutDashboardStudentAuthRoute._addFileChildren(
    mainLayoutDashboardStudentAuthRouteChildren,
  )

interface mainLayoutDashboardStudentRouteChildren {
  mainLayoutDashboardStudentAuthRoute: typeof mainLayoutDashboardStudentAuthRouteWithChildren
}

const mainLayoutDashboardStudentRouteChildren: mainLayoutDashboardStudentRouteChildren =
  {
    mainLayoutDashboardStudentAuthRoute:
      mainLayoutDashboardStudentAuthRouteWithChildren,
  }

const mainLayoutDashboardStudentRouteWithChildren =
  mainLayoutDashboardStudentRoute._addFileChildren(
    mainLayoutDashboardStudentRouteChildren,
  )

interface mainLayoutRouteChildren {
  mainLayoutDashboardIndexRoute: typeof mainLayoutDashboardIndexRoute
  mainLayoutDashboardAcademicRoute: typeof mainLayoutDashboardAcademicRouteWithChildren
  mainLayoutDashboardFinanceRoute: typeof mainLayoutDashboardFinanceRouteWithChildren
  mainLayoutDashboardInformationRoute: typeof mainLayoutDashboardInformationRouteWithChildren
  mainLayoutDashboardReportRoute: typeof mainLayoutDashboardReportRouteWithChildren
  mainLayoutDashboardSchoolRoute: typeof mainLayoutDashboardSchoolRouteWithChildren
  mainLayoutDashboardStaffRoute: typeof mainLayoutDashboardStaffRouteWithChildren
  mainLayoutDashboardStudentRoute: typeof mainLayoutDashboardStudentRouteWithChildren
}

const mainLayoutRouteChildren: mainLayoutRouteChildren = {
  mainLayoutDashboardIndexRoute: mainLayoutDashboardIndexRoute,
  mainLayoutDashboardAcademicRoute:
    mainLayoutDashboardAcademicRouteWithChildren,
  mainLayoutDashboardFinanceRoute: mainLayoutDashboardFinanceRouteWithChildren,
  mainLayoutDashboardInformationRoute:
    mainLayoutDashboardInformationRouteWithChildren,
  mainLayoutDashboardReportRoute: mainLayoutDashboardReportRouteWithChildren,
  mainLayoutDashboardSchoolRoute: mainLayoutDashboardSchoolRouteWithChildren,
  mainLayoutDashboardStaffRoute: mainLayoutDashboardStaffRouteWithChildren,
  mainLayoutDashboardStudentRoute: mainLayoutDashboardStudentRouteWithChildren,
}

const mainLayoutRouteWithChildren = mainLayoutRoute._addFileChildren(
  mainLayoutRouteChildren,
)

interface mainRouteChildren {
  mainLayoutRoute: typeof mainLayoutRouteWithChildren
}

const mainRouteChildren: mainRouteChildren = {
  mainLayoutRoute: mainLayoutRouteWithChildren,
}

const mainRouteWithChildren = mainRoute._addFileChildren(mainRouteChildren)

export interface FileRoutesByFullPath {
  '/': typeof mainLayoutRouteWithChildren
  '/signin': typeof authSigninIndexRoute
  '/dashboard': typeof mainLayoutDashboardIndexRoute
  '/dashboard/academic': typeof mainLayoutDashboardAcademicAuthRouteWithChildren
  '/dashboard/finance': typeof mainLayoutDashboardFinanceAuthRouteWithChildren
  '/dashboard/information': typeof mainLayoutDashboardInformationAuthRouteWithChildren
  '/dashboard/report': typeof mainLayoutDashboardReportAuthRouteWithChildren
  '/dashboard/school': typeof mainLayoutDashboardSchoolAuthRouteWithChildren
  '/dashboard/staff': typeof mainLayoutDashboardStaffAuthRouteWithChildren
  '/dashboard/student': typeof mainLayoutDashboardStudentAuthRouteWithChildren
  '/dashboard/academic/': typeof mainLayoutDashboardAcademicAuthIndexRoute
  '/dashboard/finance/': typeof mainLayoutDashboardFinanceAuthIndexRoute
  '/dashboard/information/': typeof mainLayoutDashboardInformationAuthIndexRoute
  '/dashboard/report/': typeof mainLayoutDashboardReportAuthIndexRoute
  '/dashboard/school/': typeof mainLayoutDashboardSchoolAuthIndexRoute
  '/dashboard/staff/': typeof mainLayoutDashboardStaffAuthIndexRoute
  '/dashboard/student/': typeof mainLayoutDashboardStudentAuthIndexRoute
  '/dashboard/academic/class': typeof mainLayoutDashboardAcademicAuthClassIndexRoute
  '/dashboard/academic/curriculum': typeof mainLayoutDashboardAcademicAuthCurriculumIndexRoute
  '/dashboard/academic/extracurricular': typeof mainLayoutDashboardAcademicAuthExtracurricularIndexRoute
  '/dashboard/academic/period': typeof mainLayoutDashboardAcademicAuthPeriodIndexRoute
  '/dashboard/academic/schedule': typeof mainLayoutDashboardAcademicAuthScheduleIndexRoute
  '/dashboard/academic/study-program': typeof mainLayoutDashboardAcademicAuthStudyProgramIndexRoute
  '/dashboard/academic/teacher': typeof mainLayoutDashboardAcademicAuthTeacherIndexRoute
  '/dashboard/finance/invoice': typeof mainLayoutDashboardFinanceAuthInvoiceIndexRoute
  '/dashboard/information/announcement': typeof mainLayoutDashboardInformationAuthAnnouncementIndexRoute
  '/dashboard/report/filling': typeof mainLayoutDashboardReportAuthFillingIndexRoute
  '/dashboard/report/list': typeof mainLayoutDashboardReportAuthListIndexRoute
  '/dashboard/report/playground': typeof mainLayoutDashboardReportAuthPlaygroundIndexRoute
  '/dashboard/report/template': typeof mainLayoutDashboardReportAuthTemplateIndexRoute
  '/dashboard/report/view': typeof mainLayoutDashboardReportAuthViewIndexRoute
  '/dashboard/school/profile': typeof mainLayoutDashboardSchoolAuthProfileIndexRoute
  '/dashboard/staff/attendance': typeof mainLayoutDashboardStaffAuthAttendanceIndexRoute
  '/dashboard/staff/profile': typeof mainLayoutDashboardStaffAuthProfileIndexRoute
  '/dashboard/student/attendance': typeof mainLayoutDashboardStudentAuthAttendanceIndexRoute
  '/dashboard/student/guardian': typeof mainLayoutDashboardStudentAuthGuardianIndexRoute
  '/dashboard/student/profile': typeof mainLayoutDashboardStudentAuthProfileIndexRoute
  '/dashboard/report/filling/$report_id': typeof mainLayoutDashboardReportAuthFillingReportidIndexRoute
  '/dashboard/report/list/new': typeof mainLayoutDashboardReportAuthListNewIndexRoute
  '/dashboard/report/view/$report_id': typeof mainLayoutDashboardReportAuthViewReportidIndexRoute
  '/dashboard/staff/profile/$staff_username': typeof mainLayoutDashboardStaffAuthProfileStaffusernameIndexRoute
  '/dashboard/student/guardian/$guardian_username': typeof mainLayoutDashboardStudentAuthGuardianGuardianusernameIndexRoute
  '/dashboard/student/profile/$student_username': typeof mainLayoutDashboardStudentAuthProfileStudentusernameIndexRoute
  '/dashboard/report/list/$report_id/edit': typeof mainLayoutDashboardReportAuthListReportidEditIndexRoute
}

export interface FileRoutesByTo {
  '/': typeof mainLayoutRouteWithChildren
  '/signin': typeof authSigninIndexRoute
  '/dashboard': typeof mainLayoutDashboardIndexRoute
  '/dashboard/academic': typeof mainLayoutDashboardAcademicAuthIndexRoute
  '/dashboard/finance': typeof mainLayoutDashboardFinanceAuthIndexRoute
  '/dashboard/information': typeof mainLayoutDashboardInformationAuthIndexRoute
  '/dashboard/report': typeof mainLayoutDashboardReportAuthIndexRoute
  '/dashboard/school': typeof mainLayoutDashboardSchoolAuthIndexRoute
  '/dashboard/staff': typeof mainLayoutDashboardStaffAuthIndexRoute
  '/dashboard/student': typeof mainLayoutDashboardStudentAuthIndexRoute
  '/dashboard/academic/class': typeof mainLayoutDashboardAcademicAuthClassIndexRoute
  '/dashboard/academic/curriculum': typeof mainLayoutDashboardAcademicAuthCurriculumIndexRoute
  '/dashboard/academic/extracurricular': typeof mainLayoutDashboardAcademicAuthExtracurricularIndexRoute
  '/dashboard/academic/period': typeof mainLayoutDashboardAcademicAuthPeriodIndexRoute
  '/dashboard/academic/schedule': typeof mainLayoutDashboardAcademicAuthScheduleIndexRoute
  '/dashboard/academic/study-program': typeof mainLayoutDashboardAcademicAuthStudyProgramIndexRoute
  '/dashboard/academic/teacher': typeof mainLayoutDashboardAcademicAuthTeacherIndexRoute
  '/dashboard/finance/invoice': typeof mainLayoutDashboardFinanceAuthInvoiceIndexRoute
  '/dashboard/information/announcement': typeof mainLayoutDashboardInformationAuthAnnouncementIndexRoute
  '/dashboard/report/filling': typeof mainLayoutDashboardReportAuthFillingIndexRoute
  '/dashboard/report/list': typeof mainLayoutDashboardReportAuthListIndexRoute
  '/dashboard/report/playground': typeof mainLayoutDashboardReportAuthPlaygroundIndexRoute
  '/dashboard/report/template': typeof mainLayoutDashboardReportAuthTemplateIndexRoute
  '/dashboard/report/view': typeof mainLayoutDashboardReportAuthViewIndexRoute
  '/dashboard/school/profile': typeof mainLayoutDashboardSchoolAuthProfileIndexRoute
  '/dashboard/staff/attendance': typeof mainLayoutDashboardStaffAuthAttendanceIndexRoute
  '/dashboard/staff/profile': typeof mainLayoutDashboardStaffAuthProfileIndexRoute
  '/dashboard/student/attendance': typeof mainLayoutDashboardStudentAuthAttendanceIndexRoute
  '/dashboard/student/guardian': typeof mainLayoutDashboardStudentAuthGuardianIndexRoute
  '/dashboard/student/profile': typeof mainLayoutDashboardStudentAuthProfileIndexRoute
  '/dashboard/report/filling/$report_id': typeof mainLayoutDashboardReportAuthFillingReportidIndexRoute
  '/dashboard/report/list/new': typeof mainLayoutDashboardReportAuthListNewIndexRoute
  '/dashboard/report/view/$report_id': typeof mainLayoutDashboardReportAuthViewReportidIndexRoute
  '/dashboard/staff/profile/$staff_username': typeof mainLayoutDashboardStaffAuthProfileStaffusernameIndexRoute
  '/dashboard/student/guardian/$guardian_username': typeof mainLayoutDashboardStudentAuthGuardianGuardianusernameIndexRoute
  '/dashboard/student/profile/$student_username': typeof mainLayoutDashboardStudentAuthProfileStudentusernameIndexRoute
  '/dashboard/report/list/$report_id/edit': typeof mainLayoutDashboardReportAuthListReportidEditIndexRoute
}

export interface FileRoutesById {
  __root__: typeof rootRoute
  '/': typeof IndexRoute
  '/(main)': typeof mainRouteWithChildren
  '/(main)/_layout': typeof mainLayoutRouteWithChildren
  '/(auth)/signin/': typeof authSigninIndexRoute
  '/(main)/_layout/dashboard/': typeof mainLayoutDashboardIndexRoute
  '/(main)/_layout/dashboard/academic': typeof mainLayoutDashboardAcademicRouteWithChildren
  '/(main)/_layout/dashboard/academic/_auth': typeof mainLayoutDashboardAcademicAuthRouteWithChildren
  '/(main)/_layout/dashboard/finance': typeof mainLayoutDashboardFinanceRouteWithChildren
  '/(main)/_layout/dashboard/finance/_auth': typeof mainLayoutDashboardFinanceAuthRouteWithChildren
  '/(main)/_layout/dashboard/information': typeof mainLayoutDashboardInformationRouteWithChildren
  '/(main)/_layout/dashboard/information/_auth': typeof mainLayoutDashboardInformationAuthRouteWithChildren
  '/(main)/_layout/dashboard/report': typeof mainLayoutDashboardReportRouteWithChildren
  '/(main)/_layout/dashboard/report/_auth': typeof mainLayoutDashboardReportAuthRouteWithChildren
  '/(main)/_layout/dashboard/school': typeof mainLayoutDashboardSchoolRouteWithChildren
  '/(main)/_layout/dashboard/school/_auth': typeof mainLayoutDashboardSchoolAuthRouteWithChildren
  '/(main)/_layout/dashboard/staff': typeof mainLayoutDashboardStaffRouteWithChildren
  '/(main)/_layout/dashboard/staff/_auth': typeof mainLayoutDashboardStaffAuthRouteWithChildren
  '/(main)/_layout/dashboard/student': typeof mainLayoutDashboardStudentRouteWithChildren
  '/(main)/_layout/dashboard/student/_auth': typeof mainLayoutDashboardStudentAuthRouteWithChildren
  '/(main)/_layout/dashboard/academic/_auth/': typeof mainLayoutDashboardAcademicAuthIndexRoute
  '/(main)/_layout/dashboard/finance/_auth/': typeof mainLayoutDashboardFinanceAuthIndexRoute
  '/(main)/_layout/dashboard/information/_auth/': typeof mainLayoutDashboardInformationAuthIndexRoute
  '/(main)/_layout/dashboard/report/_auth/': typeof mainLayoutDashboardReportAuthIndexRoute
  '/(main)/_layout/dashboard/school/_auth/': typeof mainLayoutDashboardSchoolAuthIndexRoute
  '/(main)/_layout/dashboard/staff/_auth/': typeof mainLayoutDashboardStaffAuthIndexRoute
  '/(main)/_layout/dashboard/student/_auth/': typeof mainLayoutDashboardStudentAuthIndexRoute
  '/(main)/_layout/dashboard/academic/_auth/class/': typeof mainLayoutDashboardAcademicAuthClassIndexRoute
  '/(main)/_layout/dashboard/academic/_auth/curriculum/': typeof mainLayoutDashboardAcademicAuthCurriculumIndexRoute
  '/(main)/_layout/dashboard/academic/_auth/extracurricular/': typeof mainLayoutDashboardAcademicAuthExtracurricularIndexRoute
  '/(main)/_layout/dashboard/academic/_auth/period/': typeof mainLayoutDashboardAcademicAuthPeriodIndexRoute
  '/(main)/_layout/dashboard/academic/_auth/schedule/': typeof mainLayoutDashboardAcademicAuthScheduleIndexRoute
  '/(main)/_layout/dashboard/academic/_auth/study-program/': typeof mainLayoutDashboardAcademicAuthStudyProgramIndexRoute
  '/(main)/_layout/dashboard/academic/_auth/teacher/': typeof mainLayoutDashboardAcademicAuthTeacherIndexRoute
  '/(main)/_layout/dashboard/finance/_auth/invoice/': typeof mainLayoutDashboardFinanceAuthInvoiceIndexRoute
  '/(main)/_layout/dashboard/information/_auth/announcement/': typeof mainLayoutDashboardInformationAuthAnnouncementIndexRoute
  '/(main)/_layout/dashboard/report/_auth/filling/': typeof mainLayoutDashboardReportAuthFillingIndexRoute
  '/(main)/_layout/dashboard/report/_auth/list/': typeof mainLayoutDashboardReportAuthListIndexRoute
  '/(main)/_layout/dashboard/report/_auth/playground/': typeof mainLayoutDashboardReportAuthPlaygroundIndexRoute
  '/(main)/_layout/dashboard/report/_auth/template/': typeof mainLayoutDashboardReportAuthTemplateIndexRoute
  '/(main)/_layout/dashboard/report/_auth/view/': typeof mainLayoutDashboardReportAuthViewIndexRoute
  '/(main)/_layout/dashboard/school/_auth/profile/': typeof mainLayoutDashboardSchoolAuthProfileIndexRoute
  '/(main)/_layout/dashboard/staff/_auth/attendance/': typeof mainLayoutDashboardStaffAuthAttendanceIndexRoute
  '/(main)/_layout/dashboard/staff/_auth/profile/': typeof mainLayoutDashboardStaffAuthProfileIndexRoute
  '/(main)/_layout/dashboard/student/_auth/attendance/': typeof mainLayoutDashboardStudentAuthAttendanceIndexRoute
  '/(main)/_layout/dashboard/student/_auth/guardian/': typeof mainLayoutDashboardStudentAuthGuardianIndexRoute
  '/(main)/_layout/dashboard/student/_auth/profile/': typeof mainLayoutDashboardStudentAuthProfileIndexRoute
  '/(main)/_layout/dashboard/report/_auth/filling/$report_id/': typeof mainLayoutDashboardReportAuthFillingReportidIndexRoute
  '/(main)/_layout/dashboard/report/_auth/list/new/': typeof mainLayoutDashboardReportAuthListNewIndexRoute
  '/(main)/_layout/dashboard/report/_auth/view/$report_id/': typeof mainLayoutDashboardReportAuthViewReportidIndexRoute
  '/(main)/_layout/dashboard/staff/_auth/profile/$staff_username/': typeof mainLayoutDashboardStaffAuthProfileStaffusernameIndexRoute
  '/(main)/_layout/dashboard/student/_auth/guardian/$guardian_username/': typeof mainLayoutDashboardStudentAuthGuardianGuardianusernameIndexRoute
  '/(main)/_layout/dashboard/student/_auth/profile/$student_username/': typeof mainLayoutDashboardStudentAuthProfileStudentusernameIndexRoute
  '/(main)/_layout/dashboard/report/_auth/list/$report_id/edit/': typeof mainLayoutDashboardReportAuthListReportidEditIndexRoute
}

export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/'
    | '/signin'
    | '/dashboard'
    | '/dashboard/academic'
    | '/dashboard/finance'
    | '/dashboard/information'
    | '/dashboard/report'
    | '/dashboard/school'
    | '/dashboard/staff'
    | '/dashboard/student'
    | '/dashboard/academic/'
    | '/dashboard/finance/'
    | '/dashboard/information/'
    | '/dashboard/report/'
    | '/dashboard/school/'
    | '/dashboard/staff/'
    | '/dashboard/student/'
    | '/dashboard/academic/class'
    | '/dashboard/academic/curriculum'
    | '/dashboard/academic/extracurricular'
    | '/dashboard/academic/period'
    | '/dashboard/academic/schedule'
    | '/dashboard/academic/study-program'
    | '/dashboard/academic/teacher'
    | '/dashboard/finance/invoice'
    | '/dashboard/information/announcement'
    | '/dashboard/report/filling'
    | '/dashboard/report/list'
    | '/dashboard/report/playground'
    | '/dashboard/report/template'
    | '/dashboard/report/view'
    | '/dashboard/school/profile'
    | '/dashboard/staff/attendance'
    | '/dashboard/staff/profile'
    | '/dashboard/student/attendance'
    | '/dashboard/student/guardian'
    | '/dashboard/student/profile'
    | '/dashboard/report/filling/$report_id'
    | '/dashboard/report/list/new'
    | '/dashboard/report/view/$report_id'
    | '/dashboard/staff/profile/$staff_username'
    | '/dashboard/student/guardian/$guardian_username'
    | '/dashboard/student/profile/$student_username'
    | '/dashboard/report/list/$report_id/edit'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/'
    | '/signin'
    | '/dashboard'
    | '/dashboard/academic'
    | '/dashboard/finance'
    | '/dashboard/information'
    | '/dashboard/report'
    | '/dashboard/school'
    | '/dashboard/staff'
    | '/dashboard/student'
    | '/dashboard/academic/class'
    | '/dashboard/academic/curriculum'
    | '/dashboard/academic/extracurricular'
    | '/dashboard/academic/period'
    | '/dashboard/academic/schedule'
    | '/dashboard/academic/study-program'
    | '/dashboard/academic/teacher'
    | '/dashboard/finance/invoice'
    | '/dashboard/information/announcement'
    | '/dashboard/report/filling'
    | '/dashboard/report/list'
    | '/dashboard/report/playground'
    | '/dashboard/report/template'
    | '/dashboard/report/view'
    | '/dashboard/school/profile'
    | '/dashboard/staff/attendance'
    | '/dashboard/staff/profile'
    | '/dashboard/student/attendance'
    | '/dashboard/student/guardian'
    | '/dashboard/student/profile'
    | '/dashboard/report/filling/$report_id'
    | '/dashboard/report/list/new'
    | '/dashboard/report/view/$report_id'
    | '/dashboard/staff/profile/$staff_username'
    | '/dashboard/student/guardian/$guardian_username'
    | '/dashboard/student/profile/$student_username'
    | '/dashboard/report/list/$report_id/edit'
  id:
    | '__root__'
    | '/'
    | '/(main)'
    | '/(main)/_layout'
    | '/(auth)/signin/'
    | '/(main)/_layout/dashboard/'
    | '/(main)/_layout/dashboard/academic'
    | '/(main)/_layout/dashboard/academic/_auth'
    | '/(main)/_layout/dashboard/finance'
    | '/(main)/_layout/dashboard/finance/_auth'
    | '/(main)/_layout/dashboard/information'
    | '/(main)/_layout/dashboard/information/_auth'
    | '/(main)/_layout/dashboard/report'
    | '/(main)/_layout/dashboard/report/_auth'
    | '/(main)/_layout/dashboard/school'
    | '/(main)/_layout/dashboard/school/_auth'
    | '/(main)/_layout/dashboard/staff'
    | '/(main)/_layout/dashboard/staff/_auth'
    | '/(main)/_layout/dashboard/student'
    | '/(main)/_layout/dashboard/student/_auth'
    | '/(main)/_layout/dashboard/academic/_auth/'
    | '/(main)/_layout/dashboard/finance/_auth/'
    | '/(main)/_layout/dashboard/information/_auth/'
    | '/(main)/_layout/dashboard/report/_auth/'
    | '/(main)/_layout/dashboard/school/_auth/'
    | '/(main)/_layout/dashboard/staff/_auth/'
    | '/(main)/_layout/dashboard/student/_auth/'
    | '/(main)/_layout/dashboard/academic/_auth/class/'
    | '/(main)/_layout/dashboard/academic/_auth/curriculum/'
    | '/(main)/_layout/dashboard/academic/_auth/extracurricular/'
    | '/(main)/_layout/dashboard/academic/_auth/period/'
    | '/(main)/_layout/dashboard/academic/_auth/schedule/'
    | '/(main)/_layout/dashboard/academic/_auth/study-program/'
    | '/(main)/_layout/dashboard/academic/_auth/teacher/'
    | '/(main)/_layout/dashboard/finance/_auth/invoice/'
    | '/(main)/_layout/dashboard/information/_auth/announcement/'
    | '/(main)/_layout/dashboard/report/_auth/filling/'
    | '/(main)/_layout/dashboard/report/_auth/list/'
    | '/(main)/_layout/dashboard/report/_auth/playground/'
    | '/(main)/_layout/dashboard/report/_auth/template/'
    | '/(main)/_layout/dashboard/report/_auth/view/'
    | '/(main)/_layout/dashboard/school/_auth/profile/'
    | '/(main)/_layout/dashboard/staff/_auth/attendance/'
    | '/(main)/_layout/dashboard/staff/_auth/profile/'
    | '/(main)/_layout/dashboard/student/_auth/attendance/'
    | '/(main)/_layout/dashboard/student/_auth/guardian/'
    | '/(main)/_layout/dashboard/student/_auth/profile/'
    | '/(main)/_layout/dashboard/report/_auth/filling/$report_id/'
    | '/(main)/_layout/dashboard/report/_auth/list/new/'
    | '/(main)/_layout/dashboard/report/_auth/view/$report_id/'
    | '/(main)/_layout/dashboard/staff/_auth/profile/$staff_username/'
    | '/(main)/_layout/dashboard/student/_auth/guardian/$guardian_username/'
    | '/(main)/_layout/dashboard/student/_auth/profile/$student_username/'
    | '/(main)/_layout/dashboard/report/_auth/list/$report_id/edit/'
  fileRoutesById: FileRoutesById
}

export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  mainRoute: typeof mainRouteWithChildren
  authSigninIndexRoute: typeof authSigninIndexRoute
}

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  mainRoute: mainRouteWithChildren,
  authSigninIndexRoute: authSigninIndexRoute,
}

export const routeTree = rootRoute
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()

/* ROUTE_MANIFEST_START
{
  "routes": {
    "__root__": {
      "filePath": "__root.tsx",
      "children": [
        "/",
        "/(main)",
        "/(auth)/signin/"
      ]
    },
    "/": {
      "filePath": "index.tsx"
    },
    "/(main)": {
      "filePath": "(main)",
      "children": [
        "/(main)/_layout"
      ]
    },
    "/(main)/_layout": {
      "filePath": "(main)/_layout.tsx",
      "parent": "/(main)",
      "children": [
        "/(main)/_layout/dashboard/",
        "/(main)/_layout/dashboard/academic",
        "/(main)/_layout/dashboard/finance",
        "/(main)/_layout/dashboard/information",
        "/(main)/_layout/dashboard/report",
        "/(main)/_layout/dashboard/school",
        "/(main)/_layout/dashboard/staff",
        "/(main)/_layout/dashboard/student"
      ]
    },
    "/(auth)/signin/": {
      "filePath": "(auth)/signin/index.tsx"
    },
    "/(main)/_layout/dashboard/": {
      "filePath": "(main)/_layout.dashboard/index.tsx",
      "parent": "/(main)/_layout"
    },
    "/(main)/_layout/dashboard/academic": {
      "filePath": "(main)/_layout.dashboard/academic",
      "parent": "/(main)/_layout",
      "children": [
        "/(main)/_layout/dashboard/academic/_auth"
      ]
    },
    "/(main)/_layout/dashboard/academic/_auth": {
      "filePath": "(main)/_layout.dashboard/academic/_auth.tsx",
      "parent": "/(main)/_layout/dashboard/academic",
      "children": [
        "/(main)/_layout/dashboard/academic/_auth/",
        "/(main)/_layout/dashboard/academic/_auth/class/",
        "/(main)/_layout/dashboard/academic/_auth/curriculum/",
        "/(main)/_layout/dashboard/academic/_auth/extracurricular/",
        "/(main)/_layout/dashboard/academic/_auth/period/",
        "/(main)/_layout/dashboard/academic/_auth/schedule/",
        "/(main)/_layout/dashboard/academic/_auth/study-program/",
        "/(main)/_layout/dashboard/academic/_auth/teacher/"
      ]
    },
    "/(main)/_layout/dashboard/finance": {
      "filePath": "(main)/_layout.dashboard/finance",
      "parent": "/(main)/_layout",
      "children": [
        "/(main)/_layout/dashboard/finance/_auth"
      ]
    },
    "/(main)/_layout/dashboard/finance/_auth": {
      "filePath": "(main)/_layout.dashboard/finance/_auth.tsx",
      "parent": "/(main)/_layout/dashboard/finance",
      "children": [
        "/(main)/_layout/dashboard/finance/_auth/",
        "/(main)/_layout/dashboard/finance/_auth/invoice/"
      ]
    },
    "/(main)/_layout/dashboard/information": {
      "filePath": "(main)/_layout.dashboard/information",
      "parent": "/(main)/_layout",
      "children": [
        "/(main)/_layout/dashboard/information/_auth"
      ]
    },
    "/(main)/_layout/dashboard/information/_auth": {
      "filePath": "(main)/_layout.dashboard/information/_auth.tsx",
      "parent": "/(main)/_layout/dashboard/information",
      "children": [
        "/(main)/_layout/dashboard/information/_auth/",
        "/(main)/_layout/dashboard/information/_auth/announcement/"
      ]
    },
    "/(main)/_layout/dashboard/report": {
      "filePath": "(main)/_layout.dashboard/report",
      "parent": "/(main)/_layout",
      "children": [
        "/(main)/_layout/dashboard/report/_auth"
      ]
    },
    "/(main)/_layout/dashboard/report/_auth": {
      "filePath": "(main)/_layout.dashboard/report/_auth.tsx",
      "parent": "/(main)/_layout/dashboard/report",
      "children": [
        "/(main)/_layout/dashboard/report/_auth/",
        "/(main)/_layout/dashboard/report/_auth/filling/",
        "/(main)/_layout/dashboard/report/_auth/list/",
        "/(main)/_layout/dashboard/report/_auth/playground/",
        "/(main)/_layout/dashboard/report/_auth/template/",
        "/(main)/_layout/dashboard/report/_auth/view/",
        "/(main)/_layout/dashboard/report/_auth/filling/$report_id/",
        "/(main)/_layout/dashboard/report/_auth/list/new/",
        "/(main)/_layout/dashboard/report/_auth/view/$report_id/",
        "/(main)/_layout/dashboard/report/_auth/list/$report_id/edit/"
      ]
    },
    "/(main)/_layout/dashboard/school": {
      "filePath": "(main)/_layout.dashboard/school",
      "parent": "/(main)/_layout",
      "children": [
        "/(main)/_layout/dashboard/school/_auth"
      ]
    },
    "/(main)/_layout/dashboard/school/_auth": {
      "filePath": "(main)/_layout.dashboard/school/_auth.tsx",
      "parent": "/(main)/_layout/dashboard/school",
      "children": [
        "/(main)/_layout/dashboard/school/_auth/",
        "/(main)/_layout/dashboard/school/_auth/profile/"
      ]
    },
    "/(main)/_layout/dashboard/staff": {
      "filePath": "(main)/_layout.dashboard/staff",
      "parent": "/(main)/_layout",
      "children": [
        "/(main)/_layout/dashboard/staff/_auth"
      ]
    },
    "/(main)/_layout/dashboard/staff/_auth": {
      "filePath": "(main)/_layout.dashboard/staff/_auth.tsx",
      "parent": "/(main)/_layout/dashboard/staff",
      "children": [
        "/(main)/_layout/dashboard/staff/_auth/",
        "/(main)/_layout/dashboard/staff/_auth/attendance/",
        "/(main)/_layout/dashboard/staff/_auth/profile/",
        "/(main)/_layout/dashboard/staff/_auth/profile/$staff_username/"
      ]
    },
    "/(main)/_layout/dashboard/student": {
      "filePath": "(main)/_layout.dashboard/student",
      "parent": "/(main)/_layout",
      "children": [
        "/(main)/_layout/dashboard/student/_auth"
      ]
    },
    "/(main)/_layout/dashboard/student/_auth": {
      "filePath": "(main)/_layout.dashboard/student/_auth.tsx",
      "parent": "/(main)/_layout/dashboard/student",
      "children": [
        "/(main)/_layout/dashboard/student/_auth/",
        "/(main)/_layout/dashboard/student/_auth/attendance/",
        "/(main)/_layout/dashboard/student/_auth/guardian/",
        "/(main)/_layout/dashboard/student/_auth/profile/",
        "/(main)/_layout/dashboard/student/_auth/guardian/$guardian_username/",
        "/(main)/_layout/dashboard/student/_auth/profile/$student_username/"
      ]
    },
    "/(main)/_layout/dashboard/academic/_auth/": {
      "filePath": "(main)/_layout.dashboard/academic/_auth.index.tsx",
      "parent": "/(main)/_layout/dashboard/academic/_auth"
    },
    "/(main)/_layout/dashboard/finance/_auth/": {
      "filePath": "(main)/_layout.dashboard/finance/_auth.index.tsx",
      "parent": "/(main)/_layout/dashboard/finance/_auth"
    },
    "/(main)/_layout/dashboard/information/_auth/": {
      "filePath": "(main)/_layout.dashboard/information/_auth.index.tsx",
      "parent": "/(main)/_layout/dashboard/information/_auth"
    },
    "/(main)/_layout/dashboard/report/_auth/": {
      "filePath": "(main)/_layout.dashboard/report/_auth.index.tsx",
      "parent": "/(main)/_layout/dashboard/report/_auth"
    },
    "/(main)/_layout/dashboard/school/_auth/": {
      "filePath": "(main)/_layout.dashboard/school/_auth.index.tsx",
      "parent": "/(main)/_layout/dashboard/school/_auth"
    },
    "/(main)/_layout/dashboard/staff/_auth/": {
      "filePath": "(main)/_layout.dashboard/staff/_auth.index.tsx",
      "parent": "/(main)/_layout/dashboard/staff/_auth"
    },
    "/(main)/_layout/dashboard/student/_auth/": {
      "filePath": "(main)/_layout.dashboard/student/_auth.index.tsx",
      "parent": "/(main)/_layout/dashboard/student/_auth"
    },
    "/(main)/_layout/dashboard/academic/_auth/class/": {
      "filePath": "(main)/_layout.dashboard/academic/_auth.class/index.tsx",
      "parent": "/(main)/_layout/dashboard/academic/_auth"
    },
    "/(main)/_layout/dashboard/academic/_auth/curriculum/": {
      "filePath": "(main)/_layout.dashboard/academic/_auth.curriculum/index.tsx",
      "parent": "/(main)/_layout/dashboard/academic/_auth"
    },
    "/(main)/_layout/dashboard/academic/_auth/extracurricular/": {
      "filePath": "(main)/_layout.dashboard/academic/_auth.extracurricular/index.tsx",
      "parent": "/(main)/_layout/dashboard/academic/_auth"
    },
    "/(main)/_layout/dashboard/academic/_auth/period/": {
      "filePath": "(main)/_layout.dashboard/academic/_auth.period/index.tsx",
      "parent": "/(main)/_layout/dashboard/academic/_auth"
    },
    "/(main)/_layout/dashboard/academic/_auth/schedule/": {
      "filePath": "(main)/_layout.dashboard/academic/_auth.schedule/index.jsx",
      "parent": "/(main)/_layout/dashboard/academic/_auth"
    },
    "/(main)/_layout/dashboard/academic/_auth/study-program/": {
      "filePath": "(main)/_layout.dashboard/academic/_auth.study-program/index.tsx",
      "parent": "/(main)/_layout/dashboard/academic/_auth"
    },
    "/(main)/_layout/dashboard/academic/_auth/teacher/": {
      "filePath": "(main)/_layout.dashboard/academic/_auth.teacher/index.tsx",
      "parent": "/(main)/_layout/dashboard/academic/_auth"
    },
    "/(main)/_layout/dashboard/finance/_auth/invoice/": {
      "filePath": "(main)/_layout.dashboard/finance/_auth.invoice/index.tsx",
      "parent": "/(main)/_layout/dashboard/finance/_auth"
    },
    "/(main)/_layout/dashboard/information/_auth/announcement/": {
      "filePath": "(main)/_layout.dashboard/information/_auth.announcement/index.tsx",
      "parent": "/(main)/_layout/dashboard/information/_auth"
    },
    "/(main)/_layout/dashboard/report/_auth/filling/": {
      "filePath": "(main)/_layout.dashboard/report/_auth.filling/index.tsx",
      "parent": "/(main)/_layout/dashboard/report/_auth"
    },
    "/(main)/_layout/dashboard/report/_auth/list/": {
      "filePath": "(main)/_layout.dashboard/report/_auth.list/index.tsx",
      "parent": "/(main)/_layout/dashboard/report/_auth"
    },
    "/(main)/_layout/dashboard/report/_auth/playground/": {
      "filePath": "(main)/_layout.dashboard/report/_auth.playground/index.tsx",
      "parent": "/(main)/_layout/dashboard/report/_auth"
    },
    "/(main)/_layout/dashboard/report/_auth/template/": {
      "filePath": "(main)/_layout.dashboard/report/_auth.template/index.tsx",
      "parent": "/(main)/_layout/dashboard/report/_auth"
    },
    "/(main)/_layout/dashboard/report/_auth/view/": {
      "filePath": "(main)/_layout.dashboard/report/_auth.view/index.tsx",
      "parent": "/(main)/_layout/dashboard/report/_auth"
    },
    "/(main)/_layout/dashboard/school/_auth/profile/": {
      "filePath": "(main)/_layout.dashboard/school/_auth.profile/index.tsx",
      "parent": "/(main)/_layout/dashboard/school/_auth"
    },
    "/(main)/_layout/dashboard/staff/_auth/attendance/": {
      "filePath": "(main)/_layout.dashboard/staff/_auth.attendance/index.tsx",
      "parent": "/(main)/_layout/dashboard/staff/_auth"
    },
    "/(main)/_layout/dashboard/staff/_auth/profile/": {
      "filePath": "(main)/_layout.dashboard/staff/_auth.profile/index.tsx",
      "parent": "/(main)/_layout/dashboard/staff/_auth"
    },
    "/(main)/_layout/dashboard/student/_auth/attendance/": {
      "filePath": "(main)/_layout.dashboard/student/_auth.attendance/index.tsx",
      "parent": "/(main)/_layout/dashboard/student/_auth"
    },
    "/(main)/_layout/dashboard/student/_auth/guardian/": {
      "filePath": "(main)/_layout.dashboard/student/_auth.guardian/index.tsx",
      "parent": "/(main)/_layout/dashboard/student/_auth"
    },
    "/(main)/_layout/dashboard/student/_auth/profile/": {
      "filePath": "(main)/_layout.dashboard/student/_auth.profile/index.tsx",
      "parent": "/(main)/_layout/dashboard/student/_auth"
    },
    "/(main)/_layout/dashboard/report/_auth/filling/$report_id/": {
      "filePath": "(main)/_layout.dashboard/report/_auth.filling/$report_id/index.tsx",
      "parent": "/(main)/_layout/dashboard/report/_auth"
    },
    "/(main)/_layout/dashboard/report/_auth/list/new/": {
      "filePath": "(main)/_layout.dashboard/report/_auth.list/new/index.tsx",
      "parent": "/(main)/_layout/dashboard/report/_auth"
    },
    "/(main)/_layout/dashboard/report/_auth/view/$report_id/": {
      "filePath": "(main)/_layout.dashboard/report/_auth.view/$report_id/index.tsx",
      "parent": "/(main)/_layout/dashboard/report/_auth"
    },
    "/(main)/_layout/dashboard/staff/_auth/profile/$staff_username/": {
      "filePath": "(main)/_layout.dashboard/staff/_auth.profile/$staff_username/index.tsx",
      "parent": "/(main)/_layout/dashboard/staff/_auth"
    },
    "/(main)/_layout/dashboard/student/_auth/guardian/$guardian_username/": {
      "filePath": "(main)/_layout.dashboard/student/_auth.guardian/$guardian_username/index.tsx",
      "parent": "/(main)/_layout/dashboard/student/_auth"
    },
    "/(main)/_layout/dashboard/student/_auth/profile/$student_username/": {
      "filePath": "(main)/_layout.dashboard/student/_auth.profile/$student_username/index.tsx",
      "parent": "/(main)/_layout/dashboard/student/_auth"
    },
    "/(main)/_layout/dashboard/report/_auth/list/$report_id/edit/": {
      "filePath": "(main)/_layout.dashboard/report/_auth.list/$report_id/edit/index.tsx",
      "parent": "/(main)/_layout/dashboard/report/_auth"
    }
  }
}
ROUTE_MANIFEST_END */
