/** @type {import('tailwindcss').Config} */
const config = {
  corePlugins: {
    // due to https://github.com/tailwindlabs/tailwindcss/issues/6602 - buttons disappear
    preflight: false,
  },
  content: [
    "./src/**/*.{js,ts,jsx,tsx,mdx}",
    // https://www.willliu.com/blog/Why-your-Tailwind-styles-aren-t-working-in-your-Turborepo
    "../../packages/ui/src/**/*{.js,.ts,.jsx,.tsx}",
  ],
  theme: {
    screens: {
      xxs: "320px",
      xs: "375px",
      sm: "640px",
      md: "768px",
      lg: "1024px",
      xl: "1280px",
      "2xl": "1536px",
    },
    extend: {
      fontFamily: {
        kumbh: ["Kumbh Sans"],
      },
    },
  },
  plugins: [],
};

export default config;
