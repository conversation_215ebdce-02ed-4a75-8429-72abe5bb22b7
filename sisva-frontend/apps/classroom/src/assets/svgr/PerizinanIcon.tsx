import type { SVGProps } from "react";
import * as React from "react";
const SvgComponent = (props: SVGProps<SVGSVGElement>) => (
  <svg
    viewBox="0 0 54 54"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <g clipPath="url(#clip0_2512_27611)">
      <circle opacity={0.3} cx={27} cy={27} r={27} fill="#003855" />
      <path
        d="M29.9864 2.90353L7.33588 3.89248C5.96655 3.95226 4.90489 5.11038 4.96465 6.47914L6.81145 48.7778C6.87121 50.1465 8.02977 51.2077 9.3991 51.1479L42.0518 49.7223C43.4211 49.6625 44.4828 48.5044 44.423 47.1356L42.9844 14.1861C42.9679 13.8068 42.8027 13.4493 42.5247 13.1908L32.4241 3.80065C31.7645 3.18734 30.8864 2.86424 29.9864 2.90353Z"
        fill="#FEF0E8"
      />
      <path
        d="M29.9864 2.90353L7.33588 3.89248C5.96655 3.95226 4.90489 5.11038 4.96465 6.47914L6.81145 48.7778C6.87121 50.1465 8.02977 51.2077 9.3991 51.1479L42.0518 49.7223C43.4211 49.6625 44.4828 48.5044 44.423 47.1356L42.9844 14.1861C42.9679 13.8068 42.8027 13.4493 42.5247 13.1908L32.4241 3.80065C31.7645 3.18734 30.8864 2.86424 29.9864 2.90353Z"
        fill="url(#paint0_linear_2512_27611)"
      />
      <path
        d="M32.556 3.92288C32.5207 3.89025 32.4989 3.8699 32.4899 3.86152L32.4822 3.85434C31.8057 3.22582 30.9127 2.88628 29.9905 2.90422C29.9687 2.90404 29.9461 2.90513 29.9241 2.90598L29.9124 2.90649C29.9531 2.90483 29.9929 2.90434 30.0327 2.90648C30.0718 2.90751 30.1113 2.91136 30.1492 2.91529L30.1759 2.9197C30.2521 2.93028 30.3269 2.94672 30.3993 2.96873C30.4375 2.97959 30.4759 2.99341 30.5129 3.00854C30.6851 3.07369 30.8419 3.17324 30.9759 3.29611C31.0868 3.39766 31.1814 3.51518 31.2571 3.64606C31.2737 3.67188 31.2888 3.69924 31.3012 3.72809C31.3257 3.77463 31.3473 3.82256 31.3649 3.87214C31.3696 3.88446 31.3742 3.89554 31.3776 3.90792C31.4127 4.00709 31.4369 4.11231 31.4485 4.21956C31.4528 4.25571 31.4559 4.29191 31.4575 4.3283L31.7749 11.599C31.777 11.6465 31.7804 11.6924 31.7852 11.7384C31.79 11.783 31.7947 11.8275 31.8022 11.872C31.8023 11.8747 31.8025 11.8789 31.804 11.8817C31.8101 11.9247 31.8176 11.9678 31.8278 12.0093C31.8352 12.0495 31.8452 12.0882 31.8554 12.127C31.8587 12.1394 31.8621 12.1519 31.8654 12.1643C31.8751 12.1974 31.8851 12.2305 31.8962 12.2621C31.8964 12.2677 31.898 12.2732 31.9011 12.2788C31.9107 12.3077 31.9219 12.3366 31.9329 12.3655C31.9394 12.3862 31.9473 12.4068 31.9567 12.4274C31.9615 12.4397 31.9661 12.4521 31.9722 12.463C31.9817 12.4907 31.9942 12.518 32.0079 12.5441C32.0203 12.5743 32.0357 12.6044 32.0524 12.6344C32.0723 12.6741 32.0935 12.7123 32.1162 12.7491C32.1614 12.8241 32.2093 12.896 32.2586 12.9653C32.26 12.9666 32.2601 12.968 32.2615 12.9679C32.3402 13.0722 32.427 13.1705 32.5191 13.2615C32.5497 13.2909 32.5819 13.3216 32.6138 13.3496C32.6473 13.3789 32.6807 13.4068 32.714 13.4334L32.7257 13.4441L43.3779 23.2009L42.9842 14.1856C42.9677 13.8063 42.8026 13.4488 42.5245 13.1903L32.556 3.92288Z"
        fill="url(#paint1_linear_2512_27611)"
      />
      <path
        d="M42.8321 13.5999C42.8321 13.5999 42.7496 13.3957 42.4488 13.12C42.337 13.0176 32.4839 3.85596 32.4839 3.85596C31.7863 3.20752 30.8577 2.86567 29.9058 2.90723C30.7273 2.87136 31.4224 3.50806 31.4583 4.32927L31.7757 11.5994C31.8355 12.9682 32.994 14.0293 34.3634 13.9696L42.8321 13.5999Z"
        fill="#2A2A61"
      />
      <path
        d="M46.3093 20.6632C47.1802 20.0967 48.1034 19.6178 49.0541 19.1985C49.9062 18.8227 50.7548 18.2467 51.5146 17.4869C53.8246 15.177 53.8629 12.6208 52.31 11.0679C50.7571 9.51491 48.2009 9.55332 45.891 11.8633C45.1312 12.6231 44.5553 13.4717 44.1795 14.3238C43.7602 15.2745 43.2813 16.1977 42.7148 17.0686L42.7147 17.0687C41.9425 18.2559 40.7398 19.0971 39.3598 19.4152L37.6792 19.8027L39.2197 23.8881L43.5752 25.6988L43.9627 24.0181C44.2809 22.6381 45.1221 21.4354 46.3093 20.6632Z"
        fill="#F96756"
      />
      <path
        d="M52.3099 11.0682C51.631 10.3893 50.7605 10.0146 49.8025 10.0007C50.0162 10.1416 50.2182 10.3056 50.4056 10.493C51.977 12.0643 51.9535 14.6354 49.6435 16.9454C48.8837 17.7052 48.0334 18.2795 47.1785 18.6525C46.2247 19.0686 45.2989 19.5449 44.4261 20.1097C43.2364 20.8794 42.3974 22.0842 42.0855 23.4706L41.5898 24.7798L43.575 25.699L43.9625 24.0184C44.2807 22.6383 45.1219 21.4356 46.3091 20.6634C47.18 20.0969 48.1032 19.618 49.0539 19.1987C49.906 18.8229 50.7546 18.247 51.5144 17.4872C53.8245 15.1772 53.8629 12.6211 52.3099 11.0682Z"
        fill="#E55544"
      />
      <path
        d="M44.3515 32.1052L38.7323 24.4924L31.2726 19.0264L30.2382 20.0608C29.9206 20.3784 29.9206 20.8932 30.2382 21.2108L42.167 33.1397C42.4846 33.4573 42.9995 33.4573 43.3171 33.1397L44.3515 32.1052Z"
        fill="#2A2A61"
      />
      <path
        d="M45.0442 32.798L30.58 18.3338C30.4115 18.1653 30.4115 17.8922 30.58 17.7237L31.5888 16.7149C32.418 15.8857 33.7622 15.8857 34.5914 16.7149L46.6631 28.7866C47.4923 29.6157 47.4923 30.96 46.6631 31.7892L45.6543 32.798C45.4857 32.9665 45.2126 32.9665 45.0442 32.798Z"
        fill="#F96756"
      />
      <path
        d="M46.6631 28.7867L34.5914 16.715C33.9151 16.0386 32.896 15.9142 32.0933 16.3413C32.2746 16.4378 32.4449 16.5622 32.5977 16.715L44.8482 28.9655C45.6774 29.7947 45.6774 31.139 44.8482 31.9681L44.5313 32.2851L45.0443 32.798C45.2127 32.9665 45.4859 32.9665 45.6543 32.798L46.6632 31.7893C47.4922 30.9601 47.4922 29.6158 46.6631 28.7867Z"
        fill="#E55544"
      />
      <path
        d="M21 43C27.6274 43 33 37.6274 33 31C33 24.3726 27.6274 19 21 19C14.3726 19 9 24.3726 9 31C9 37.6274 14.3726 43 21 43Z"
        fill="#2A2A61"
      />
      <path
        d="M21 39C25.4183 39 29 35.4183 29 31C29 26.5817 25.4183 23 21 23C16.5817 23 13 26.5817 13 31C13 35.4183 16.5817 39 21 39Z"
        fill="#FFFCFA"
      />
      <path
        d="M23.0812 28.1887L23.0811 28.1888L19.5865 31.7203L18.4384 30.5601C17.9644 30.0811 17.1938 30.0812 16.7198 30.56C16.2486 31.036 16.2487 31.8058 16.7198 32.2818L18.7272 34.3103C18.9635 34.5491 19.2754 34.6695 19.5865 34.6695C19.8976 34.6695 20.2094 34.5492 20.4457 34.3103C20.4458 34.3102 20.4458 34.3102 20.4459 34.3102L24.7997 29.9106C25.2709 29.4346 25.2708 28.6648 24.7997 28.1888C24.3257 27.7098 23.5552 27.7099 23.0812 28.1887Z"
        fill="#F96756"
        stroke="#F96756"
      />
    </g>
    <defs>
      <linearGradient
        id="paint0_linear_2512_27611"
        x1={23.6622}
        y1={3.17965}
        x2={25.7254}
        y2={50.4351}
        gradientUnits="userSpaceOnUse"
      >
        <stop offset={0.263542} stopColor="#FEF0E8" stopOpacity={0} />
        <stop offset={1} stopColor="#D7C9AD" stopOpacity={0.5} />
      </linearGradient>
      <linearGradient
        id="paint1_linear_2512_27611"
        x1={46.0213}
        y1={20.9458}
        x2={29.9576}
        y2={6.21894}
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="#D7C9AD" stopOpacity={0} />
        <stop offset={1} stopColor="#D7C9AD" />
      </linearGradient>
      <clipPath id="clip0_2512_27611">
        <rect width={54} height={54} fill="white" />
      </clipPath>
    </defs>
  </svg>
);
export { SvgComponent as PerizinanIcon };
