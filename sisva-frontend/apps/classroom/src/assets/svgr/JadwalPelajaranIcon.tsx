import type { SVGProps } from "react";
import * as React from "react";
const SvgComponent = (props: SVGProps<SVGSVGElement>) => (
  <svg
    viewBox="0 0 54 54"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <circle opacity={0.3} cx={27} cy={27} r={27} fill="#003855" />
    <g clipPath="url(#clip0_1417_19910)">
      <path
        d="M45.9175 46.6247L9.94944 48.1951C7.75582 48.2909 5.89899 46.5689 5.80207 44.349L4.40255 12.2946C4.30562 10.0747 6.00531 8.19751 8.19892 8.10173L44.167 6.53133C46.3606 6.43556 48.2174 8.1575 48.3143 10.3774L49.7138 42.4318C49.8108 44.6517 48.1111 46.5289 45.9175 46.6247Z"
        fill="#FEF0E8"
      />
      <path
        d="M48.49 14.4009L48.3122 10.3291C48.2165 8.13544 46.3606 6.43482 44.167 6.5306L8.19892 8.101C6.00531 8.19686 4.30469 10.0528 4.40047 12.2463L4.57824 16.3181L48.49 14.4009Z"
        fill="#2A2A61"
      />
      <path
        d="M25.1355 19.6263C25.6815 20.1717 26.4433 20.4975 27.2749 20.4611C28.8494 20.3924 30.07 19.0604 30.0012 17.4859C29.9635 16.6231 29.5846 15.8106 28.9479 15.2271L20.6445 7.61849L14.9429 7.86743L15.0017 9.2148C15.038 10.0465 15.4289 10.777 16.0198 11.2734L25.1355 19.6263Z"
        fill="url(#paint0_linear_1417_19910)"
      />
      <path
        d="M41.9456 18.8922C42.4915 19.4376 43.2533 19.7633 44.085 19.727C45.6595 19.6583 46.88 18.3263 46.8113 16.7518C46.7736 15.889 46.3947 15.0765 45.758 14.493L37.4546 6.88436L31.7529 7.1333L31.8118 8.48067C31.8481 9.31234 32.239 10.0428 32.8298 10.5392L41.9456 18.8922Z"
        fill="url(#paint1_linear_1417_19910)"
      />
      <path
        d="M17.9768 11.9413C16.4024 12.01 15.0704 10.7895 15.0016 9.21501L14.8772 6.36418C14.8084 4.7897 16.029 3.4577 17.6034 3.38896C19.1779 3.32022 20.5099 4.54077 20.5786 6.11524L20.7031 8.96599C20.7719 10.5404 19.5512 11.8726 17.9768 11.9413Z"
        fill="#F96756"
      />
      <path
        d="M34.7608 11.2084C33.1863 11.2771 31.8543 10.0566 31.7856 8.4821L31.6611 5.63127C31.5923 4.05679 32.813 2.72479 34.3874 2.65605C35.9618 2.58731 37.2938 3.80786 37.3626 5.38233L37.4871 8.23308C37.5558 9.80747 36.3352 11.1396 34.7608 11.2084Z"
        fill="#F96756"
      />
      <path
        d="M38.5133 22.1439L15.1922 23.1621C14.7783 23.1802 14.4575 23.5303 14.4756 23.9441C14.4937 24.3579 14.8438 24.6787 15.2576 24.6607L38.5787 23.6425C38.9925 23.6244 39.3134 23.2743 39.2953 22.8605C39.2772 22.4466 38.9271 22.1258 38.5133 22.1439Z"
        fill="#F96756"
      />
      <path
        d="M38.8575 30.0389L15.5364 31.0571C15.1226 31.0752 14.8018 31.4253 14.8198 31.8391C14.8379 32.2529 15.188 32.5738 15.6018 32.5557L38.923 31.5375C39.3368 31.5194 39.6576 31.1693 39.6395 30.7555C39.6215 30.3417 39.2713 30.0208 38.8575 30.0389Z"
        fill="#F96756"
      />
      <path
        d="M39.201 37.9041L15.8799 38.9224C15.4661 38.9404 15.1453 39.2905 15.1633 39.7044C15.1814 40.1182 15.5315 40.439 15.9453 40.4209L39.2665 39.4027C39.6803 39.3846 40.0011 39.0345 39.983 38.6207C39.965 38.2069 39.6149 37.8861 39.201 37.9041Z"
        fill="#F96756"
      />
      <path
        d="M37.2172 40.9957L36.324 20.5383C36.3059 20.1245 35.9558 19.8037 35.542 19.8218C35.1282 19.8398 34.8073 20.19 34.8254 20.6038L35.272 30.8324L35.7186 41.0611C35.7367 41.4749 36.0868 41.7957 36.5006 41.7777C36.9144 41.7596 37.2352 41.4095 37.2172 40.9957Z"
        fill="#F96756"
      />
      <path
        d="M19.0797 41.7874L18.1865 21.3301C18.1685 20.9163 17.8183 20.5955 17.4045 20.6135C16.9907 20.6316 16.6699 20.9817 16.688 21.3955L17.1345 31.6242L17.5811 41.8528C17.5992 42.2667 17.9493 42.5875 18.3631 42.5694C18.777 42.5513 19.0978 42.2012 19.0797 41.7874Z"
        fill="#F96756"
      />
      <path
        d="M28.6261 41.3704L27.7329 20.9131C27.7148 20.4993 27.3647 20.1785 26.9509 20.1965C26.5371 20.2146 26.2163 20.5647 26.2343 20.9785L26.6809 31.2072L27.1275 41.4359C27.1456 41.8497 27.4957 42.1705 27.9095 42.1524C28.3233 42.1344 28.6442 41.7842 28.6261 41.3704Z"
        fill="#F96756"
      />
    </g>
    <defs>
      <linearGradient
        id="paint0_linear_1417_19910"
        x1={23.7194}
        y1={14.3854}
        x2={13.1196}
        y2={4.67243}
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="#2A2A61" stopOpacity={0} />
        <stop offset={1} stopColor="#E9E9FF" />
      </linearGradient>
      <linearGradient
        id="paint1_linear_1417_19910"
        x1={40.5294}
        y1={13.6513}
        x2={29.9297}
        y2={3.9383}
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="#2A2A61" stopOpacity={0} />
        <stop offset={1} stopColor="#E9E9FF" />
      </linearGradient>
      <clipPath id="clip0_1417_19910">
        <rect
          width={44}
          height={45}
          fill="white"
          transform="translate(4.03955 3.98096) rotate(-2.5)"
        />
      </clipPath>
    </defs>
  </svg>
);
export { SvgComponent as JadwalPelajaranIcon };
