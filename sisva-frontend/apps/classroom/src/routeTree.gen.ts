/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { createFileRoute } from '@tanstack/react-router'

// Import Routes

import { Route as rootRoute } from './routes/__root'
import { Route as mainLayoutImport } from './routes/(main)/_layout'
import { Route as mainLayoutIndexImport } from './routes/(main)/_layout/index'
import { Route as authSigninIndexImport } from './routes/(auth)/signin/index'
import { Route as mainLayoutTeachingmaterialsIndexImport } from './routes/(main)/_layout/teaching_materials/index'
import { Route as mainLayoutTasksIndexImport } from './routes/(main)/_layout/tasks/index'
import { Route as mainLayoutSchedulesIndexImport } from './routes/(main)/_layout/schedules/index'
import { Route as mainLayoutProfileIndexImport } from './routes/(main)/_layout/profile/index'
import { Route as mainLayoutClassesIndexImport } from './routes/(main)/_layout/classes/index'
import { Route as mainLayoutClassesClassidAuthImport } from './routes/(main)/_layout/classes/$class_id/_auth'
import { Route as mainLayoutClassesClassidAuthIndexImport } from './routes/(main)/_layout/classes/$class_id/_auth.index'
import { Route as mainLayoutClassesClassidAuthTeachingplansIndexImport } from './routes/(main)/_layout/classes/$class_id/_auth.teaching_plans/index'
import { Route as mainLayoutClassesClassidAuthTasksIndexImport } from './routes/(main)/_layout/classes/$class_id/_auth.tasks/index'
import { Route as mainLayoutClassesClassidAuthTeachingplansNewIndexImport } from './routes/(main)/_layout/classes/$class_id/_auth.teaching_plans/new/index'
import { Route as mainLayoutClassesClassidAuthTeachingplansTeachingplanidIndexImport } from './routes/(main)/_layout/classes/$class_id/_auth.teaching_plans/$teaching_plan_id/index'
import { Route as mainLayoutClassesClassidAuthTasksTaskidIndexImport } from './routes/(main)/_layout/classes/$class_id/_auth.tasks/$task_id/index'
import { Route as mainLayoutClassesClassidAuthTeachingplansTeachingplanidEditIndexImport } from './routes/(main)/_layout/classes/$class_id/_auth.teaching_plans/$teaching_plan_id/edit/index'

// Create Virtual Routes

const mainImport = createFileRoute('/(main)')()
const mainLayoutClassesClassidImport = createFileRoute(
  '/(main)/_layout/classes/$class_id',
)()

// Create/Update Routes

const mainRoute = mainImport.update({
  id: '/(main)',
  getParentRoute: () => rootRoute,
} as any)

const mainLayoutRoute = mainLayoutImport.update({
  id: '/_layout',
  getParentRoute: () => mainRoute,
} as any)

const mainLayoutIndexRoute = mainLayoutIndexImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => mainLayoutRoute,
} as any)

const authSigninIndexRoute = authSigninIndexImport.update({
  id: '/(auth)/signin/',
  path: '/signin/',
  getParentRoute: () => rootRoute,
} as any)

const mainLayoutClassesClassidRoute = mainLayoutClassesClassidImport.update({
  id: '/classes/$class_id',
  path: '/classes/$class_id',
  getParentRoute: () => mainLayoutRoute,
} as any)

const mainLayoutTeachingmaterialsIndexRoute =
  mainLayoutTeachingmaterialsIndexImport.update({
    id: '/teaching_materials/',
    path: '/teaching_materials/',
    getParentRoute: () => mainLayoutRoute,
  } as any)

const mainLayoutTasksIndexRoute = mainLayoutTasksIndexImport.update({
  id: '/tasks/',
  path: '/tasks/',
  getParentRoute: () => mainLayoutRoute,
} as any)

const mainLayoutSchedulesIndexRoute = mainLayoutSchedulesIndexImport.update({
  id: '/schedules/',
  path: '/schedules/',
  getParentRoute: () => mainLayoutRoute,
} as any)

const mainLayoutProfileIndexRoute = mainLayoutProfileIndexImport.update({
  id: '/profile/',
  path: '/profile/',
  getParentRoute: () => mainLayoutRoute,
} as any)

const mainLayoutClassesIndexRoute = mainLayoutClassesIndexImport.update({
  id: '/classes/',
  path: '/classes/',
  getParentRoute: () => mainLayoutRoute,
} as any)

const mainLayoutClassesClassidAuthRoute =
  mainLayoutClassesClassidAuthImport.update({
    id: '/_auth',
    getParentRoute: () => mainLayoutClassesClassidRoute,
  } as any)

const mainLayoutClassesClassidAuthIndexRoute =
  mainLayoutClassesClassidAuthIndexImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => mainLayoutClassesClassidAuthRoute,
  } as any)

const mainLayoutClassesClassidAuthTeachingplansIndexRoute =
  mainLayoutClassesClassidAuthTeachingplansIndexImport.update({
    id: '/teaching_plans/',
    path: '/teaching_plans/',
    getParentRoute: () => mainLayoutClassesClassidAuthRoute,
  } as any)

const mainLayoutClassesClassidAuthTasksIndexRoute =
  mainLayoutClassesClassidAuthTasksIndexImport.update({
    id: '/tasks/',
    path: '/tasks/',
    getParentRoute: () => mainLayoutClassesClassidAuthRoute,
  } as any)

const mainLayoutClassesClassidAuthTeachingplansNewIndexRoute =
  mainLayoutClassesClassidAuthTeachingplansNewIndexImport.update({
    id: '/teaching_plans/new/',
    path: '/teaching_plans/new/',
    getParentRoute: () => mainLayoutClassesClassidAuthRoute,
  } as any)

const mainLayoutClassesClassidAuthTeachingplansTeachingplanidIndexRoute =
  mainLayoutClassesClassidAuthTeachingplansTeachingplanidIndexImport.update({
    id: '/teaching_plans/$teaching_plan_id/',
    path: '/teaching_plans/$teaching_plan_id/',
    getParentRoute: () => mainLayoutClassesClassidAuthRoute,
  } as any)

const mainLayoutClassesClassidAuthTasksTaskidIndexRoute =
  mainLayoutClassesClassidAuthTasksTaskidIndexImport.update({
    id: '/tasks/$task_id/',
    path: '/tasks/$task_id/',
    getParentRoute: () => mainLayoutClassesClassidAuthRoute,
  } as any)

const mainLayoutClassesClassidAuthTeachingplansTeachingplanidEditIndexRoute =
  mainLayoutClassesClassidAuthTeachingplansTeachingplanidEditIndexImport.update(
    {
      id: '/teaching_plans/$teaching_plan_id/edit/',
      path: '/teaching_plans/$teaching_plan_id/edit/',
      getParentRoute: () => mainLayoutClassesClassidAuthRoute,
    } as any,
  )

// Populate the FileRoutesByPath interface

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/(main)': {
      id: '/(main)'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof mainImport
      parentRoute: typeof rootRoute
    }
    '/(main)/_layout': {
      id: '/(main)/_layout'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof mainLayoutImport
      parentRoute: typeof mainRoute
    }
    '/(auth)/signin/': {
      id: '/(auth)/signin/'
      path: '/signin'
      fullPath: '/signin'
      preLoaderRoute: typeof authSigninIndexImport
      parentRoute: typeof rootRoute
    }
    '/(main)/_layout/': {
      id: '/(main)/_layout/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof mainLayoutIndexImport
      parentRoute: typeof mainLayoutImport
    }
    '/(main)/_layout/classes/': {
      id: '/(main)/_layout/classes/'
      path: '/classes'
      fullPath: '/classes'
      preLoaderRoute: typeof mainLayoutClassesIndexImport
      parentRoute: typeof mainLayoutImport
    }
    '/(main)/_layout/profile/': {
      id: '/(main)/_layout/profile/'
      path: '/profile'
      fullPath: '/profile'
      preLoaderRoute: typeof mainLayoutProfileIndexImport
      parentRoute: typeof mainLayoutImport
    }
    '/(main)/_layout/schedules/': {
      id: '/(main)/_layout/schedules/'
      path: '/schedules'
      fullPath: '/schedules'
      preLoaderRoute: typeof mainLayoutSchedulesIndexImport
      parentRoute: typeof mainLayoutImport
    }
    '/(main)/_layout/tasks/': {
      id: '/(main)/_layout/tasks/'
      path: '/tasks'
      fullPath: '/tasks'
      preLoaderRoute: typeof mainLayoutTasksIndexImport
      parentRoute: typeof mainLayoutImport
    }
    '/(main)/_layout/teaching_materials/': {
      id: '/(main)/_layout/teaching_materials/'
      path: '/teaching_materials'
      fullPath: '/teaching_materials'
      preLoaderRoute: typeof mainLayoutTeachingmaterialsIndexImport
      parentRoute: typeof mainLayoutImport
    }
    '/(main)/_layout/classes/$class_id': {
      id: '/(main)/_layout/classes/$class_id'
      path: '/classes/$class_id'
      fullPath: '/classes/$class_id'
      preLoaderRoute: typeof mainLayoutClassesClassidImport
      parentRoute: typeof mainLayoutImport
    }
    '/(main)/_layout/classes/$class_id/_auth': {
      id: '/(main)/_layout/classes/$class_id/_auth'
      path: '/classes/$class_id'
      fullPath: '/classes/$class_id'
      preLoaderRoute: typeof mainLayoutClassesClassidAuthImport
      parentRoute: typeof mainLayoutClassesClassidRoute
    }
    '/(main)/_layout/classes/$class_id/_auth/': {
      id: '/(main)/_layout/classes/$class_id/_auth/'
      path: '/'
      fullPath: '/classes/$class_id/'
      preLoaderRoute: typeof mainLayoutClassesClassidAuthIndexImport
      parentRoute: typeof mainLayoutClassesClassidAuthImport
    }
    '/(main)/_layout/classes/$class_id/_auth/tasks/': {
      id: '/(main)/_layout/classes/$class_id/_auth/tasks/'
      path: '/tasks'
      fullPath: '/classes/$class_id/tasks'
      preLoaderRoute: typeof mainLayoutClassesClassidAuthTasksIndexImport
      parentRoute: typeof mainLayoutClassesClassidAuthImport
    }
    '/(main)/_layout/classes/$class_id/_auth/teaching_plans/': {
      id: '/(main)/_layout/classes/$class_id/_auth/teaching_plans/'
      path: '/teaching_plans'
      fullPath: '/classes/$class_id/teaching_plans'
      preLoaderRoute: typeof mainLayoutClassesClassidAuthTeachingplansIndexImport
      parentRoute: typeof mainLayoutClassesClassidAuthImport
    }
    '/(main)/_layout/classes/$class_id/_auth/tasks/$task_id/': {
      id: '/(main)/_layout/classes/$class_id/_auth/tasks/$task_id/'
      path: '/tasks/$task_id'
      fullPath: '/classes/$class_id/tasks/$task_id'
      preLoaderRoute: typeof mainLayoutClassesClassidAuthTasksTaskidIndexImport
      parentRoute: typeof mainLayoutClassesClassidAuthImport
    }
    '/(main)/_layout/classes/$class_id/_auth/teaching_plans/$teaching_plan_id/': {
      id: '/(main)/_layout/classes/$class_id/_auth/teaching_plans/$teaching_plan_id/'
      path: '/teaching_plans/$teaching_plan_id'
      fullPath: '/classes/$class_id/teaching_plans/$teaching_plan_id'
      preLoaderRoute: typeof mainLayoutClassesClassidAuthTeachingplansTeachingplanidIndexImport
      parentRoute: typeof mainLayoutClassesClassidAuthImport
    }
    '/(main)/_layout/classes/$class_id/_auth/teaching_plans/new/': {
      id: '/(main)/_layout/classes/$class_id/_auth/teaching_plans/new/'
      path: '/teaching_plans/new'
      fullPath: '/classes/$class_id/teaching_plans/new'
      preLoaderRoute: typeof mainLayoutClassesClassidAuthTeachingplansNewIndexImport
      parentRoute: typeof mainLayoutClassesClassidAuthImport
    }
    '/(main)/_layout/classes/$class_id/_auth/teaching_plans/$teaching_plan_id/edit/': {
      id: '/(main)/_layout/classes/$class_id/_auth/teaching_plans/$teaching_plan_id/edit/'
      path: '/teaching_plans/$teaching_plan_id/edit'
      fullPath: '/classes/$class_id/teaching_plans/$teaching_plan_id/edit'
      preLoaderRoute: typeof mainLayoutClassesClassidAuthTeachingplansTeachingplanidEditIndexImport
      parentRoute: typeof mainLayoutClassesClassidAuthImport
    }
  }
}

// Create and export the route tree

interface mainLayoutClassesClassidAuthRouteChildren {
  mainLayoutClassesClassidAuthIndexRoute: typeof mainLayoutClassesClassidAuthIndexRoute
  mainLayoutClassesClassidAuthTasksIndexRoute: typeof mainLayoutClassesClassidAuthTasksIndexRoute
  mainLayoutClassesClassidAuthTeachingplansIndexRoute: typeof mainLayoutClassesClassidAuthTeachingplansIndexRoute
  mainLayoutClassesClassidAuthTasksTaskidIndexRoute: typeof mainLayoutClassesClassidAuthTasksTaskidIndexRoute
  mainLayoutClassesClassidAuthTeachingplansTeachingplanidIndexRoute: typeof mainLayoutClassesClassidAuthTeachingplansTeachingplanidIndexRoute
  mainLayoutClassesClassidAuthTeachingplansNewIndexRoute: typeof mainLayoutClassesClassidAuthTeachingplansNewIndexRoute
  mainLayoutClassesClassidAuthTeachingplansTeachingplanidEditIndexRoute: typeof mainLayoutClassesClassidAuthTeachingplansTeachingplanidEditIndexRoute
}

const mainLayoutClassesClassidAuthRouteChildren: mainLayoutClassesClassidAuthRouteChildren =
  {
    mainLayoutClassesClassidAuthIndexRoute:
      mainLayoutClassesClassidAuthIndexRoute,
    mainLayoutClassesClassidAuthTasksIndexRoute:
      mainLayoutClassesClassidAuthTasksIndexRoute,
    mainLayoutClassesClassidAuthTeachingplansIndexRoute:
      mainLayoutClassesClassidAuthTeachingplansIndexRoute,
    mainLayoutClassesClassidAuthTasksTaskidIndexRoute:
      mainLayoutClassesClassidAuthTasksTaskidIndexRoute,
    mainLayoutClassesClassidAuthTeachingplansTeachingplanidIndexRoute:
      mainLayoutClassesClassidAuthTeachingplansTeachingplanidIndexRoute,
    mainLayoutClassesClassidAuthTeachingplansNewIndexRoute:
      mainLayoutClassesClassidAuthTeachingplansNewIndexRoute,
    mainLayoutClassesClassidAuthTeachingplansTeachingplanidEditIndexRoute:
      mainLayoutClassesClassidAuthTeachingplansTeachingplanidEditIndexRoute,
  }

const mainLayoutClassesClassidAuthRouteWithChildren =
  mainLayoutClassesClassidAuthRoute._addFileChildren(
    mainLayoutClassesClassidAuthRouteChildren,
  )

interface mainLayoutClassesClassidRouteChildren {
  mainLayoutClassesClassidAuthRoute: typeof mainLayoutClassesClassidAuthRouteWithChildren
}

const mainLayoutClassesClassidRouteChildren: mainLayoutClassesClassidRouteChildren =
  {
    mainLayoutClassesClassidAuthRoute:
      mainLayoutClassesClassidAuthRouteWithChildren,
  }

const mainLayoutClassesClassidRouteWithChildren =
  mainLayoutClassesClassidRoute._addFileChildren(
    mainLayoutClassesClassidRouteChildren,
  )

interface mainLayoutRouteChildren {
  mainLayoutIndexRoute: typeof mainLayoutIndexRoute
  mainLayoutClassesIndexRoute: typeof mainLayoutClassesIndexRoute
  mainLayoutProfileIndexRoute: typeof mainLayoutProfileIndexRoute
  mainLayoutSchedulesIndexRoute: typeof mainLayoutSchedulesIndexRoute
  mainLayoutTasksIndexRoute: typeof mainLayoutTasksIndexRoute
  mainLayoutTeachingmaterialsIndexRoute: typeof mainLayoutTeachingmaterialsIndexRoute
  mainLayoutClassesClassidRoute: typeof mainLayoutClassesClassidRouteWithChildren
}

const mainLayoutRouteChildren: mainLayoutRouteChildren = {
  mainLayoutIndexRoute: mainLayoutIndexRoute,
  mainLayoutClassesIndexRoute: mainLayoutClassesIndexRoute,
  mainLayoutProfileIndexRoute: mainLayoutProfileIndexRoute,
  mainLayoutSchedulesIndexRoute: mainLayoutSchedulesIndexRoute,
  mainLayoutTasksIndexRoute: mainLayoutTasksIndexRoute,
  mainLayoutTeachingmaterialsIndexRoute: mainLayoutTeachingmaterialsIndexRoute,
  mainLayoutClassesClassidRoute: mainLayoutClassesClassidRouteWithChildren,
}

const mainLayoutRouteWithChildren = mainLayoutRoute._addFileChildren(
  mainLayoutRouteChildren,
)

interface mainRouteChildren {
  mainLayoutRoute: typeof mainLayoutRouteWithChildren
}

const mainRouteChildren: mainRouteChildren = {
  mainLayoutRoute: mainLayoutRouteWithChildren,
}

const mainRouteWithChildren = mainRoute._addFileChildren(mainRouteChildren)

export interface FileRoutesByFullPath {
  '/': typeof mainLayoutIndexRoute
  '/signin': typeof authSigninIndexRoute
  '/classes': typeof mainLayoutClassesIndexRoute
  '/profile': typeof mainLayoutProfileIndexRoute
  '/schedules': typeof mainLayoutSchedulesIndexRoute
  '/tasks': typeof mainLayoutTasksIndexRoute
  '/teaching_materials': typeof mainLayoutTeachingmaterialsIndexRoute
  '/classes/$class_id': typeof mainLayoutClassesClassidAuthRouteWithChildren
  '/classes/$class_id/': typeof mainLayoutClassesClassidAuthIndexRoute
  '/classes/$class_id/tasks': typeof mainLayoutClassesClassidAuthTasksIndexRoute
  '/classes/$class_id/teaching_plans': typeof mainLayoutClassesClassidAuthTeachingplansIndexRoute
  '/classes/$class_id/tasks/$task_id': typeof mainLayoutClassesClassidAuthTasksTaskidIndexRoute
  '/classes/$class_id/teaching_plans/$teaching_plan_id': typeof mainLayoutClassesClassidAuthTeachingplansTeachingplanidIndexRoute
  '/classes/$class_id/teaching_plans/new': typeof mainLayoutClassesClassidAuthTeachingplansNewIndexRoute
  '/classes/$class_id/teaching_plans/$teaching_plan_id/edit': typeof mainLayoutClassesClassidAuthTeachingplansTeachingplanidEditIndexRoute
}

export interface FileRoutesByTo {
  '/signin': typeof authSigninIndexRoute
  '/': typeof mainLayoutIndexRoute
  '/classes': typeof mainLayoutClassesIndexRoute
  '/profile': typeof mainLayoutProfileIndexRoute
  '/schedules': typeof mainLayoutSchedulesIndexRoute
  '/tasks': typeof mainLayoutTasksIndexRoute
  '/teaching_materials': typeof mainLayoutTeachingmaterialsIndexRoute
  '/classes/$class_id': typeof mainLayoutClassesClassidAuthIndexRoute
  '/classes/$class_id/tasks': typeof mainLayoutClassesClassidAuthTasksIndexRoute
  '/classes/$class_id/teaching_plans': typeof mainLayoutClassesClassidAuthTeachingplansIndexRoute
  '/classes/$class_id/tasks/$task_id': typeof mainLayoutClassesClassidAuthTasksTaskidIndexRoute
  '/classes/$class_id/teaching_plans/$teaching_plan_id': typeof mainLayoutClassesClassidAuthTeachingplansTeachingplanidIndexRoute
  '/classes/$class_id/teaching_plans/new': typeof mainLayoutClassesClassidAuthTeachingplansNewIndexRoute
  '/classes/$class_id/teaching_plans/$teaching_plan_id/edit': typeof mainLayoutClassesClassidAuthTeachingplansTeachingplanidEditIndexRoute
}

export interface FileRoutesById {
  __root__: typeof rootRoute
  '/(main)': typeof mainRouteWithChildren
  '/(main)/_layout': typeof mainLayoutRouteWithChildren
  '/(auth)/signin/': typeof authSigninIndexRoute
  '/(main)/_layout/': typeof mainLayoutIndexRoute
  '/(main)/_layout/classes/': typeof mainLayoutClassesIndexRoute
  '/(main)/_layout/profile/': typeof mainLayoutProfileIndexRoute
  '/(main)/_layout/schedules/': typeof mainLayoutSchedulesIndexRoute
  '/(main)/_layout/tasks/': typeof mainLayoutTasksIndexRoute
  '/(main)/_layout/teaching_materials/': typeof mainLayoutTeachingmaterialsIndexRoute
  '/(main)/_layout/classes/$class_id': typeof mainLayoutClassesClassidRouteWithChildren
  '/(main)/_layout/classes/$class_id/_auth': typeof mainLayoutClassesClassidAuthRouteWithChildren
  '/(main)/_layout/classes/$class_id/_auth/': typeof mainLayoutClassesClassidAuthIndexRoute
  '/(main)/_layout/classes/$class_id/_auth/tasks/': typeof mainLayoutClassesClassidAuthTasksIndexRoute
  '/(main)/_layout/classes/$class_id/_auth/teaching_plans/': typeof mainLayoutClassesClassidAuthTeachingplansIndexRoute
  '/(main)/_layout/classes/$class_id/_auth/tasks/$task_id/': typeof mainLayoutClassesClassidAuthTasksTaskidIndexRoute
  '/(main)/_layout/classes/$class_id/_auth/teaching_plans/$teaching_plan_id/': typeof mainLayoutClassesClassidAuthTeachingplansTeachingplanidIndexRoute
  '/(main)/_layout/classes/$class_id/_auth/teaching_plans/new/': typeof mainLayoutClassesClassidAuthTeachingplansNewIndexRoute
  '/(main)/_layout/classes/$class_id/_auth/teaching_plans/$teaching_plan_id/edit/': typeof mainLayoutClassesClassidAuthTeachingplansTeachingplanidEditIndexRoute
}

export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/'
    | '/signin'
    | '/classes'
    | '/profile'
    | '/schedules'
    | '/tasks'
    | '/teaching_materials'
    | '/classes/$class_id'
    | '/classes/$class_id/'
    | '/classes/$class_id/tasks'
    | '/classes/$class_id/teaching_plans'
    | '/classes/$class_id/tasks/$task_id'
    | '/classes/$class_id/teaching_plans/$teaching_plan_id'
    | '/classes/$class_id/teaching_plans/new'
    | '/classes/$class_id/teaching_plans/$teaching_plan_id/edit'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/signin'
    | '/'
    | '/classes'
    | '/profile'
    | '/schedules'
    | '/tasks'
    | '/teaching_materials'
    | '/classes/$class_id'
    | '/classes/$class_id/tasks'
    | '/classes/$class_id/teaching_plans'
    | '/classes/$class_id/tasks/$task_id'
    | '/classes/$class_id/teaching_plans/$teaching_plan_id'
    | '/classes/$class_id/teaching_plans/new'
    | '/classes/$class_id/teaching_plans/$teaching_plan_id/edit'
  id:
    | '__root__'
    | '/(main)'
    | '/(main)/_layout'
    | '/(auth)/signin/'
    | '/(main)/_layout/'
    | '/(main)/_layout/classes/'
    | '/(main)/_layout/profile/'
    | '/(main)/_layout/schedules/'
    | '/(main)/_layout/tasks/'
    | '/(main)/_layout/teaching_materials/'
    | '/(main)/_layout/classes/$class_id'
    | '/(main)/_layout/classes/$class_id/_auth'
    | '/(main)/_layout/classes/$class_id/_auth/'
    | '/(main)/_layout/classes/$class_id/_auth/tasks/'
    | '/(main)/_layout/classes/$class_id/_auth/teaching_plans/'
    | '/(main)/_layout/classes/$class_id/_auth/tasks/$task_id/'
    | '/(main)/_layout/classes/$class_id/_auth/teaching_plans/$teaching_plan_id/'
    | '/(main)/_layout/classes/$class_id/_auth/teaching_plans/new/'
    | '/(main)/_layout/classes/$class_id/_auth/teaching_plans/$teaching_plan_id/edit/'
  fileRoutesById: FileRoutesById
}

export interface RootRouteChildren {
  mainRoute: typeof mainRouteWithChildren
  authSigninIndexRoute: typeof authSigninIndexRoute
}

const rootRouteChildren: RootRouteChildren = {
  mainRoute: mainRouteWithChildren,
  authSigninIndexRoute: authSigninIndexRoute,
}

export const routeTree = rootRoute
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()

/* ROUTE_MANIFEST_START
{
  "routes": {
    "__root__": {
      "filePath": "__root.tsx",
      "children": [
        "/(main)",
        "/(auth)/signin/"
      ]
    },
    "/(main)": {
      "filePath": "(main)",
      "children": [
        "/(main)/_layout"
      ]
    },
    "/(main)/_layout": {
      "filePath": "(main)/_layout.tsx",
      "parent": "/(main)",
      "children": [
        "/(main)/_layout/",
        "/(main)/_layout/classes/",
        "/(main)/_layout/profile/",
        "/(main)/_layout/schedules/",
        "/(main)/_layout/tasks/",
        "/(main)/_layout/teaching_materials/",
        "/(main)/_layout/classes/$class_id"
      ]
    },
    "/(auth)/signin/": {
      "filePath": "(auth)/signin/index.tsx"
    },
    "/(main)/_layout/": {
      "filePath": "(main)/_layout/index.tsx",
      "parent": "/(main)/_layout"
    },
    "/(main)/_layout/classes/": {
      "filePath": "(main)/_layout/classes/index.tsx",
      "parent": "/(main)/_layout"
    },
    "/(main)/_layout/profile/": {
      "filePath": "(main)/_layout/profile/index.tsx",
      "parent": "/(main)/_layout"
    },
    "/(main)/_layout/schedules/": {
      "filePath": "(main)/_layout/schedules/index.tsx",
      "parent": "/(main)/_layout"
    },
    "/(main)/_layout/tasks/": {
      "filePath": "(main)/_layout/tasks/index.tsx",
      "parent": "/(main)/_layout"
    },
    "/(main)/_layout/teaching_materials/": {
      "filePath": "(main)/_layout/teaching_materials/index.tsx",
      "parent": "/(main)/_layout"
    },
    "/(main)/_layout/classes/$class_id": {
      "filePath": "(main)/_layout/classes/$class_id",
      "parent": "/(main)/_layout",
      "children": [
        "/(main)/_layout/classes/$class_id/_auth"
      ]
    },
    "/(main)/_layout/classes/$class_id/_auth": {
      "filePath": "(main)/_layout/classes/$class_id/_auth.tsx",
      "parent": "/(main)/_layout/classes/$class_id",
      "children": [
        "/(main)/_layout/classes/$class_id/_auth/",
        "/(main)/_layout/classes/$class_id/_auth/tasks/",
        "/(main)/_layout/classes/$class_id/_auth/teaching_plans/",
        "/(main)/_layout/classes/$class_id/_auth/tasks/$task_id/",
        "/(main)/_layout/classes/$class_id/_auth/teaching_plans/$teaching_plan_id/",
        "/(main)/_layout/classes/$class_id/_auth/teaching_plans/new/",
        "/(main)/_layout/classes/$class_id/_auth/teaching_plans/$teaching_plan_id/edit/"
      ]
    },
    "/(main)/_layout/classes/$class_id/_auth/": {
      "filePath": "(main)/_layout/classes/$class_id/_auth.index.tsx",
      "parent": "/(main)/_layout/classes/$class_id/_auth"
    },
    "/(main)/_layout/classes/$class_id/_auth/tasks/": {
      "filePath": "(main)/_layout/classes/$class_id/_auth.tasks/index.tsx",
      "parent": "/(main)/_layout/classes/$class_id/_auth"
    },
    "/(main)/_layout/classes/$class_id/_auth/teaching_plans/": {
      "filePath": "(main)/_layout/classes/$class_id/_auth.teaching_plans/index.tsx",
      "parent": "/(main)/_layout/classes/$class_id/_auth"
    },
    "/(main)/_layout/classes/$class_id/_auth/tasks/$task_id/": {
      "filePath": "(main)/_layout/classes/$class_id/_auth.tasks/$task_id/index.tsx",
      "parent": "/(main)/_layout/classes/$class_id/_auth"
    },
    "/(main)/_layout/classes/$class_id/_auth/teaching_plans/$teaching_plan_id/": {
      "filePath": "(main)/_layout/classes/$class_id/_auth.teaching_plans/$teaching_plan_id/index.tsx",
      "parent": "/(main)/_layout/classes/$class_id/_auth"
    },
    "/(main)/_layout/classes/$class_id/_auth/teaching_plans/new/": {
      "filePath": "(main)/_layout/classes/$class_id/_auth.teaching_plans/new/index.tsx",
      "parent": "/(main)/_layout/classes/$class_id/_auth"
    },
    "/(main)/_layout/classes/$class_id/_auth/teaching_plans/$teaching_plan_id/edit/": {
      "filePath": "(main)/_layout/classes/$class_id/_auth.teaching_plans/$teaching_plan_id/edit/index.tsx",
      "parent": "/(main)/_layout/classes/$class_id/_auth"
    }
  }
}
ROUTE_MANIFEST_END */
