import { yupResolver } from "@hookform/resolvers/yup";
import { <PERSON><PERSON>, <PERSON>ack, Typography } from "@mui/material";
import {
  useDeleteSessionDataFromCookies,
  useWriteSessionDataToCookies,
} from "@sisva/hooks/query/useAuth";
import { useWriteSchoolToLocalStorage } from "@sisva/hooks/query/useSchools";
import { useNotificationAPI } from "@sisva/providers";
import { MUIThemeProvider } from "@sisva/providers";
import type { School } from "@sisva/types/apiTypes";
import { Image } from "@sisva/ui";
import { domainMappings, getFileUrl } from "@sisva/utils";
import { themeConfigMUI } from "@sisva/utils";
import { getRouteApi, Link } from "@tanstack/react-router";
import { useLocalStorageState } from "ahooks";
import axios from "axios";
import { useEffect, useState } from "react";
import {
  CheckboxElement,
  PasswordElement,
  TextFieldElement,
  useForm,
} from "react-hook-form-mui";
import { object, string } from "yup";

import LogoSisva from "#/assets/images/Sisva-LogoType-Black.png";
import { SchoolCodeIllustration } from "#/assets/svgr/SchoolCodeIllustration";

export default function SignInPage() {
  const notification = useNotificationAPI();
  const [step, setStep] = useState(1);
  const [display, setDisplay] = useState<"form" | "information">("form");
  const [school] = useLocalStorageState<School>("schoolData", {
    listenStorageChange: true,
  });
  const routeApi = getRouteApi("/(auth)/signin/");
  const navigate = routeApi.useNavigate();
  const { school_code } = routeApi.useSearch();
  const domainMapping = domainMappings[window.location.host];
  const [blank, setBlank] = useState(!!school_code || !!domainMapping);

  const { mutate: writeSchoolToLocalStorage } = useWriteSchoolToLocalStorage({
    onError() {
      notification.error({
        message: "Sekolah tidak ditemukan",
      });
    },
    onSuccess() {
      setStep(2);
    },
  });

  const { control, handleSubmit } = useForm({
    values: {
      code: "",
    },
    resolver: yupResolver(
      object({
        code: string().required("Kode sekolah harus diisi"),
      })
    ),
  });

  useEffect(() => {
    if (domainMapping && school_code) {
      navigate({ to: "/signin" });
      return;
    }
    const code = domainMapping?.code ?? school_code;
    if (code) {
      writeSchoolToLocalStorage(code, {
        onSettled() {
          setBlank(false);
        },
      });
    }
  }, [domainMapping, navigate, school_code, writeSchoolToLocalStorage]);

  if (blank) return null;

  if (step === 1)
    return (
      <div className="flex flex-col items-center justify-center h-svh px-4 gap-2">
        <SchoolCodeIllustration color={"#008CD5"} />
        <Typography
          sx={{
            textAlign: "center",
            fontWeight: 700,
            fontSize: 24,
            color: "#008CD5",
            mt: 2,
          }}
        >
          Hai, Selamat Datang di Sisva!
        </Typography>
        <Typography sx={{ textAlign: "center", mb: 3 }}>
          Masukkan kode sekolah untuk melanjutkan.
        </Typography>
        <Stack
          sx={{ width: "100%", alignItems: "center", gap: 1 }}
          component="form"
          onSubmit={handleSubmit((value) => {
            writeSchoolToLocalStorage(value.code);
          })}
        >
          <TextFieldElement
            control={control}
            sx={{ maxWidth: "280px", width: "100%" }}
            name="code"
            label="Kode Sekolah"
          />
          <Button
            variant="contained"
            sx={{ maxWidth: "280px", width: "100%" }}
            type="submit"
          >
            Lanjutkan
          </Button>
        </Stack>
      </div>
    );

  return (
    <MUIThemeProvider themeConfig={themeConfigMUI}>
      <div className="grid grid-cols-1  md:grid-cols-2 h-svh w-full">
        <div className="size-full relative hidden md:block">
          <Image
            src={getFileUrl(school!.landing_image_uri, school!.id)}
            alt="Background"
            fill
            className="object-cover"
          />
        </div>
        <div className="flex flex-col items-center px-4 justify-center gap-4">
          <div className="size-36 relative">
            <Image
              src={getFileUrl(school!.logo_uri, school!.id)}
              alt="Background"
              fill
              className="object-contain"
            />
          </div>
          <Stack
            sx={{
              gap: 0.5,
              alignItems: "center",
            }}
          >
            <div className="text-2xl font-bold">{school!.name}</div>
            <Stack
              direction="row"
              sx={{
                alignItems: "center",
                gap: "6px",
              }}
            >
              <Typography sx={{ fontSize: 13 }} color="grey">
                Powered by
              </Typography>
              <Image alt="Web Image" src={LogoSisva} width={56} height={30} />
            </Stack>
          </Stack>
          <div className="w-full max-w-[450px]">
            {display === "information" && (
              <Information onBackClick={() => setDisplay("form")} />
            )}
            {display === "form" && (
              <LoginForm onInfoClick={() => setDisplay("information")} />
            )}
          </div>
        </div>
      </div>
    </MUIThemeProvider>
  );
}

function LoginForm({ onInfoClick }: { onInfoClick?: () => void }) {
  const notification = useNotificationAPI();
  const [school] = useLocalStorageState<School>("schoolData", {
    listenStorageChange: true,
  });
  const routeApi = getRouteApi("/(auth)/signin/");
  const navigate = routeApi.useNavigate();
  const { control, handleSubmit } = useForm({
    values: {
      username: "",
      password: "",
      school_id: school!.id,
      remember: false,
    },
  });

  const { mutate: deleteSessionDataFromCookies } =
    useDeleteSessionDataFromCookies();

  const { mutate: writeSessionDataToCookies } = useWriteSessionDataToCookies({
    async onSuccess({ user }) {
      if (user.type !== "student" && user.type !== "teacher") {
        deleteSessionDataFromCookies();
        return notification.error({
          message: "Hanya akun guru dan siswa yang dapat masuk",
        });
      }

      notification.success({ message: "Login berhasil" });
      navigate({ to: "/" });
    },
    onError(error) {
      if (axios.isAxiosError(error)) {
        try {
          if (error.response?.data?.errors.includes("INVALID_PASSWORD")) {
            return notification.error({
              message: "Password salah",
            });
          }

          if (error.response?.data?.errors.includes("DATA_NOT_FOUND")) {
            return notification.error({
              message: "Username tidak ditemukan",
            });
          }
        } catch (error) {}
      }
      notification.error({
        message: "Login gagal",
      });
    },
  });

  return (
    <>
      <Stack
        sx={{
          mb: 2,
        }}
      >
        <Typography
          sx={{
            fontSize: 18,
            fontWeight: "bold",
          }}
        >
          Login
        </Typography>
        <Typography
          color="grey"
          sx={{
            fontSize: 13,
          }}
        >
          Silakan masukkan detail akun anda.
        </Typography>
      </Stack>
      <Stack
        component="form"
        sx={{
          gap: "8px",
          mb: 2,
        }}
        onSubmit={handleSubmit((values) => {
          writeSessionDataToCookies(values);
        })}
      >
        <Typography
          variant="body2"
          sx={{
            fontWeight: "500",
            fontSize: 13,
          }}
        >
          Username
        </Typography>
        <TextFieldElement
          control={control}
          name="username"
          placeholder="Masukan Username"
        />
        <Typography
          variant="body2"
          sx={{
            fontWeight: "500",
            fontSize: 13,
          }}
        >
          Password
        </Typography>
        <PasswordElement
          control={control}
          name="password"
          placeholder="Masukan Password"
        />
        <CheckboxElement control={control} name="remember" label="Ingat Saya" />
        <Button type="submit" variant="contained">
          Masuk
        </Button>
      </Stack>
      <Typography
        align="center"
        sx={{
          mt: 3,
          fontSize: 13,
        }}
      >
        Tidak bisa masuk?
      </Typography>
      <Stack
        sx={{
          flexDirection: "row",
          justifyContent: "center",
        }}
      >
        <Typography
          onClick={onInfoClick}
          color="primary"
          align="center"
          sx={{
            fontWeight: "500",
            fontSize: 13,
            cursor: "pointer",
          }}
        >
          Lihat informasi selengkapnya
        </Typography>
      </Stack>
    </>
  );
}

function Information({ onBackClick }: { onBackClick?: () => void }) {
  return (
    <>
      <Stack
        sx={{
          mb: 2,
        }}
      >
        <Typography
          sx={{
            fontSize: 18,
            fontWeight: "bold",
          }}
        >
          Informasi Akun
        </Typography>
        <Typography
          color="grey"
          sx={{
            fontSize: 13,
          }}
        >
          Berikut adalah beberapa informasi umum yang perlu diketahui.
        </Typography>
        <ul className="list-inside flex flex-col gap-2 py-4">
          <Typography
            component="li"
            sx={{
              fontSize: 13,
            }}
          >
            Akun Sisva dibuat dan dikelola oleh administrator sekolah.
          </Typography>
          <Typography
            component="li"
            sx={{
              fontSize: 13,
            }}
          >
            Akun Sisva hanya diberikan kepada sekolah yang telah bekerjasama
            dengan aplikasi Sisva.
          </Typography>
          <Typography
            component="li"
            sx={{
              fontSize: 13,
            }}
          >
            Untuk pembuatan akun Sisva, silakan menghubungi administrator
            sekolah.
          </Typography>
          <Typography
            component="li"
            sx={{
              fontSize: 13,
            }}
          >
            Jika lupa password akun Sisva, silakan menghubungi administrator
            sekolah.
          </Typography>
        </ul>
      </Stack>
      <Stack
        sx={{
          gap: "8px",
          mb: 2,
        }}
      >
        <Button type="submit" variant="contained" onClick={onBackClick}>
          Kembali ke halaman Login
        </Button>
      </Stack>
    </>
  );
}
