import CMS<PERSON><PERSON> from "@sisva/api/cms";
import UsersAP<PERSON> from "@sisva/api/users";
import type { School, User } from "@sisva/types/apiTypes";
import { createFileRoute, redirect } from "@tanstack/react-router";
import { object, optional, string } from "valibot";

import SignInPage from "./-components/SignInPage";

export const Route = createFileRoute("/(auth)/signin/")({
  validateSearch: object({
    school_code: optional(string()),
  }),
  async beforeLoad() {
    let currentUser: User;
    let currentSchool: School;
    try {
      currentUser = (await UsersAPI.getCurrentUser()).data.data;
      currentSchool = (await CMSAPI.getCurrentSchool()).data.data;
    } catch {}

    if (!!currentUser! && !!currentSchool!) throw redirect({ to: "/" });
  },
  component: SignInPage,
  head: () => ({
    meta: [
      {
        title: "Login | Sisva",
      },
    ],
  }),
});
