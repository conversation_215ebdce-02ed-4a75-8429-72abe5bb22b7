import { useSubject } from "@sisva/hooks/query/academic/useSubjects";
import {
  useStudentsTasks,
  useTeachersTasks,
} from "@sisva/hooks/query/classroom/useTasks";
import { useCurrentUser } from "@sisva/providers";
import { SearchSm } from "@untitled-ui/icons-react";
import { Input, Skeleton, theme } from "antd";
import { Empty } from "antd";
import dayjs from "dayjs";
import id from "dayjs/locale/id";
import fuzzysort from "fuzzysort";
import { Fragment, useDeferredValue, useState } from "react";

import { GroupHeader } from "#/routes/(main)/-components/GroupHeader";
import TaskCard from "#/routes/(main)/-components/TaskCard";
dayjs.locale(id);

export default function TasksPage() {
  const currentUser = useCurrentUser();
  const [search, setSearch] = useState("");
  const defferedSearch = useDeferredValue(search);

  const token = theme.useToken().token;
  const { data: teachhersTasks = [], isLoading: L1 } = useTeachersTasks(
    currentUser.id
  );
  const { data: studentsTasks = [], isLoading: L2 } = useStudentsTasks(
    currentUser.id
  );

  const tasks = currentUser.type === "student" ? studentsTasks : teachhersTasks;

  const filteredTasks = fuzzysort
    .go(defferedSearch, tasks, {
      keys: ["name", "description", "class.subject_name", "class.teacher_name"],
      all: !defferedSearch,
    })
    .map((result) => result.obj);

  const tasksGroupedBySubjectId = Object.entries(
    Object.groupBy(filteredTasks, (task) => task.class?.subject_id ?? 0)
  );

  return (
    <div className="flex flex-col items-center px-4 grow">
      <div className="flex flex-col py-8 w-full max-w-5xl gap-4 h-full">
        <h2>Daftar Tugas</h2>
        <div className="flex gap-2">
          <Input
            placeholder="Cari"
            onChange={(e) => setSearch(e.target.value)}
            value={search}
            suffix={<SearchSm color={token.colorTextLabel} />}
          />
        </div>
        <div className="size-full flex flex-col gap-4">
          <div className="flex flex-col items-start gap-4">
            <div className="w-full flex flex-col gap-4">
              {(() => {
                if (L1 || L2) return <Skeleton active />;
                if (tasksGroupedBySubjectId.length === 0) return <Empty />;
                return tasksGroupedBySubjectId.map(([subject_id, tasks]) => {
                  return (
                    <Fragment key={subject_id}>
                      <SubjectTitle subject_id={subject_id} />
                      <div className="grid grid-cols-1  lg:grid-cols-2 gap-4">
                        {tasks?.map((task) => (
                          <TaskCard key={task.id} task={task} />
                        ))}
                      </div>
                    </Fragment>
                  );
                });
              })()}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

function SubjectTitle({ subject_id }: { subject_id: number | string }) {
  const { data: subject } = useSubject(subject_id);

  return <GroupHeader text={subject?.name ?? ""} />;
}
