import { useCurrentUser } from "@sisva/providers";
import { SisvaScheduleComponent } from "@sisva/ui";

export default function JadwalSection() {
  const currentUser = useCurrentUser();

  return (
    <div className="shadow-xl rounded-lg p-4 flex flex-col gap-4">
      <div className="font-semibold">Jadwal</div>
      <div className="overflow-auto max-h-64 pe-2 size-full flex flex-col gap-4">
        <div className="flex flex-col gap-4">
          <SisvaScheduleComponent
            user_id={currentUser.id}
            currentView="Day"
            timeScale={{ enable: true, interval: 60, slotCount: 4 }}
          />
        </div>
      </div>
    </div>
  );
}
