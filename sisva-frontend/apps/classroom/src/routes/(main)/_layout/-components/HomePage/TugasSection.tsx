import { useClass } from "@sisva/hooks/query/academic/useClasses";
import { useSubmissions } from "@sisva/hooks/query/classroom/useSubmissions";
import {
  useStudentsTasks,
  useTeachersTasks,
} from "@sisva/hooks/query/classroom/useTasks";
import { useCurrentUser } from "@sisva/providers";
import type { Task, User } from "@sisva/types/apiTypes";
import { isDateEmpty } from "@sisva/utils";
import { Empty, Skeleton, Tag, theme } from "antd";
import dayjs from "dayjs";

export default function TugasSection() {
  const currentUser = useCurrentUser();

  return (
    <div className="shadow-xl rounded-lg p-4 gap-4 flex flex-col">
      <div className="font-semibold">Tugas</div>
      <div className="overflow-auto max-h-64 pe-2 size-full flex flex-col gap-4">
        {currentUser.type === "student" && (
          <TasksPerStudent key={currentUser.id} student={currentUser} />
        )}
        {currentUser.type === "teacher" && (
          <TasksPerTeacher key={currentUser.id} teacher={currentUser} />
        )}
      </div>
    </div>
  );
}

function TasksPerStudent({ student }: { student: User }) {
  const { data: tasks = [], isLoading } = useStudentsTasks(student.id);

  return (
    <div className="flex flex-col items-start gap-4">
      <div className="w-full flex flex-col gap-2">
        {(() => {
          if (isLoading) return <Skeleton active />;
          if (tasks.length === 0) return <Empty />;
          return tasks.map((task) => (
            <TaskCard key={task.id} task={task} student={student} />
          ));
        })()}
      </div>
    </div>
  );
}

function TasksPerTeacher({ teacher }: { teacher: User }) {
  const { data: tasks = [], isLoading } = useTeachersTasks(teacher.id);

  return (
    <div className="flex flex-col items-start gap-4">
      <div className="w-full flex flex-col gap-2">
        {(() => {
          if (isLoading) return <Skeleton active />;
          if (tasks.length === 0) return <Empty />;
          return tasks.map((task) => <TaskCard key={task.id} task={task} />);
        })()}
      </div>
    </div>
  );
}

function TaskCard({ task, student }: { task: Task; student?: User }) {
  const { data: class_ } = useClass(task.class_id);
  const token = theme.useToken().token;
  const { data: submissions = [] } = useSubmissions({ task_id: task.id });
  const submitted = (() => {
    const submission = submissions.find(
      (submission) => submission.student_id === student?.id
    );
    if (submission) return true;
    return false;
  })();

  return (
    <div
      style={{
        backgroundColor: token.colorPrimary,
      }}
      className="text-white text-xs p-2 rounded-md flex flex-col gap-1 relative overflow-hidden"
    >
      <div className="size-32 absolute rounded-full bg-gradient-to-r from-slate-50/10 to-slate-50/0 rotate-45 right-0 bottom-6" />

      <div className="text-base font-semibold">{task.name}</div>
      <div>{class_?.subject_name}</div>
      <div>{class_?.teacher_name}</div>
      <div className="flex justify-between pt-1 items-center">
        {(() => {
          if (student && task.allow_submission)
            return (
              <Tag color={submitted ? "success" : "error"} bordered={false}>
                {submitted ? "Sudah Mengumpulkan" : "Belum Mengumpulkan"}
              </Tag>
            );
          return <div />;
        })()}
        <div className="flex gap-2 items-center">
          <div>Deadline</div>
          <div>
            {(() => {
              const deadlineDayjs = dayjs(
                task.deadline,
                "DD/MM/YYYY hh:mm A Z"
              );
              return isDateEmpty(deadlineDayjs)
                ? "Tidak ada"
                : deadlineDayjs.format("DD/MM/YYYY hh:mm A");
            })()}
          </div>
        </div>
      </div>
    </div>
  );
}
