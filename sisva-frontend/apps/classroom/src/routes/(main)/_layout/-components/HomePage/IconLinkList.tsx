import { Link } from "@tanstack/react-router";

import { JadwalPelajaranIcon } from "#/assets/svgr/JadwalPelajaranIcon";
import { TeksIcon } from "#/assets/svgr/TeksIcon";
import { TugasIcon } from "#/assets/svgr/TugasIcon";

export default function IconLinkList() {
  return (
    <div className="flex gap-8 flex-wrap justify-center">
      <Link to="/tasks" className="flex flex-col items-center gap-1">
        <TugasIcon className="w-14" />
        <div className="w-20 text-center text-sm">Tugas</div>
      </Link>
      <Link
        to="/teaching_materials"
        className="flex flex-col items-center gap-1"
      >
        <TeksIcon className="w-14" />
        <div className="w-20 text-center text-sm"><PERSON><PERSON></div>
      </Link>
      <Link to="/schedules" className="flex flex-col items-center gap-1">
        <JadwalPelajaranIcon className="w-14" />
        <div className="w-20 text-center text-sm">J<PERSON>wal Pelajaran</div>
      </Link>
    </div>
  );
}
