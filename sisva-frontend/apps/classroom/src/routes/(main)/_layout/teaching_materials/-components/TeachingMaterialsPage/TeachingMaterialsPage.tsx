import { useSubject } from "@sisva/hooks/query/academic/useSubjects";
import { useTeachingMaterials } from "@sisva/hooks/query/classroom/useTeachingMaterials";
import { useCurrentUser } from "@sisva/providers";
import { cn } from "@sisva/utils";
import { SearchSm } from "@untitled-ui/icons-react";
import { Form, Input, Select, Skeleton, theme } from "antd";
import { Empty } from "antd";
import dayjs from "dayjs";
import id from "dayjs/locale/id";
import fuzzysort from "fuzzysort";
import { Fragment, useDeferredValue } from "react";
import { useForm } from "react-hook-form";
import { FormItem } from "react-hook-form-antd";

import { CreateTeachingMaterialButton } from "#/routes/(main)/-components/CreateTeachingMaterialButton";
import { GroupHeader } from "#/routes/(main)/-components/GroupHeader";
import TeachingMaterialCard from "#/routes/(main)/-components/TeachingMaterialCard";

dayjs.locale(id);

export default function TeachingMaterialsPage() {
  const currentUser = useCurrentUser();

  const token = theme.useToken().token;
  const { data: teachingMaterials = [], isLoading: L1 } =
    useTeachingMaterials();

  const { control, watch } = useForm({
    defaultValues: {
      searchText: "",
      curriculum_name: undefined,
      study_program_name: undefined,
      grade: undefined,
      subject_name: undefined,
    },
  });

  const searchText = watch("searchText");
  const curriculum_name = watch("curriculum_name");
  const grade = watch("grade");
  const study_program_name = watch("study_program_name");
  const subject_name = watch("subject_name");

  const defferedSearch = useDeferredValue(searchText);

  const curriculumNames = Array.from(
    new Set(teachingMaterials.map((item) => item.curriculum_name))
  );
  const grades = Array.from(
    new Set(teachingMaterials.map((item) => item.grade))
  );
  const studyProgramNames = Array.from(
    new Set(teachingMaterials.map((item) => item.study_program_name))
  );
  const subjectNames = Array.from(
    new Set(teachingMaterials.map((item) => item.subject_name))
  );

  const filteredTeachingMaterials = fuzzysort
    .go(
      defferedSearch,
      teachingMaterials
        .filter((item) =>
          curriculum_name ? item.curriculum_name === curriculum_name : true
        )
        .filter((item) => (grade ? item.grade === grade : true))
        .filter((item) =>
          study_program_name
            ? item.study_program_name === study_program_name
            : true
        )
        .filter((item) =>
          subject_name ? item.subject_name === subject_name : true
        ),
      {
        keys: [
          "subject_name",
          "description",
          "curriculum_name",
          "study_program_name",
          "grade",
        ],
        all: !defferedSearch,
      }
    )
    .map((data) => data.obj);

  const teachingMaterialsGroupedBySubjectId = Object.entries(
    Object.groupBy(filteredTeachingMaterials, (material) => material.subject_id)
  );

  return (
    <div className="flex flex-col items-center px-4 grow">
      <div className="flex flex-col py-8 w-full max-w-5xl gap-4 h-full">
        <div className="flex justify-between gap-4">
          <h2>Daftar Bahan Ajar</h2>
          {currentUser?.type !== "student" && <CreateTeachingMaterialButton />}
        </div>
        <div className="flex flex-col gap-2">
          <Form className="contents thick-scrollbar">
            <FormItem control={control} name="searchText" className="contents">
              <Input
                placeholder="Cari"
                suffix={<SearchSm color={token.colorTextLabel} />}
              />
            </FormItem>
            <div className="flex gap-2 overflow-auto pe-1">
              <FormItem
                control={control}
                name="curriculum_name"
                className={cn("min-w-48")}
              >
                <Select
                  options={curriculumNames.map((item) => ({
                    label: item,
                    value: item,
                  }))}
                  placeholder="Kurikulum"
                  allowClear
                />
              </FormItem>
              <FormItem
                control={control}
                name="grade"
                className={cn("min-w-48")}
              >
                <Select
                  options={grades.map((item) => ({
                    label: item,
                    value: item,
                  }))}
                  placeholder="Tingkatan"
                  allowClear
                />
              </FormItem>
              <FormItem
                control={control}
                name="study_program_name"
                className={cn("min-w-48")}
              >
                <Select
                  options={studyProgramNames.map((item) => ({
                    label: item,
                    value: item,
                  }))}
                  placeholder="Program Studi"
                  allowClear
                  showSearch
                />
              </FormItem>
              <FormItem
                control={control}
                name="subject_name"
                className={cn("min-w-48")}
              >
                <Select
                  options={subjectNames.map((item) => ({
                    label: item,
                    value: item,
                  }))}
                  placeholder="Mata Pelajaran"
                  allowClear
                  showSearch
                />
              </FormItem>
            </div>
          </Form>
        </div>
        <div className="size-full flex flex-col gap-4">
          <div className="flex flex-col items-start gap-4">
            <div className="w-full flex flex-col gap-4">
              {(() => {
                if (L1) return <Skeleton active />;
                if (teachingMaterialsGroupedBySubjectId.length === 0)
                  return <Empty />;
                return teachingMaterialsGroupedBySubjectId.map(
                  ([subject_id, materials]) => {
                    return (
                      <Fragment key={subject_id}>
                        <SubjectTitle subject_id={subject_id} />
                        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                          {materials?.map((material) => (
                            <TeachingMaterialCard
                              key={material.id}
                              material={material}
                            />
                          ))}
                        </div>
                      </Fragment>
                    );
                  }
                );
              })()}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

function SubjectTitle({ subject_id }: { subject_id: number | string }) {
  const { data: subject } = useSubject(subject_id);

  return <GroupHeader text={subject?.name ?? ""} />;
}
