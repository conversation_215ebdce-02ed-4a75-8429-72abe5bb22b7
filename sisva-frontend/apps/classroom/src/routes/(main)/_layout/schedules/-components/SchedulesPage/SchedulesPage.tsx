import { useCurrentUser } from "@sisva/providers";
import { SisvaScheduleComponent } from "@sisva/ui";

export default function SchedulesPage() {
  const currentUser = useCurrentUser();
  return (
    <div className="flex flex-col items-center px-4 grow">
      <div className="size-full max-w-5xl py-8 flex flex-col gap-4">
        <h2><PERSON>adwal Pelajaran</h2>
        <div className="flex gap-2">{/* filters */}</div>
        <div className="size-full flex flex-col gap-4">
          <div className="flex flex-col gap-4 thick-scrollbar">
            <div className="overflow-auto">
              <div className="w-[1600px] h-[65svh]">
                <SisvaScheduleComponent user_id={currentUser.id} />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
