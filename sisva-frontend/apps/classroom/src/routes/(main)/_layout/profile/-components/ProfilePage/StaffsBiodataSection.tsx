import { yupResolver } from "@hookform/resolvers/yup";
import { useUploadFile } from "@sisva/hooks/query/file/useFiles";
import { invalidateCurrentUser } from "@sisva/hooks/query/useAuth";
import { useUpdateStaff } from "@sisva/hooks/query/user/useStaffTeachers";
import { useNotificationAPI } from "@sisva/providers";
import { useCurrentUser } from "@sisva/providers";
import {
  genderOptions,
  nationalityOptions,
  religionOptions,
} from "@sisva/types/dropdownOptions";
import type { UpdateStaffForm } from "@sisva/types/formTypes";
import { updateStaffSchema } from "@sisva/types/formTypes";
import {
  getGenderText,
  getNationalityText,
  getReligionText,
} from "@sisva/types/types";
import { AvatarWithAcronym } from "@sisva/ui";
import { cn } from "@sisva/utils";
import { useQueryClient } from "@tanstack/react-query";
import { useRouter } from "@tanstack/react-router";
import { useToggle } from "ahooks";
import {
  Button,
  Divider,
  Form,
  Input,
  InputNumber,
  Select,
  Upload,
} from "antd";
import { useForm } from "react-hook-form";
import { FormItem as RHFFormItem } from "react-hook-form-antd";

export default function StaffsBiodataSection() {
  const notification = useNotificationAPI();
  const currentUser = useCurrentUser();
  const { mutateAsync: uploadFile } = useUploadFile();
  const [isEdit, { toggle: toggleEdit }] = useToggle(false);
  const router = useRouter();
  const queryClient = useQueryClient();

  const { mutate: updateStaff } = useUpdateStaff({
    user_id: currentUser.id,
    async onSuccess() {
      invalidateCurrentUser(queryClient);
      await router.invalidate();
      notification.success({
        message: "Data berhasil disimpan",
      });
      toggleEdit();
    },
    onError() {
      notification.error({
        message: "Data gagal disimpan",
      });
    },
  });

  const jsonText = (() => {
    try {
      return JSON.parse(currentUser.detail.json_text);
    } catch (error) {
      return {};
    }
  })();

  const { control, handleSubmit, reset, setValue, watch } = useForm({
    values: {
      name: currentUser.name,
      permissions: currentUser.permissions,
      email: jsonText.email,
      phone: jsonText.phone,
      gender: jsonText.gender,
      nationality: jsonText.nationality,
      nik: currentUser.nik,
      nuptk: jsonText.nuptk,
      religion: jsonText.religion,
      address: jsonText.address,
      profile_image_uri: currentUser.profile_image_uri,
    },
    resolver: yupResolver(updateStaffSchema),
  });

  return (
    <div className="flex flex-col items-center">
      <div className="flex flex-col items-center gap-2">
        <AvatarWithAcronym
          name={currentUser.name}
          uri={watch("profile_image_uri")}
          size={64}
        />
        {isEdit && (
          <Upload
            beforeUpload={async (file) => {
              const formData = new FormData();
              formData.append("file", file);
              const uri = await uploadFile(formData);
              setValue("profile_image_uri", uri);
            }}
            multiple={false}
            maxCount={1}
            accept="image/*"
            showUploadList={false}
          >
            <Button size="small">Ubah Foto Profil</Button>
          </Upload>
        )}
      </div>
      <Divider />
      <Form
        layout="vertical"
        className="w-full"
        onFinish={handleSubmit((values) => {
          updateStaff(values);
        })}
        key={String(isEdit)}
      >
        <div className="flex flex-col items-end">
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-x-8 w-full">
            <FormItem
              label="Email"
              value={jsonText.email}
              name="email"
              control={control}
              isEdit={isEdit}
            />
            <FormItem
              label="NIK"
              value={currentUser.nik}
              name="nik"
              isEdit={isEdit}
              control={control}
              inputType="number"
            />
            <FormItem
              label="Nomor Telepon"
              value={jsonText.phone}
              name="phone"
              isEdit={isEdit}
              inputType="number"
              control={control}
            />
            <FormItem
              label="NUPTK"
              value={jsonText.nuptk}
              name="nuptk"
              control={control}
              isEdit={isEdit}
            />
            <FormItem
              label="Jenis Kelamin"
              value={jsonText.gender}
              name="gender"
              isEdit={isEdit}
              options={genderOptions}
              getTextFn={getGenderText}
              control={control}
            />
            <FormItem
              label="Agama"
              value={jsonText.religion}
              name="religion"
              isEdit={isEdit}
              options={religionOptions}
              getTextFn={getReligionText}
              control={control}
            />
            <FormItem
              label="Kewarganegaraan"
              value={jsonText.nationality}
              name="nationality"
              isEdit={isEdit}
              options={nationalityOptions}
              getTextFn={getNationalityText}
              control={control}
            />
            <FormItem
              label="Alamat"
              value={jsonText.address}
              name="address"
              isEdit={isEdit}
              control={control}
            />
          </div>
          {isEdit && (
            <div className="flex gap-2">
              <Button
                type="default"
                shape="round"
                onClick={() => {
                  toggleEdit();
                  reset();
                }}
              >
                Batal
              </Button>
              <Button type="primary" shape="round" htmlType="submit">
                Simpan
              </Button>
            </div>
          )}
        </div>
      </Form>
      {!isEdit && (
        <Button
          type="primary"
          shape="round"
          className="self-end"
          onClick={toggleEdit}
        >
          Edit
        </Button>
      )}
    </div>
  );
}

function FormItem<T extends string>({
  label,
  value,
  name,
  isEdit,
  options,
  inputType = "text",
  getTextFn,
  control,
}: {
  label: string;
  value: T;
  name: keyof UpdateStaffForm;
  isEdit: boolean;
  options?: {
    label: string;
    value: string;
  }[];
  inputType?: "text" | "select" | "number";
  control: ReturnType<typeof useForm<UpdateStaffForm>>["control"];
  getTextFn?: (value: T) => string;
}) {
  return (
    <RHFFormItem
      label={<div className="font-medium">{label}</div>}
      name={name}
      control={control}
    >
      {(() => {
        if (isEdit) {
          if (options) return <Select options={options} placeholder={label} />;
          if (inputType === "number")
            return (
              <InputNumber
                placeholder={label}
                controls={false}
                style={{ width: "100%" }}
              />
            );
          return <Input placeholder={label} />;
        }

        return (
          <div
            className={cn("text-sm text-neutral-500", {
              italic: !value,
            })}
          >
            {(getTextFn ? getTextFn(value) : value) || "Data tidak tersedia"}
          </div>
        );
      })()}
    </RHFFormItem>
  );
}
