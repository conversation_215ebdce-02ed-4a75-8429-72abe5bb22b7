import { useClassWithSubjectAndTeacherAndStudentGroup } from "@sisva/hooks/query/academic/useClasses";
import { useSelectedPeriod } from "@sisva/hooks/query/academic/usePeriods";
import { getRouteApi } from "@tanstack/react-router";
import type { TabsProps } from "antd";
import { Alert, Tabs } from "antd";
import dayjs from "dayjs";

import BahanAjarTab from "./BahanAjarTab";
import KehadiranTab from "./KehadiranTab";
import NilaiTab from "./NilaiTab";
import RencanaPembelajaranTab from "./RencanaPembelajaranTab";
import TugasTab from "./TugasTab";
import UjianTab from "./UjianTab";
dayjs.locale("id");

export default function ClassPage() {
  const routeApi = getRouteApi("/(main)/_layout/classes/$class_id/_auth/");
  const { class_id } = routeApi.useParams();

  const { data: selectedPeriod } = useSelectedPeriod();
  const { data: class_ } =
    useClassWithSubjectAndTeacherAndStudentGroup(class_id);
  const readOnly = !(selectedPeriod?.id === class_?.student_group?.period_id);

  const items: TabsProps["items"] = [
    {
      key: "1",
      label: <span className="font-medium">Rencana Pembelajaran</span>,
      children: <RencanaPembelajaranTab />,
    },
    {
      key: "2",
      label: <span className="font-medium">Bahan Ajar</span>,
      children: <BahanAjarTab />,
    },
    {
      key: "3",
      label: <span className="font-medium">Ujian</span>,
      children: <UjianTab />,
    },
    {
      key: "4",
      label: <span className="font-medium">Tugas</span>,
      children: <TugasTab />,
    },
    {
      key: "5",
      label: <span className="font-medium">Nilai</span>,
      children: <NilaiTab />,
    },
    {
      key: "6",
      label: <span className="font-medium">Kehadiran</span>,
      children: <KehadiranTab />,
    },
  ];

  return (
    <div className="flex flex-col items-center px-4 grow">
      <div className="size-full max-w-5xl py-8 flex flex-col gap-4">
        {readOnly && (
          <Alert
            message="Kelas ini sedang tidak digunakan"
            type="warning"
            showIcon
          />
        )}
        <Tabs defaultActiveKey="1" items={items} centered />
      </div>
    </div>
  );
}
