import {
  useStudentsClasses,
  useTeachersClasses,
} from "@sisva/hooks/query/academic/useClasses";
import { useClassSchedulesByClassId } from "@sisva/hooks/query/academic/useClassSchedules";
import {
  usePeriods,
  useSelectedPeriod,
} from "@sisva/hooks/query/academic/usePeriods";
import { useCurrentUser } from "@sisva/providers";
import { getDayText } from "@sisva/types/types";
import { AvatarWithAcronymByID } from "@sisva/ui";
import { cn } from "@sisva/utils";
import { Link } from "@tanstack/react-router";
import { SearchSm } from "@untitled-ui/icons-react";
import { Alert, Empty, Form, Input, Select, theme } from "antd";
import dayjs from "dayjs";
import fuzzysort from "fuzzysort";
import { useForm } from "react-hook-form";
import { FormItem } from "react-hook-form-antd";
dayjs.locale("id");

export default function ClassesPage() {
  const currentUser = useCurrentUser();
  const token = theme.useToken().token;
  const { data: selectedPeriod } = useSelectedPeriod();
  const { data: periods = [] } = usePeriods();

  //this will be [] if currentUser is not student
  const { data: studentsClasses = [] } = useStudentsClasses(currentUser.id);
  //this will be [] if currentUser is not teacher
  const { data: teachersClasses = [] } = useTeachersClasses(currentUser.id);

  const { control, watch } = useForm({
    defaultValues: {
      searchText: "",
      period_name: undefined,
      study_program_name: undefined,
      grade: undefined,
      student_group_name: undefined,
      subject_name: undefined,
      teacher_name: undefined,
    },
  });

  const searctText = watch("searchText");
  const period_name = watch("period_name");
  const student_group_name = watch("student_group_name");
  const grade = watch("grade");
  const study_program_name = watch("study_program_name");
  const subject_name = watch("subject_name");
  const teacher_name = watch("teacher_name");

  const classes = (
    currentUser.type === "student" ? studentsClasses : teachersClasses
  ).filter((item) =>
    period_name
      ? item.student_group?.period_name === period_name
      : item.student_group?.period_id === selectedPeriod?.id
  );

  const periodNames = periods.map((item) => item.name);

  const studentGroupNames = Array.from(
    new Set(classes.map((class_) => class_.student_group_name))
  );
  const grades = Array.from(
    new Set(classes.map((class_) => class_.student_group?.grade))
  );
  const studyProgramNames = Array.from(
    new Set(classes.map((class_) => class_.student_group?.study_program_name))
  );
  const subjectNames = Array.from(
    new Set(classes.map((class_) => class_.subject_name))
  );
  const teacherNames = Array.from(
    new Set(classes.map((class_) => class_.teacher_name))
  );

  const filteredClasses = fuzzysort
    .go(
      searctText,
      classes
        .filter((class_) =>
          grade ? class_.student_group?.grade === grade : true
        )
        .filter((class_) =>
          study_program_name
            ? class_.student_group?.study_program_name === study_program_name
            : true
        )
        .filter((class_) =>
          student_group_name
            ? class_.student_group_name === student_group_name
            : true
        )
        .filter((class_) =>
          subject_name ? class_?.subject_name === subject_name : true
        )
        .filter((class_) =>
          teacher_name ? class_.teacher_name === teacher_name : true
        ),

      {
        keys: ["student_group_name", "subject_name", "teacher_name"],
        all: !searctText,
      }
    )
    .map((result) => result.obj);

  return (
    <div className="flex flex-col items-center px-4 grow">
      <div className="size-full max-w-5xl py-8 flex flex-col gap-4">
        <h2>Daftar Kelas</h2>
        <div className="flex flex-col gap-2">
          <Form className="contents thick-scrollbar">
            <FormItem control={control} name="searchText" className="contents">
              <Input
                placeholder="Cari"
                suffix={<SearchSm color={token.colorTextLabel} />}
              />
            </FormItem>
            <div className="flex gap-2 overflow-auto pe-1">
              <FormItem
                control={control}
                name="period_name"
                className={cn("min-w-48", {
                  hidden: currentUser.type === "student",
                })}
              >
                <Select
                  options={periodNames.map((item) => ({
                    label: item,
                    value: item,
                  }))}
                  placeholder="Periode"
                  allowClear
                  showSearch
                />
              </FormItem>
              <FormItem
                control={control}
                name="student_group_name"
                className={cn("min-w-48", {
                  hidden: currentUser.type === "student",
                })}
              >
                <Select
                  options={studentGroupNames.map((item) => ({
                    label: item,
                    value: item,
                  }))}
                  placeholder="Kelas"
                  allowClear
                  showSearch
                />
              </FormItem>
              <FormItem
                control={control}
                name="grade"
                className={cn("min-w-48", {
                  hidden: currentUser.type === "student",
                })}
              >
                <Select
                  options={grades.map((item) => ({
                    label: item,
                    value: item,
                  }))}
                  placeholder="Tingkatan"
                  allowClear
                />
              </FormItem>
              <FormItem
                control={control}
                name="study_program_name"
                className={cn("min-w-48", {
                  hidden: currentUser.type === "student",
                })}
              >
                <Select
                  options={studyProgramNames.map((item) => ({
                    label: item,
                    value: item,
                  }))}
                  placeholder="Program Studi"
                  allowClear
                />
              </FormItem>
              <FormItem
                control={control}
                name="subject_name"
                className={cn("min-w-48", {
                  hidden: currentUser.type === "teacher",
                })}
              >
                <Select
                  options={subjectNames.map((item) => ({
                    label: item,
                    value: item,
                  }))}
                  placeholder="Mata Pelajaran"
                  allowClear
                  showSearch
                />
              </FormItem>
              <FormItem
                control={control}
                name="teacher_name"
                className={cn("min-w-48", {
                  hidden: currentUser.type === "teacher",
                })}
              >
                <Select
                  options={teacherNames.map((item) => ({
                    label: item,
                    value: item,
                  }))}
                  placeholder="Guru"
                  allowClear
                  showSearch
                />
              </FormItem>
            </div>
          </Form>
        </div>

        {period_name && period_name !== selectedPeriod?.name && (
          <Alert
            message="Kelas ini sedang tidak digunakan"
            type="warning"
            showIcon
          />
        )}
        <div className="size-full flex flex-col gap-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 thick-scrollbar">
            {filteredClasses.length ? (
              filteredClasses.map((class_) => {
                return (
                  <Link
                    key={class_.id}
                    to="/classes/$class_id"
                    params={{
                      class_id: class_.id,
                    }}
                  >
                    <div
                      className="flex flex-col gap-8 p-4 rounded-lg relative"
                      style={{ backgroundColor: token.colorPrimary }}
                    >
                      <div
                        className="absolute -top-4 -right-4 sm:-top-5 sm:-right-5 md:-top-6 md:-right-6 
                   w-16 h-16 sm:w-20 sm:h-20 md:w-24 md:h-24 
                   bg-white rounded-full opacity-15"
                      />
                      <div className="flex gap-2">
                        <AvatarWithAcronymByID user_id={class_.teacher_id} />
                        <div className="flex flex-col gap-1">
                          <div className="rounded-full bg-white px-2 py-1">
                            <span
                              className="text-sm font-semibold break-all line-clamp-1"
                              style={{ color: token.colorPrimary }}
                            >
                              {class_?.subject_name} -{" "}
                              {class_?.student_group_name}
                            </span>
                          </div>
                          <p className="text-sm text-white">
                            {class_?.teacher_name}
                          </p>
                        </div>
                      </div>
                      <hr className="border border-white border-dashed" />
                      <div className="relative p-4 bg-white rounded-xl">
                        <div
                          className="absolute left-0 w-1 h-12 -translate-y-1/2 rounded-r-full top-1/2"
                          style={{ backgroundColor: token.colorPrimary }}
                        ></div>
                        {
                          <div className="flex flex-col gap-1">
                            <h3 className="text-sm font-semibold">Jadwal</h3>
                            <div className="flex flex-col gap-1">
                              <ScheduleCard class_id={class_.id} />
                            </div>
                          </div>
                        }
                      </div>
                    </div>
                  </Link>
                );
              })
            ) : (
              <Empty className="col-span-2" />
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

function ScheduleCard({ class_id }: { class_id: number }) {
  const { data: schedules = [] } = useClassSchedulesByClassId(class_id);

  return (
    <div className="flex flex-col gap-1">
      {schedules.map((schedule) => (
        <div key={schedule.id} className="flex items-center gap-1">
          <span className="leading-tight text-sm text-neutral-600">
            {getDayText(schedule.day)},
          </span>
          <span className="leading-tight text-sm text-neutral-600">
            {dayjs(schedule.start_time, "hh:mm A Z").format("hh:mm")} -{" "}
            {dayjs(schedule.end_time, "hh:mm A Z").format("hh:mm")}
          </span>
        </div>
      ))}
    </div>
  );
}
