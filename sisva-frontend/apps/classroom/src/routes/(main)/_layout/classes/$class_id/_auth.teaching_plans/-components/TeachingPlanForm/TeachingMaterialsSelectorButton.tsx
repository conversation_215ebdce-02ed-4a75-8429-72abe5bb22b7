import { useClassWithSubjectAndTeacherAndStudentGroup } from "@sisva/hooks/query/academic/useClasses";
import { useTeachingMaterials } from "@sisva/hooks/query/classroom/useTeachingMaterials";
import { useGetFileUrl } from "@sisva/hooks/utils";
import { cn } from "@sisva/utils";
import type { ReactNode } from "@tanstack/react-router";
import { Download01, File06, SearchSm } from "@untitled-ui/icons-react";
import { useBoolean } from "ahooks";
import { Button, Form, Input, Modal, Select, Table, theme } from "antd";
import type { ColumnsType } from "antd/es/table";
import fuzzysort from "fuzzysort";
import { useDeferredValue, useState } from "react";
import { useForm } from "react-hook-form";
import { FormItem } from "react-hook-form-antd";

export function TeachingMaterialsSelectorButton({
  class_id,
  excludedTeachingMaterialIds,
  renderTrigger,
  onOk,
}: {
  class_id?: number;
  excludedTeachingMaterialIds?: number[];
  renderTrigger?: (onClick: () => void) => ReactNode;
  onOk?: (teachingMaterialIds: number[]) => void;
}) {
  const [open, { toggle }] = useBoolean();
  const token = theme.useToken().token;

  const getFileUrl = useGetFileUrl();

  const [selectedTeachingMaterialIds, setSelectedTeachingMaterialIds] =
    useState<number[]>([]);

  const { data: class_ } =
    useClassWithSubjectAndTeacherAndStudentGroup(class_id);
  const { data: teachingMaterials = [] } = useTeachingMaterials();
  const teachingMaterialsFilteredByClass = teachingMaterials
    .filter((item) => {
      if (!class_) return true;
      return (
        item.subject_id === class_?.subject_id &&
        item.grade === class_?.student_group?.grade
      );
    })
    .filter((item) => {
      if (!excludedTeachingMaterialIds) return true;
      return !excludedTeachingMaterialIds.includes(item.id);
    });

  const { control, watch } = useForm({
    defaultValues: {
      searchText: "",
      curriculum_name: undefined,
      study_program_name: undefined,
      grade: undefined,
      subject_name: undefined,
    },
  });

  const searchText = watch("searchText");
  const curriculum_name = watch("curriculum_name");
  const grade = watch("grade");
  const study_program_name = watch("study_program_name");
  const subject_name = watch("subject_name");

  const defferedSearch = useDeferredValue(searchText);

  const curriculumNames = Array.from(
    new Set(
      teachingMaterialsFilteredByClass.map((item) => item.curriculum_name)
    )
  );
  const grades = Array.from(
    new Set(teachingMaterialsFilteredByClass.map((item) => item.grade))
  );
  const studyProgramNames = Array.from(
    new Set(
      teachingMaterialsFilteredByClass.map((item) => item.study_program_name)
    )
  );
  const subjectNames = Array.from(
    new Set(teachingMaterialsFilteredByClass.map((item) => item.subject_name))
  );

  const filteredTeachingMaterials = fuzzysort
    .go(
      defferedSearch,
      teachingMaterialsFilteredByClass
        .filter((item) =>
          curriculum_name ? item.curriculum_name === curriculum_name : true
        )
        .filter((item) => (grade ? item.grade === grade : true))
        .filter((item) =>
          study_program_name
            ? item.study_program_name === study_program_name
            : true
        )
        .filter((item) =>
          subject_name ? item.subject_name === subject_name : true
        ),
      {
        keys: [
          "subject_name",
          "description",
          "curriculum_name",
          "study_program_name",
          "grade",
        ],
        all: !defferedSearch,
      }
    )
    .map((data) => data.obj);

  const columns: ColumnsType<(typeof filteredTeachingMaterials)[number]> = [
    {
      title: "Judul",
      dataIndex: "description",
      render: (value, record) => {
        return (
          <div className="flex items-center gap-1">
            <File06 width={16} />
            <div>{value}</div>
          </div>
        );
      },
    },
    {
      title: "Aksi",
      render: (_, record) => {
        return (
          <div className="flex gap-2 ">
            {record.attachment_file_uri && (
              <Button
                size="small"
                href={getFileUrl(record.attachment_file_uri)}
                variant="outlined"
                icon={<Download01 width={20} />}
              ></Button>
            )}
          </div>
        );
      },
      width: 80,
    },
  ];

  return (
    <>
      {renderTrigger ? (
        renderTrigger(toggle)
      ) : (
        <Button onClick={toggle} variant="outlined" color="primary">
          Pilih bahan ajar
        </Button>
      )}
      <Modal
        title="Pilih bahan ajar"
        open={open}
        onCancel={() => {
          toggle();
          setSelectedTeachingMaterialIds([]);
        }}
        onOk={() => {
          onOk?.(selectedTeachingMaterialIds);
          setSelectedTeachingMaterialIds([]);
          toggle();
        }}
        width={800}
      >
        <div className="flex flex-col gap-2 pb-4">
          <Form className="contents thick-scrollbar">
            <FormItem control={control} name="searchText" className="contents">
              <Input
                placeholder="Cari"
                suffix={<SearchSm color={token.colorTextLabel} />}
              />
            </FormItem>
            <div
              className={cn("flex gap-2 overflow-auto pe-1", {
                hidden: !!class_,
              })}
            >
              <FormItem
                control={control}
                name="curriculum_name"
                className={cn("min-w-48")}
              >
                <Select
                  options={curriculumNames.map((item) => ({
                    label: item,
                    value: item,
                  }))}
                  placeholder="Kurikulum"
                  allowClear
                />
              </FormItem>
              <FormItem
                control={control}
                name="grade"
                className={cn("min-w-48")}
              >
                <Select
                  options={grades.map((item) => ({
                    label: item,
                    value: item,
                  }))}
                  placeholder="Tingkatan"
                  allowClear
                />
              </FormItem>
              <FormItem
                control={control}
                name="study_program_name"
                className={cn("min-w-48")}
              >
                <Select
                  options={studyProgramNames.map((item) => ({
                    label: item,
                    value: item,
                  }))}
                  placeholder="Program Studi"
                  allowClear
                  showSearch
                />
              </FormItem>
              <FormItem
                control={control}
                name="subject_name"
                className={cn("min-w-48")}
              >
                <Select
                  options={subjectNames.map((item) => ({
                    label: item,
                    value: item,
                  }))}
                  placeholder="Mata Pelajaran"
                  allowClear
                  showSearch
                />
              </FormItem>
            </div>
          </Form>
        </div>
        <Table
          rowSelection={{
            type: "checkbox",
            selectedRowKeys: selectedTeachingMaterialIds,
            onChange(selectedRowKeys, selectedRows, info) {
              setSelectedTeachingMaterialIds(selectedRows.map((row) => row.id));
            },
          }}
          rowKey={(record) => record.id}
          bordered
          columns={columns}
          dataSource={filteredTeachingMaterials}
          pagination={{
            pageSize: 8,
          }}
        />
      </Modal>
    </>
  );
}
