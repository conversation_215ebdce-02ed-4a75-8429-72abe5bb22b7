import { createFileRoute, notFound } from "@tanstack/react-router";
import { number, object, parse, pipe, transform, unknown } from "valibot";

import TeachingPlanNewPage from "./-components/TeachingPlanNewPage";

export const Route = createFileRoute(
  "/(main)/_layout/classes/$class_id/_auth/teaching_plans/new/"
)({
  params: {
    parse(params) {
      return parse(
        object({
          class_id: pipe(unknown(), transform(Number), number()),
        }),
        params
      );
    },
  },
  beforeLoad({ context: { currentUser } }) {
    if (currentUser.type === "student") throw notFound();
  },
  head() {
    return {
      meta: [
        {
          title: "Rencana pembelajaran | Sisva",
        },
      ],
    };
  },
  component: TeachingPlanNewPage,
});
