import { useToggle } from "ahooks";
import dayjs from "dayjs";
import id from "dayjs/locale/id";
import { type ReactNode, useEffect, useRef, useState } from "react";
import { useForm } from "react-hook-form";

dayjs.locale(id);

import { useClass } from "@sisva/hooks/query/academic/useClasses";
import {
  useStudentClassAttendance,
  useUpdateStudentClassAttendance,
} from "@sisva/hooks/query/attendance/useAttendance";
import { useUser } from "@sisva/hooks/query/user/useUsers";
import { useNotificationAPI } from "@sisva/providers";
import type { Attendance } from "@sisva/types/types";
import { AvatarWithAcronymByID } from "@sisva/ui";
import { CheckCircle } from "@untitled-ui/icons-react";
import { Button, Divider, Form, Modal } from "antd";
import { deepEqual } from "fast-equals";

export default function UpdateStudentClassAttendanceButton({
  initialStudentId,
  date_id,
  class_id,
  studentIds,
  renderTrigger,
}: {
  initialStudentId?: string;
  date_id: number;
  class_id: number;
  studentIds: string[];
  renderTrigger?: (onClick: () => void) => ReactNode;
}) {
  const [visible, { toggle }] = useToggle(false);
  const notification = useNotificationAPI();

  const [studentId, setStudentId] = useState(initialStudentId ?? studentIds[0]);

  const continuons = useRef(true);

  // reset studentId when studentIds or date_id changed
  const prevStudentIdsRef = useRef<string[]>([]);
  const prevDateIdRef = useRef(date_id);
  useEffect(() => {
    if (
      studentIds.length &&
      !deepEqual(prevStudentIdsRef.current, studentIds)
    ) {
      setStudentId(initialStudentId ?? studentIds[0]);
      prevStudentIdsRef.current = [...studentIds];
    }
    if (date_id !== prevDateIdRef.current) {
      setStudentId(initialStudentId ?? studentIds[0]);
      prevDateIdRef.current = date_id;
    }
  }, [initialStudentId, studentIds, date_id]);

  const { data: student } = useUser(studentId);
  const { data: class_ } = useClass(class_id);
  const { data: studentClassAttendance } = useStudentClassAttendance({
    date_id,
    student_id: studentId,
    class_id,
  });

  const { control, handleSubmit, reset, watch, setValue } = useForm<{
    date_id: number;
    student_id: string;
    class_id: number;
    status: Attendance;
  }>({
    values: {
      date_id: date_id,
      student_id: studentId ?? "",
      class_id: class_id,
      status: studentClassAttendance?.status ?? "present",
    },
  });

  const status = watch("status") as Attendance;
  const counter = studentId ? studentIds.indexOf(studentId) + 1 : 0;
  const maxCounter = studentIds.length;

  const { mutate: updateStudentClassAttendance } =
    useUpdateStudentClassAttendance({
      onSuccess: () => {
        if (!continuons.current) {
          continuons.current = true;
          toggle();
          reset();
          if (initialStudentId) setStudentId(initialStudentId);
        } else {
          const currentStudentIdIndex = studentId
            ? studentIds.indexOf(studentId)
            : 0;
          setStudentId(
            studentIds[(currentStudentIdIndex + 1) % studentIds.length]
          );
        }

        notification.success({ message: "Kehadiran berhasil diperbarui" });
      },
      onError: () => {
        notification.error({ message: "Kehadiran gagal diperbarui" });
      },
    });

  return (
    <>
      {renderTrigger ? (
        renderTrigger(toggle)
      ) : (
        <Button variant="solid" color="primary" onClick={toggle}>
          Absensi
        </Button>
      )}
      <Modal
        open={visible}
        onCancel={() => {
          reset();
          toggle();
          if (initialStudentId) setStudentId(initialStudentId);
        }}
        footer={null}
        // width={800}
      >
        <Form
          onFinish={handleSubmit((value) => {
            updateStudentClassAttendance({
              ...value,
            });
          })}
        >
          <div className="flex gap-4">
            <div className="text-xl font-semibold">Absensi</div>
            <div className="text-xl font-semibold">
              ({counter}/{maxCounter})
            </div>
          </div>
          <div className="text-xs text-neutral-500">
            {dayjs(String(date_id), "YYYYMMDD").format("dddd, DD MMMM YYYY")}
          </div>
          <Divider />
          <div className="flex flex-col items-center gap-6">
            <div className="flex gap-4 items-center">
              <AvatarWithAcronymByID user_id={studentId} size={48} />
              <div className="flex flex-col gap-0.5 text-sm">
                <div>{student?.name}</div>
                <div className="text-neutral-500">
                  {class_?.student_group_name}
                </div>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4 w-72">
              <Button
                icon={status === "present" && <CheckCircle />}
                variant={status === "present" ? "solid" : "outlined"}
                color="primary"
                className="p-8"
                onClick={() => {
                  setValue("status", "present");
                }}
              >
                Hadir
              </Button>
              <Button
                icon={status === "sick" && <CheckCircle />}
                variant={status === "sick" ? "solid" : "outlined"}
                color="primary"
                className="p-8"
                onClick={() => {
                  setValue("status", "sick");
                }}
              >
                Sakit
              </Button>
              <Button
                icon={status === "leave" && <CheckCircle />}
                variant={status === "leave" ? "solid" : "outlined"}
                color="primary"
                className="p-8"
                onClick={() => {
                  setValue("status", "leave");
                }}
              >
                Izin
              </Button>
              <Button
                icon={status === "absent" && <CheckCircle />}
                variant={status === "absent" ? "solid" : "outlined"}
                color="primary"
                className="p-8"
                onClick={() => {
                  setValue("status", "absent");
                }}
              >
                Alpha
              </Button>
            </div>
          </div>
          <Divider />

          <div className="flex justify-end w-full gap-3">
            <Button
              onClick={() => {
                const currentStudentIdIndex = studentId
                  ? studentIds.indexOf(studentId)
                  : 0;
                setStudentId(
                  studentIds[(currentStudentIdIndex - 1) % studentIds.length]
                );
              }}
              disabled={counter === 1}
            >
              Kembali
            </Button>
            <Button
              htmlType="submit"
              variant="solid"
              color="primary"
              onClick={() => {
                continuons.current = false;
              }}
            >
              Simpan
            </Button>
            <Button
              htmlType="submit"
              variant="solid"
              color="primary"
              disabled={counter === maxCounter}
            >
              Simpan dan Lanjut
            </Button>
          </div>
        </Form>
      </Modal>
    </>
  );
}
