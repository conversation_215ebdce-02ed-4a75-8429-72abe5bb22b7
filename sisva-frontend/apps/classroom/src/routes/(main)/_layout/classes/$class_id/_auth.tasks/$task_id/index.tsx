import { taskQueryOptions } from "@sisva/hooks/query/classroom/useTasks";
import { createFileRoute, notFound } from "@tanstack/react-router";
import { number, object, parse, pipe, transform, unknown } from "valibot";

import TaskDetailPage from "./-components/TaskDetailPage";

export const Route = createFileRoute(
  "/(main)/_layout/classes/$class_id/_auth/tasks/$task_id/"
)({
  params: {
    parse(params) {
      const parsedParam = parse(
        object({
          class_id: pipe(unknown(), transform(Number), number()),
          task_id: pipe(unknown(), transform(Number), number()),
        }),
        params
      );
      return parsedParam;
    },
  },
  async beforeLoad({
    params: { class_id, task_id },
    context: { queryClient },
  }) {
    try {
      const task = await queryClient.fetchQuery(taskQueryOptions(task_id));

      if (task.class_id !== Number(class_id)) throw notFound();
    } catch {
      throw notFound();
    }
  },
  component: TaskDetailPage,
});
