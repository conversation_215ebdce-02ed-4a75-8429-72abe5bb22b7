import { useClassWithSubjectAndTeacherAndStudentGroup } from "@sisva/hooks/query/academic/useClasses";
import { useSelectedPeriod } from "@sisva/hooks/query/academic/usePeriods";
import { useSubject } from "@sisva/hooks/query/academic/useSubjects";
import { useTeachingMaterialsByClassId } from "@sisva/hooks/query/classroom/useTeachingMaterials";
import {
  useTeachingPlan,
  useTeachingPlansByClassId,
} from "@sisva/hooks/query/classroom/useTeachingPlans";
import { useCurrentUser } from "@sisva/providers";
import { cn } from "@sisva/utils";
import { getRouteApi } from "@tanstack/react-router";
import { SearchSm } from "@untitled-ui/icons-react";
import { Empty, Form, Input, Select, Skeleton, Switch, theme } from "antd";
import fuzzysort from "fuzzysort";
import { useDeferredValue } from "react";
import { Fragment } from "react/jsx-runtime";
import { useForm } from "react-hook-form";
import { FormItem } from "react-hook-form-antd";

import { CreateTeachingMaterialButton } from "#/routes/(main)/-components/CreateTeachingMaterialButton";
import { GroupHeader } from "#/routes/(main)/-components/GroupHeader";
import TeachingMaterialCard from "#/routes/(main)/-components/TeachingMaterialCard";

export default function BahanAjarTab() {
  const routeApi = getRouteApi("/(main)/_layout/classes/$class_id/_auth/");
  const { class_id } = routeApi.useParams();

  const currentUser = useCurrentUser();

  const token = theme.useToken().token;

  const { data: selectedPeriod } = useSelectedPeriod();
  const { data: class_ } =
    useClassWithSubjectAndTeacherAndStudentGroup(class_id);
  const readOnly = !(selectedPeriod?.id === class_?.student_group?.period_id);

  const { data: teachingPlans = [], isLoading: L1 } =
    useTeachingPlansByClassId(class_id);
  const teachingMaterialsWithTeachingPlan = teachingPlans.flatMap((item) =>
    item.teaching_materials.map((material) => ({
      ...material,
      teaching_plan_id: item.id,
    }))
  );
  const { data: teachingMaterials = [], isLoading: L2 } =
    useTeachingMaterialsByClassId(class_id);

  const { control, watch } = useForm({
    defaultValues: {
      searchText: "",
      curriculum_name: undefined,
      study_program_name: undefined,
      grade: undefined,
      subject_name: undefined,
      showAll: false,
    },
  });

  const showAll = watch("showAll");

  const searchText = watch("searchText");
  const curriculum_name = watch("curriculum_name");
  const grade = watch("grade");
  const study_program_name = watch("study_program_name");
  const subject_name = watch("subject_name");

  const defferedSearch = useDeferredValue(searchText);

  const curriculumNames = Array.from(
    new Set(
      teachingMaterialsWithTeachingPlan.map((item) => item.curriculum_name)
    )
  );
  const grades = Array.from(
    new Set(teachingMaterialsWithTeachingPlan.map((item) => item.grade))
  );
  const studyProgramNames = Array.from(
    new Set(
      teachingMaterialsWithTeachingPlan.map((item) => item.study_program_name)
    )
  );
  const subjectNames = Array.from(
    new Set(teachingMaterialsWithTeachingPlan.map((item) => item.subject_name))
  );

  const filteredTeachingMaterials = fuzzysort
    .go(
      defferedSearch,
      (showAll
        ? teachingMaterials.map((item) => ({ ...item, teaching_plan_id: 0 }))
        : teachingMaterialsWithTeachingPlan
      )
        .filter((item) =>
          curriculum_name ? item.curriculum_name === curriculum_name : true
        )
        .filter((item) => (grade ? item.grade === grade : true))
        .filter((item) =>
          study_program_name
            ? item.study_program_name === study_program_name
            : true
        )
        .filter((item) =>
          subject_name ? item.subject_name === subject_name : true
        ),
      {
        keys: [
          "subject_name",
          "description",
          "curriculum_name",
          "study_program_name",
          "grade",
          "teaching_plan.title",
        ],
        all: !defferedSearch,
      }
    )
    .map((data) => data.obj);

  const teachingMaterialsGroupedByTeachingPlanId = Object.entries(
    Object.groupBy(
      filteredTeachingMaterials,
      (material) => material.teaching_plan_id
    )
  );

  const teachingMaterialsGroupedBySubjectId = Object.entries(
    Object.groupBy(filteredTeachingMaterials, (material) => material.subject_id)
  );

  return (
    <div className="flex flex-col gap-4">
      <div className="flex justify-between gap-4">
        <h2>Bahan Ajar</h2>
      </div>
      <div className={cn("flex flex-col gap-2", { hidden: false })}>
        <Form className="contents thick-scrollbar">
          <FormItem control={control} name="searchText" className="contents">
            <Input
              placeholder="Cari"
              suffix={<SearchSm color={token.colorTextLabel} />}
            />
          </FormItem>
          <div className="flex gap-2 justify-end overflow-auto px-1">
            <FormItem
              control={control}
              name="showAll"
              className={cn("contents", {
                // hide this until further development
                hidden: true,
              })}
            >
              <Switch
                checkedChildren="Mata pelajaran"
                unCheckedChildren="Rencana pembelajaran"
              />
            </FormItem>

            {currentUser?.type !== "student" && !readOnly && (
              <CreateTeachingMaterialButton
                class_id={class_id}
                selectTeachingPlan
              />
            )}
            <FormItem
              control={control}
              name="curriculum_name"
              className={cn("min-w-48")}
              hidden
            >
              <Select
                options={curriculumNames.map((item) => ({
                  label: item,
                  value: item,
                }))}
                placeholder="Kurikulum"
                allowClear
              />
            </FormItem>
            <FormItem
              control={control}
              name="grade"
              className={cn("min-w-48")}
              hidden
            >
              <Select
                options={grades.map((item) => ({
                  label: item,
                  value: item,
                }))}
                placeholder="Tingkatan"
                allowClear
              />
            </FormItem>
            <FormItem
              control={control}
              name="study_program_name"
              className={cn("min-w-48")}
              hidden
            >
              <Select
                options={studyProgramNames.map((item) => ({
                  label: item,
                  value: item,
                }))}
                placeholder="Program Studi"
                allowClear
                showSearch
              />
            </FormItem>
            <FormItem
              control={control}
              name="subject_name"
              className={cn("min-w-48")}
              hidden
            >
              <Select
                options={subjectNames.map((item) => ({
                  label: item,
                  value: item,
                }))}
                placeholder="Mata Pelajaran"
                allowClear
                showSearch
              />
            </FormItem>
          </div>
        </Form>
      </div>
      <div className="size-full flex flex-col gap-4">
        <div className="flex flex-col items-start gap-4">
          <div className="w-full flex flex-col gap-4">
            {(() => {
              if (L1 || L2) return <Skeleton active />;

              if (showAll) {
                if (teachingMaterialsGroupedBySubjectId.length === 0)
                  return <Empty />;
                return teachingMaterialsGroupedBySubjectId.map(
                  ([subject_id, materials]) => {
                    return (
                      <Fragment key={subject_id}>
                        <SubjectTitle subject_id={Number(subject_id)} />
                        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                          {materials?.map((material) => (
                            <TeachingMaterialCard
                              key={material.id}
                              material={material}
                            />
                          ))}
                        </div>
                      </Fragment>
                    );
                  }
                );
              }

              if (teachingMaterialsGroupedByTeachingPlanId.length === 0)
                return <Empty />;
              return teachingMaterialsGroupedByTeachingPlanId.map(
                ([teaching_plan_id, materials]) => {
                  return (
                    <Fragment key={teaching_plan_id}>
                      <TeachingPlanTitle
                        teaching_plan_id={Number(teaching_plan_id)}
                      />
                      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                        {materials?.map((material) => (
                          <TeachingMaterialCard
                            key={material.id}
                            material={material}
                          />
                        ))}
                      </div>
                    </Fragment>
                  );
                }
              );
            })()}
          </div>
        </div>
      </div>
    </div>
  );
}

function TeachingPlanTitle({ teaching_plan_id }: { teaching_plan_id: number }) {
  const { data: teachingPlan } = useTeachingPlan(teaching_plan_id);

  return <GroupHeader text={teachingPlan?.title ?? ""} />;
}

function SubjectTitle({ subject_id }: { subject_id: number }) {
  const { data: subject } = useSubject(subject_id);

  return <GroupHeader text={subject?.name ?? ""} />;
}
