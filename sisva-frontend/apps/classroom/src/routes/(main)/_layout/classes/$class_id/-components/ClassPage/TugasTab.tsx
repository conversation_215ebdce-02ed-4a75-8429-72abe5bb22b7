import { useClassWithSubjectAndTeacherAndStudentGroup } from "@sisva/hooks/query/academic/useClasses";
import { useSelectedPeriod } from "@sisva/hooks/query/academic/usePeriods";
import { useSubject } from "@sisva/hooks/query/academic/useSubjects";
import { useTasks } from "@sisva/hooks/query/classroom/useTasks";
import {
  useTeachingPlan,
  useTeachingPlansByClassId,
} from "@sisva/hooks/query/classroom/useTeachingPlans";
import { useCurrentUser } from "@sisva/providers";
import { cn } from "@sisva/utils";
import { getRouteApi } from "@tanstack/react-router";
import { SearchSm } from "@untitled-ui/icons-react";
import { Empty, Form, Input, Skeleton, Switch, theme } from "antd";
import fuzzysort from "fuzzysort";
import { useDeferredValue } from "react";
import { Fragment } from "react/jsx-runtime";
import { useForm } from "react-hook-form";
import { FormItem } from "react-hook-form-antd";

import { CreateTaskButton } from "#/routes/(main)/-components/CreateTaskButton";
import { GroupHeader } from "#/routes/(main)/-components/GroupHeader";
import TaskCard from "#/routes/(main)/-components/TaskCard";

export default function TugasTab() {
  const routeApi = getRouteApi("/(main)/_layout/classes/$class_id/_auth/");
  const { class_id } = routeApi.useParams();

  const currentUser = useCurrentUser();

  const token = theme.useToken().token;

  const { data: selectedPeriod } = useSelectedPeriod();
  const { data: class_ } =
    useClassWithSubjectAndTeacherAndStudentGroup(class_id);
  const readOnly = !(selectedPeriod?.id === class_?.student_group?.period_id);
  const { data: allTasks = [] } = useTasks({ class_id });

  const { data: teachingPlans = [], isLoading: L1 } =
    useTeachingPlansByClassId(class_id);

  const tasks = allTasks.map((task) => {
    const teachingPlan = teachingPlans.find((plan) => {
      const taskIds = plan.tasks.map((task) => task.id);
      return taskIds.includes(task.id);
    });
    return {
      ...task,
      class: class_,
      teaching_plan_id: teachingPlan?.id,
      teaching_plan: teachingPlan,
    };
  });

  const { control, watch } = useForm({
    defaultValues: {
      searchText: "",
      showAll: false,
    },
  });

  const showAll = watch("showAll");

  const searchText = watch("searchText");
  const defferedSearch = useDeferredValue(searchText);

  const filteredTasks = fuzzysort
    .go(defferedSearch, tasks, {
      keys: ["name", "description"],
      all: !defferedSearch,
    })
    .map((data) => data.obj);

  const tasksGroupedByTeachingPlanId = Object.entries(
    Object.groupBy(filteredTasks, (task) => task.teaching_plan_id ?? 0)
  );
  const tasksGroupedBySubjectId = Object.entries(
    Object.groupBy(filteredTasks, (task) => task.class?.subject_id ?? 0)
  );

  return (
    <div className="flex flex-col gap-4">
      <div className="flex justify-between gap-4">
        <h2>Tugas</h2>
        {currentUser?.type !== "student" && (
          <></>
          // button
        )}
      </div>
      <div className="flex flex-col gap-2">
        <Form className="contents thick-scrollbar">
          <FormItem control={control} name="searchText" className="contents">
            <Input
              placeholder="Cari"
              suffix={<SearchSm color={token.colorTextLabel} />}
            />
          </FormItem>
          <div className={cn("flex justify-end gap-2 overflow-auto pe-1")}>
            <FormItem
              control={control}
              name="showAll"
              className={cn("contents", {
                hidden: true,
              })}
            >
              <Switch
                checkedChildren="Mata pelajaran"
                unCheckedChildren="Rencana pembelajaran"
              />
            </FormItem>

            {currentUser?.type !== "student" && !readOnly && (
              <CreateTaskButton class_id={class_id} selectTeachingPlan />
            )}
          </div>
        </Form>
      </div>
      <div className="size-full flex flex-col gap-4">
        <div className="flex flex-col items-start gap-4">
          <div className="w-full flex flex-col gap-4">
            {(() => {
              if (L1) return <Skeleton active />;

              if (showAll) {
                if (tasksGroupedBySubjectId.length === 0) return <Empty />;
                return tasksGroupedBySubjectId.map(([subject_id, tasks]) => {
                  return (
                    <Fragment key={subject_id}>
                      <SubjectTitle subject_id={Number(subject_id)} />
                      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                        {tasks?.map((task) => (
                          <TaskCard key={task.id} task={task} />
                        ))}
                      </div>
                    </Fragment>
                  );
                });
              }

              if (tasksGroupedByTeachingPlanId.length === 0) return <Empty />;
              return tasksGroupedByTeachingPlanId.map(
                ([teaching_plan_id, tasks]) => {
                  if (teaching_plan_id === "0") return null;
                  return (
                    <Fragment key={teaching_plan_id}>
                      <TeachingPlanTitle
                        teaching_plan_id={Number(teaching_plan_id)}
                      />
                      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                        {tasks?.map((task) => (
                          <TaskCard key={task.id} task={task} />
                        ))}
                      </div>
                    </Fragment>
                  );
                }
              );
            })()}
          </div>
        </div>
      </div>
    </div>
  );
}

function TeachingPlanTitle({ teaching_plan_id }: { teaching_plan_id: number }) {
  const { data: teachingPlan } = useTeachingPlan(teaching_plan_id);

  return <GroupHeader text={teachingPlan?.title ?? ""} />;
}

function SubjectTitle({ subject_id }: { subject_id: number }) {
  const { data: subject } = useSubject(subject_id);

  return <GroupHeader text={subject?.name ?? ""} />;
}
