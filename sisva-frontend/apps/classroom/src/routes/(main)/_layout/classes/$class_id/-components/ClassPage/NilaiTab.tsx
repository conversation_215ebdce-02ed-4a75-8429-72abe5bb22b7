import { useTasks } from "@sisva/hooks/query/classroom/useTasks";
import { useStudentsByClassId } from "@sisva/hooks/query/user/useStudents";
import { useCurrentUser } from "@sisva/providers";
import { AvatarWithAcronymByID } from "@sisva/ui";
import { cn } from "@sisva/utils";
import { getRouteApi, Link } from "@tanstack/react-router";
import { BarChart10, Edit03, SearchSm } from "@untitled-ui/icons-react";
import { Empty, Form, Input, Skeleton, Table, theme } from "antd";
import type { ColumnsType } from "antd/es/table";
import dayjs from "dayjs";
import { sort } from "fast-sort";
import { useDeferredValue } from "react";
import { useForm } from "react-hook-form";
import { FormItem } from "react-hook-form-antd";

const averageRowId = crypto.randomUUID();

export default function NilaiTab() {
  const routeApi = getRouteApi("/(main)/_layout/classes/$class_id/_auth/");
  const { class_id } = routeApi.useParams();

  const currentUser = useCurrentUser();

  const token = theme.useToken().token;

  const { data: tasks = [], isLoading: L2 } = useTasks({ class_id });
  const { data: students = [], isLoading: L1 } = useStudentsByClassId(class_id);

  const studentsWithTasksAsObject = sort(
    students.map((student) => {
      return {
        ...student,
        tasks: student.tasks.reduce(
          (
            result: Record<string, (typeof students)[number]["tasks"][number]>,
            task
          ) => {
            result[task.id.toString()] = task;
            return result;
          },
          {}
        ),
        score_average: Math.round(
          student.tasks.length
            ? student.tasks.reduce((curr, task) => {
                curr += task.score?.value ?? 0;
                return curr;
              }, 0) / student.tasks.length
            : 0
        ),
      };
    })
  ).desc((student) => student.score_average);

  const tasksWithAverage = tasks.map((task) => {
    const scoresWithThisTaskId = studentsWithTasksAsObject.map(
      (student) => student.tasks[task.id.toString()]?.score?.value ?? 0
    );
    return {
      ...task,
      average: Math.round(
        scoresWithThisTaskId.reduce((a, b) => a + b, 0) /
          scoresWithThisTaskId.length
      ),
    };
  });

  const fakeStudentAsAverageRow = structuredClone(studentsWithTasksAsObject[0]);
  if (fakeStudentAsAverageRow) {
    fakeStudentAsAverageRow.id = averageRowId;
    fakeStudentAsAverageRow.name = "Rata - rata";
    tasksWithAverage.forEach((task) => {
      const taskId = task.id.toString();
      const fakeStudentTask = fakeStudentAsAverageRow.tasks[taskId];
      if (fakeStudentTask) {
        fakeStudentTask.score = fakeStudentTask.score || {
          feedback: "",
          student_id: averageRowId,
          task_id: task.id,
          value: 0,
        };
        fakeStudentTask.score.student_id = averageRowId;
        fakeStudentTask.score.value = task.average;
      }
    });

    fakeStudentAsAverageRow.score_average = tasksWithAverage.length
      ? tasksWithAverage.reduce((curr, task) => {
          curr += task.average;
          return curr;
        }, 0) / tasksWithAverage.length
      : 0;
    studentsWithTasksAsObject.unshift(fakeStudentAsAverageRow);
  }

  //  ───────────────────────────── for teacher ─────────────────────────────

  const columnsForTeacher: ColumnsType<
    (typeof studentsWithTasksAsObject)[number]
  > = tasks.map((task) => ({
    title: (
      <div className="flex flex-col gap-2 items-center text-center">
        <Link
          to="/classes/$class_id/tasks/$task_id"
          params={{
            class_id: class_id,
            task_id: task.id,
          }}
          className="flex gap-1 items-center text-blue-600"
        >
          <div className="underline text-sm">{task.name}</div>
          <Edit03 width={16} className="min-w-4" />
        </Link>
        <div className="text-xs font-light">
          {dayjs(task.start_time, "DD/MM/YYYY h:mm A Z").format("D MMM YYYY")}
        </div>
      </div>
    ),
    dataIndex: ["tasks", task.id.toString(), "score", "value"],
    render: (value) => {
      return <div className="flex justify-center">{value ?? 0}</div>;
    },
    width: 180,
  }));

  columnsForTeacher.unshift({
    title: "Rata - rata",
    dataIndex: "score_average",
    render: (value) => {
      return <div className="flex justify-center">{value ?? 0}</div>;
    },
    width: 110,
  });

  columnsForTeacher.unshift({
    title: <div className="flex justify-center">Siswa</div>,
    dataIndex: "name",
    render: (_, record) => {
      return (
        <div className="flex gap-2 items-center">
          {record.id === averageRowId ? (
            <BarChart10 width={32} />
          ) : (
            <AvatarWithAcronymByID size={32} user_id={record.id} />
          )}
          <div>{record.name}</div>
        </div>
      );
    },
  });
  //  ───────────────────────────── for teacher ─────────────────────────────

  //  ───────────────────────────── for student ─────────────────────────────

  // this will be undefined if currentUser is not a student
  const currentStudent = studentsWithTasksAsObject.find(
    (student) => student.id === currentUser.id
  );
  const currentStudentTasks = Object.values(currentStudent?.tasks ?? {}).map(
    (task) => {
      return {
        ...task,
        average: tasksWithAverage.find((item) => item.id === task.id)?.average,
      };
    }
  );
  const columnsForStudent: ColumnsType<(typeof currentStudentTasks)[number]> = [
    {
      title: <div className="flex justify-center">Tugas</div>,
      dataIndex: "name",
      render: (value, record) => {
        return (
          <div className="flex flex-col gap-1">
            <div className="font-medium">{value}</div>
            <div className="text-xs text-neutral-500">
              {dayjs(record.start_time, "DD/MM/YYYY h:mm A Z").format(
                "D MMM YYYY"
              )}
            </div>
          </div>
        );
      },
    },
    {
      title: (
        <div className="flex justify-center items-center gap-2">
          <BarChart10 width={20} />
          <div>Rata - rata</div>
        </div>
      ),
      dataIndex: "average",
      render: (value) => {
        return <div className="flex justify-center">{value}</div>;
      },
    },
    {
      title: (
        <div className="flex gap-2 items-center justify-center">
          <AvatarWithAcronymByID size={32} user_id={currentUser.id} />
          <div>{currentUser.name}</div>
        </div>
      ),
      dataIndex: ["score", "value"],
      render: (value) => {
        return <div className="flex justify-center">{value ?? 0}</div>;
      },
    },
  ];

  //  ───────────────────────────── for student ─────────────────────────────

  const { control, watch } = useForm({
    defaultValues: {
      searchText: "",
    },
  });

  const searchText = watch("searchText");
  const defferedSearch = useDeferredValue(searchText);

  return (
    <div className="flex flex-col gap-4">
      <div className="flex justify-between gap-4">
        <h2>Nilai</h2>
        {currentUser?.type !== "student" && (
          <></>
          // button
        )}
      </div>
      <div className="flex flex-col gap-2">
        <Form className={cn("contents thick-scrollbar", { hidden: true })}>
          <FormItem control={control} name="searchText" className="contents">
            <Input
              placeholder="Cari"
              suffix={<SearchSm color={token.colorTextLabel} />}
            />
          </FormItem>
          <div className="flex gap-2 overflow-auto pe-1">{/* filters */}</div>
        </Form>
      </div>
      <div className="size-full flex flex-col gap-4">
        <div className="flex flex-col items-start gap-4">
          <div className="w-full flex flex-col gap-4">
            {(() => {
              if (L1) return <Skeleton active />;
              if (students.length === 0) return <Empty />;
              if (currentUser.type === "teacher")
                return (
                  <Table
                    dataSource={studentsWithTasksAsObject}
                    columns={columnsForTeacher}
                    rowKey={(record) => record.id}
                    pagination={false}
                    bordered
                    scroll={{
                      x: "max-content",
                      y: "50svh",
                    }}
                  />
                );
              if (currentUser.type === "student")
                return (
                  <Table
                    dataSource={currentStudentTasks}
                    columns={columnsForStudent}
                    rowKey={(record) => record.id}
                    bordered
                    scroll={{
                      x: "max-content",
                      y: "50svh",
                    }}
                  />
                );
            })()}
          </div>
        </div>
      </div>
    </div>
  );
}
