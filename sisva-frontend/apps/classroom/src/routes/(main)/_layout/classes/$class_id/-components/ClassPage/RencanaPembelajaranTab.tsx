import { useClassWithSubjectAndTeacherAndStudentGroup } from "@sisva/hooks/query/academic/useClasses";
import { useSelectedPeriod } from "@sisva/hooks/query/academic/usePeriods";
import { useTeachingPlansByClassId } from "@sisva/hooks/query/classroom/useTeachingPlans";
import { useGetFileUrl } from "@sisva/hooks/utils";
import { useCurrentUser } from "@sisva/providers";
import { createRedirectUrl } from "@sisva/utils";
import { getRouteApi, Link } from "@tanstack/react-router";
import { Download01, PencilLine, Plus } from "@untitled-ui/icons-react";
import { Button, Empty, Skeleton } from "antd";

import { env } from "#/env";
import CrepeEditor from "#/routes/(main)/-components/CrepeEditor";
import { DeleteTeachingPlanButton } from "#/routes/(main)/-components/DeleteTeachingPlanButton";
import { GroupHeader } from "#/routes/(main)/-components/GroupHeader";

export default function RencanaPembelajaranTab() {
  const routeApi = getRouteApi("/(main)/_layout/classes/$class_id/_auth/");
  const { class_id } = routeApi.useParams();
  const currentUser = useCurrentUser();
  const getFileUrl = useGetFileUrl();
  const { data: selectedPeriod } = useSelectedPeriod();
  const { data: class_ } =
    useClassWithSubjectAndTeacherAndStudentGroup(class_id);
  const readOnly = !(selectedPeriod?.id === class_?.student_group?.period_id);

  const { data: teachingPlans = [], isLoading: L1 } =
    useTeachingPlansByClassId(class_id);

  return (
    <div className="flex flex-col gap-4">
      <div className="flex justify-between sm:gap-4 flex-col sm:flex-row gap-2">
        <h2>Rencana Pembelajaran</h2>
        {currentUser.type !== "student" && !readOnly && (
          <Link
            to="/classes/$class_id/teaching_plans/new"
            params={{
              class_id: class_id,
            }}
          >
            <Button icon={<Plus />} color="primary" variant="solid">
              Tambah Rencana Pembelajaran
            </Button>
          </Link>
        )}
      </div>

      <div className="flex flex-col gap-2">{/* filters */}</div>
      <div className="size-full flex flex-col gap-4">
        <div className="flex flex-col gap-4 thick-scrollbar">
          {(() => {
            if (L1) return <Skeleton active />;
            if (teachingPlans.length === 0) return <Empty />;

            return teachingPlans.map((plan) => {
              return (
                <div
                  key={plan.id}
                  className="flex flex-col gap-4 pb-4 border-[0] border-b border-solid border-neutral-400"
                >
                  <GroupHeader
                    text={plan.title}
                    popoverContent={
                      currentUser.type !== "student" &&
                      !readOnly && (
                        <div className="flex flex-col gap-0.5">
                          <Link
                            to="/classes/$class_id/teaching_plans/$teaching_plan_id/edit"
                            params={{
                              class_id: class_id,
                              teaching_plan_id: plan.id,
                            }}
                            className="w-full"
                          >
                            <Button
                              variant="text"
                              color="default"
                              className="w-full"
                            >
                              Edit
                            </Button>
                          </Link>
                          <a
                            href={createRedirectUrl({
                              payload: {
                                to: "createExamWithExistingTeachingPlanIds",
                                teaching_plan_ids: [plan.id],
                              },
                              isDev: env.DEV,
                            })}
                            target="_blank"
                            rel="noreferrer"
                          >
                            <Button
                              variant="text"
                              color="default"
                              className="w-full"
                            >
                              Tambah Ujian
                            </Button>
                          </a>
                          <DeleteTeachingPlanButton
                            teaching_plan_id={plan.id}
                            renderTrigger={(onClick) => {
                              return (
                                <Button
                                  variant="text"
                                  color="default"
                                  onClick={onClick}
                                >
                                  Hapus
                                </Button>
                              );
                            }}
                          />
                        </div>
                      )
                    }
                  />

                  {plan.markdown && (
                    <div className="flex flex-col gap-1">
                      <div className="text-sm font-bold">Deskripsi</div>
                      <CrepeEditor defaultValue={plan.markdown} readOnly />
                    </div>
                  )}
                  {plan.teaching_materials?.length > 0 && (
                    <div className="flex flex-col gap-1">
                      <div className="text-sm font-bold">Bahan Ajar</div>
                      <div>
                        {plan.teaching_materials?.map((material) => {
                          return (
                            <a
                              key={material.id}
                              href={getFileUrl(material.attachment_file_uri)}
                              className="font-kumbh flex gap-1 items-center text-blue-600"
                            >
                              <Download01 width={16} height={16} />
                              {material.description}
                            </a>
                          );
                        })}
                      </div>
                    </div>
                  )}
                  {plan.tasks?.length > 0 && (
                    <div className="flex flex-col gap-1">
                      <div className="text-sm font-bold">Tugas</div>
                      <div>
                        {plan.tasks?.map((task) => {
                          return (
                            <Link
                              key={task.id}
                              to="/classes/$class_id/tasks/$task_id"
                              params={{
                                class_id: class_id,
                                task_id: task.id,
                              }}
                              className="font-kumbh flex gap-1 items-center text-blue-600"
                            >
                              <PencilLine width={16} height={16} />
                              {task.name}
                            </Link>
                          );
                        })}
                      </div>
                    </div>
                  )}
                  {plan.exams?.length > 0 && (
                    <div className="flex flex-col gap-1">
                      <div className="text-sm font-bold">Ujian</div>
                      <div>
                        {plan.exams?.map((exams) => {
                          return (
                            <a
                              key={exams.id}
                              href={createRedirectUrl({
                                payload: {
                                  to:
                                    currentUser.type === "student"
                                      ? "examSession"
                                      : "viewExam",
                                  exam_id: exams.id,
                                },
                                isDev: env.DEV,
                              })}
                              target="_blank"
                              rel="noreferrer"
                              className="font-kumbh flex gap-1 items-center text-blue-600"
                            >
                              <PencilLine width={16} height={16} />
                              {exams.name}
                            </a>
                          );
                        })}
                      </div>
                    </div>
                  )}
                  {plan.teaching_goal && (
                    <div className="flex flex-col gap-1">
                      <div className="text-sm font-bold">
                        Tujuan Pembelajran
                      </div>
                      <CrepeEditor defaultValue={plan.teaching_goal} readOnly />
                    </div>
                  )}
                  {plan.teaching_activity && (
                    <div className="flex flex-col gap-1">
                      <div className="text-sm font-bold">
                        Kegiatan Pembelajaran
                      </div>
                      <CrepeEditor
                        defaultValue={plan.teaching_activity}
                        readOnly
                      />
                    </div>
                  )}
                  {plan.teaching_scoring && (
                    <div className="flex flex-col gap-1">
                      <div className="text-sm font-bold">Penilaian</div>
                      <CrepeEditor
                        defaultValue={plan.teaching_scoring}
                        readOnly
                      />
                    </div>
                  )}
                </div>
              );
            });
          })()}
        </div>
      </div>
    </div>
  );
}
