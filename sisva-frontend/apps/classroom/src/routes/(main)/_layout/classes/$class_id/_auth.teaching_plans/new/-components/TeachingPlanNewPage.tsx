import { getRouteApi } from "@tanstack/react-router";

import TeachingPlanForm from "../../-components/TeachingPlanForm";

export default function TeachingPlanNewPage() {
  const routeApi = getRouteApi(
    "/(main)/_layout/classes/$class_id/_auth/teaching_plans/new/"
  );
  const { class_id } = routeApi.useParams();
  const navigate = routeApi.useNavigate();
  return (
    <div className="flex flex-col items-center px-4 grow">
      <div className="size-full max-w-5xl py-8 flex flex-col gap-4">
        <TeachingPlanForm
          class_id={class_id}
          onSuccess={() => {
            navigate({
              to: "/classes/$class_id",
              params: {
                class_id,
              },
            });
          }}
        />
      </div>
    </div>
  );
}
