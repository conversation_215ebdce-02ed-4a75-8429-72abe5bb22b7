import { classesQeuryOptions } from "@sisva/hooks/query/academic/useClasses";
import { studentInStudentGroupsQueryOptions } from "@sisva/hooks/query/academic/useStudentGroups";
import { createFileRoute, notFound, Outlet } from "@tanstack/react-router";
import { number, object, parse, pipe, transform, unknown } from "valibot";

export const Route = createFileRoute("/(main)/_layout/classes/$class_id/_auth")(
  {
    params: {
      parse(params) {
        return parse(
          object({
            class_id: pipe(unknown(), transform(Number), number()),
          }),
          params
        );
      },
    },
    async beforeLoad({
      context: { currentUser, queryClient },
      params: { class_id },
    }) {
      try {
        const classes = await queryClient.fetchQuery(classesQeuryOptions);
        const studentInStudentGroups = await queryClient.fetchQuery(
          studentInStudentGroupsQueryOptions
        );

        const studentsStudentGroupId = studentInStudentGroups.find(
          (item) => item.student_id === currentUser.id
        );

        const allowClassIds = classes
          .filter((item) => {
            if (currentUser.type === "student") {
              return (
                item.student_group_id ===
                studentsStudentGroupId?.student_group_id
              );
            } else {
              return item.teacher_id === currentUser.id;
            }
          })
          .map((item) => item.id);
        if (!allowClassIds.includes(class_id)) throw notFound();
      } catch {
        throw notFound();
      }
    },

    component: Outlet,
  }
);
