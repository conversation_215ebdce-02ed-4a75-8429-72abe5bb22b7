import { createFileRoute, notFound } from "@tanstack/react-router";
import { number, object, parse, pipe, transform, unknown } from "valibot";

import TeachingPlanEditPage from "./-components/TeachingPlanEditPage";

export const Route = createFileRoute(
  "/(main)/_layout/classes/$class_id/_auth/teaching_plans/$teaching_plan_id/edit/"
)({
  params: {
    parse(params) {
      return parse(
        object({
          class_id: pipe(unknown(), transform(Number), number()),
          teaching_plan_id: pipe(unknown(), transform(Number), number()),
        }),
        params
      );
    },
  },
  beforeLoad({ context: { currentUser } }) {
    if (currentUser.type === "student") throw notFound();
  },
  head() {
    return {
      meta: [
        {
          title: "Rencana pembelajaran | Sisva",
        },
      ],
    };
  },
  component: TeachingPlanEditPage,
});
