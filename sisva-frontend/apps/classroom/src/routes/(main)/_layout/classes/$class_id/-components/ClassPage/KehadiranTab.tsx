import { useStudentsWithStudentGroupAndClassAttendance } from "@sisva/hooks/query/user/useStudents";
import { useCurrentUser } from "@sisva/providers";
import { type Attendance, getAttendanceText } from "@sisva/types/types";
import { AvatarWithAcronymByID } from "@sisva/ui";
import { cn } from "@sisva/utils";
import { getRouteApi } from "@tanstack/react-router";
import { Edit03, SearchSm } from "@untitled-ui/icons-react";
import {
  Button,
  type ButtonProps,
  DatePicker,
  type DatePickerProps,
  Empty,
  Form,
  Input,
  Skeleton,
  Table,
  theme,
} from "antd";
import type { ColumnsType } from "antd/es/table";
import dayjs from "dayjs";
import isoWeek from "dayjs/plugin/isoWeek";
import { sort } from "fast-sort";
import { useDeferredValue, useState } from "react";
import { useForm } from "react-hook-form";
import { FormItem } from "react-hook-form-antd";
dayjs.extend(isoWeek);

import { useClassWithSubjectAndTeacherAndStudentGroup } from "@sisva/hooks/query/academic/useClasses";
import { useSelectedPeriod } from "@sisva/hooks/query/academic/usePeriods";
import { useStudentClassAttendancesMultiple } from "@sisva/hooks/query/attendance/useAttendance";

import UpdateStudentClassAttendanceButton from "../UpdateStudentClassAttendanceButton";

function getDateCodes() {
  const startOfWeek = dayjs().isoWeekday(1);
  const endOfWeek = dayjs().isoWeekday(6);
  const dateIds: number[] = [];
  for (
    let date = startOfWeek;
    date.isBefore(endOfWeek) || date.isSame(endOfWeek);
    date = date.add(1, "day")
  ) {
    dateIds.push(Number(date.format("YYYYMMDD")));
  }
  return dateIds;
}

const dateIds = getDateCodes();

export default function KehadiranTab() {
  const routeApi = getRouteApi("/(main)/_layout/classes/$class_id/_auth/");
  const { class_id } = routeApi.useParams();

  const currentUser = useCurrentUser();

  const token = theme.useToken().token;

  const { data: selectedPeriod } = useSelectedPeriod();
  const { data: class_ } =
    useClassWithSubjectAndTeacherAndStudentGroup(class_id);
  const readOnly = !(selectedPeriod?.id === class_?.student_group?.period_id);

  const [selectedDateId, setSelectedDateId] = useState(
    Number(dayjs().format("YYYYMMDD"))
  ); // for teacher
  const [selectedDateIds, setSelectedDateIds] = useState<number[]>(dateIds); // for student

  function getBgColor(status: Attendance): ButtonProps["color"] {
    switch (status) {
      case "present":
        return "default";
      case "sick":
        return "primary";
      case "leave":
        return "gold";
      case "absent":
        return "danger";
    }
  }

  const onDatePickerChange: DatePickerProps["onChange"] = (date) => {
    if (date) {
      //  ───────────────────────────── for student ─────────────────────────────
      const startOfWeek = date.isoWeekday(1);
      const endOfWeek = date.isoWeekday(6);
      const date_codes: number[] = [];
      for (
        let date = startOfWeek;
        date.isBefore(endOfWeek) || date.isSame(endOfWeek);
        date = date.add(1, "day")
      ) {
        date_codes.push(Number(date.format("YYYYMMDD")));
      }
      setSelectedDateIds(date_codes);
      //  ───────────────────────────── for student ─────────────────────────────

      //  ───────────────────────────── for teacher ─────────────────────────────
      setSelectedDateId(Number(date.format("YYYYMMDD")));
      //  ───────────────────────────── for teacher ─────────────────────────────
    } else {
      setSelectedDateId(Number(dayjs().format("YYYYMMDD")));
      setSelectedDateIds(dateIds);
    }
  };

  //  ───────────────────────────── for student ─────────────────────────────
  const { data: studentClassAttendancesMultiple, isLoading: L2 } =
    useStudentClassAttendancesMultiple(selectedDateIds);
  const currentStudentsAttendances = studentClassAttendancesMultiple.map(
    (schedules, i) => {
      const schedule = schedules.find(
        (schedule) =>
          schedule.class_id === class_id &&
          schedule.student_id === currentUser.id
      );
      const scheduleFallback: typeof schedule = {
        class_id: class_id,
        date_id: selectedDateIds[i]!,
        status: "present",
        student_id: currentUser.id,
        student_name: currentUser.name,
      };
      return schedule ? schedule : scheduleFallback;
    }
  );

  const columnsForStudent: ColumnsType<
    (typeof currentStudentsAttendances)[number]
  > = [
    {
      title: <div className="flex justify-center">Waktu</div>,
      dataIndex: "date_id",
      render: (_, record) => {
        const date = dayjs(record?.date_id.toString(), "YYYYMMDD");
        return (
          <div className="flex flex-col items-center">
            <div>{date.format("dddd")}</div>
            <div className="text-xs text-neutral-500">
              {date.format("D MMMM YYYY")}
            </div>
          </div>
        );
      },
    },
    {
      title: () => {
        return (
          <div className="flex gap-2 items-center justify-center">
            <AvatarWithAcronymByID size={32} user_id={currentUser.id} />
            <div>{currentUser.name}</div>
          </div>
        );
      },
      dataIndex: "status",
      render: (value: Attendance, record) => {
        return (
          <div className="flex justify-center">
            <Button
              variant={value === "present" ? "text" : "solid"}
              color={getBgColor(value)}
              shape="round"
              className="shadow-none cursor-default"
            >
              {getAttendanceText(value)}
            </Button>
          </div>
        );
      },
    },
  ];

  //  ───────────────────────────── for student ─────────────────────────────

  //  ───────────────────────────── for teacher ─────────────────────────────
  const { data: students = [], isLoading: L1 } =
    useStudentsWithStudentGroupAndClassAttendance({
      date_id: selectedDateId,
      class_id: class_id,
    });

  const sortedStudents = sort(
    students.map((student) => {
      const class_attendance: typeof student.class_attendance =
        student.class_attendance
          ? student.class_attendance
          : {
              class_id,
              date_id: selectedDateId,
              status: "present",
              student_id: student.id,
              student_name: student.name,
            };
      return {
        ...student,
        class_attendance,
      };
    })
  ).asc((student) => student.name);
  const studentIds = sortedStudents.map((student) => student.id);

  const columnsForTeacher: ColumnsType<(typeof sortedStudents)[number]> = [
    {
      title: <div className="flex justify-center">Siswa</div>,
      dataIndex: "name",
      render: (_, record) => {
        return (
          <div className="flex gap-2 items-center">
            <AvatarWithAcronymByID size={32} user_id={record.id} />
            <div>{record.name}</div>
          </div>
        );
      },
    },
    {
      title: () => {
        const date = dayjs(selectedDateId.toString(), "YYYYMMDD");
        return (
          <div className="flex flex-col items-center">
            <div>{date.format("dddd")}</div>
            <div className="text-xs text-neutral-500">
              {date.format("D MMMM YYYY")}
            </div>
          </div>
        );
      },
      dataIndex: ["class_attendance", "status"],
      render: (value: Attendance, record) => {
        return (
          <div className="flex justify-center">
            <UpdateStudentClassAttendanceButton
              initialStudentId={record.id}
              studentIds={studentIds}
              date_id={selectedDateId}
              class_id={class_id}
              renderTrigger={(onClick) => {
                return (
                  <Button
                    disabled={readOnly}
                    variant={value === "present" ? "text" : "solid"}
                    color={getBgColor(value)}
                    onClick={onClick}
                    shape="round"
                    className="shadow-none"
                    icon={<Edit03 width={16} height={16} />}
                    iconPosition="end"
                  >
                    {getAttendanceText(value)}
                  </Button>
                );
              }}
            />
          </div>
        );
      },
    },
  ];

  //  ───────────────────────────── for teacher ─────────────────────────────

  const { control, watch } = useForm({
    defaultValues: {
      searchText: "",
    },
  });

  const searchText = watch("searchText");
  const defferedSearch = useDeferredValue(searchText);

  return (
    <div className="flex flex-col gap-4">
      <div className="flex justify-between gap-4">
        <h2>Kehadiran</h2>
        <div className="flex gap-4 items-center">
          <DatePicker
            placeholder="Pilih tanggal"
            onChange={onDatePickerChange}
            picker={currentUser.type !== "student" ? undefined : "week"}
          />
          {currentUser.type !== "student" && !readOnly && (
            <UpdateStudentClassAttendanceButton
              date_id={selectedDateId}
              class_id={class_id}
              studentIds={studentIds}
            />
          )}
        </div>
      </div>
      <div className="flex flex-col gap-2">
        <Form className={cn("contents thick-scrollbar", { hidden: true })}>
          <FormItem control={control} name="searchText" className="contents">
            <Input
              placeholder="Cari"
              suffix={<SearchSm color={token.colorTextLabel} />}
            />
          </FormItem>
          <div className="flex gap-2 overflow-auto pe-1">{/* filters */}</div>
        </Form>
      </div>
      <div className="size-full flex flex-col gap-4">
        <div className="flex flex-col items-start gap-4">
          <div className="w-full flex flex-col gap-4">
            {(() => {
              if (L1) return <Skeleton active />;
              if (students.length === 0) return <Empty />;
              if (currentUser.type === "teacher")
                return (
                  <Table
                    dataSource={sortedStudents}
                    columns={columnsForTeacher}
                    rowKey={(record) => record.id}
                    pagination={false}
                    bordered
                    scroll={{
                      x: "max-content",
                    }}
                  />
                );
              if (currentUser.type === "student")
                return (
                  <Table
                    dataSource={currentStudentsAttendances}
                    columns={columnsForStudent}
                    rowKey={(record) => record.date_id}
                    pagination={false}
                    bordered
                    scroll={{
                      x: "max-content",
                    }}
                  />
                );
            })()}
          </div>
        </div>
      </div>
    </div>
  );
}
