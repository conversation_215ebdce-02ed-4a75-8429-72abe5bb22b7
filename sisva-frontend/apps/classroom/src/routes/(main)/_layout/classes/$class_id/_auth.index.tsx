import { createFileRoute } from "@tanstack/react-router";
import { number, object, parse, pipe, transform, unknown } from "valibot";

import ClassPage from "./-components/ClassPage";

export const Route = createFileRoute(
  "/(main)/_layout/classes/$class_id/_auth/"
)({
  params: {
    parse(params) {
      return parse(
        object({
          class_id: pipe(unknown(), transform(Number), number()),
        }),
        params
      );
    },
  },
  head() {
    return {
      meta: [
        {
          title: "Kelas | Sisva",
        },
      ],
    };
  },
  component: ClassPage,
});
