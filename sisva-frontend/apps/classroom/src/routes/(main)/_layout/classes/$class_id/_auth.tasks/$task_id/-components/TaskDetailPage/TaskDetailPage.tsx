import { valibotResolver } from "@hookform/resolvers/valibot";
import { useClassWithSubjectAndTeacherAndStudentGroup } from "@sisva/hooks/query/academic/useClasses";
import { useSetScore } from "@sisva/hooks/query/classroom/useScores";
import {
  useSetSubmission,
  useSubmissions,
} from "@sisva/hooks/query/classroom/useSubmissions";
import {} from "@sisva/hooks/query/classroom/useSubmissions";
import { useTask } from "@sisva/hooks/query/classroom/useTasks";
import { useStudentsByClassId } from "@sisva/hooks/query/user/useStudents";
import { useGetFileUrl } from "@sisva/hooks/utils";
import { useCurrentUser, useNotificationAPI } from "@sisva/providers";
import type { Score, Submission } from "@sisva/types/apiTypes";
import { AvatarWithAcronymByID } from "@sisva/ui";
import { cn, isDateEmpty } from "@sisva/utils";
import { getRouteApi } from "@tanstack/react-router";
import { Download01, SearchSm } from "@untitled-ui/icons-react";
import {
  Alert,
  Button,
  Divider,
  Empty,
  Form,
  Input,
  InputNumber,
  theme,
} from "antd";
import dayjs from "dayjs";
import id from "dayjs/locale/id";
import fuzzysort from "fuzzysort";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { FormItem } from "react-hook-form-antd";
import {
  type InferOutput,
  maxValue,
  minValue,
  nonEmpty,
  number,
  object,
  pipe,
  string,
} from "valibot";

import UploadForm from "#/routes/(main)/-components/UploadForm";
dayjs.locale(id);

export default function TaskDetailPage() {
  const routeApi = getRouteApi(
    "/(main)/_layout/classes/$class_id/_auth/tasks/$task_id/"
  );
  const { class_id, task_id } = routeApi.useParams();
  const token = theme.useToken().token;
  const currentUser = useCurrentUser();

  const getFileUrl = useGetFileUrl();
  const [selectedStudentId, setSelectedStudentId] = useState<
    string | undefined
  >();

  const { data: task } = useTask({ task_id });
  const { data: class_ } =
    useClassWithSubjectAndTeacherAndStudentGroup(class_id);
  const { data: submissions = [] } = useSubmissions({ task_id });
  const { data: students = [], isLoading: L1 } = useStudentsByClassId(class_id);
  const selectedStudent = students.find(
    (student) => student.id === selectedStudentId
  );

  const selectedStudentsTask = selectedStudent?.tasks.find(
    (task) => task.id === task_id
  );
  const selectedStudentsSubmission = submissions.find(
    (item) =>
      item.task_id === task_id && item.student_id === selectedStudent?.id
  );
  const currentStudentsSubmission = submissions.find(
    (item) => item.task_id === task_id && item.student_id === currentUser.id
  );
  const currentStudentsScore = students
    .find((student) => student.id === currentUser.id)
    ?.tasks.find((task) => task.id === task_id)?.score;

  const firstStudentId = students[0]?.id;
  useEffect(() => {
    if (firstStudentId) setSelectedStudentId(firstStudentId);
  }, [firstStudentId]);

  const { control, watch } = useForm({
    defaultValues: {
      searchText: "",
    },
  });

  const searchText = watch("searchText");

  const filteredStudents = fuzzysort
    .go(searchText, students, {
      keys: ["name"],
      all: !searchText,
    })
    .map((data) => data.obj);

  return (
    <div className="flex flex-col items-center px-4 grow">
      <div className="size-full max-w-5xl py-8 flex flex-col gap-4">
        <div className="flex flex-col gap-4 shadow-lg rounded-lg p-4">
          <div className="text-lg font-bold">{task?.name}</div>
          <div className="flex gap-2 items-center justify-between text-sm">
            <div>
              <div className="font-medium">Guru</div>
              <div className="text-neutral-600">{class_?.teacher_name}</div>
            </div>
            <div>
              <div className="font-medium">Deadline</div>
              <div className="text-neutral-600">
                {(() => {
                  const deadlineDayjs = dayjs(
                    task?.deadline,
                    "DD/MM/YYYY h:mm A Z"
                  );
                  return isDateEmpty(deadlineDayjs)
                    ? "Tidak ada"
                    : deadlineDayjs.format("dddd DD/MM/YYYY hh:mm A");
                })()}
              </div>
            </div>
            <div>
              <div className="font-medium">Pengumpulan</div>
              <div className="text-neutral-600">
                {task?.allow_submission ? "Terima" : "Tidak perlu"}
              </div>
            </div>
          </div>
          <Divider />
          <div className="text-sm flex gap-4 justify-between items-end">
            <div>
              {task?.description && (
                <div className="font-medium">Deskripsi</div>
              )}
              <div className="text-neutral-600">{task?.description}</div>
            </div>
            {task?.attachment_file_uri && (
              <Button
                href={getFileUrl(task?.attachment_file_uri ?? "")}
                variant="outlined"
                icon={<Download01 width={20} />}
              >
                Lampiran
              </Button>
            )}
          </div>
        </div>
        {/* ─────────────────────────────────── for teacher ─────────────────────────────────── */}
        <div
          className={cn("flex rounded-lg shadow-lg flex-col md:flex-row", {
            hidden: currentUser.type === "student",
          })}
        >
          <div className="md:w-96 w-full border-[0] border-r border-solid border-neutral-300">
            <div className="p-4 font-semibold border-[0] border-b border-solid border-neutral-300">
              Daftar Siswa
            </div>
            <div className="p-4 border-[0] border-b border-solid border-neutral-300">
              <Form>
                <FormItem
                  control={control}
                  name="searchText"
                  className="contents"
                >
                  <Input
                    placeholder="Cari siswa"
                    suffix={<SearchSm color={token.colorTextLabel} />}
                  />
                </FormItem>
              </Form>
            </div>
            <div className="h-[50svh] overflow-auto">
              {filteredStudents.length > 0 ? (
                filteredStudents.map((student) => {
                  const task = student.tasks.find(
                    (task) => task.id === task_id
                  );

                  return (
                    <div
                      key={student.id}
                      className="flex gap-2 items-center p-4 text-sm border-[0] border-b border-solid border-neutral-300 cursor-pointer hover:bg-neutral-100 last:border-b-0"
                      onClick={() => {
                        setSelectedStudentId(student.id);
                      }}
                    >
                      <AvatarWithAcronymByID user_id={student.id} />
                      <div className="flex-1">{student.name}</div>
                      <div className="flex items-center gap-0.5">
                        {task?.score ? (
                          task.score.value
                        ) : (
                          <div className="border border-solid w-5 h-3 border-black rounded-sm"></div>
                        )}
                        <div>/</div>
                        <div>100</div>
                      </div>
                    </div>
                  );
                })
              ) : (
                <div className="flex flex-col justify-center h-full">
                  <Empty />
                </div>
              )}
            </div>
          </div>
          <div className="flex flex-col gap-4 p-4 flex-1">
            <div className="font-semibold">{selectedStudent?.name}</div>

            {selectedStudentsSubmission ? (
              <div className="text-sm flex flex-col gap-4">
                <div className="text-neutral-600">
                  {dayjs(
                    selectedStudentsSubmission?.submission_time,
                    "DD/MM/YYYY h:mm A Z"
                  ).format("dddd DD/MM/YYYY h:mm A")}
                </div>
                <div>
                  <Button
                    href={getFileUrl(
                      selectedStudentsSubmission?.attachment_file_uri ?? ""
                    )}
                    variant="outlined"
                    icon={<Download01 width={20} />}
                  >
                    {selectedStudentsSubmission?.attachment_file_uri}
                  </Button>
                </div>
                <div className="flex flex-col gap-1">
                  <div className="font-semibold">Catatan pengumpulan</div>
                  <Input.TextArea
                    value={selectedStudentsSubmission?.note}
                    readOnly
                    style={{
                      resize: "none",
                    }}
                    className="cursor-default hover:border-neutral-300"
                  />
                </div>
              </div>
            ) : (
              <div className="text-neutral-600 text-sm">Belum mengumpulkan</div>
            )}
            <Divider />

            {selectedStudent && (
              <ScoreForm
                score={selectedStudentsTask?.score}
                student_id={selectedStudent?.id}
                task_id={task_id}
              />
            )}
          </div>
        </div>

        {/* ─────────────────────────────────── for student ─────────────────────────────────── */}
        <div
          className={cn("flex rounded-lg shadow-lg flex-col p-4 gap-4", {
            hidden: currentUser.type === "teacher",
          })}
        >
          <div className="font-medium ">{currentUser.name}</div>
          {currentStudentsScore ? (
            <>
              <div className="flex flex-col gap-1">
                <div>Nilai</div>
                <InputNumber
                  value={currentStudentsScore.value}
                  readOnly
                  className="hover:border-neutral-300"
                />
              </div>
              <div className="flex flex-col gap-1">
                <div>Komentar penilaian</div>
                <Input.TextArea
                  value={currentStudentsScore.feedback}
                  readOnly
                  className="hover:border-neutral-300 cursor-default"
                  style={{
                    resize: "none",
                  }}
                />
              </div>
            </>
          ) : (
            <div className="text-sm text-neutral-600">Belum dinilai</div>
          )}
          <Divider />
          <SubmissionForm
            submission={currentStudentsSubmission}
            task_id={task_id}
            student_id={currentUser.id}
          />
        </div>
      </div>
    </div>
  );
}

function ScoreForm({
  score,
  student_id,
  task_id,
}: {
  score?: Score;
  student_id: string;
  task_id: number;
}) {
  const notification = useNotificationAPI();

  const scoreSchema = object({
    student_id: pipe(string(), nonEmpty("Siswa harus dipilih")),
    feedback: pipe(string()),
    value: pipe(
      number("Nilai harus diisi"),
      minValue(0, "Nilai minimum adalah 0"),
      maxValue(100, "Nilai maxium adalah 100")
    ),
  });
  type ScoreForm = InferOutput<typeof scoreSchema>;
  const { control, handleSubmit, setValue, reset, watch, clearErrors } =
    useForm<ScoreForm>({
      values: {
        student_id: student_id,
        //@ts-expect-error allow undefined or null
        value: score?.value ?? null,
        feedback: score?.feedback ?? "",
      },
      resolver: valibotResolver(scoreSchema),
    });

  const { mutate: setScore } = useSetScore({
    onSuccess() {
      notification.success({ message: "Nilai berhasil diperbarui" });
    },
    onError() {
      notification.error({ message: "Nilai gagal diperbarui" });
    },
  });

  return (
    <Form
      onFinish={handleSubmit((values) => {
        setScore({
          ...values,
          task_id: task_id,
        });
      })}
      layout="vertical"
    >
      <FormItem
        control={control}
        name="value"
        label={<div className="font-semibold">Nilai</div>}
      >
        <InputNumber min={0} max={100} />
      </FormItem>
      <FormItem
        control={control}
        name="feedback"
        label={<div className="font-semibold">Komentar penilaian</div>}
      >
        <Input.TextArea placeholder="Tulis komentar penilaian di sini" />
      </FormItem>
      <div>
        <Button htmlType="submit" variant="solid" color="primary">
          Simpan Nilai
        </Button>
      </div>
    </Form>
  );
}

function SubmissionForm({
  submission,
  student_id,
  task_id,
}: {
  submission?: Submission;
  student_id: string;
  task_id: number;
}) {
  const notification = useNotificationAPI();

  const { data: task } = useTask({ task_id });
  const deadlineDayjs = dayjs(task?.deadline, "DD/MM/YYYY h:mm A Z");
  const isDeadlineEmpty = isDateEmpty(deadlineDayjs);

  const disableBecauseDeadline =
    !task?.allow_overdue_submission &&
    deadlineDayjs.isBefore(dayjs()) &&
    !isDeadlineEmpty;

  const disable = !!(
    disableBecauseDeadline ||
    !task?.allow_submission ||
    submission
  );

  const submissionSchema = object({
    student_id: pipe(string(), nonEmpty("Siswa harus dipilih")),
    note: pipe(string()),
    attachment_file_uri: pipe(string(), nonEmpty("Lampiran tugas harus ada")),
  });
  type SubmissionForm = InferOutput<typeof submissionSchema>;
  const { control, handleSubmit, setValue, reset, watch, clearErrors } =
    useForm<SubmissionForm>({
      values: {
        student_id: student_id,
        note: submission?.note ?? "",
        attachment_file_uri: submission?.attachment_file_uri ?? "",
      },
      resolver: valibotResolver(submissionSchema),
    });

  const { mutate: setSubmission } = useSetSubmission({
    task_id,
    onSuccess() {
      notification.success({ message: "Tugas berhasil dikumpulkan" });
    },
    onError() {
      notification.error({ message: "Tugas gagal dikumpulkan" });
    },
  });

  return (
    <Form
      onFinish={handleSubmit((values) => {
        setSubmission({
          ...values,
        });
      })}
      layout="vertical"
    >
      {disableBecauseDeadline && !submission && (
        <div className="pb-4">
          <Alert
            message="Tidak dapat mengumpulkan karena deadline sudah lewat"
            type="error"
          />
        </div>
      )}
      <div className="pb-4">
        <UploadForm
          buttonText="Tambah lampiran tugas"
          control={control}
          name="attachment_file_uri"
          setValue={setValue}
          clearErrors={clearErrors}
          disabled={disable}
        />
      </div>
      <FormItem
        control={control}
        name="note"
        label={<div className="font-semibold">Catatan</div>}
        disabled={disable}
      >
        <Input.TextArea placeholder="Tulis catatan di sini" />
      </FormItem>
      <div className="flex justify-end">
        <Button
          htmlType="submit"
          variant="solid"
          color="primary"
          disabled={disable}
        >
          Kumpulkan
        </Button>
      </div>
    </Form>
  );
}
