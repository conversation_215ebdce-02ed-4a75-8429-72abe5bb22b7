import { useClassWithSubjectAndTeacherAndStudentGroup } from "@sisva/hooks/query/academic/useClasses";
import { useSelectedPeriod } from "@sisva/hooks/query/academic/usePeriods";
import { useSubject } from "@sisva/hooks/query/academic/useSubjects";
import { useExamsByClassId } from "@sisva/hooks/query/exam/useExams";
import { useCurrentUser } from "@sisva/providers";
import { createRedirectUrl } from "@sisva/utils";
import { getRouteApi } from "@tanstack/react-router";
import { Plus, SearchSm } from "@untitled-ui/icons-react";
import { Button, Empty, Form, Input, Skeleton, theme } from "antd";
import fuzzysort from "fuzzysort";
import { useDeferredValue } from "react";
import { Fragment } from "react/jsx-runtime";
import { useForm } from "react-hook-form";
import { FormItem } from "react-hook-form-antd";

import { env } from "#/env";
import ExamCard from "#/routes/(main)/-components/ExamCard";
import { GroupHeader } from "#/routes/(main)/-components/GroupHeader";

export default function UjianTab() {
  const routeApi = getRouteApi("/(main)/_layout/classes/$class_id/_auth/");
  const { class_id } = routeApi.useParams();

  const currentUser = useCurrentUser();

  const token = theme.useToken().token;

  const { data: selectedPeriod, isLoading: L1 } = useSelectedPeriod();
  const { data: class_, isLoading: L2 } =
    useClassWithSubjectAndTeacherAndStudentGroup(class_id);
  const readOnly = !(selectedPeriod?.id === class_?.student_group?.period_id);
  const { data: exams = [], isLoading: L3 } = useExamsByClassId(class_id);

  const isLoading = L1 || L2 || L3;

  const { control, watch } = useForm({
    defaultValues: {
      searchText: "",
    },
  });

  const searchText = watch("searchText");
  const defferedSearch = useDeferredValue(searchText);

  const filteredExams = fuzzysort
    .go(defferedSearch, exams, {
      keys: ["name", "description"],
      all: !defferedSearch,
    })
    .map((data) => data.obj);

  const examsGroupedBySubjectId = Object.entries(
    Object.groupBy(filteredExams, (exam) => exam.class?.subject_id ?? 0)
  );

  return (
    <div className="flex flex-col gap-4">
      <div className="flex justify-between gap-4">
        <h2>Ujian</h2>
        {currentUser?.type !== "student" && (
          <></>
          // button
        )}
      </div>
      <div className="flex flex-col gap-2">
        <Form className="contents thick-scrollbar">
          <FormItem control={control} name="searchText" className="contents">
            <Input
              placeholder="Cari"
              suffix={<SearchSm color={token.colorTextLabel} />}
            />
          </FormItem>
          <div className="flex justify-end gap-2 overflow-auto pe-1">
            {currentUser?.type !== "student" && !readOnly && (
              <a
                href={createRedirectUrl({
                  payload: {
                    to: "createExam",
                    class_id: class_id,
                  },
                  isDev: env.DEV,
                })}
                target="_blank"
                rel="noreferrer"
              >
                <Button icon={<Plus />} variant="solid" color="primary">
                  Tambah Ujian
                </Button>
              </a>
            )}
          </div>
        </Form>
      </div>
      <div className="size-full flex flex-col gap-4">
        <div className="flex flex-col items-start gap-4">
          <div className="w-full flex flex-col gap-4">
            {(() => {
              if (isLoading) return <Skeleton active />;

              if (examsGroupedBySubjectId.length === 0) return <Empty />;
              return examsGroupedBySubjectId.map(([subject_id, exams]) => {
                return (
                  <Fragment key={subject_id}>
                    <SubjectTitle subject_id={Number(subject_id)} />
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                      {exams?.map((exam) => (
                        <ExamCard key={exam.id} exam_id={exam.id} />
                      ))}
                    </div>
                  </Fragment>
                );
              });
            })()}
          </div>
        </div>
      </div>
    </div>
  );
}

function SubjectTitle({ subject_id }: { subject_id: number }) {
  const { data: subject } = useSubject(subject_id);

  return <GroupHeader text={subject?.name ?? ""} />;
}
