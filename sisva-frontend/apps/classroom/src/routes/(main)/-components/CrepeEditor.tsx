import "@milkdown/crepe/theme/common/style.css";
import "@milkdown/crepe/theme/frame.css";
import "./milkdown.css";

import { Crepe } from "@milkdown/crepe";
import { Milkdown, MilkdownProvider, useEditor } from "@milkdown/react";
import { useUploadFile } from "@sisva/hooks/query/file/useFiles";
import { useGetFileUrl } from "@sisva/hooks/utils";

type Props = {
  onMarkdownChange?: (markdown: string) => void;
  placeholder?: string;
  readOnly?: boolean;
  defaultValue?: string;
};

function Editor({
  onMarkdownChange,
  readOnly = false,
  placeholder = "Masukan teks...",
  defaultValue,
}: Props) {
  const { mutateAsync: uploadFile } = useUploadFile();
  const getFileUrl = useGetFileUrl();
  const { get } = useEditor((root) => {
    const crepe = new Crepe({
      root,
      defaultValue: defaultValue,
      featureConfigs: {
        [Crepe.Feature.Placeholder]: {
          text: placeholder,
        },
        [Crepe.Feature.BlockEdit]: {
          slashMenuTextGroupLabel: "Teks",
          slashMenuListGroupLabel: "Daftar",
          slashMenuAdvancedGroupLabel: "Lainnya",
          slashMenuImageLabel: "Gambar",
          slashMenuCodeBlockLabel: "Kode",
          slashMenuTableLabel: "Tabel",
          slashMenuMathLabel: "Matematika",
          slashMenuQuoteLabel: "Kutipan",
          slashMenuDividerLabel: "Pembatas",
          slashMenuBulletListLabel: "Daftar Berpoin",
          slashMenuOrderedListLabel: "Daftar Berurutan",
          slashMenuTaskListLabel: "Daftar Tugas",
        },
        [Crepe.Feature.ImageBlock]: {
          async onUpload(file) {
            const formData = new FormData();
            formData.append("file", file);
            const uri = await uploadFile(formData);
            return getFileUrl(uri);
          },
          blockUploadButton: () => "Unggah gambar",
          blockUploadPlaceholderText: "atau tambah link",
        },
      },
    });
    crepe.on((listener) => {
      listener.markdownUpdated((ctx, markdown) => {
        onMarkdownChange?.(markdown);
      });
    });
    crepe.setReadonly(readOnly);
    return crepe;
  });

  return <Milkdown />;
}

export default function CrepeEditor(props: Props) {
  return (
    <MilkdownProvider>
      <Editor {...props} />
    </MilkdownProvider>
  );
}
