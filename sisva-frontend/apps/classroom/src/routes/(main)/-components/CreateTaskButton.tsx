import { valibotResolver } from "@hookform/resolvers/valibot";
import { useGenerateTask, useEditTask } from "@sisva/hooks/query/classroom/useAi"; // Add this import
import {
  useCreateTask,
  useDeleteTask,
  useTask,
  useUpdateTask,
} from "@sisva/hooks/query/classroom/useTasks";
import {
  useTeachingPlan,
  useTeachingPlansByClassId,
  useUpdateTeachingPlan,
} from "@sisva/hooks/query/classroom/useTeachingPlans";
import { useNotificationAPI } from "@sisva/providers";
import { isDateEmpty } from "@sisva/utils";
import type { ReactNode } from "@tanstack/react-router";
import { Plus, Stars02 } from "@untitled-ui/icons-react"; // Add Sparkles icon
import { useBoolean } from "ahooks";
import { useState } from "react";
import {
  Button,
  DatePicker,
  Form,
  Input,
  Modal,
  Radio,
  Select,
  Spin,
} from "antd";
import type { Dayjs } from "dayjs";
import dayjs from "dayjs";
import { useForm } from "react-hook-form";
import { FormItem } from "react-hook-form-antd";
import {
  boolean,
  custom,
  forward,
  nonEmpty,
  nullable,
  number,
  object,
  optional,
  partialCheck,
  pipe,
  string,
} from "valibot";

import UploadForm from "./UploadForm";

// this component has 3 mode: create, edit, delete
export function CreateTaskButton({
  task_id,
  class_id,
  renderTrigger,
  deleteMode,
  onCreateSuccess,
  selectTeachingPlan,
  className, // Add className prop for AI generation
  subjectName, // Add subjectName prop for AI generation
}: {
  task_id?: number;
  class_id?: number;
  renderTrigger?: (onClick: () => void) => ReactNode;
  deleteMode?: boolean;
  onCreateSuccess?: (teaching_material_id: number) => void;
  selectTeachingPlan?: boolean;
  className?: string; // Add this prop
  subjectName?: string; // Add this prop
}) {
  const [open, { toggle }] = useBoolean();
  const [
    aiGenerating,
    { setTrue: setAiGenerating, setFalse: setAiGeneratingFalse },
  ] = useBoolean();
  const [
    aiGenerateModalOpen,
    { setTrue: setAiGenerateModalOpen, setFalse: setAiGenerateModalClose },
  ] = useBoolean();
  const [aiGenerateTitle, setAiGenerateTitle] = useState("");
  const [aiGenerateSpecifications, setAiGenerateSpecifications] = useState("");
  const [
    aiEditModalOpen,
    { setTrue: setAiEditModalOpen, setFalse: setAiEditModalClose },
  ] = useBoolean();
  const [aiEditInstructions, setAiEditInstructions] = useState("");
  const [
    aiEditing,
    { setTrue: setAiEditing, setFalse: setAiEditingFalse },
  ] = useBoolean();
  const notification = useNotificationAPI();

  const { data: task } = useTask({
    task_id,
  });
  const { data: teachingPlans = [] } = useTeachingPlansByClassId(class_id);

  const deadlineDayjs = task?.deadline
    ? dayjs(task.deadline, "DD/MM/YYYY h:mm A Z")
    : null;
  const isDeadlineEmpty = deadlineDayjs ? isDateEmpty(deadlineDayjs) : false;

  const { control, handleSubmit, setValue, reset, watch, clearErrors } =
    useForm({
      values: {
        name: task?.name ?? "",
        description: task?.description ?? "",
        class_id: task?.class_id ?? class_id ?? 0,
        deadline_dayjs: isDeadlineEmpty ? null : deadlineDayjs,
        attachment_file_uri: task?.attachment_file_uri ?? "",
        allow_submission: task?.allow_submission ?? true,
        allow_overdue_submission: task?.allow_overdue_submission ?? false,
        teaching_plan_id: undefined,
      },
      resolver: valibotResolver(
        pipe(
          object({
            name: pipe(string(), nonEmpty("Nama harus diisi")),
            description: pipe(string()),
            class_id: number(),
            deadline_dayjs: nullable(
              custom<Dayjs>((input) => {
                try {
                  //@ts-expect-error check if valid dayjs object
                  return input.isValid();
                } catch {
                  return false;
                }
              })
            ),
            attachment_file_uri: pipe(string()),
            allow_submission: boolean(),
            allow_overdue_submission: boolean(),
            teaching_plan_id: selectTeachingPlan
              ? number("Rencana Pembelajaran wajib dilipih")
              : optional(number()),
          }),
          forward(
            partialCheck(
              [["allow_submission"], ["deadline_dayjs"]],
              ({ allow_submission, deadline_dayjs }) => {
                if (allow_submission && deadline_dayjs === null) return false;
                return true;
              },
              "Deadline harus diisi"
            ),
            ["deadline_dayjs"]
          )
        )
      ),
    });

  const teaching_plan_id = watch("teaching_plan_id");
  const { data: teachingPlan } = useTeachingPlan(teaching_plan_id);

  const { mutateAsync: createTask } = useCreateTask();

  const { mutate: updateTask } = useUpdateTask({
    onSuccess() {
      notification.success({ message: "Tugas berhasil perbarui" });
      toggle();
      reset();
    },
    onError() {
      notification.error({ message: "Tugas gagal perbarui" });
    },
  });

  const { mutate: deleteTask } = useDeleteTask({
    onSuccess() {
      notification.success({ message: "Tugas berhasil dihapus" });
      toggle();
    },
    onError() {
      notification.error({ message: "Tugas gagal dihapus" });
    },
  });

  const { mutate: updateTeachingPlan } = useUpdateTeachingPlan();

  // Add AI generation hook
  const { mutate: generateTask } = useGenerateTask();

  // Add AI edit hook
  const { mutate: editTask } = useEditTask();

  // Function to handle AI generation modal open
  const handleAIGenerateModalOpen = () => {
    const currentName = watch("name");
    setAiGenerateTitle(currentName || ""); // Set current name as default
    setAiGenerateModalOpen();
  };

  // Function to handle AI generation
  const handleAIGenerate = () => {
    const selectedTeachingPlan = teachingPlans.find(
      (plan) => plan.id === teaching_plan_id
    );

    if (!aiGenerateTitle.trim()) {
      notification.warning({
        message: "Judul tugas harus diisi",
      });
      return;
    }

    setAiGenerating();
    generateTask(
      {
        title: aiGenerateTitle,
        className,
        planTitle: selectedTeachingPlan?.title,
        subjectName,
        specifications: aiGenerateSpecifications || undefined,
      },
      {
        onSuccess(data) {
          // Set the generated data to form
          setValue("name", data.name);
          setValue("description", data.description);
          setAiGeneratingFalse();
          setAiGenerateModalClose();
          setAiGenerateTitle("");
          setAiGenerateSpecifications("");
          notification.success({
            message: "Tugas berhasil di-generate dengan AI",
          });
        },
        onError(error) {
          setAiGeneratingFalse();
          notification.error({ message: "Gagal generate tugas dengan AI" });
          console.error("AI Generation Error:", error);
        },
      }
    );
  };

  // Function to handle AI edit
  const handleAIEdit = () => {
    const currentName = watch("name");
    const currentDescription = watch("description");
    const selectedTeachingPlan = teachingPlans.find(
      (plan) => plan.id === teaching_plan_id
    );

    if (!currentName.trim()) {
      notification.warning({
        message: "Silakan isi nama tugas terlebih dahulu",
      });
      return;
    }

    if (!aiEditInstructions.trim()) {
      notification.warning({
        message: "Silakan isi instruksi edit terlebih dahulu",
      });
      return;
    }

    setAiEditing();
    editTask(
      {
        currentTask: {
          name: currentName,
          description: currentDescription,
        },
        instructions: aiEditInstructions,
        className,
        planTitle: selectedTeachingPlan?.title,
        subjectName,
      },
      {
        onSuccess(data) {
          // Set the edited data to form
          setValue("name", data.name);
          setValue("description", data.description);
          setAiEditingFalse();
          setAiEditModalClose();
          setAiEditInstructions("");
          notification.success({
            message: "Tugas berhasil diedit dengan AI",
          });
        },
        onError(error) {
          setAiEditingFalse();
          notification.error({ message: "Gagal edit tugas dengan AI" });
          console.error("AI Edit Error:", error);
        },
      }
    );
  };

  return (
    <>
      {renderTrigger ? (
        renderTrigger(toggle)
      ) : (
        <Button
          onClick={toggle}
          icon={<Plus />}
          variant="solid"
          color="primary"
        >
          Tambah Tugas
        </Button>
      )}
      <Modal
        title={(() => {
          if (task_id) {
            if (deleteMode) return "Hapus Bahan Ajar";
            return "Edit Tugas";
          }
          return "Tambah Tugas";
        })()}
        open={open}
        onCancel={() => {
          reset();
          toggle();
        }}
        footer={null}
        width={640}
      >
        <Form
          onFinish={handleSubmit(async (values) => {
            if (task_id) {
              if (deleteMode) {
                return deleteTask(task_id);
              }
              return updateTask({
                ...values,
                task_id: task_id,
                deadline:
                  values.deadline_dayjs?.format("DD/MM/YYYY h:mm A Z") ?? "",
              });
            }
            createTask(
              {
                ...values,
                deadline:
                  values.deadline_dayjs?.format("DD/MM/YYYY h:mm A Z") ?? "",
              },
              {
                onSuccess({ task_id }) {
                  notification.success({ message: "Tugas berhasil dibuat" });
                  toggle();
                  reset();
                  onCreateSuccess?.(task_id);

                  if (teachingPlan) {
                    updateTeachingPlan(
                      {
                        ...teachingPlan,
                        teaching_materials:
                          teachingPlan.teaching_materials ?? [],
                        tasks: [...(teachingPlan.tasks ?? []), { id: task_id }],

                        teaching_plan_id: teachingPlan.id,
                      },
                      {
                        onSuccess() {
                          notification.success({
                            message: "Rencana Pembelajaran berhasil perbarui",
                          });
                        },
                        onError() {
                          notification.error({
                            message: "Rencana Pembelajaran gagal perbarui",
                          });
                        },
                      }
                    );
                  }
                },
                onError() {
                  notification.error({ message: "Tugas gagal dibuat" });
                },
              }
            );
          })}
          layout="vertical"
          requiredMark={false}
        >
          <div className="flex items-end gap-2 mb-4">
            <div className="flex-1">
              <FormItem control={control} name="name" label="Nama">
                <Input placeholder="Nama tugas" disabled={deleteMode} />
              </FormItem>
            </div>
            {!deleteMode && (
              <div className="flex gap-2">
                {!task_id && (
                  <Button
                    type="default"
                    icon={<Stars02 />}
                    onClick={handleAIGenerateModalOpen}
                    disabled={aiGenerating}
                    className="mb-2"
                  >
                    AI Generate
                  </Button>
                )}
                {task_id && (
                  <Button
                    type="default"
                    icon={<Stars02 />}
                    onClick={setAiEditModalOpen}
                    disabled={aiEditing}
                    className="mb-2"
                  >
                    Edit dengan AI
                  </Button>
                )}
              </div>
            )}
          </div>

          <FormItem control={control} name="description" label="Deskripsi">
            <Input.TextArea
              placeholder="Deskripsi tugas"
              disabled={deleteMode || aiGenerating}
              autoSize={{ minRows: 3 }}
            />
          </FormItem>

          <div className="grid grid-cols-1 sm:grid-cols-2 gap-x-4">
            <FormItem
              control={control}
              name="allow_submission"
              label="Pengumpulan tugas"
              disabled={deleteMode}
            >
              <Radio.Group
                options={[
                  { value: true, label: "Terima" },
                  { value: false, label: "Tidak Perlu" },
                ]}
              />
            </FormItem>
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-x-4">
            <FormItem
              control={control}
              name="allow_overdue_submission"
              label="Pengumpulan tugas yang tarlambat"
              hidden={!watch("allow_submission")}
              disabled={deleteMode}
            >
              <Radio.Group
                options={[
                  { value: true, label: "Terima" },
                  { value: false, label: "Jangan Terima" },
                ]}
              />
            </FormItem>
            <FormItem
              control={control}
              name="deadline_dayjs"
              label="Deadline"
              hidden={!watch("allow_submission")}
              disabled={deleteMode}
            >
              <DatePicker showTime format="DD/MM/YYYY HH:mm" />
            </FormItem>
          </div>
          {selectTeachingPlan && (
            <FormItem
              control={control}
              name="teaching_plan_id"
              label="Rencana Pembelajaran"
            >
              <Select
                placeholder="Pilih rencana pembelajaran"
                options={teachingPlans.map(({ id, title }) => ({
                  label: title,
                  value: id,
                }))}
                showSearch
                disabled={deleteMode}
              />
            </FormItem>
          )}
          <div className="pb-4">
            <div className="pb-2">Lampiran</div>
            <UploadForm
              buttonText="Tambah lampiran"
              control={control}
              name="attachment_file_uri"
              setValue={setValue}
              clearErrors={clearErrors}
              disabled={deleteMode}
            />
          </div>

          <div className="flex justify-end w-full gap-3">
            <Button
              onClick={() => {
                reset();
                toggle();
              }}
            >
              Batal
            </Button>
            <Button
              htmlType="submit"
              variant="solid"
              color={deleteMode ? "danger" : "primary"}
              disabled={aiGenerating}
            >
              {deleteMode ? "Hapus" : "Simpan"}
            </Button>
          </div>
        </Form>
      </Modal>

      {/* AI Generate Modal */}
      <Modal
        title={
          <div className="flex items-center gap-2">
            <Stars02 width={20} />
            Buat Tugas dengan AI
          </div>
        }
        open={aiGenerateModalOpen}
        onOk={handleAIGenerate}
        onCancel={() => {
          setAiGenerateModalClose();
          setAiGenerateTitle("");
          setAiGenerateSpecifications("");
        }}
        okText="Buat"
        cancelText="Batal"
        confirmLoading={aiGenerating}
        width={600}
      >
        <div className="flex flex-col gap-4">
          <div>
            <div className="mb-2 font-medium">Judul Tugas:</div>
            <Input
              value={aiGenerateTitle}
              onChange={(e) => setAiGenerateTitle(e.target.value)}
              placeholder="Masukan judul tugas..."
            />
          </div>
          <div>
            <div className="mb-2 font-medium">
              Spesifikasi tambahan (opsional):
            </div>
            <Input.TextArea
              value={aiGenerateSpecifications}
              onChange={(e) => setAiGenerateSpecifications(e.target.value)}
              placeholder="Masukan spesifikasi khusus untuk tugas ini, misalnya: 'Fokus pada praktik langsung', 'Sesuaikan untuk siswa SMA', 'Tambahkan elemen kreatif', dll."
              rows={4}
            />
          </div>
          <div className="text-sm text-gray-600">
            AI akan membuat tugas berdasarkan judul dan spesifikasi yang Anda berikan.
          </div>
        </div>
      </Modal>

      {/* AI Edit Modal */}
      <Modal
        title={
          <div className="flex items-center gap-2">
            <Stars02 width={20} />
            Edit Tugas dengan AI
          </div>
        }
        open={aiEditModalOpen}
        onOk={handleAIEdit}
        onCancel={() => {
          setAiEditModalClose();
          setAiEditInstructions("");
        }}
        okText="Edit"
        cancelText="Batal"
        confirmLoading={aiEditing}
        width={600}
      >
        <div className="flex flex-col gap-4">
          <div>
            <div className="mb-2 font-medium">Instruksi edit:</div>
            <Input.TextArea
              value={aiEditInstructions}
              onChange={(e) => setAiEditInstructions(e.target.value)}
              placeholder="Masukan instruksi untuk mengedit tugas ini, misalnya: 'Buat lebih detail', 'Sederhanakan bahasa', 'Tambahkan contoh praktis', dll."
              rows={4}
            />
          </div>
          <div className="text-sm text-gray-600">
            AI akan mengedit tugas sesuai dengan instruksi yang Anda berikan.
          </div>
        </div>
      </Modal>
    </>
  );
}
