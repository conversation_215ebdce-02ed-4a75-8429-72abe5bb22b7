import { useGetFileUrl } from "@sisva/hooks/utils";
import { useCurrentUser } from "@sisva/providers";
import type { TeachingMaterial } from "@sisva/types/apiTypes";
import {
  DotsVertical,
  Download01,
  Edit01,
  File06,
  Trash01,
} from "@untitled-ui/icons-react";
import { Card, Divider, Dropdown, theme } from "antd";

import { CreateTeachingMaterialButton } from "./CreateTeachingMaterialButton";

export default function TeachingMaterialCard({
  material,
}: {
  material: TeachingMaterial;
}) {
  const token = theme.useToken().token;
  const getFileUrl = useGetFileUrl();
  const currentUser = useCurrentUser();

  const dropdownItems = [
    {
      label: (
        <a href={getFileUrl(material.attachment_file_uri)} download>
          <div className="flex items-center gap-2 font-kumbh">
            <Download01 width={20} height={20} /> Download
          </div>
        </a>
      ),
      key: "download",
    },
    {
      label: (
        <CreateTeachingMaterialButton
          teaching_material_id={material.id}
          renderTrigger={(onClick) => (
            <div className="flex items-center gap-2" onClick={onClick}>
              <Edit01 width={20} height={20} /> Edit
            </div>
          )}
        />
      ),
      key: "edit",
    },
    {
      label: (
        <CreateTeachingMaterialButton
          teaching_material_id={material.id}
          deleteMode
          renderTrigger={(onClick) => (
            <div className="flex items-center gap-2" onClick={onClick}>
              <Trash01 width={20} height={20} /> Hapus
            </div>
          )}
        />
      ),
      key: "delete",
    },
  ] as const;

  const filteredDropdownItems = dropdownItems.filter((item) => {
    let pass = true;
    if (!material.attachment_file_uri && item.key === "download") pass = false;
    if (currentUser?.type === "student") {
      if (item.key === "edit") pass = false;
      if (item.key === "delete") pass = false;
    }
    return pass;
  });

  return (
    <Card
      cover={
        <div
          className="h-20"
          style={{
            backgroundColor: token.colorPrimary,
          }}
        />
      }
      className="rounded-xl shadow-sm bg-white text-neutral-800 font-kumbh"
    >
      {filteredDropdownItems.length > 0 && (
        <Dropdown menu={{ items: filteredDropdownItems }} trigger={["click"]}>
          <div className="cursor-pointer absolute top-2 right-2 flex items-center justify-center w-7 h-7 rounded-full bg-white">
            <DotsVertical width={20} height={20} className="text-neutral-600" />
          </div>
        </Dropdown>
      )}
      <div className=" flex flex-col gap-4">
        <div
          className="rounded-full px-2 py-1 w-fit"
          style={{
            backgroundColor: token.colorPrimary,
          }}
        >
          <span className="text-white text-sm font-semibold line-clamp-1">
            {material.subject_name}
          </span>
        </div>
        <div className="flex gap-2 items-center">
          <File06 color={token.colorPrimary} />
          <span className="text-base font-medium uppercase">
            {material.description}
          </span>
        </div>
        <div className="flex items-center justify-between gap-16">
          <div className="flex flex-col gap-2">
            <span className="text-sm font-semibold">Program Studi</span>
            <span className="text-sm text-neutral-500">
              {material.study_program_name}
            </span>
          </div>
          <div className="flex flex-col gap-2">
            <span className="text-sm font-semibold">Tingkatan</span>
            <span className="text-sm text-neutral-500">{material.grade}</span>
          </div>
        </div>
        <Divider
          className="my-2 h-[2px] rounded-full opacity-50"
          style={{ backgroundColor: token.colorPrimary }}
        />
        <div className="flex items-center gap-2">
          <div
            className="w-5 h-5 rounded-full opacity-30"
            style={{ backgroundColor: token.colorPrimary }}
          />
          <span className="text-sm text-neutral-600">
            {material.curriculum_name}
          </span>
        </div>
      </div>
    </Card>
  );
}
