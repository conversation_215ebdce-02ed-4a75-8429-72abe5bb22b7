import { useExamWithClasses } from "@sisva/hooks/query/exam/useExams";
import { useCurrentUser } from "@sisva/providers";
import { createRedirectUrl } from "@sisva/utils";
import { Button, Tag } from "antd";
import dayjs from "dayjs";

import { env } from "#/env";

export default function ExamCard({ exam_id }: { exam_id: number | undefined }) {
  const { data: exam } = useExamWithClasses({ exam_id });
  const currentUser = useCurrentUser();

  return (
    <div
      key={exam?.id}
      className="p-4 rounded-md flex flex-col gap-1 relative overflow-hidden shadow-lg"
    >
      <div className="text-xl font-semibold">{exam?.name}</div>
      <div className="py-2 text-neutral-600 list-inside">
        {exam?.description}
      </div>
      <div className="flex flex-col gap-4 flex-1 justify-end">
        <div className="flex justify-end pt-1 items-end">
          <div className="flex flex-col gap-1 items-end">
            <div>Tanggal</div>
            <div className="text-neutral-600">
              {exam &&
                dayjs(exam.start_time, "DD/MM/YYYY hh:mm A Z").format(
                  "dddd DD/MM/YYYY hh:mm A"
                )}
            </div>
          </div>
        </div>
        <div className="flex justify-between gap-2 items-end">
          <div>
            {(() => {
              if (!exam) return;
              const startTime = dayjs(exam.start_time, "DD/MM/YYYY h:mm A Z");
              const endTime = dayjs(exam.end_time, "DD/MM/YYYY h:mm A Z");
              if (startTime.isAfter(dayjs())) return <Tag>Belum dimulai</Tag>;
              if (startTime.isBefore(dayjs()) && endTime.isAfter(dayjs()))
                return <Tag color="warning">Sedang berlangsung</Tag>;
              if (endTime.isBefore(dayjs()))
                return <Tag color="success">Selesai</Tag>;
            })()}
          </div>
          <div className="flex gap-2">
            <a
              href={
                exam_id
                  ? createRedirectUrl({
                      payload: {
                        to:
                          currentUser.type === "student"
                            ? "examSession"
                            : "viewExam",
                        exam_id: exam_id,
                      },
                      isDev: env.DEV,
                    })
                  : undefined
              }
              target="_blank"
              rel="noreferrer"
            >
              <Button variant="solid" color="primary">
                Detail
              </Button>
            </a>
          </div>
        </div>
      </div>
    </div>
  );
}
