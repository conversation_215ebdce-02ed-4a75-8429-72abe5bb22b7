import { useSubmissions } from "@sisva/hooks/query/classroom/useSubmissions";
import type {
  StudentsTask,
  TeachersTask,
} from "@sisva/hooks/query/classroom/useTasks";
import { useCurrentUser } from "@sisva/providers";
import { isDateEmpty } from "@sisva/utils";
import { Link } from "@tanstack/react-router";
import { Button, Tag } from "antd";
import dayjs from "dayjs";

import { CreateTaskButton } from "./CreateTaskButton";

export default function TaskCard({
  task,
}: {
  task: TeachersTask | StudentsTask;
}) {
  const currentUser = useCurrentUser();

  const { data: submissions = [] } = useSubmissions({ task_id: task.id });
  const submitted = (() => {
    const submission = submissions.find(
      (submission) => submission.student_id === currentUser.id
    );
    if (submission) return true;
    return false;
  })();

  return (
    <div
      key={task.id}
      className="p-4 rounded-md flex flex-col gap-1 relative overflow-hidden shadow-lg"
    >
      <div className="text-xl font-semibold">{task.name}</div>
      {currentUser.type === "student" && <div>{task?.class?.teacher_name}</div>}
      <div className="py-2 text-neutral-600 list-inside">
        {task.description}
      </div>
      <div className="flex flex-col gap-4 flex-1 justify-end">
        <div className="flex justify-end pt-1 items-end">
          <div className="flex flex-col gap-1 items-end">
            <div>Deadline</div>
            <div className="text-neutral-600">
              {(() => {
                const deadlineDayjs = dayjs(
                  task.deadline,
                  "DD/MM/YYYY hh:mm A Z"
                );
                return isDateEmpty(deadlineDayjs)
                  ? "Tidak ada"
                  : deadlineDayjs.format("dddd DD/MM/YYYY hh:mm A");
              })()}
            </div>
          </div>
        </div>
        <div className="flex justify-between gap-2 items-end">
          {(() => {
            if (currentUser.type === "student" && task.allow_submission)
              return (
                <Tag color={submitted ? "success" : "error"}>
                  {submitted ? "Sudah Mengumpulkan" : "Belum Mengumpulkan"}
                </Tag>
              );
            return <div />;
          })()}
          <div className="flex gap-2">
            {currentUser.type !== "student" && (
              <>
                <CreateTaskButton
                  task_id={task.id}
                  renderTrigger={(onClick) => {
                    return (
                      <Button
                        onClick={onClick}
                        color="danger"
                        variant="outlined"
                      >
                        Hapus
                      </Button>
                    );
                  }}
                  deleteMode
                />
                <CreateTaskButton
                  task_id={task.id}
                  renderTrigger={(onClick) => {
                    return <Button onClick={onClick}>Edit</Button>;
                  }}
                />
              </>
            )}
            <Link
              to="/classes/$class_id/tasks/$task_id"
              params={{
                class_id: task.class?.id ?? 0,
                task_id: task.id,
              }}
            >
              <Button variant="solid" color="primary">
                Detail
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
