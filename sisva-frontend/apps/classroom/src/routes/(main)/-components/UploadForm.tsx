import { ErrorMessage } from "@hookform/error-message";
import { useUploadFile } from "@sisva/hooks/query/file/useFiles";
import { useGetFileUrl } from "@sisva/hooks/utils";
import { File05, Trash01 } from "@untitled-ui/icons-react";
import { Button, theme, Upload } from "antd";
import { useState } from "react";
import {
  type Control,
  type FieldPath,
  type FieldValues,
  type UseFormClearErrors,
  type UseFormSetValue,
  useFormState,
  useWatch,
} from "react-hook-form";

export default function UploadForm<T extends FieldValues = FieldValues>({
  control,
  disabled,
  name,
  setValue,
  clearErrors,
  buttonText = "Tambah file",
}: {
  control: Control<T>;
  disabled?: boolean;
  name: FieldPath<T>;
  setValue: UseFormSetValue<T>;
  clearErrors: UseFormClearErrors<T>;
  buttonText?: string;
}) {
  const [fileName, setFileName] = useState("");
  const { mutateAsync: uploadFile } = useUploadFile();
  const getFileUrl = useGetFileUrl();
  const value = useWatch({ control, name });
  const { errors } = useFormState({ control });
  const token = theme.useToken().token;

  return (
    <>
      <Upload
        beforeUpload={async (file) => {
          const formData = new FormData();
          formData.append("file", file);
          const uri = await uploadFile(formData);
          setValue(name, uri);
          setFileName(file.name);
          clearErrors(name);
          return false;
        }}
        multiple={false}
        maxCount={1}
        type="drag"
        accept="image/*, .pdf, .doc, .docx"
        showUploadList={false}
        disabled={disabled}
      >
        <div className="flex flex-col gap-2 items-center">
          <div className="flex items-center gap-2 justify-center">
            <File05 width={16} height={16} />
            <span className="text-gray-500 text-xs">Upload file di sini</span>
          </div>

          <Button color="primary" variant="outlined" disabled={disabled}>
            {buttonText}
          </Button>

          <div className="text-xs text-gray-500">
            Menerima JPG, PNG, PDF. Ukuran Max: 5MB
          </div>
        </div>
      </Upload>
      {value && (
        <div className="p-2 rounded-lg mt-2 flex items-center justify-between border-neutral-200 border border-solid">
          <div className="flex items-center gap-2">
            <File05 width={20} height={20} />
            <a
              href={getFileUrl(value)}
              download
              target="_blank"
              rel="noreferrer"
            >
              <span className="text-sm text-neutral-500 font-semibold hover:text-neutral-600 transition-colors">
                {fileName ? fileName : value}
              </span>
            </a>
          </div>
          {!disabled && (
            <Trash01
              width={20}
              height={20}
              onClick={() => {
                //@ts-expect-error idk
                setValue(name, "");
                setFileName("");
              }}
              color={token.colorPrimary}
              className="cursor-pointer"
            />
          )}
        </div>
      )}
      <ErrorMessage
        errors={errors}
        //@ts-expect-error idk
        name={name}
        render={({ message }) => (
          <div
            style={{
              color: token.colorErrorText,
            }}
          >
            {message}
          </div>
        )}
      />
    </>
  );
}
