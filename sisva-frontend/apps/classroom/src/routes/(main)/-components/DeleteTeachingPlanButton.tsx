import { valibotResolver } from "@hookform/resolvers/valibot";
import {
  useDeleteTeachingPlan,
  useTeachingPlan,
} from "@sisva/hooks/query/classroom/useTeachingPlans";
import { useNotificationAPI } from "@sisva/providers";
import type { ReactNode } from "@tanstack/react-router";
import { useBoolean } from "ahooks";
import { Button, Form, Modal } from "antd";
import { useForm } from "react-hook-form";
import { number, object } from "valibot";

export function DeleteTeachingPlanButton({
  teaching_plan_id,
  renderTrigger,
}: {
  teaching_plan_id: number;
  renderTrigger?: (onClick: () => void) => ReactNode;
}) {
  const [open, { toggle }] = useBoolean();
  const notification = useNotificationAPI();

  const { data: teachingPlan } = useTeachingPlan(teaching_plan_id);

  const { control, handleSubmit, setValue, reset, watch, clearErrors } =
    useForm({
      values: {
        teaching_plan_id,
      },
      resolver: valibotResolver(
        object({
          teaching_plan_id: number(),
        })
      ),
    });

  const { mutate: deleteTeachingPlan } = useDeleteTeachingPlan({
    onSuccess() {
      notification.success({
        message: "Rencana pembelajaran berhasil dihapus",
      });
      toggle();
    },
    onError() {
      notification.error({ message: "Rencana pembelajaran gagal dihapus" });
    },
  });

  return (
    <>
      {renderTrigger ? (
        renderTrigger(toggle)
      ) : (
        <Button onClick={toggle} variant="solid" color="danger">
          Hapus
        </Button>
      )}
      <Modal
        title="Hapus Rencana Pembelajaran"
        open={open}
        onCancel={() => {
          reset();
          toggle();
        }}
        footer={null}
      >
        <Form
          onFinish={handleSubmit((values) => {
            return deleteTeachingPlan(teaching_plan_id);
          })}
          layout="vertical"
          requiredMark={false}
        >
          <div className="flex pb-4">
            <div>
              Anda akan menghapus rencana pembelajaran dengan judul{" "}
              <span className="font-semibold ">{teachingPlan?.title}</span>.
              Apakah anda yakin?
            </div>
          </div>
          <div className="flex justify-end w-full gap-3">
            <Button
              onClick={() => {
                reset();
                toggle();
              }}
            >
              Batal
            </Button>
            <Button htmlType="submit" variant="solid" color="danger">
              Hapus
            </Button>
          </div>
        </Form>
      </Modal>
    </>
  );
}
