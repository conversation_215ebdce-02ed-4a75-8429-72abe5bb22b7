import { jsPDF } from "jspdf";
/**
 * Converts markdown text to a PDF file and returns it as a Blob
 * @param title The title of the PDF
 * @param markdownContent The markdown content to convert
 * @returns A Blob containing the PDF file
 */
export async function markdownToPdf(
  title: string,
  markdownContent: string
): Promise<Blob> {
  // Create a new jsPDF instance
  const doc = new jsPDF({
    orientation: "portrait",
    unit: "mm",
    format: "a4",
  });

  // Add title to the PDF
  doc.setFontSize(18);
  doc.text(title, 14, 20);
  doc.setFontSize(12);

  // Process the content
  let y = 30; // Starting y position after the title
  const pageWidth = doc.internal.pageSize.getWidth();
  const margin = 14;
  const textWidth = pageWidth - 2 * margin;

  // Split the markdown content into lines
  const lines = markdownContent.split("\n");

  // Process each line
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i]?.trim() || "";

    // Skip empty lines
    if (!line) {
      y += 5;
      continue;
    }

    // Check if it's a heading (starts with #)
    if (line.startsWith("#")) {
      const level = line.match(/^#+/)?.[0].length || 1;
      const headingText = line.replace(/^#+\s*/, "");

      // Set font size based on heading level
      const fontSize = 20 - level; // h1 = 19, h2 = 18, etc.
      doc.setFontSize(fontSize);

      // Check if we need a new page
      if (y + 10 > doc.internal.pageSize.getHeight() - margin) {
        doc.addPage();
        y = margin;
      }

      // Add the heading
      doc.text(headingText, margin, y);
      y += 10;

      // Reset font size
      doc.setFontSize(12);
      continue;
    }

    // Check if it's a list item
    if (line.match(/^[*\-+]\s/) || line.match(/^\d+\.\s/)) {
      const listText = line
        .replace(/^[*\-+]\s/, "• ")
        .replace(/^\d+\.\s/, (match) => match);

      // Split the text to fit the page width
      const textLines = doc.splitTextToSize(listText, textWidth);

      // Check if we need a new page
      if (
        y + textLines.length * 7 >
        doc.internal.pageSize.getHeight() - margin
      ) {
        doc.addPage();
        y = margin;
      }

      // Add the list item
      doc.text(textLines, margin, y);
      y += textLines.length * 7;
      continue;
    }

    // Regular paragraph
    const textLines = doc.splitTextToSize(line, textWidth);

    // Check if we need a new page
    if (y + textLines.length * 7 > doc.internal.pageSize.getHeight() - margin) {
      doc.addPage();
      y = margin;
    }

    // Add the paragraph
    doc.text(textLines, margin, y);
    y += textLines.length * 7 + 3;
  }

  // Return the PDF as a blob
  return doc.output("blob");
}
