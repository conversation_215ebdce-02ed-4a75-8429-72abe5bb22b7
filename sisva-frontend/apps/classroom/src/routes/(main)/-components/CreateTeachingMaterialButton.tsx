import { valibotResolver } from "@hookform/resolvers/valibot";
import {
  useCurriculum,
  useCurriculums,
} from "@sisva/hooks/query/academic/useCurriculums";
import { useStudentGroupByClassId } from "@sisva/hooks/query/academic/useStudentGroups";
import {
  useStudyProgram,
  useStudyPrograms,
} from "@sisva/hooks/query/academic/useStudyPrograms";
import {
  useSubject,
  useSubjectByClassId,
  useSubjects,
} from "@sisva/hooks/query/academic/useSubjects";
import { useGenerateTeachingMaterials } from "@sisva/hooks/query/classroom/useAi"; // Added AI hook
import {
  useCreateTeachingMaterials,
  useDeleteTeachingMaterials,
  useTeachingMaterial,
  useUpdateTeachingMaterials,
} from "@sisva/hooks/query/classroom/useTeachingMaterials";
import {
  useTeachingPlan,
  useTeachingPlansByClassId,
  useUpdateTeachingPlan,
} from "@sisva/hooks/query/classroom/useTeachingPlans";
import { useUploadFile } from "@sisva/hooks/query/file/useFiles";
import { useNotificationAPI } from "@sisva/providers";
import type { ReactNode } from "@tanstack/react-router";
import { Plus, Stars01 } from "@untitled-ui/icons-react"; // Changed Stars to Stars01
import { useBoolean } from "ahooks";
import { Button, Form, Input, Modal, Select } from "antd";
import { useForm } from "react-hook-form";
import { FormItem } from "react-hook-form-antd";
import { nonEmpty, number, object, optional, pipe, string } from "valibot";

import { markdownToPdf } from "./markdownToPdf";
import UploadForm from "./UploadForm";

// this component has 3 mode: create, edit, delete
export function CreateTeachingMaterialButton({
  teaching_material_id,
  class_id,
  renderTrigger,
  deleteMode,
  onCreateSuccess,
  selectTeachingPlan,
}: {
  teaching_material_id?: number;
  class_id?: number;
  renderTrigger?: (onClick: () => void) => ReactNode;
  deleteMode?: boolean;
  onCreateSuccess?: (teaching_material_id: number) => void;
  selectTeachingPlan?: boolean;
}) {
  const [open, { toggle }] = useBoolean();
  const notification = useNotificationAPI();

  const { data: teachingMaterial } = useTeachingMaterial(teaching_material_id);
  const { data: subjectFromClassIdProps } = useSubjectByClassId(class_id);
  const { data: studentGroupFromClassIdProps } =
    useStudentGroupByClassId(class_id);
  const { data: teachingPlans = [] } = useTeachingPlansByClassId(class_id);
  // Mendapatkan fungsi uploadFile dari hook useUploadFile
  const { mutateAsync: uploadFile } = useUploadFile(); // Menggunakan mutateAsync untuk langsung mendapatkan promise
  const { control, handleSubmit, setValue, reset, watch, clearErrors } =
    useForm({
      values: {
        description: teachingMaterial?.description ?? "",
        attachment_file_uri: teachingMaterial?.attachment_file_uri ?? "",
        //@ts-expect-error allow undefined
        subject_id: teachingMaterial?.subject_id ?? subjectFromClassIdProps?.id,
        //@ts-expect-error allow undefined
        grade: teachingMaterial?.grade ?? studentGroupFromClassIdProps?.grade,
        //@ts-expect-error allow undefined
        curriculum_id:
          teachingMaterial?.curriculum_id ??
          subjectFromClassIdProps?.curriculum_id,
        //@ts-expect-error allow undefined
        study_program_id:
          teachingMaterial?.study_program_id ??
          subjectFromClassIdProps?.study_program_id,
      },
      resolver: valibotResolver(
        object({
          description: pipe(string(), nonEmpty("Judul harus diisi")),
          attachment_file_uri: pipe(
            string(),
            nonEmpty("File bahan ajar harus ada")
          ),
          subject_id: pipe(number("Mata pelajaran wajib dipilih")),
          grade: pipe(
            string("Tingkatan wajib dipilih"),
            nonEmpty("Tingkatan wajib dipilih")
          ),
          curriculum_id: pipe(number("Kurikulum wajib dipilih")),
          study_program_id: pipe(number("Program Studi wajib dipilih")),
          teaching_plan_id: selectTeachingPlan
            ? number("Rencana Pembelajaran wajib dilipih")
            : optional(number()),
        })
      ),
    });

  const { data: curriculums = [] } = useCurriculums();
  const { data: curriculum } = useCurriculum(watch("curriculum_id"));
  const { data: studyPrograms = [] } = useStudyPrograms();
  const filteredStudyPrograms = studyPrograms.filter((item) =>
    curriculum?.study_programs?.some(
      (studyProgram) => studyProgram.id === item.id
    )
  );
  const { data: subjects = [] } = useSubjects();
  const { data: subject } = useSubject(watch("subject_id"));
  const { data: studyProgram } = useStudyProgram(subject?.study_program_id);
  const grades = studyProgram?.grades ?? [];
  const filteredSubjects = subjects.filter(
    (subject) =>
      subject.curriculum_id === watch("curriculum_id") &&
      subject.study_program_id === watch("study_program_id")
  );

  const teaching_plan_id = watch("teaching_plan_id");
  const { data: teachingPlan } = useTeachingPlan(teaching_plan_id);

  const { mutate: createTeachingMaterial } = useCreateTeachingMaterials();

  const { mutate: updateTeachingMaterial } = useUpdateTeachingMaterials({
    onSuccess() {
      notification.success({ message: "Bahan ajar berhasil perbarui" });
      toggle();
      reset();
    },
    onError() {
      notification.error({ message: "Bahan ajar gagal perbarui" });
    },
  });

  const { mutate: deleteTeachingMaterial } = useDeleteTeachingMaterials({
    onSuccess() {
      notification.success({ message: "Bahan ajar berhasil dihapus" });
      toggle();
    },
    onError() {
      notification.error({ message: "Bahan ajar gagal dihapus" });
    },
  });

  const { mutate: updateTeachingPlan } = useUpdateTeachingPlan();

  // Experimental JsPDF
  const {
    mutate: generateAIMaterial,
    isPending: isGeneratingAIMaterial,
  } = useGenerateTeachingMaterials(); // Changed isLoading to isPending

  const watchedDescription = watch("description");
  const watchedSubjectId = watch("subject_id");
  const watchedAttachmentUri = watch("attachment_file_uri");

  const handleAiGenerate = () => {
    const selectedSubjectObj = subjects.find((s) => s.id === watchedSubjectId);
    const subjectName = selectedSubjectObj?.name;

    if (!watchedDescription || !subjectName) {
      notification.error({
        message:
          "Judul dan Mata Pelajaran harus diisi untuk generate dengan AI.",
      });
      return;
    }

    generateAIMaterial(
      {
        title: watchedDescription,
        subject: subjectName,
      },
      {
        onSuccess: async (data) => {
          try {
            const pdfBlob = await markdownToPdf(data.title, data.content);

            // 1. Buat objek File dari Blob
            const file = new File([pdfBlob], `${data.title}.pdf`, {
              type: "application/pdf",
            });

            // 2. Buat FormData dan masukkan file
            const formData = new FormData();
            formData.append("file", file);

            // 3. Unggah file ke server dan dapatkan URI-nya
            const fileUri = await uploadFile(formData); // Panggil fungsi uploadFile

            // 4. Simpan URI file yang telah diunggah ke dalam form
            setValue("attachment_file_uri", fileUri, {
              // Simpan URI, bukan data Base64
              shouldValidate: true,
              shouldDirty: true,
            });

            // Update description only if it was the default placeholder or empty
            if (
              watchedDescription === "" ||
              watchedDescription.startsWith("Bahan Ajar untuk")
            ) {
              setValue("description", data.title, {
                shouldValidate: true,
                shouldDirty: true,
              });
            }

            notification.success({
              message: "Bahan ajar berhasil digenerate oleh AI.",
            });
          } catch (error) {
            console.error("PDF Generation or Upload Error:", error);
            notification.error({
              message: "Gagal mengenerate bahan ajar dengan AI.",
            });
          }
        },
        onError: (error) => {
          console.error("AI Generation Error:", error);
          notification.error({
            message: "Gagal mengenerate bahan ajar dengan AI.",
          });
        },
      }
    );
  };

  return (
    <>
      {renderTrigger ? (
        renderTrigger(toggle)
      ) : (
        <Button
          onClick={toggle}
          icon={<Plus />}
          variant="solid"
          color="primary"
        >
          Tambah Bahan Ajar
        </Button>
      )}
      <Modal
        title={(() => {
          if (teaching_material_id) {
            if (deleteMode) return "Hapus Bahan Ajar";
            return "Edit Bahan Ajar";
          }
          return "Tambah Bahan Ajar";
        })()}
        open={open}
        onCancel={() => {
          reset();
          toggle();
        }}
        footer={null}
        width={800}
      >
        <Form
          onFinish={handleSubmit((values) => {
            if (teaching_material_id) {
              if (deleteMode) {
                return deleteTeachingMaterial(teaching_material_id);
              }
              return updateTeachingMaterial({
                ...values,
                teaching_material_id,
              });
            }
            createTeachingMaterial(values, {
              onSuccess({ teaching_material_id }) {
                notification.success({ message: "Bahan ajar berhasil dibuat" });
                toggle();
                reset();
                onCreateSuccess?.(teaching_material_id);

                if (teachingPlan) {
                  updateTeachingPlan(
                    {
                      ...teachingPlan,
                      tasks: teachingPlan.tasks ?? [],
                      teaching_materials: [
                        ...(teachingPlan.teaching_materials ?? []),
                        { id: teaching_material_id },
                      ],

                      teaching_plan_id: teachingPlan.id,
                    },
                    {
                      onSuccess() {
                        notification.success({
                          message: "Rencana Pembelajaran berhasil perbarui",
                        });
                      },
                      onError() {
                        notification.error({
                          message: "Rencana Pembelajaran gagal perbarui",
                        });
                      },
                    }
                  );
                }
              },
              onError() {
                notification.error({ message: "Bahan ajar gagal dibuat" });
              },
            });
          })}
          layout="vertical"
          requiredMark={false}
        >
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-x-4">
            <FormItem control={control} name="curriculum_id" label="Kurikulum">
              <Select
                placeholder="Pilih kurikulum"
                options={curriculums.map(({ id, name }) => ({
                  label: name,
                  value: id,
                }))}
                disabled={deleteMode}
                open={class_id ? false : undefined}
              />
            </FormItem>
            <FormItem
              control={control}
              name="study_program_id"
              label="Program Studi"
            >
              <Select
                placeholder="Pilih program studi"
                options={filteredStudyPrograms.map(({ id, name }) => ({
                  label: name,
                  value: id,
                }))}
                disabled={!watch("curriculum_id") || deleteMode}
                open={class_id ? false : undefined}
              />
            </FormItem>
            <FormItem
              control={control}
              name="subject_id"
              label="Mata Pelajaran"
            >
              <Select
                placeholder="Pilih mata pelajaran"
                options={filteredSubjects.map(({ id, name }) => ({
                  label: name,
                  value: id,
                }))}
                disabled={
                  !watch("curriculum_id") ||
                  !watch("study_program_id") ||
                  deleteMode
                }
                open={class_id ? false : undefined}
              />
            </FormItem>
            <FormItem control={control} name="grade" label="Tingkatan">
              <Select
                placeholder="Pilih tingkatan"
                options={grades.map((grade) => ({
                  label: grade,
                  value: grade,
                }))}
                disabled={!watch("subject_id") || deleteMode}
                open={class_id ? false : undefined}
              />
            </FormItem>
          </div>
          <FormItem control={control} name="description" label="Judul">
            <Input placeholder="Masukan judul" disabled={deleteMode} />
          </FormItem>
          {selectTeachingPlan && (
            <FormItem
              control={control}
              name="teaching_plan_id"
              label="Rencana Pembelajaran"
            >
              <Select
                placeholder="Pilih rencana pembelajaran"
                options={teachingPlans.map(({ id, title }) => ({
                  label: title,
                  value: id,
                }))}
                showSearch
                disabled={deleteMode}
              />
            </FormItem>
          )}
          <div className="pb-4">
            <div className="pb-2">File</div>
            <div className="flex items-start gap-2">
              <UploadForm
                buttonText="Tambah file bahan ajar"
                control={control}
                name="attachment_file_uri"
                setValue={setValue}
                clearErrors={clearErrors}
                disabled={deleteMode || isGeneratingAIMaterial}
              />
                {/* //EXPERIMENTAL
                {!deleteMode && (
                  <Button
                    onClick={handleAiGenerate}
                    loading={isGeneratingAIMaterial}
                    disabled={
                      isGeneratingAIMaterial ||
                      !watchedDescription ||
                      !watchedSubjectId
                    }
                    icon={<Stars01 />} // Changed Stars to Stars01
                  >
                    Generate dengan AI
                  </Button>
                )}
                */}

            </div>
            {watchedAttachmentUri?.startsWith("data:application/pdf") && (
              <div className="text-sm text-gray-500 mt-1 italic">
                PDF hasil generate AI telah dilampirkan. Anda dapat menggantinya
                dengan mengunggah file lain atau menyimpannya langsung.
              </div>
            )}
          </div>

          <div className="flex justify-end w-full gap-3">
            <Button
              onClick={() => {
                reset();
                toggle();
              }}
            >
              Batal
            </Button>
            <Button
              htmlType="submit"
              variant="solid"
              color={deleteMode ? "danger" : "primary"}
              loading={isGeneratingAIMaterial}
              disabled={isGeneratingAIMaterial}
            >
              {deleteMode ? "Hapus" : "Simpan"}
            </Button>
          </div>
        </Form>
      </Modal>
    </>
  );
}
