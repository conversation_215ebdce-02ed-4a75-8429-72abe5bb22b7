import { currentUserQueryOptions } from "@sisva/hooks/query/user/useUsers";
import { currentSchoolQueryOptions } from "@sisva/hooks/query/useSchools";
import {
  CurrentUserProvider,
  MUIThemeProvider,
  SchoolProvider,
  type SchoolWithMoreData,
} from "@sisva/providers";
import type { School, User } from "@sisva/types/apiTypes";
import {
  deleteSessionDataFromCookies,
  getFileUrl,
  themeConfigMUI,
} from "@sisva/utils";
import { createFileRoute, Outlet, redirect } from "@tanstack/react-router";
import { ConfigProvider } from "antd";
import idID from "antd/locale/id_ID";

import NavBar from "./-components/NavBar";

export const Route = createFileRoute("/(main)/_layout")({
  component: Layout,
  async beforeLoad({ context: { queryClient } }) {
    let currentUser: User;
    let currentSchool: School;
    try {
      currentUser = await queryClient.fetchQuery(currentUserQueryOptions);
      currentSchool = await queryClient.fetchQuery(currentSchoolQueryOptions);
    } catch {
      throw redirect({ to: "/signin" });
    }

    if (currentUser.type !== "student" && currentUser.type !== "teacher") {
      deleteSessionDataFromCookies();
      throw redirect({ to: "/signin" });
    }

    return {
      currentUser,
      currentSchool,
    };
  },
  loader({ context: { currentSchool, currentUser } }) {
    return {
      currentUser,
      currentSchool,
    };
  },
});

function Layout() {
  const { currentUser, currentSchool } = Route.useLoaderData();
  const school = currentSchool as SchoolWithMoreData;

  school.logo_url = getFileUrl(school.logo_uri, school.id);
  school.landing_image_url = getFileUrl(school.landing_image_uri, school.id);

  themeConfigMUI.palette.primary.main = school.theme_json_text;

  return (
    <CurrentUserProvider currentUser={currentUser}>
      <SchoolProvider school={school}>
        <MUIThemeProvider themeConfig={themeConfigMUI}>
          <ConfigProvider
            theme={{
              token: {
                // ! theme_json_text somehow is not a json
                colorPrimary: school.theme_json_text,
                fontFamily: "Kumbh Sans",
              },
            }}
            locale={idID}
          >
            <div className="flex flex-col h-svh">
              <div className="hidden sm:block">
                <NavBar />
              </div>
              <div className="grow overflow-auto flex flex-col">
                <Outlet />
              </div>
              <div className="sm:hidden">
                <NavBar />
              </div>
            </div>
          </ConfigProvider>
        </MUIThemeProvider>
      </SchoolProvider>
    </CurrentUserProvider>
  );
}
