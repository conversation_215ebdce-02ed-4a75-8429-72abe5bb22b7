/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { createFileRoute } from '@tanstack/react-router'

// Import Routes

import { Route as rootRoute } from './routes/__root'
import { Route as mainLayoutImport } from './routes/(main)/_layout'
import { Route as mainLayoutIndexImport } from './routes/(main)/_layout.index'
import { Route as authSigninIndexImport } from './routes/(auth)/signin/index'
import { Route as mainLayoutTasksIndexImport } from './routes/(main)/_layout/tasks/index'
import { Route as mainLayoutSchedulesIndexImport } from './routes/(main)/_layout/schedules/index'
import { Route as mainLayoutProfileIndexImport } from './routes/(main)/_layout/profile/index'
import { Route as mainLayoutInvoicesIndexImport } from './routes/(main)/_layout/invoices/index'
import { Route as mainLayoutAttendancesIndexImport } from './routes/(main)/_layout/attendances/index'
import { Route as mainLayoutAnnouncementsIndexImport } from './routes/(main)/_layout/announcements/index'

// Create Virtual Routes

const mainImport = createFileRoute('/(main)')()

// Create/Update Routes

const mainRoute = mainImport.update({
  id: '/(main)',
  getParentRoute: () => rootRoute,
} as any)

const mainLayoutRoute = mainLayoutImport.update({
  id: '/_layout',
  getParentRoute: () => mainRoute,
} as any)

const mainLayoutIndexRoute = mainLayoutIndexImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => mainLayoutRoute,
} as any)

const authSigninIndexRoute = authSigninIndexImport.update({
  id: '/(auth)/signin/',
  path: '/signin/',
  getParentRoute: () => rootRoute,
} as any)

const mainLayoutTasksIndexRoute = mainLayoutTasksIndexImport.update({
  id: '/tasks/',
  path: '/tasks/',
  getParentRoute: () => mainLayoutRoute,
} as any)

const mainLayoutSchedulesIndexRoute = mainLayoutSchedulesIndexImport.update({
  id: '/schedules/',
  path: '/schedules/',
  getParentRoute: () => mainLayoutRoute,
} as any)

const mainLayoutProfileIndexRoute = mainLayoutProfileIndexImport.update({
  id: '/profile/',
  path: '/profile/',
  getParentRoute: () => mainLayoutRoute,
} as any)

const mainLayoutInvoicesIndexRoute = mainLayoutInvoicesIndexImport.update({
  id: '/invoices/',
  path: '/invoices/',
  getParentRoute: () => mainLayoutRoute,
} as any)

const mainLayoutAttendancesIndexRoute = mainLayoutAttendancesIndexImport.update(
  {
    id: '/attendances/',
    path: '/attendances/',
    getParentRoute: () => mainLayoutRoute,
  } as any,
)

const mainLayoutAnnouncementsIndexRoute =
  mainLayoutAnnouncementsIndexImport.update({
    id: '/announcements/',
    path: '/announcements/',
    getParentRoute: () => mainLayoutRoute,
  } as any)

// Populate the FileRoutesByPath interface

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/(main)': {
      id: '/(main)'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof mainImport
      parentRoute: typeof rootRoute
    }
    '/(main)/_layout': {
      id: '/(main)/_layout'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof mainLayoutImport
      parentRoute: typeof mainRoute
    }
    '/(auth)/signin/': {
      id: '/(auth)/signin/'
      path: '/signin'
      fullPath: '/signin'
      preLoaderRoute: typeof authSigninIndexImport
      parentRoute: typeof rootRoute
    }
    '/(main)/_layout/': {
      id: '/(main)/_layout/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof mainLayoutIndexImport
      parentRoute: typeof mainLayoutImport
    }
    '/(main)/_layout/announcements/': {
      id: '/(main)/_layout/announcements/'
      path: '/announcements'
      fullPath: '/announcements'
      preLoaderRoute: typeof mainLayoutAnnouncementsIndexImport
      parentRoute: typeof mainLayoutImport
    }
    '/(main)/_layout/attendances/': {
      id: '/(main)/_layout/attendances/'
      path: '/attendances'
      fullPath: '/attendances'
      preLoaderRoute: typeof mainLayoutAttendancesIndexImport
      parentRoute: typeof mainLayoutImport
    }
    '/(main)/_layout/invoices/': {
      id: '/(main)/_layout/invoices/'
      path: '/invoices'
      fullPath: '/invoices'
      preLoaderRoute: typeof mainLayoutInvoicesIndexImport
      parentRoute: typeof mainLayoutImport
    }
    '/(main)/_layout/profile/': {
      id: '/(main)/_layout/profile/'
      path: '/profile'
      fullPath: '/profile'
      preLoaderRoute: typeof mainLayoutProfileIndexImport
      parentRoute: typeof mainLayoutImport
    }
    '/(main)/_layout/schedules/': {
      id: '/(main)/_layout/schedules/'
      path: '/schedules'
      fullPath: '/schedules'
      preLoaderRoute: typeof mainLayoutSchedulesIndexImport
      parentRoute: typeof mainLayoutImport
    }
    '/(main)/_layout/tasks/': {
      id: '/(main)/_layout/tasks/'
      path: '/tasks'
      fullPath: '/tasks'
      preLoaderRoute: typeof mainLayoutTasksIndexImport
      parentRoute: typeof mainLayoutImport
    }
  }
}

// Create and export the route tree

interface mainLayoutRouteChildren {
  mainLayoutIndexRoute: typeof mainLayoutIndexRoute
  mainLayoutAnnouncementsIndexRoute: typeof mainLayoutAnnouncementsIndexRoute
  mainLayoutAttendancesIndexRoute: typeof mainLayoutAttendancesIndexRoute
  mainLayoutInvoicesIndexRoute: typeof mainLayoutInvoicesIndexRoute
  mainLayoutProfileIndexRoute: typeof mainLayoutProfileIndexRoute
  mainLayoutSchedulesIndexRoute: typeof mainLayoutSchedulesIndexRoute
  mainLayoutTasksIndexRoute: typeof mainLayoutTasksIndexRoute
}

const mainLayoutRouteChildren: mainLayoutRouteChildren = {
  mainLayoutIndexRoute: mainLayoutIndexRoute,
  mainLayoutAnnouncementsIndexRoute: mainLayoutAnnouncementsIndexRoute,
  mainLayoutAttendancesIndexRoute: mainLayoutAttendancesIndexRoute,
  mainLayoutInvoicesIndexRoute: mainLayoutInvoicesIndexRoute,
  mainLayoutProfileIndexRoute: mainLayoutProfileIndexRoute,
  mainLayoutSchedulesIndexRoute: mainLayoutSchedulesIndexRoute,
  mainLayoutTasksIndexRoute: mainLayoutTasksIndexRoute,
}

const mainLayoutRouteWithChildren = mainLayoutRoute._addFileChildren(
  mainLayoutRouteChildren,
)

interface mainRouteChildren {
  mainLayoutRoute: typeof mainLayoutRouteWithChildren
}

const mainRouteChildren: mainRouteChildren = {
  mainLayoutRoute: mainLayoutRouteWithChildren,
}

const mainRouteWithChildren = mainRoute._addFileChildren(mainRouteChildren)

export interface FileRoutesByFullPath {
  '/': typeof mainLayoutIndexRoute
  '/signin': typeof authSigninIndexRoute
  '/announcements': typeof mainLayoutAnnouncementsIndexRoute
  '/attendances': typeof mainLayoutAttendancesIndexRoute
  '/invoices': typeof mainLayoutInvoicesIndexRoute
  '/profile': typeof mainLayoutProfileIndexRoute
  '/schedules': typeof mainLayoutSchedulesIndexRoute
  '/tasks': typeof mainLayoutTasksIndexRoute
}

export interface FileRoutesByTo {
  '/signin': typeof authSigninIndexRoute
  '/': typeof mainLayoutIndexRoute
  '/announcements': typeof mainLayoutAnnouncementsIndexRoute
  '/attendances': typeof mainLayoutAttendancesIndexRoute
  '/invoices': typeof mainLayoutInvoicesIndexRoute
  '/profile': typeof mainLayoutProfileIndexRoute
  '/schedules': typeof mainLayoutSchedulesIndexRoute
  '/tasks': typeof mainLayoutTasksIndexRoute
}

export interface FileRoutesById {
  __root__: typeof rootRoute
  '/(main)': typeof mainRouteWithChildren
  '/(main)/_layout': typeof mainLayoutRouteWithChildren
  '/(auth)/signin/': typeof authSigninIndexRoute
  '/(main)/_layout/': typeof mainLayoutIndexRoute
  '/(main)/_layout/announcements/': typeof mainLayoutAnnouncementsIndexRoute
  '/(main)/_layout/attendances/': typeof mainLayoutAttendancesIndexRoute
  '/(main)/_layout/invoices/': typeof mainLayoutInvoicesIndexRoute
  '/(main)/_layout/profile/': typeof mainLayoutProfileIndexRoute
  '/(main)/_layout/schedules/': typeof mainLayoutSchedulesIndexRoute
  '/(main)/_layout/tasks/': typeof mainLayoutTasksIndexRoute
}

export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/'
    | '/signin'
    | '/announcements'
    | '/attendances'
    | '/invoices'
    | '/profile'
    | '/schedules'
    | '/tasks'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/signin'
    | '/'
    | '/announcements'
    | '/attendances'
    | '/invoices'
    | '/profile'
    | '/schedules'
    | '/tasks'
  id:
    | '__root__'
    | '/(main)'
    | '/(main)/_layout'
    | '/(auth)/signin/'
    | '/(main)/_layout/'
    | '/(main)/_layout/announcements/'
    | '/(main)/_layout/attendances/'
    | '/(main)/_layout/invoices/'
    | '/(main)/_layout/profile/'
    | '/(main)/_layout/schedules/'
    | '/(main)/_layout/tasks/'
  fileRoutesById: FileRoutesById
}

export interface RootRouteChildren {
  mainRoute: typeof mainRouteWithChildren
  authSigninIndexRoute: typeof authSigninIndexRoute
}

const rootRouteChildren: RootRouteChildren = {
  mainRoute: mainRouteWithChildren,
  authSigninIndexRoute: authSigninIndexRoute,
}

export const routeTree = rootRoute
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()

/* ROUTE_MANIFEST_START
{
  "routes": {
    "__root__": {
      "filePath": "__root.tsx",
      "children": [
        "/(main)",
        "/(auth)/signin/"
      ]
    },
    "/(main)": {
      "filePath": "(main)",
      "children": [
        "/(main)/_layout"
      ]
    },
    "/(main)/_layout": {
      "filePath": "(main)/_layout.tsx",
      "parent": "/(main)",
      "children": [
        "/(main)/_layout/",
        "/(main)/_layout/announcements/",
        "/(main)/_layout/attendances/",
        "/(main)/_layout/invoices/",
        "/(main)/_layout/profile/",
        "/(main)/_layout/schedules/",
        "/(main)/_layout/tasks/"
      ]
    },
    "/(auth)/signin/": {
      "filePath": "(auth)/signin/index.tsx"
    },
    "/(main)/_layout/": {
      "filePath": "(main)/_layout.index.tsx",
      "parent": "/(main)/_layout"
    },
    "/(main)/_layout/announcements/": {
      "filePath": "(main)/_layout/announcements/index.tsx",
      "parent": "/(main)/_layout"
    },
    "/(main)/_layout/attendances/": {
      "filePath": "(main)/_layout/attendances/index.tsx",
      "parent": "/(main)/_layout"
    },
    "/(main)/_layout/invoices/": {
      "filePath": "(main)/_layout/invoices/index.tsx",
      "parent": "/(main)/_layout"
    },
    "/(main)/_layout/profile/": {
      "filePath": "(main)/_layout/profile/index.tsx",
      "parent": "/(main)/_layout"
    },
    "/(main)/_layout/schedules/": {
      "filePath": "(main)/_layout/schedules/index.tsx",
      "parent": "/(main)/_layout"
    },
    "/(main)/_layout/tasks/": {
      "filePath": "(main)/_layout/tasks/index.tsx",
      "parent": "/(main)/_layout"
    }
  }
}
ROUTE_MANIFEST_END */
