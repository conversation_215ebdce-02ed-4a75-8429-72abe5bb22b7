import { useUsersInvoicesWithBill } from "@sisva/hooks/query/finance/useInvoices";
import type { User } from "@sisva/types/apiTypes";
import type { Invoice } from "@sisva/types/apiTypes";
import type { InvoiceStatus } from "@sisva/types/types";
import { getInvoiceStatusText } from "@sisva/types/types";
import { formatToRupiah } from "@sisva/utils";
import type { TableProps } from "antd";
import { Skeleton, Table } from "antd";
import { Tag } from "antd/lib";
import { deepEqual } from "fast-equals";
import fuzzysort from "fuzzysort";
import { memo } from "react";

export default memo(function StudentsInvoice({
  student,
  search,
}: {
  student: User;
  search: string;
}) {
  const { data: invoices = [], isLoading } = useUsersInvoicesWithBill(
    student.id
  );

  const filteredInvoices = fuzzysort
    .go(search, invoices, {
      keys: ["bill.name"],
      all: !search,
    })
    .map((result) => result.obj);

  const columns: TableProps<Invoice>["columns"] = [
    {
      dataIndex: "id",
      title: "ID Invoice",
      render: (id) => <div>#{id}</div>,
    },
    {
      dataIndex: "bill",
      title: "Pembayaran",
      render: (bill) => <div>{bill?.name}</div>,
    },
    {
      dataIndex: "bill",
      title: "Total Harga",
      render: (bill) => <div>{formatToRupiah(bill?.amount)}</div>,
    },
    {
      dataIndex: "amount",
      title: "Nilai Invoice",
      render: (amount) => <div>{formatToRupiah(amount)}</div>,
    },
    {
      dataIndex: "status",
      title: "Status",
      render: (status: InvoiceStatus) => (
        <div>
          <Tag
            color={(() => {
              switch (status) {
                case "pending":
                  return "error";
                case "inreview":
                  return "processing";
                case "done":
                  return "success";
              }
            })()}
          >
            {getInvoiceStatusText(status)}
          </Tag>
        </div>
      ),
    },
  ];

  if (isLoading) return <Skeleton active />;

  return (
    <Table<Invoice>
      scroll={{ x: "100%" }}
      className="w-full"
      bordered
      dataSource={filteredInvoices}
      rowKey="id"
      columns={columns}
    />
  );
}, deepEqual);
