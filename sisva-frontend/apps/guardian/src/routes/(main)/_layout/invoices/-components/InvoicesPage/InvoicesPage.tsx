import { useGuardiansStudents } from "@sisva/hooks/query/user/useGuardians";
import { useCurrentUser } from "@sisva/providers";
import { Empty, Input, Skeleton } from "antd";
import { useDeferredValue, useState } from "react";

import GroupHeaderUser from "#/routes/(main)/-components/GroupHeaderUser";
import UserFilter from "#/routes/(main)/-components/UserFilter";

import StudentsInvoice from "./StudentsInvoice";

export default function InvoicesPage() {
  const currentUser = useCurrentUser();
  const [search, setSearch] = useState("");
  // TODO: add search functionality
  const defferedSearch = useDeferredValue(search);
  const { data: students = [], isLoading } = useGuardiansStudents(
    currentUser.id
  );

  const [selectedStudentId, setSelectedStudentId] = useState<
    string | undefined
  >();

  const filteredStudents = students.filter((student) => {
    if (!selectedStudentId) return true;
    return student.id === selectedStudentId;
  });

  return (
    <div className="w-full flex flex-col items-center px-4 grow">
      <div className="w-full py-8 flex flex-col gap-4 max-w-5xl h-full">
        <h2>Invoice</h2>
        <div className="flex gap-2">
          <Input
            placeholder="Cari"
            onChange={(e) => setSearch(e.target.value)}
            value={search}
          />
          <UserFilter
            users={students}
            setSelectedUserId={setSelectedStudentId}
          />
        </div>
        <div className="size-full flex flex-col gap-4">
          {(() => {
            if (isLoading) return <Skeleton active />;
            if (filteredStudents.length === 0) {
              return (
                <div className="size-full flex flex-col justify-center">
                  <Empty />
                </div>
              );
            }
            return filteredStudents.map((student) => (
              <div
                key={student.id}
                className="flex flex-col gap-4 thick-scrollbar"
              >
                <GroupHeaderUser user={student} />
                <StudentsInvoice student={student} search={defferedSearch} />
              </div>
            ));
          })()}
        </div>
      </div>
    </div>
  );
}
