import { useStudentsStudentAttendancesMultiple } from "@sisva/hooks/query/attendance/useAttendance";
import { useGuardiansStudents } from "@sisva/hooks/query/user/useGuardians";
import { useCurrentUser } from "@sisva/providers";
import type { User } from "@sisva/types/apiTypes";
import type { Attendance } from "@sisva/types/types";
import { getAttendanceText } from "@sisva/types/types";
import { cn } from "@sisva/utils";
import { Empty, Input, Skeleton, Table, theme } from "antd";
import dayjs from "dayjs";
import isoWeek from "dayjs/plugin/isoWeek";
import { deepEqual } from "fast-equals";
import fuzzysort from "fuzzysort";
import { memo, useDeferredValue, useState } from "react";
import { match } from "ts-pattern";

import GroupHeaderUser from "#/routes/(main)/-components/GroupHeaderUser";
import UserFilter from "#/routes/(main)/-components/UserFilter";

import WeekFilter from "./WeekFilter";

dayjs.extend(isoWeek);

export default function AttendancesPage() {
  const currentUser = useCurrentUser();
  const [search, setSearch] = useState("");
  const defferedSearch = useDeferredValue(search);
  const { data: students = [], isLoading } = useGuardiansStudents(
    currentUser.id
  );
  const [selectedDateCodes, setSelectedDateCodes] = useState<
    number[] | null | undefined
  >();

  const [selectedStudentId, setSelectedStudentId] = useState<
    string | undefined
  >();

  let filteredStudents = students.filter((student) => {
    if (!selectedStudentId) return true;
    return student.id === selectedStudentId;
  });

  filteredStudents = fuzzysort
    .go(defferedSearch, filteredStudents, {
      keys: ["name"],
      all: !defferedSearch,
    })
    .map((result) => result.obj);

  return (
    <div className="w-full flex flex-col items-center px-4 grow">
      <div className="w-full py-8 flex flex-col gap-4 max-w-5xl h-full">
        <h2>Absensi</h2>
        <div className="flex gap-2 flex-col sm:flex-row">
          <Input
            placeholder="Cari"
            onChange={(e) => setSearch(e.target.value)}
            value={search}
          />
          <UserFilter
            users={students}
            setSelectedUserId={setSelectedStudentId}
          />
          <WeekFilter setSelectedDateCodes={setSelectedDateCodes} />
        </div>
        <div className="size-full flex flex-col gap-4">
          {(() => {
            if (isLoading) return <Skeleton active />;
            if (filteredStudents.length === 0)
              return (
                <div className="size-full flex flex-col justify-center">
                  <Empty />
                </div>
              );
            return (
              <AttendancesPerStudentList
                students={filteredStudents}
                selectedDateCodes={selectedDateCodes}
              />
            );
          })()}
        </div>
      </div>
    </div>
  );
}

const AttendancesPerStudentList = memo(function AttendancesPerStudentList({
  students,
  selectedDateCodes,
}: {
  students: User[];
  selectedDateCodes: number[] | null | undefined;
}) {
  return students.map((student) => (
    <AttendancesPerStudent
      key={student.id}
      student={student}
      selectedDateCodes={selectedDateCodes}
    />
  ));
}, deepEqual);

function AttendancesPerStudent({
  student,
  selectedDateCodes,
}: {
  student: User;
  selectedDateCodes: number[] | null | undefined;
}) {
  const token = theme.useToken().token;
  const date_codes =
    selectedDateCodes ??
    [...Array(6)].map((_, i) =>
      Number(dayjs().isoWeekday(1).add(i, "day").format("YYYYMMDD"))
    );

  const { data: studentsStudentAttendancesMultiple, isLoading } =
    useStudentsStudentAttendancesMultiple({
      date_codes,
      student_id: student.id,
    });

  const schedules = studentsStudentAttendancesMultiple.map(
    (schedules, i) =>
      schedules[0] ?? {
        date_id: date_codes[i],
        student_id: student.id,
        status: "present" as Attendance,
        student_name: student.name,
      }
  );

  function getBgColor(status: Attendance) {
    return match(status)
      .with("present", () => "")
      .with("sick", () => token.colorPrimary)
      .with("leave", () => token.colorWarning)
      .with("absent", () => token.colorError)
      .exhaustive();
  }

  return (
    <div className="flex flex-col items-start gap-4">
      <GroupHeaderUser user={student} />
      <Table<(typeof schedules)[number]>
        className="w-full"
        bordered
        dataSource={schedules}
        rowKey="date_id"
        columns={[
          {
            width: 256,
            align: "center",
            dataIndex: "date_id",
            title: "Hari ",
            render: (date_id) => {
              return (
                <div>
                  <div className="font-semibold">
                    {dayjs(String(date_id)).format("dddd")}
                  </div>
                  <div className="text-sm text-neutral-500">
                    {dayjs(String(date_id)).format("DD MMMM YYYY")}
                  </div>
                </div>
              );
            },
          },
          {
            dataIndex: "status",
            align: "center",
            title: "Status",
            render: (status: Attendance) => {
              return (() => {
                if (isLoading) return <Skeleton.Button active />;
                return (
                  <div className="flex justify-center">
                    <div
                      style={{
                        backgroundColor: getBgColor(status),
                      }}
                      className={cn("px-5 py-1 rounded-full font-medium", {
                        "text-white": status !== "present",
                      })}
                    >
                      {getAttendanceText(status)}
                    </div>
                  </div>
                );
              })();
            },
          },
        ]}
      />
    </div>
  );
}
