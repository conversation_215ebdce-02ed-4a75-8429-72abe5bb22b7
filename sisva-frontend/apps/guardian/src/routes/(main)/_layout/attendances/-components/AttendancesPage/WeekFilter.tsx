import { DatePicker } from "antd";

export default function WeekFilter({
  setSelectedDateCodes,
}: {
  setSelectedDateCodes: (value: number[] | null) => void;
}) {
  return (
    <DatePicker
      className="w-full sm:max-w-40"
      picker="week"
      onChange={(date) => {
        if (date) {
          const startOfWeek = date.isoWeekday(1);
          const endOfWeek = date.isoWeekday(6);
          const date_codes: number[] = [];
          for (
            let date = startOfWeek;
            date.isBefore(endOfWeek) || date.isSame(endOfWeek);
            date = date.add(1, "day")
          ) {
            date_codes.push(Number(date.format("YYYYMMDD")));
          }
          setSelectedDateCodes(date_codes);
        } else {
          setSelectedDateCodes(null);
        }
      }}
    />
  );
}
