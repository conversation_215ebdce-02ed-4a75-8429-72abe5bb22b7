import { useStudentAnnouncements } from "@sisva/hooks/query/academic/useAnnouncements";
import { useSchool } from "@sisva/providers";
import type { Announcement } from "@sisva/types/apiTypes";
import { getFileUrl } from "@sisva/utils";
import { Empty, Image, Input, Skeleton } from "antd";
import fuzzysort from "fuzzysort";
import { useDeferredValue, useState } from "react";

export default function AnnouncementsPage() {
  const [search, setSearch] = useState("");
  const defferedSearch = useDeferredValue(search);
  const { data: announcements = [], isLoading } = useStudentAnnouncements();

  const filteredAnnouncements = fuzzysort
    .go(defferedSearch, announcements, {
      keys: ["title", "text"],
      all: !defferedSearch,
    })
    .map((result) => result.obj);

  return (
    <div className="w-full px-4 flex flex-col items-center grow">
      <div className="flex flex-col py-8 w-full max-w-5xl gap-4 h-full">
        <h2>Pengumuman</h2>
        <Input
          placeholder="Cari"
          onChange={(e) => setSearch(e.target.value)}
          value={search}
        />
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 h-full">
          {(() => {
            if (isLoading) return <Skeleton active />;
            if (filteredAnnouncements.length === 0) {
              return (
                <div className="flex flex-col justify-center size-full">
                  <Empty />
                </div>
              );
            }
            return filteredAnnouncements.map((announcement) => (
              <AnnouncementCard
                key={announcement.id}
                announcement={announcement}
              />
            ));
          })()}
        </div>
      </div>
    </div>
  );
}

function AnnouncementCard({ announcement }: { announcement: Announcement }) {
  const school_id = useSchool()?.id;
  const imageUrl = getFileUrl(announcement.image_uri, school_id);

  return (
    <div className="flex items-start gap-4 p-4 rounded-md shadow-md">
      <div className="">
        <div className="font-semibold mb-4 text-lg">{announcement.title}</div>
        {imageUrl && (
          <div className="float-left pe-2 pb-1">
            <Image
              src={imageUrl}
              alt={announcement.title}
              width={96}
              height={96}
              className="min-w-24 object-cover"
            />
          </div>
        )}
        <div className="text-neutral-500 text-sm">{announcement.text}</div>
      </div>
    </div>
  );
}
