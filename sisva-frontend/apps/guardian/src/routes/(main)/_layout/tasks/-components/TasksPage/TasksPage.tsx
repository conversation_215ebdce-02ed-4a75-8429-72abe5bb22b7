import { useClasses } from "@sisva/hooks/query/academic/useClasses";
import { useSubmissions } from "@sisva/hooks/query/classroom/useSubmissions";
import { useStudentsTasks } from "@sisva/hooks/query/classroom/useTasks";
import { useGuardiansStudents } from "@sisva/hooks/query/user/useGuardians";
import { useCurrentUser } from "@sisva/providers";
import type { Class, Task, User } from "@sisva/types/apiTypes";
import { isDateEmpty } from "@sisva/utils";
import { Input, Skeleton, Tag, theme } from "antd";
import { Empty } from "antd";
import dayjs from "dayjs";
import fuzzysort from "fuzzysort";
import { useDeferredValue, useMemo, useState } from "react";

import GroupHeaderUser from "#/routes/(main)/-components/GroupHeaderUser";
import UserFilter from "#/routes/(main)/-components/UserFilter";

export default function TasksPage() {
  const currentUser = useCurrentUser();
  const [search, setSearch] = useState("");
  const defferedSearch = useDeferredValue(search);
  const { data: students = [], isLoading } = useGuardiansStudents(
    currentUser.id
  );
  const [selectedStudentId, setSelectedStudentId] = useState<
    string | undefined
  >();

  const filteredStudents = students.filter((student) => {
    if (!selectedStudentId) return true;
    return student.id === selectedStudentId;
  });

  return (
    <div className="flex flex-col items-center px-4 grow">
      <div className="flex flex-col py-8 w-full max-w-5xl gap-4 h-full">
        <h2>Daftar Tugas</h2>
        <div className="flex gap-2">
          <Input
            placeholder="Cari"
            onChange={(e) => setSearch(e.target.value)}
            value={search}
          />
          <UserFilter
            users={students}
            setSelectedUserId={setSelectedStudentId}
          />
        </div>
        <div className="size-full flex flex-col gap-4">
          {(() => {
            if (isLoading) return <Skeleton active />;
            if (filteredStudents.length === 0)
              return (
                <div className="size-full flex flex-col justify-center">
                  <Empty />
                </div>
              );
            return filteredStudents.map((student) => (
              <TasksPerStudent
                key={student.id}
                student={student}
                search={defferedSearch}
              />
            ));
          })()}
        </div>
      </div>
    </div>
  );
}

function TasksPerStudent({
  student,
  search,
}: {
  student: User;
  search: string;
}) {
  const token = theme.useToken().token;
  const { data: tasks = [], isLoading: L1 } = useStudentsTasks(student.id);
  const { data: classes = [], isLoading: L2 } = useClasses();

  const tasksWithClassData = useMemo(() => {
    const classMap = new Map(classes.map((class_) => [class_.id, class_]));

    return tasks.map((task) => ({
      ...task,
      class: classMap.get(task.class_id),
    }));
  }, [tasks, classes]);

  const filteredTasks = fuzzysort
    .go(search, tasksWithClassData, {
      keys: ["name", "description", "class.subject_name", "class.teacher_name"],
      all: !search,
    })
    .map((result) => result.obj);

  return (
    <div className="flex flex-col items-start gap-4">
      <GroupHeaderUser user={student} />
      <div className="w-full flex flex-col gap-4">
        {(() => {
          if (L1 || L2) return <Skeleton active />;
          if (filteredTasks.length === 0) return <Empty />;
          return filteredTasks.map((task) => (
            <TaskCard key={task.id} task={task} student={student} />
          ));
        })()}
      </div>
    </div>
  );
}

function TaskCard({
  task,
  student,
}: {
  task: Task & {
    class: Class | undefined;
  };
  student: User;
}) {
  const token = theme.useToken().token;

  const { data: submissions = [] } = useSubmissions({ task_id: task.id });
  const submitted = (() => {
    const submission = submissions.find(
      (submission) => submission.student_id === student.id
    );
    if (submission) return true;
    return false;
  })();

  return (
    <div
      key={task.id}
      style={{
        backgroundColor: token.colorPrimary,
      }}
      className="text-white p-4 rounded-md flex flex-col gap-1 relative overflow-hidden"
    >
      <div className="size-32 absolute rounded-full bg-gradient-to-r from-slate-50/10 to-slate-50/0 rotate-45 right-0 bottom-6" />

      <div className="text-xl font-semibold">{task.name}</div>
      <div>{task?.class?.subject_name}</div>
      <div>{task?.class?.teacher_name}</div>
      <div className="py-2 text-neutral-200 list-inside">
        {task?.description}
      </div>
      <div className="flex justify-between pt-1 items-center">
        <Tag color={submitted ? "success" : "error"} bordered={false}>
          {submitted ? "Sudah Mengumpulkan" : "Belum Mengumpulkan"}
        </Tag>
        <div className="flex gap-2 items-center">
          <div>Deadline</div>
          <div>
            {(() => {
              const deadlineDayjs = dayjs(
                task.deadline,
                "DD/MM/YYYY hh:mm A Z"
              );
              return isDateEmpty(deadlineDayjs)
                ? "Tidak ada"
                : deadlineDayjs.format("DD/MM/YYYY hh:mm A");
            })()}
          </div>
        </div>
      </div>
    </div>
  );
}
