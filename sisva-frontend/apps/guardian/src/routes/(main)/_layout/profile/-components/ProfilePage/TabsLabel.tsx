import { cn } from "@sisva/utils";
import { ChevronRight } from "@untitled-ui/icons-react";
import { theme } from "antd";
import type { JSX, SVGProps } from "react";

export default function TabsLabel({
  Icon,
  title,
  description,
  active = false,
  onClick,
}: {
  Icon: (props: SVGProps<SVGSVGElement>) => JSX.Element;
  title: string;
  description: string;
  active?: boolean;
  onClick?: () => void;
}) {
  const token = theme.useToken().token;
  return (
    <div
      className={cn(
        "flex items-center gap-4 justify-between  p-4 rounded-lg cursor-pointer hover:bg-neutral-100 transition-colors",
        {
          "bg-neutral-200": active,
        }
      )}
      onClick={onClick}
    >
      <div className="flex items-center gap-8">
        {<Icon color={token.colorPrimary} />}
        <div>
          <div className="font-medium">{title}</div>
          <div className="text-sm text-neutral-500">{description}</div>
        </div>
      </div>
      <ChevronRight color={token.colorPrimary} />
    </div>
  );
}
