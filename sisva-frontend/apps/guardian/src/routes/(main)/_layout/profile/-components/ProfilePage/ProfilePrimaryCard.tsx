import { useCurrentUser } from "@sisva/providers";
import { AvatarWithAcronym } from "@sisva/ui";
import { theme } from "antd";

export default function ProfilePrimaryCard() {
  const currentUser = useCurrentUser();
  const token = theme.useToken().token;
  return (
    <div
      style={{
        backgroundColor: token.colorPrimary,
      }}
      className="text-white p-6 flex flex-col items-center justify-center rounded-xl overflow-hidden relative gap-2"
    >
      <div className="size-40 absolute rounded-xl bg-gradient-to-r from-slate-50/0 to-slate-50/10 rotate-45 -left-12 bottom-0" />
      <div className="size-48 absolute rounded-3xl bg-gradient-to-r from-slate-50/0 to-slate-50/10 rotate-[135deg] -right-12 -bottom-16" />
      <div className="text-lg font-bold">Profilku</div>
      <AvatarWithAcronym
        name={currentUser.name}
        uri={currentUser.profile_image_uri}
        size={64}
      />
      <div className="text-lg font-bold">{currentUser.name}</div>
      <div className="text-sm">@{currentUser.username}</div>
    </div>
  );
}
