import { useDeleteSessionDataFromCookies } from "@sisva/hooks/query/useAuth";
import { useNotificationAPI } from "@sisva/providers";
import { useSchool } from "@sisva/providers";
import { useNavigate } from "@tanstack/react-router";
import { Lock01, LogOut04, User01 } from "@untitled-ui/icons-react";
import { useToggle } from "ahooks";
import { Divider, Modal } from "antd";
import { useState } from "react";

import BiodataSection from "./BiodataSection";
import PasswordSection from "./PasswordSection";
import ProfilePrimaryCard from "./ProfilePrimaryCard";
import TabsLabel from "./TabsLabel";

export default function ProfilePage() {
  const navigate = useNavigate();
  const school = useSchool();
  const notification = useNotificationAPI();
  const [currentTab, setCurrentTab] = useState<"biodata" | "security">(
    "biodata"
  );
  const [open, { toggle }] = useToggle();

  const { mutate: deleteSessionDataFromCookies } =
    useDeleteSessionDataFromCookies({
      onSuccess() {
        notification.success({
          message: "Berhasil keluar dari akun",
        });
        navigate({ to: "/signin", search: { school_code: school.code } });
      },
    });

  return (
    <>
      <Modal
        title="Keluar dari akun?"
        open={open}
        onOk={() => deleteSessionDataFromCookies()}
        onCancel={toggle}
      ></Modal>
      <div className="size-full flex flex-col items-center px-4">
        <div className="size-full max-w-5xl pt-8 flex flex-col md:flex-row gap-8">
          <div className="md:w-96 w-full flex flex-col gap-8 md:min-w-96">
            <ProfilePrimaryCard />
            <div className="flex flex-col shadow-md rounded-lg p-4">
              <div className="flex flex-col gap-4">
                <TabsLabel
                  Icon={User01}
                  title="Biodata"
                  description="Informasi biodata akunmu"
                  active={currentTab === "biodata"}
                  onClick={() => setCurrentTab("biodata")}
                />
                <TabsLabel
                  Icon={Lock01}
                  title="Keamanan"
                  description="Pengaturan password akunmu"
                  active={currentTab === "security"}
                  onClick={() => setCurrentTab("security")}
                />
              </div>
              <Divider />
              <TabsLabel
                Icon={LogOut04}
                title="Sign Out"
                description="Keluar dari akun"
                onClick={toggle}
              />
            </div>
          </div>
          <div className="grow pb-4 sm:overflow-auto pe-2">
            {(() => {
              switch (currentTab) {
                case "biodata":
                  return <BiodataSection />;
                case "security":
                  return <PasswordSection />;
              }
            })()}
          </div>
        </div>
      </div>
    </>
  );
}
