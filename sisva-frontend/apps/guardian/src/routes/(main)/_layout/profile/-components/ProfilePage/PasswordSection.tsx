import { yup<PERSON><PERSON>olver } from "@hookform/resolvers/yup";
import { useUpdatePassword } from "@sisva/hooks/query/user/useUsers";
import { useNotificationAPI } from "@sisva/providers";
import { useCurrentUser } from "@sisva/providers";
import { updatePasswordSchema } from "@sisva/types/formTypes";
import { Button, Form, Input } from "antd";
import { useForm } from "react-hook-form";
import { FormItem } from "react-hook-form-antd";

export default function PasswordSection() {
  const notification = useNotificationAPI();
  const currentUser = useCurrentUser();

  const { control, handleSubmit, reset } = useForm({
    resolver: yupResolver(updatePasswordSchema),
  });

  const { mutate: updatePassword } = useUpdatePassword({
    onSuccess() {
      notification.success({
        message: "Password berhasil diubah",
      });
      reset();
    },
    onError() {
      notification.error({
        message: "Password gagal diubah",
      });
    },
  });

  return (
    <div className="flex flex-col items-center">
      <Form
        layout="vertical"
        className="w-full"
        onFinish={handleSubmit((values) => {
          updatePassword({
            user_id: currentUser.id,
            current_password: values.current_password,
            new_password: values.new_password,
          });
        })}
        requiredMark={false}
      >
        <div className="flex flex-col items-end">
          <div className="grid grid-cols-1 gap-x-8 w-full">
            <FormItem
              control={control}
              label={<div className="font-medium">Password Lama</div>}
              name="current_password"
            >
              <Input.Password placeholder="Password Lama" />
            </FormItem>
            <FormItem
              control={control}
              label={<div className="font-medium">Password Baru</div>}
              name="new_password"
            >
              <Input.Password placeholder="Password Baru" />
            </FormItem>
            <FormItem
              control={control}
              label={
                <div className="font-medium">Konfirmasi Password Baru</div>
              }
              name="confirm_password"
            >
              <Input.Password placeholder="Konfirmasi Password Baru" />
            </FormItem>
          </div>

          <div className="flex gap-2">
            <Button type="primary" shape="round" htmlType="submit">
              Simpan
            </Button>
          </div>
        </div>
      </Form>
    </div>
  );
}
