import { useGuardiansStudents } from "@sisva/hooks/query/user/useGuardians";
import { useCurrentUser } from "@sisva/providers";
import type { User } from "@sisva/types/apiTypes";
import { SisvaScheduleComponent } from "@sisva/ui";
import { Empty, Input, Skeleton } from "antd";
import { deepEqual } from "fast-equals";
import fuzzysort from "fuzzysort";
import { memo, useDeferredValue, useState } from "react";

import GroupHeaderUser from "#/routes/(main)/-components/GroupHeaderUser";
import UserFilter from "#/routes/(main)/-components/UserFilter";

export default function SchedulesPage() {
  const currentUser = useCurrentUser();
  const [search, setSearch] = useState("");
  const defferedSearch = useDeferredValue(search);
  const { data: students = [], isLoading } = useGuardiansStudents(
    currentUser.id
  );

  const [selectedStudentId, setSelectedStudentId] = useState<
    string | undefined
  >();

  let filteredStudents = students.filter((student) => {
    if (!selectedStudentId) return true;
    return student.id === selectedStudentId;
  });

  filteredStudents = fuzzysort
    .go(defferedSearch, filteredStudents, {
      keys: ["name"],
      all: !defferedSearch,
    })
    .map((result) => result.obj);

  return (
    <div className="flex flex-col items-center px-4 grow">
      <div className="size-full max-w-5xl py-8 flex flex-col gap-4">
        <h2>Jadwal Pelajaran</h2>
        <div className="flex gap-2">
          <Input
            placeholder="Cari"
            onChange={(e) => setSearch(e.target.value)}
            value={search}
          />
          <UserFilter
            users={students}
            setSelectedUserId={setSelectedStudentId}
          />
        </div>
        <div className="size-full flex flex-col gap-4">
          {(() => {
            if (isLoading) return <Skeleton active />;
            if (filteredStudents.length === 0)
              return (
                <div className="size-full flex flex-col justify-center">
                  <Empty />
                </div>
              );
            return <StudentsSchedules students={filteredStudents} />;
          })()}
        </div>
      </div>
    </div>
  );
}

const StudentsSchedules = memo(function StudentsSchedules({
  students,
}: {
  students: User[];
}) {
  return students.map((student) => (
    <div key={student.id} className="flex flex-col gap-4 thick-scrollbar">
      <GroupHeaderUser user={student} />
      <div className="overflow-auto">
        <div className="w-[1600px] h-[50svh]">
          <SisvaScheduleComponent user_id={student.id} />
        </div>
      </div>
    </div>
  ));
}, deepEqual);
