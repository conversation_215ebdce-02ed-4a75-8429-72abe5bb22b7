import { useUser } from "@sisva/hooks/query/user/useUsers";
import type { User } from "@sisva/types/apiTypes";
import { AvatarWithAcronym } from "@sisva/ui";

export default function GroupHeaderUser({
  user_id,
  user,
}: {
  user_id?: string;
  user?: User;
}) {
  const { data: userFromFetchingId } = useUser(user_id, {
    enabled: !user,
  });

  return (
    <div className="flex items-center gap-4 bg-neutral-200 w-full p-2 ps-3 rounded-md relative overflow-hidden">
      <div className="absolute h-8 w-1 bg-neutral-500 -left-px top-1/2 -translate-y-1/2 rounded-sm" />
      <AvatarWithAcronym
        name={userFromFetchingId?.name || user!.name}
        uri={userFromFetchingId?.profile_image_uri || user!.profile_image_uri}
        size={40}
      />
      <div>
        <div className="font-semibold">
          {userFromFetchingId?.name || user!.name}
        </div>
        <div className="text-neutral-500 text-sm"></div>
      </div>
    </div>
  );
}
