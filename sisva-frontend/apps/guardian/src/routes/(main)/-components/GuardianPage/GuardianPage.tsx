import { useGuardiansStudents } from "@sisva/hooks/query/user/useGuardians";
import { useCurrentUser } from "@sisva/providers";
import { UserGreetingBanner } from "@sisva/ui";
import { Divider } from "antd";
import { useState } from "react";

import UserFilter from "../UserFilter";
import AnnouncementsSection from "./AnnouncementsSection";
import IconLinkList from "./IconLinkList";
import JadwalSection from "./JadwalSection";
import TugasSection from "./TugasSection";

export default function GuardianPage() {
  const currentUser = useCurrentUser();
  const { data: students = [] } = useGuardiansStudents(currentUser.id);
  const [selectedStudentId, setSelectedStudentId] = useState<
    string | undefined
  >();

  return (
    <div className="py-8 flex flex-col items-center px-4 gap-8">
      <UserGreetingBanner />
      <IconLinkList />
      <div className="flex w-full max-w-5xl items-end flex-col">
        <Divider />
        <UserFilter users={students} setSelectedUserId={setSelectedStudentId} />
      </div>
      <div className="grid grid-cols-1 lg:grid-cols-2 w-full max-w-5xl min-h-80 gap-8">
        <TugasSection selectedStudentId={selectedStudentId} />
        <JadwalSection selectedStudentId={selectedStudentId} />
      </div>
      <AnnouncementsSection />
    </div>
  );
}
