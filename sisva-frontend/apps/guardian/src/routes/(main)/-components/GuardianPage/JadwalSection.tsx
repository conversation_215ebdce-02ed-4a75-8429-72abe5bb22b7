import { useGuardiansStudents } from "@sisva/hooks/query/user/useGuardians";
import { useCurrentUser } from "@sisva/providers";
import { SisvaScheduleComponent } from "@sisva/ui";
import { Empty, Skeleton } from "antd";

import GroupHeaderUser from "../GroupHeaderUser";

export default function JadwalSection({
  selectedStudentId,
}: {
  selectedStudentId: string | undefined;
}) {
  const currentUser = useCurrentUser();
  const { data: students = [], isLoading } = useGuardiansStudents(
    currentUser.id
  );

  const filteredStudents = students.filter((student) => {
    if (!selectedStudentId) return true;
    return student.id === selectedStudentId;
  });

  return (
    <div className="shadow-xl rounded-lg p-4 flex flex-col gap-4">
      <div className="font-semibold">Jadwal</div>
      <div className="overflow-auto max-h-64 pe-2 size-full flex flex-col gap-4">
        {(() => {
          if (isLoading) return <Skeleton active />;
          if (filteredStudents.length === 0)
            return (
              <div className="size-full flex flex-col justify-center">
                <Empty />
              </div>
            );
          return filteredStudents.map((student) => (
            <div key={student.id} className="flex flex-col gap-4">
              <GroupHeaderUser user={student} />
              <SisvaScheduleComponent
                currentView="Day"
                user_id={student.id}
                timeScale={{ enable: true, interval: 60, slotCount: 4 }}
              />
            </div>
          ));
        })()}
      </div>
    </div>
  );
}
