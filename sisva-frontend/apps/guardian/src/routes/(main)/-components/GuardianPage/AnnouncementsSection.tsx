import { useStudentAnnouncements } from "@sisva/hooks/query/academic/useAnnouncements";
import { useSchool } from "@sisva/providers";
import type { Announcement } from "@sisva/types/apiTypes";
import { getFileUrl } from "@sisva/utils";
import { Empty, Image, Skeleton } from "antd";

export default function AnnouncementsSection() {
  const { data: announcements = [], isLoading } = useStudentAnnouncements();

  return (
    <div className="rounded-xl shadow-xl w-full max-w-5xl min-h-48 p-8 gap-4 flex flex-col">
      <div className="font-semibold text-lg">Pengumuman</div>
      <div className="flex flex-col gap-4">
        {(() => {
          if (isLoading) return <Skeleton active />;
          if (announcements.length === 0) return <Empty />;
          return announcements
            .slice(0, 3)
            .map((announcement) => (
              <AnnouncementCard
                key={announcement.id}
                announcement={announcement}
              />
            ));
        })()}
      </div>
    </div>
  );
}

function AnnouncementCard({ announcement }: { announcement: Announcement }) {
  const school_id = useSchool()?.id;
  const imageUrl = getFileUrl(announcement.image_uri, school_id);

  return (
    <div className="flex flex-col sm:flex-row items-start gap-4 p-4 rounded-md shadow-md">
      {imageUrl && (
        <Image
          src={imageUrl}
          alt={announcement.title}
          width={96}
          height={96}
          className="min-w-24 object-cover"
        />
      )}
      <div className="flex flex-col gap-1">
        <div className="font-semibold">{announcement.title}</div>
        <div className="text-neutral-500 text-sm">{announcement.text}</div>
      </div>
    </div>
  );
}
