import { Link } from "@tanstack/react-router";

import { InvoiceIcon } from "#/assets/svgr/InvoiceIcon";
import { JadwalPelajaranIcon } from "#/assets/svgr/JadwalPelajaranIcon";
import { PerizinanIcon } from "#/assets/svgr/PerizinanIcon";
import { TeksIcon } from "#/assets/svgr/TeksIcon";
import { TugasIcon } from "#/assets/svgr/TugasIcon";

export default function IconLinkList() {
  return (
    <div className="flex gap-8 flex-wrap justify-center">
      <Link to="/schedules" className="flex flex-col items-center gap-1">
        <JadwalPelajaranIcon className="w-14" />
        <div className="w-20 text-center text-sm">Jadwal Pelajaran</div>
      </Link>
      <Link to="/tasks" className="flex flex-col items-center gap-1">
        <TugasIcon className="w-14" />
        <div className="w-20 text-center text-sm">Tugas</div>
      </Link>
      <Link to="/attendances" className="flex flex-col items-center gap-1">
        <PerizinanIcon className="w-14" />
        <div className="w-20 text-center text-sm">Absensi</div>
      </Link>
      <Link to="/announcements" className="flex flex-col items-center gap-1">
        <TeksIcon className="w-14" />
        <div className="w-20 text-center text-sm">Pengumuman</div>
      </Link>
      <Link to="/invoices" className="flex flex-col items-center gap-1">
        <InvoiceIcon className="w-14" />
        <div className="w-20 text-center text-sm">Invoice</div>
      </Link>
    </div>
  );
}
