import { useSchool } from "@sisva/providers";
import { Image } from "@sisva/ui";
import { Link, useMatchRoute } from "@tanstack/react-router";
import { Home05, User01 } from "@untitled-ui/icons-react";
import { theme } from "antd";

export default function NavBar() {
  const school = useSchool();
  const token = theme.useToken().token;
  const matchRoute = useMatchRoute();

  function getHomeColor() {
    if (matchRoute({ to: "/" })) return token.colorPrimaryActive;
    return token.colorPrimary;
  }

  function getUserColor() {
    if (matchRoute({ to: "/profile" })) return token.colorPrimaryActive;
    return token.colorPrimary;
  }

  return (
    <div className="h-24 min-h-24 sm:shadow-md flex items-center justify-between px-8 shadow-[0_-4px_6px_-1px_rgba(0,0,0,0.1)] sm:rounded-t-none rounded-t-2xl">
      <div className="sm:flex items-center gap-6 hidden">
        <div className="h-16 w-16 relative">
          <Link to="/">
            <Image
              alt="Logo sekolah"
              fill
              className="object-contain"
              src={school.logo_url || ""}
            />
          </Link>
        </div>
        <div className="font-bold text-lg">{school.name}</div>
      </div>
      <div className="flex items-center gap-8 sm:justify-end justify-evenly flex-1">
        <Link
          className="flex flex-col items-center font-bold"
          to="/"
          style={{
            color: getHomeColor(),
          }}
        >
          <Home05 fill={getHomeColor()} />
          <div>Beranda</div>
        </Link>
        <Link
          className="flex flex-col items-center font-medium"
          to="/profile"
          style={{
            color: getUserColor(),
          }}
        >
          <User01 fill={getUserColor()} />
          <div>Profil</div>
        </Link>
      </div>
    </div>
  );
}
