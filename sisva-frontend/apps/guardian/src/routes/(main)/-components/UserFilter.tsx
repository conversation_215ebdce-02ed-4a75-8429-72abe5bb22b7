import type { User } from "@sisva/types/apiTypes";
import { Select } from "antd";

export default function UserFilter({
  users,
  setSelectedUserId,
}: {
  users: User[];
  setSelectedUserId: (id: string | undefined) => void;
}) {
  return (
    <Select
      className="w-full sm:max-w-64"
      onChange={(value) => {
        setSelectedUserId(value);
      }}
      allowClear
      placeholder="Pilih Murid"
      options={users.map((user) => ({ value: user.id, label: user.name }))}
    />
  );
}
