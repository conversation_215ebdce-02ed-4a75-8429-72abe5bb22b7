import type { SVGProps } from "react";
import * as React from "react";
const SvgComponent = (props: SVGProps<SVGSVGElement>) => (
  <svg
    viewBox="0 0 54 54"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <g clipPath="url(#clip0_1417_20448)">
      <circle opacity={0.3} cx={27} cy={27} r={27} fill="#003855" />
      <path
        d="M46.8864 5.96014L49.6296 42.0514C49.7184 43.2187 48.843 44.2381 47.6757 44.3269L11.5845 47.0701C10.4171 47.1589 9.39777 46.2834 9.30899 45.1161L6.56577 9.02492C6.47699 7.85754 7.35242 6.8382 8.51971 6.74942L44.6109 4.0062C45.7783 3.91742 46.7977 4.79276 46.8864 5.96014Z"
        fill="#F96756"
      />
      <path
        d="M47.0507 8.83566V45.0309C47.0507 46.2017 46.1005 47.1518 44.9299 47.1518H8.73458C7.56383 47.1518 6.61377 46.2016 6.61377 45.0309V8.83566C6.61377 7.66491 7.56393 6.71484 8.73458 6.71484H44.9299C46.1006 6.71475 47.0507 7.665 47.0507 8.83566Z"
        fill="#2A2A61"
      />
      <path
        d="M33.5239 49.4027L5.96179 47.3291C4.79376 47.2411 3.91823 46.2231 4.00607 45.0551L6.72145 8.96179C6.80939 7.79376 7.82742 6.91823 8.99545 7.00607L45.0888 9.72145C46.2569 9.80938 47.1324 10.8274 47.0446 11.9954L44.9294 40.1112C44.905 40.4348 44.7543 40.7358 44.5099 40.9493L35.6303 48.7016C35.0502 49.2078 34.2915 49.4604 33.5239 49.4027Z"
        fill="#FEF0E8"
      />
      <path
        d="M16.7836 21.6973L41.0773 23.5392C41.7251 23.5884 42.2901 23.103 42.3392 22.4552C42.3883 21.8074 41.9008 21.2423 41.2529 21.1931L16.9637 19.3515C16.3159 19.3024 15.7487 19.7876 15.6996 20.4354C15.6505 21.0832 16.1358 21.6482 16.7836 21.6973Z"
        fill="#F96756"
      />
      <path
        d="M13.7533 19.9606C13.8159 19.1345 13.1971 18.4141 12.371 18.3515C11.5449 18.2888 10.8245 18.9077 10.7619 19.7338C10.6993 20.5598 11.3181 21.2803 12.1442 21.3429C12.9702 21.4055 13.6907 20.7866 13.7533 19.9606Z"
        fill="#F96756"
      />
      <path
        d="M15.7836 27.6969L26.1242 28.4809C26.772 28.53 27.3369 28.0447 27.3861 27.3969C27.4352 26.749 26.9484 26.184 26.3006 26.1349L15.9629 25.351C15.3151 25.3019 14.7487 25.7872 14.6996 26.435C14.6505 27.0828 15.1358 27.6478 15.7836 27.6969Z"
        fill="#F96756"
      />
      <path
        d="M12.7535 25.9606C12.8162 25.1345 12.1973 24.4141 11.3712 24.3515C10.5452 24.2888 9.82476 24.9077 9.76213 25.7338C9.6995 26.5598 10.3184 27.2803 11.1444 27.3429C11.9705 27.4055 12.6909 26.7866 12.7535 25.9606Z"
        fill="#F96756"
      />
      <path
        d="M11.2424 33.2889L32.0443 34.8661C32.6921 34.9153 33.2571 34.4299 33.3062 33.7821C33.3553 33.1343 32.87 32.5693 32.2222 32.5202L11.4203 30.943C10.7725 30.8939 10.2075 31.3792 10.1584 32.0271C10.1093 32.6749 10.5946 33.2398 11.2424 33.2889Z"
        fill="#F96756"
      />
      <path
        d="M35.6299 48.352L38.7606 45.6187L4.13329 43.0137L4.00607 44.7056C3.92095 45.8365 4.7406 46.8279 5.85323 46.9685C5.88904 46.973 5.92532 46.9767 5.96188 46.9794L33.5239 49.053C34.2914 49.1108 35.0501 48.8582 35.6299 48.352Z"
        fill="url(#paint0_linear_1417_20448)"
      />
      <path
        d="M44.7833 40.2584C44.7833 40.2584 44.7075 40.4307 44.443 40.6583C44.3447 40.7428 35.6826 48.3064 35.6826 48.3064C35.0693 48.8418 34.267 49.1088 33.4551 49.0478C34.1559 49.1005 34.7667 48.5751 34.8194 47.8743L35.2861 41.6708C35.374 40.5027 36.3921 39.6272 37.5601 39.7151L44.7833 40.2584Z"
        fill="#D7C9AD"
      />
    </g>
    <defs>
      <linearGradient
        id="paint0_linear_1417_20448"
        x1={21.38}
        y1={44.4861}
        x2={21.38}
        y2={48.9143}
        gradientUnits="userSpaceOnUse"
      >
        <stop offset={0.0968749} stopColor="#D7C9AD" stopOpacity={0} />
        <stop offset={1} stopColor="#D7C9AD" stopOpacity={0.58} />
      </linearGradient>
      <clipPath id="clip0_1417_20448">
        <rect width={54} height={54} fill="white" />
      </clipPath>
    </defs>
  </svg>
);
export { SvgComponent as TeksIcon };
