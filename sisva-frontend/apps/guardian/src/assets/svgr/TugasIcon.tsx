import type { SVGProps } from "react";
import * as React from "react";
const SvgComponent = (props: SVGProps<SVGSVGElement>) => (
  <svg
    viewBox="0 0 54 54"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <g clipPath="url(#clip0_1417_19911)">
      <circle opacity={0.3} cx={27} cy={27} r={27} fill="#003855" />
      <g clipPath="url(#clip1_1417_19911)">
        <path
          d="M5.22021 4.87695L2.41984 41.7201C2.32921 42.9118 3.22289 43.9523 4.41449 44.043L41.2576 46.8433C42.4493 46.934 43.4899 46.0403 43.5805 44.8487L46.3809 8.00558C46.4715 6.81388 45.5778 5.7733 44.3862 5.68267L7.54311 2.8823C6.35142 2.79167 5.31075 3.68525 5.22021 4.87695Z"
          fill="#F96756"
        />
        <path
          d="M5.05245 7.81246V44.7618C5.05245 45.9569 6.0224 46.9268 7.21745 46.9268H44.1668C45.3619 46.9268 46.3318 45.9569 46.3318 44.7618V7.81246C46.3318 6.61732 45.3618 5.64746 44.1668 5.64746H7.21745C6.0223 5.64737 5.05245 6.61741 5.05245 7.81246Z"
          fill="#2A2A61"
        />
        <path
          d="M18.861 48.8679L46.9973 46.7511C48.1897 46.6613 49.0835 45.622 48.9938 44.4297L46.2219 7.58445C46.1321 6.39208 45.0928 5.49831 43.9005 5.58799L7.05516 8.35993C5.86279 8.4497 4.96902 9.48894 5.0586 10.6813L7.21795 39.3828C7.24284 39.7131 7.39663 40.0204 7.64613 40.2384L16.7107 48.1521C17.303 48.6689 18.0774 48.9268 18.861 48.8679Z"
          fill="#FEF0E8"
        />
        <path
          d="M21.8683 31C21.8683 31 40.606 24.8228 41.8396 18.6032C43.8657 8.38793 26.242 5.82988 14.6629 13.3214C7.4726 17.9734 6.46967 29.2569 27.2724 26.7572"
          stroke="url(#paint0_linear_1417_19911)"
          strokeLinecap="round"
        />
        <path
          d="M19.2139 16.0568L18.845 16.6469C18.7687 16.7428 18.6622 16.7923 18.5884 16.8007C18.3301 16.8301 18.1148 16.5805 18.0855 16.3221C18.0645 16.1377 18.1391 15.9174 18.2864 15.7885L19.4257 14.413C19.502 14.3172 19.5947 14.2568 19.7423 14.24C20.0129 14.2092 20.3386 14.3341 20.3694 14.6047L21.2955 22.7469C21.3277 23.0297 21.0124 23.2151 20.6926 23.2515C20.3606 23.2892 20.024 23.178 19.9918 22.8952L19.2139 16.0568Z"
          fill="#F96756"
        />
        <path
          d="M22.8932 20.3585L22.4861 16.7794C22.2594 14.7869 23.367 13.9009 24.9291 13.7232C26.4788 13.547 27.7818 14.1587 28.0084 16.1512L28.4155 19.7303C28.6422 21.7228 27.5099 22.6115 25.9602 22.7878C24.3981 22.9655 23.1198 22.351 22.8932 20.3585ZM26.7047 16.2996C26.5816 15.2172 25.9708 14.7759 25.0606 14.8794C24.1381 14.9843 23.6667 15.5487 23.7898 16.6311L24.1969 20.2102C24.3201 21.2926 24.9063 21.7367 25.8287 21.6318C26.7389 21.5282 27.235 20.961 27.1119 19.8786L26.7047 16.2996Z"
          fill="#F96756"
        />
        <path
          d="M29.0499 19.6585L28.6428 16.0794C28.4162 14.0869 29.5238 13.201 31.0859 13.0233C32.6355 12.847 33.9385 13.4587 34.1652 15.4512L34.5723 19.0303C34.7989 21.0228 33.6666 21.9116 32.117 22.0879C30.5549 22.2655 29.2766 21.651 29.0499 19.6585ZM32.8615 15.5996C32.7384 14.5172 32.1275 14.0759 31.2174 14.1794C30.2949 14.2844 29.8234 14.8488 29.9465 15.9312L30.3537 19.5103C30.4768 20.5926 31.063 21.0367 31.9855 20.9318C32.8956 20.8283 33.3917 20.2611 33.2686 19.1787L32.8615 15.5996Z"
          fill="#F96756"
        />
        <path
          d="M16.7111 48.1527L10.5 42.7031L48.5 40L48.9938 44.4303C49.0807 45.5847 48.244 46.5968 47.1082 46.7404C47.0716 46.745 47.0346 46.7487 46.9972 46.7515L18.861 48.8682C18.0775 48.9273 17.3031 48.6694 16.7111 48.1527Z"
          fill="url(#paint1_linear_1417_19911)"
        />
        <path
          d="M7.3672 39.89C7.3672 39.89 7.44453 40.0659 7.71461 40.2982C7.8149 40.3845 16.6575 48.1056 16.6575 48.1056C17.2836 48.6522 18.1026 48.9248 18.9314 48.8624C18.216 48.9162 17.5924 48.3799 17.5386 47.6645L17.0622 41.3318C16.9724 40.1394 15.9332 39.2456 14.7408 39.3353L7.3672 39.89Z"
          fill="#F96756"
        />
      </g>
      <g clipPath="url(#clip2_1417_19911)">
        <path
          d="M40.846 53.0177L53.437 40.4268C54.1876 39.6761 54.1876 38.459 53.437 37.7083L43.8835 28.1548C43.1328 27.4041 41.9157 27.4041 41.1651 28.1548L26.563 42.7569C25.8123 43.5076 25.8123 44.7247 26.563 45.4753L34.1054 53.0177C34.4596 53.3719 34.9401 53.5709 35.4409 53.5709H39.5105C40.0114 53.5709 40.4918 53.3719 40.846 53.0177Z"
          fill="url(#paint2_linear_1417_19911)"
        />
        <path
          d="M32.5004 36.8193L30.5884 38.7313V49.5007L34.1053 53.0176C34.4595 53.3718 34.9399 53.5708 35.4408 53.5708H39.5103C40.0113 53.5708 40.4917 53.3718 40.8458 53.0176L44.7721 49.0913L32.5004 36.8193Z"
          fill="url(#paint3_linear_1417_19911)"
        />
        <path
          d="M53.241 41.0638L43.4585 50.8462C43.2286 51.0761 42.8558 51.0761 42.6259 50.8462L30.7462 38.9665C30.5163 38.7366 30.5163 38.3637 30.7462 38.1338L40.5285 28.3514C40.7585 28.1215 41.1313 28.1215 41.3612 28.3514L53.2409 40.2311C53.4709 40.4611 53.4709 40.8339 53.241 41.0638Z"
          fill="#2A2A61"
        />
        <path
          d="M32.7089 36.1711L30.7462 38.1339C30.5163 38.3638 30.5163 38.7366 30.7462 38.9665L42.6259 50.8463C42.8558 51.0762 43.2286 51.0762 43.4585 50.8463L45.4213 48.8835L32.7089 36.1711Z"
          fill="#2A2A61"
        />
        <path
          d="M27.6674 41.6533L26.563 42.7577C25.8123 43.5084 25.8123 44.7255 26.563 45.4761L34.1054 53.0185C34.4596 53.3727 34.9401 53.5717 35.4409 53.5717H39.5105C39.535 53.5717 39.5594 53.5708 39.5839 53.5699L27.6674 41.6533Z"
          fill="url(#paint4_linear_1417_19911)"
        />
        <path
          d="M44.8033 43.7345L38.2655 37.1967C37.9115 36.8427 37.9115 36.2688 38.2655 35.9148L40.9148 33.2655C41.2688 32.9115 41.8427 32.9115 42.1967 33.2655L48.7345 39.8033C49.0885 40.1573 49.0885 40.7312 48.7345 41.0852L46.0852 43.7345C45.7312 44.0885 45.1573 44.0885 44.8033 43.7345Z"
          fill="#F96756"
        />
        <path
          d="M45.1994 54.4078H33.2626C33.02 54.4078 32.8232 54.2111 32.8232 53.9684C32.8232 53.7258 33.02 53.5291 33.2626 53.5291H45.1995C45.4421 53.5291 45.6388 53.7258 45.6388 53.9684C45.6388 54.2111 45.442 54.4078 45.1994 54.4078Z"
          fill="#2A2A61"
        />
      </g>
    </g>
    <defs>
      <linearGradient
        id="paint0_linear_1417_19911"
        x1={37.171}
        y1={8.58257}
        x2={20.5703}
        y2={34.2951}
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="#2A2A61" />
        <stop offset={1} stopColor="#2A2A61" stopOpacity={0.7} />
      </linearGradient>
      <linearGradient
        id="paint1_linear_1417_19911"
        x1={30}
        y1={41.5}
        x2={31.258}
        y2={48.7267}
        gradientUnits="userSpaceOnUse"
      >
        <stop offset={0.233484} stopColor="#D7C9AD" stopOpacity={0} />
        <stop offset={0.997917} stopColor="#D7C9AD" />
      </linearGradient>
      <linearGradient
        id="paint2_linear_1417_19911"
        x1={38.6015}
        y1={40.1939}
        x2={48.2437}
        y2={49.8356}
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="#FBFBFB" />
        <stop offset={1} stopColor="#C1C1C1" />
      </linearGradient>
      <linearGradient
        id="paint3_linear_1417_19911"
        x1={34.0436}
        y1={47.548}
        x2={39.0758}
        y2={42.5158}
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="#D3B4A0" stopOpacity={0} />
        <stop offset={1} stopColor="#D3B4A0" />
      </linearGradient>
      <linearGradient
        id="paint4_linear_1417_19911"
        x1={32.4175}
        y1={48.8198}
        x2={29.5511}
        y2={51.6862}
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="#D3B4A0" stopOpacity={0} />
        <stop offset={1} stopColor="#D3B4A0" />
      </linearGradient>
      <clipPath id="clip0_1417_19911">
        <rect width={54} height={54} fill="white" />
      </clipPath>
      <clipPath id="clip1_1417_19911">
        <rect
          width={49}
          height={49}
          fill="white"
          transform="matrix(-1 0 0 1 49 0)"
        />
      </clipPath>
      <clipPath id="clip2_1417_19911">
        <rect
          width={28}
          height={28}
          fill="white"
          transform="translate(26 27)"
        />
      </clipPath>
    </defs>
  </svg>
);
export { SvgComponent as TugasIcon };
