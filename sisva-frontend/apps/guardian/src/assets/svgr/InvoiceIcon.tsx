import type { SVGProps } from "react";
import * as React from "react";
const SvgComponent = (props: SVGProps<SVGSVGElement>) => (
  <svg
    viewBox="0 0 54 54"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <g clipPath="url(#clip0_2511_23063)">
      <circle opacity={0.3} cx={27} cy={27} r={27} fill="#003855" />
      <g clipPath="url(#clip1_2511_23063)">
        <path
          d="M14.4583 8.58331V27C14.4551 27.3334 14.3494 27.6578 14.1556 27.9291C13.9618 28.2004 13.6893 28.4055 13.3749 28.5166C13.2025 28.5856 13.0189 28.6223 12.8333 28.625H6.33326C5.90403 28.6194 5.49395 28.4464 5.19041 28.1428C4.88688 27.8393 4.71387 27.4292 4.70826 27V8.58331C4.70647 7.33994 5.18196 6.1433 6.03664 5.24024C6.89132 4.33718 8.06 3.79659 9.30159 3.72998H9.58326C10.8724 3.72997 12.1091 4.24061 13.0228 5.15017C13.9364 6.05973 14.4525 7.29414 14.4583 8.58331Z"
          fill="#2A2A61"
        />
        <path
          d="M46.9582 16.1666V46.0233C46.9579 46.6792 46.7875 47.324 46.4635 47.8944C46.1396 48.4648 45.6732 48.9414 45.1099 49.2776C44.5466 49.6138 43.9057 49.7982 43.2499 49.8126C42.5941 49.827 41.9457 49.671 41.3682 49.3599L40.6748 48.9699C39.826 48.3563 38.7966 48.0436 37.7498 48.0816C36.6868 48.0468 35.6424 48.367 34.7815 48.9916C33.4048 49.8762 31.8029 50.3466 30.1665 50.3466C28.5301 50.3466 26.9282 49.8762 25.5515 48.9916C24.6829 48.3848 23.6423 48.0734 22.5832 48.1032C21.5306 48.0718 20.4964 48.3836 19.6365 48.9916L18.9648 49.3816C18.3873 49.6927 17.739 49.8487 17.0831 49.8343C16.4273 49.8198 15.7864 49.6355 15.2231 49.2993C14.6599 48.9631 14.1935 48.4865 13.8695 47.916C13.5455 47.3456 13.3751 46.7009 13.3748 46.0449V8.04158C13.3768 6.93597 12.9562 5.87142 12.1989 5.06584C11.4417 4.26026 10.4051 3.77459 9.30151 3.70825H34.4998C37.8022 3.71398 40.9677 5.02839 43.3029 7.36354C45.638 9.69869 46.9525 12.8642 46.9582 16.1666Z"
          fill="#FEF0E8"
        />
        <path
          d="M46.9582 16.1666V46.0233C46.9579 46.6792 46.7875 47.324 46.4635 47.8944C46.1396 48.4648 45.6732 48.9414 45.1099 49.2776C44.5466 49.6138 43.9057 49.7982 43.2499 49.8126C42.5941 49.827 41.9457 49.671 41.3682 49.3599L40.6748 48.9699C39.826 48.3563 38.7966 48.0436 37.7498 48.0816C36.6868 48.0468 35.6424 48.367 34.7815 48.9916C33.4048 49.8762 31.8029 50.3466 30.1665 50.3466C28.5301 50.3466 26.9282 49.8762 25.5515 48.9916C24.6829 48.3848 23.6423 48.0734 22.5832 48.1032C21.5306 48.0718 20.4964 48.3836 19.6365 48.9916L18.9648 49.3816C18.3873 49.6927 17.739 49.8487 17.0831 49.8343C16.4273 49.8198 15.7864 49.6355 15.2231 49.2993C14.6599 48.9631 14.1935 48.4865 13.8695 47.916C13.5455 47.3456 13.3751 46.7009 13.3748 46.0449V8.04158C13.3768 6.93597 12.9562 5.87142 12.1989 5.06584C11.4417 4.26026 10.4051 3.77459 9.30151 3.70825H34.4998C37.8022 3.71398 40.9677 5.02839 43.3029 7.36354C45.638 9.69869 46.9525 12.8642 46.9582 16.1666Z"
          fill="url(#paint0_linear_2511_23063)"
        />
        <path
          d="M30 37.9307H19C18.4477 37.9307 18 38.3784 18 38.9307C18 39.4829 18.4477 39.9307 19 39.9307H30C30.5523 39.9307 31 39.4829 31 38.9307C31 38.3784 30.5523 37.9307 30 37.9307Z"
          fill="#2A2A61"
        />
        <path
          d="M40.2023 32H18.9653C18.4322 32 18 32.4322 18 32.9653C18 33.4984 18.4322 33.9306 18.9653 33.9306H40.2023C40.7354 33.9306 41.1676 33.4984 41.1676 32.9653C41.1676 32.4322 40.7354 32 40.2023 32Z"
          fill="#2A2A61"
        />
        <g clipPath="url(#clip2_2511_23063)">
          <path
            d="M39.0561 13.6315C39.6105 16.2601 39.1376 19.0013 37.7343 21.2922C36.3311 23.5831 34.104 25.2498 31.5104 25.9502C31.3293 26.0001 31.1467 26.0427 30.9633 26.0816C28.2391 26.6594 25.397 26.1314 23.0621 24.6137C20.7273 23.096 19.0909 20.7129 18.5131 17.9888C17.9353 15.2646 18.4633 12.4225 19.981 10.0876C21.4987 7.75277 23.8818 6.11645 26.606 5.53863C26.7894 5.49973 26.9736 5.46449 27.1593 5.43659C29.8139 5.0238 32.5258 5.64296 34.7382 7.16694C36.9507 8.69092 38.4957 11.0041 39.0561 13.6315Z"
            fill="#F96756"
          />
          <path
            d="M37.5887 13.9426C37.6409 14.1884 37.682 14.4365 37.7113 14.6833C37.9945 16.9222 37.4259 19.1859 36.1183 21.0253C34.8107 22.8647 32.8596 24.1455 30.6519 24.6138C28.4442 25.082 26.1412 24.7036 24.1995 23.5533C22.2578 22.4031 20.8192 20.5652 20.1691 18.4041C20.0957 18.1667 20.0326 17.9232 19.9805 17.6774C19.4852 15.3424 19.9378 12.9063 21.2387 10.905C22.5395 8.9037 24.5822 7.50114 26.9172 7.00587C29.2522 6.5106 31.6883 6.96319 33.6896 8.26408C35.6909 9.56497 37.0935 11.6076 37.5887 13.9426Z"
            fill="#E65544"
          />
          <path
            d="M30.652 24.6142C35.4961 23.5868 38.6567 19.1406 37.7113 14.6834C36.7659 10.2262 32.0725 7.44585 27.2284 8.47333C22.3842 9.5008 19.2237 13.947 20.1691 18.4042C21.1145 22.8614 25.8078 25.6417 30.652 24.6142Z"
            fill="#FB7667"
          />
          <path
            d="M28.3912 13.1154C28.2669 12.5319 27.9161 12.0217 27.416 11.6966C26.9158 11.3714 26.307 11.258 25.7234 11.3812L24.256 11.6925C24.1587 11.7131 24.0736 11.7715 24.0194 11.8549C23.9652 11.9383 23.9463 12.0398 23.967 12.1371L25.5232 19.4739C25.5438 19.5712 25.6022 19.6563 25.6856 19.7105C25.769 19.7647 25.8705 19.7835 25.9678 19.7629C26.0651 19.7423 26.1502 19.6838 26.2044 19.6004C26.2586 19.5171 26.2775 19.4156 26.2568 19.3183L25.5565 16.0167L26.4087 15.836L28.5129 18.9823C28.5403 19.0233 28.5755 19.0585 28.6165 19.0858C28.6575 19.1131 28.7035 19.1321 28.7518 19.1417C28.8001 19.1513 28.8499 19.1512 28.8982 19.1416C28.9465 19.1319 28.9925 19.1129 29.0334 19.0854C29.0744 19.058 29.1096 19.0228 29.1369 18.9818C29.1642 18.9408 29.1832 18.8948 29.1928 18.8465C29.2024 18.7982 29.2023 18.7484 29.1927 18.7001C29.183 18.6518 29.164 18.6058 29.1365 18.5649L27.1602 15.6106C27.6153 15.3928 27.984 15.0284 28.2072 14.5759C28.4304 14.1234 28.4952 13.6091 28.3912 13.1154ZM26.5014 15.0496L25.4009 15.283L24.7785 12.3483L25.879 12.1149C26.2681 12.0324 26.6742 12.1078 27.0077 12.3246C27.3413 12.5414 27.575 12.8818 27.6576 13.271C27.7401 13.6602 27.6647 14.0662 27.4479 14.3998C27.231 14.7333 26.8906 14.9671 26.5014 15.0496Z"
            fill="#FEF0E8"
            stroke="#FEF0E8"
            strokeWidth={0.5}
          />
          <path
            d="M31.1145 13.3043L29.6471 13.6156C29.5498 13.6362 29.4647 13.6946 29.4105 13.778C29.3563 13.8614 29.3374 13.9629 29.3581 14.0602L30.9143 21.397C30.9349 21.4943 30.9933 21.5794 31.0767 21.6336C31.1601 21.6878 31.2616 21.7066 31.3589 21.686C31.4562 21.6654 31.5413 21.6069 31.5955 21.5235C31.6497 21.4402 31.6686 21.3386 31.6479 21.2414L30.9477 17.9398L32.0482 17.7064C32.6319 17.5826 33.1426 17.2319 33.4678 16.7316C33.793 16.2313 33.9062 15.6222 33.7824 15.0385C33.6585 14.4547 33.3079 13.9441 32.8076 13.6189C32.3072 13.2936 31.6982 13.1805 31.1145 13.3043ZM31.8926 16.9727L30.792 17.2061L30.1696 14.2714L31.2701 14.038C31.6593 13.9554 32.0653 14.0309 32.3988 14.2477C32.7324 14.4645 32.9661 14.8049 33.0487 15.1941C33.1312 15.5833 33.0558 15.9893 32.839 16.3228C32.6222 16.6564 32.2817 16.8902 31.8926 16.9727Z"
            fill="#FEF0E8"
            stroke="#FEF0E8"
            strokeWidth={0.5}
          />
        </g>
      </g>
    </g>
    <defs>
      <linearGradient
        id="paint0_linear_2511_23063"
        x1={28.1298}
        y1={3.70825}
        x2={28.1298}
        y2={50.3466}
        gradientUnits="userSpaceOnUse"
      >
        <stop offset={0.404167} stopColor="#FEF0E8" stopOpacity={0} />
        <stop offset={1} stopColor="#D7C9AD" stopOpacity={0.48} />
      </linearGradient>
      <clipPath id="clip0_2511_23063">
        <rect width={54} height={54} fill="white" />
      </clipPath>
      <clipPath id="clip1_2511_23063">
        <rect width={48} height={48} fill="white" transform="translate(3 3)" />
      </clipPath>
      <clipPath id="clip2_2511_23063">
        <rect
          width={24}
          height={24}
          fill="white"
          transform="translate(14.5559 6.56128) rotate(-11.9753)"
        />
      </clipPath>
    </defs>
  </svg>
);
export { SvgComponent as InvoiceIcon };
