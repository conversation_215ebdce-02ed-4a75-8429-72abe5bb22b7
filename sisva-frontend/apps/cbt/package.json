{"name": "cbt", "version": "0.0.0", "private": true, "type": "module", "scripts": {"lint": "eslint ./src", "lint:fix": "eslint ./src --fix", "typecheck": "tsc --noEmit", "dev": "vite --port=3004", "build": "vite build", "serve": "vite preview", "start": "vite"}, "devDependencies": {"@sisva/eslint-config": "workspace:*", "@sisva/typescript-config": "workspace:*", "@tanstack/eslint-plugin-router": "^1.114.12", "@tanstack/router-plugin": "^1.112.0", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@vitejs/plugin-react": "^4.3.2", "autoprefixer": "^10.4.20", "eslint": "^9.21.0", "postcss": "^8.5.3", "tailwindcss": "3", "vite": "^6.0.3", "vite-plugin-webfont-dl": "^3.10.4"}, "dependencies": {"@ant-design/icons": "^6.0.0", "@ant-design/v5-patch-for-react-19": "^1.0.3", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@hookform/error-message": "^2.0.1", "@hookform/resolvers": "^4.1.2", "@mantine/hooks": "^7.17.1", "@milkdown/crepe": "^7.6.4", "@milkdown/kit": "^7.6.4", "@milkdown/react": "^7.6.4", "@mui/icons-material": "^6.4.6", "@mui/material": "^6.4.6", "@mui/x-data-grid": "^7.27.2", "@mui/x-date-pickers": "^7.27.1", "@sisva/api": "workspace:*", "@sisva/hooks": "workspace:*", "@sisva/providers": "workspace:*", "@sisva/types": "workspace:*", "@sisva/ui": "workspace:*", "@sisva/utils": "workspace:*", "@syncfusion/ej2-base": "^26.0.0", "@t3-oss/env-core": "^0.12.0", "@tanstack/react-query": "^5.66.11", "@tanstack/react-query-devtools": "^5.66.11", "@tanstack/react-router": "^1.112.0", "@tanstack/router-devtools": "^1.112.0", "@untitled-ui/icons-react": "^0.1.4", "ahooks": "^3.8.4", "antd": "^5.24.2", "axios": "^1.8.1", "clsx": "^2.1.1", "currency.js": "^2.0.4", "dayjs": "^1.11.13", "dayjs-range-extend": "^1.0.4", "dompurify": "^3.2.4", "fast-equals": "^5.2.2", "fast-sort": "^3.4.1", "formik": "^2.4.6", "fuzzysort": "^3.1.0", "jotai": "^2.12.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.54.2", "react-hook-form-antd": "^1.1.3", "react-hook-form-mui": "^7.5.0", "react-hot-toast": "^2.5.2", "tailwind-merge": "^3.0.2", "ts-pattern": "^5.6.2", "valibot": "1.1.0", "xlsx": "^0.18.5", "yup": "^1.6.1"}}