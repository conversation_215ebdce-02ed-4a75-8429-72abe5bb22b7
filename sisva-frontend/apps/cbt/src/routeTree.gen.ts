/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { createFileRoute } from '@tanstack/react-router'

// Import Routes

import { Route as rootRoute } from './routes/__root'
import { Route as mainLayoutImport } from './routes/(main)/_layout'
import { Route as mainLayoutIndexImport } from './routes/(main)/_layout/index'
import { Route as authSigninIndexImport } from './routes/(auth)/signin/index'
import { Route as mainLayoutProfileIndexImport } from './routes/(main)/_layout/profile/index'
import { Route as mainLayoutExamsAdminAuthImport } from './routes/(main)/_layout/exams/_adminAuth'
import { Route as mainLayoutExamsAdminAuthIndexImport } from './routes/(main)/_layout/exams/_adminAuth.index'
import { Route as mainLayoutExamsExamidSessionAuthImport } from './routes/(main)/_layout/exams/$exam_id/_sessionAuth'
import { Route as mainLayoutExamsExamidAdminAuthImport } from './routes/(main)/_layout/exams/$exam_id/_adminAuth'
import { Route as mainLayoutExamsAdminAuthNewIndexImport } from './routes/(main)/_layout/exams/_adminAuth.new/index'
import { Route as mainLayoutExamsExamidAdminAuthIndexImport } from './routes/(main)/_layout/exams/$exam_id/_adminAuth.index'
import { Route as mainLayoutExamsExamidSessionAuthSessionIndexImport } from './routes/(main)/_layout/exams/$exam_id/_sessionAuth.session/index'

// Create Virtual Routes

const mainImport = createFileRoute('/(main)')()
const mainLayoutExamsImport = createFileRoute('/(main)/_layout/exams')()
const mainLayoutExamsExamidImport = createFileRoute(
  '/(main)/_layout/exams/$exam_id',
)()

// Create/Update Routes

const mainRoute = mainImport.update({
  id: '/(main)',
  getParentRoute: () => rootRoute,
} as any)

const mainLayoutRoute = mainLayoutImport.update({
  id: '/_layout',
  getParentRoute: () => mainRoute,
} as any)

const mainLayoutExamsRoute = mainLayoutExamsImport.update({
  id: '/exams',
  path: '/exams',
  getParentRoute: () => mainLayoutRoute,
} as any)

const mainLayoutIndexRoute = mainLayoutIndexImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => mainLayoutRoute,
} as any)

const authSigninIndexRoute = authSigninIndexImport.update({
  id: '/(auth)/signin/',
  path: '/signin/',
  getParentRoute: () => rootRoute,
} as any)

const mainLayoutExamsExamidRoute = mainLayoutExamsExamidImport.update({
  id: '/$exam_id',
  path: '/$exam_id',
  getParentRoute: () => mainLayoutExamsRoute,
} as any)

const mainLayoutProfileIndexRoute = mainLayoutProfileIndexImport.update({
  id: '/profile/',
  path: '/profile/',
  getParentRoute: () => mainLayoutRoute,
} as any)

const mainLayoutExamsAdminAuthRoute = mainLayoutExamsAdminAuthImport.update({
  id: '/_adminAuth',
  getParentRoute: () => mainLayoutExamsRoute,
} as any)

const mainLayoutExamsAdminAuthIndexRoute =
  mainLayoutExamsAdminAuthIndexImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => mainLayoutExamsAdminAuthRoute,
  } as any)

const mainLayoutExamsExamidSessionAuthRoute =
  mainLayoutExamsExamidSessionAuthImport.update({
    id: '/_sessionAuth',
    getParentRoute: () => mainLayoutExamsExamidRoute,
  } as any)

const mainLayoutExamsExamidAdminAuthRoute =
  mainLayoutExamsExamidAdminAuthImport.update({
    id: '/_adminAuth',
    getParentRoute: () => mainLayoutExamsExamidRoute,
  } as any)

const mainLayoutExamsAdminAuthNewIndexRoute =
  mainLayoutExamsAdminAuthNewIndexImport.update({
    id: '/new/',
    path: '/new/',
    getParentRoute: () => mainLayoutExamsAdminAuthRoute,
  } as any)

const mainLayoutExamsExamidAdminAuthIndexRoute =
  mainLayoutExamsExamidAdminAuthIndexImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => mainLayoutExamsExamidAdminAuthRoute,
  } as any)

const mainLayoutExamsExamidSessionAuthSessionIndexRoute =
  mainLayoutExamsExamidSessionAuthSessionIndexImport.update({
    id: '/session/',
    path: '/session/',
    getParentRoute: () => mainLayoutExamsExamidSessionAuthRoute,
  } as any)

// Populate the FileRoutesByPath interface

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/(main)': {
      id: '/(main)'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof mainImport
      parentRoute: typeof rootRoute
    }
    '/(main)/_layout': {
      id: '/(main)/_layout'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof mainLayoutImport
      parentRoute: typeof mainRoute
    }
    '/(auth)/signin/': {
      id: '/(auth)/signin/'
      path: '/signin'
      fullPath: '/signin'
      preLoaderRoute: typeof authSigninIndexImport
      parentRoute: typeof rootRoute
    }
    '/(main)/_layout/': {
      id: '/(main)/_layout/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof mainLayoutIndexImport
      parentRoute: typeof mainLayoutImport
    }
    '/(main)/_layout/exams': {
      id: '/(main)/_layout/exams'
      path: '/exams'
      fullPath: '/exams'
      preLoaderRoute: typeof mainLayoutExamsImport
      parentRoute: typeof mainLayoutImport
    }
    '/(main)/_layout/exams/_adminAuth': {
      id: '/(main)/_layout/exams/_adminAuth'
      path: '/exams'
      fullPath: '/exams'
      preLoaderRoute: typeof mainLayoutExamsAdminAuthImport
      parentRoute: typeof mainLayoutExamsRoute
    }
    '/(main)/_layout/profile/': {
      id: '/(main)/_layout/profile/'
      path: '/profile'
      fullPath: '/profile'
      preLoaderRoute: typeof mainLayoutProfileIndexImport
      parentRoute: typeof mainLayoutImport
    }
    '/(main)/_layout/exams/$exam_id': {
      id: '/(main)/_layout/exams/$exam_id'
      path: '/$exam_id'
      fullPath: '/exams/$exam_id'
      preLoaderRoute: typeof mainLayoutExamsExamidImport
      parentRoute: typeof mainLayoutExamsImport
    }
    '/(main)/_layout/exams/$exam_id/_adminAuth': {
      id: '/(main)/_layout/exams/$exam_id/_adminAuth'
      path: '/$exam_id'
      fullPath: '/exams/$exam_id'
      preLoaderRoute: typeof mainLayoutExamsExamidAdminAuthImport
      parentRoute: typeof mainLayoutExamsExamidRoute
    }
    '/(main)/_layout/exams/$exam_id/_sessionAuth': {
      id: '/(main)/_layout/exams/$exam_id/_sessionAuth'
      path: ''
      fullPath: '/exams/$exam_id'
      preLoaderRoute: typeof mainLayoutExamsExamidSessionAuthImport
      parentRoute: typeof mainLayoutExamsExamidImport
    }
    '/(main)/_layout/exams/_adminAuth/': {
      id: '/(main)/_layout/exams/_adminAuth/'
      path: '/'
      fullPath: '/exams/'
      preLoaderRoute: typeof mainLayoutExamsAdminAuthIndexImport
      parentRoute: typeof mainLayoutExamsAdminAuthImport
    }
    '/(main)/_layout/exams/$exam_id/_adminAuth/': {
      id: '/(main)/_layout/exams/$exam_id/_adminAuth/'
      path: '/'
      fullPath: '/exams/$exam_id/'
      preLoaderRoute: typeof mainLayoutExamsExamidAdminAuthIndexImport
      parentRoute: typeof mainLayoutExamsExamidAdminAuthImport
    }
    '/(main)/_layout/exams/_adminAuth/new/': {
      id: '/(main)/_layout/exams/_adminAuth/new/'
      path: '/new'
      fullPath: '/exams/new'
      preLoaderRoute: typeof mainLayoutExamsAdminAuthNewIndexImport
      parentRoute: typeof mainLayoutExamsAdminAuthImport
    }
    '/(main)/_layout/exams/$exam_id/_sessionAuth/session/': {
      id: '/(main)/_layout/exams/$exam_id/_sessionAuth/session/'
      path: '/session'
      fullPath: '/exams/$exam_id/session'
      preLoaderRoute: typeof mainLayoutExamsExamidSessionAuthSessionIndexImport
      parentRoute: typeof mainLayoutExamsExamidSessionAuthImport
    }
  }
}

// Create and export the route tree

interface mainLayoutExamsAdminAuthRouteChildren {
  mainLayoutExamsAdminAuthIndexRoute: typeof mainLayoutExamsAdminAuthIndexRoute
  mainLayoutExamsAdminAuthNewIndexRoute: typeof mainLayoutExamsAdminAuthNewIndexRoute
}

const mainLayoutExamsAdminAuthRouteChildren: mainLayoutExamsAdminAuthRouteChildren =
  {
    mainLayoutExamsAdminAuthIndexRoute: mainLayoutExamsAdminAuthIndexRoute,
    mainLayoutExamsAdminAuthNewIndexRoute:
      mainLayoutExamsAdminAuthNewIndexRoute,
  }

const mainLayoutExamsAdminAuthRouteWithChildren =
  mainLayoutExamsAdminAuthRoute._addFileChildren(
    mainLayoutExamsAdminAuthRouteChildren,
  )

interface mainLayoutExamsExamidAdminAuthRouteChildren {
  mainLayoutExamsExamidAdminAuthIndexRoute: typeof mainLayoutExamsExamidAdminAuthIndexRoute
}

const mainLayoutExamsExamidAdminAuthRouteChildren: mainLayoutExamsExamidAdminAuthRouteChildren =
  {
    mainLayoutExamsExamidAdminAuthIndexRoute:
      mainLayoutExamsExamidAdminAuthIndexRoute,
  }

const mainLayoutExamsExamidAdminAuthRouteWithChildren =
  mainLayoutExamsExamidAdminAuthRoute._addFileChildren(
    mainLayoutExamsExamidAdminAuthRouteChildren,
  )

interface mainLayoutExamsExamidSessionAuthRouteChildren {
  mainLayoutExamsExamidSessionAuthSessionIndexRoute: typeof mainLayoutExamsExamidSessionAuthSessionIndexRoute
}

const mainLayoutExamsExamidSessionAuthRouteChildren: mainLayoutExamsExamidSessionAuthRouteChildren =
  {
    mainLayoutExamsExamidSessionAuthSessionIndexRoute:
      mainLayoutExamsExamidSessionAuthSessionIndexRoute,
  }

const mainLayoutExamsExamidSessionAuthRouteWithChildren =
  mainLayoutExamsExamidSessionAuthRoute._addFileChildren(
    mainLayoutExamsExamidSessionAuthRouteChildren,
  )

interface mainLayoutExamsExamidRouteChildren {
  mainLayoutExamsExamidAdminAuthRoute: typeof mainLayoutExamsExamidAdminAuthRouteWithChildren
  mainLayoutExamsExamidSessionAuthRoute: typeof mainLayoutExamsExamidSessionAuthRouteWithChildren
}

const mainLayoutExamsExamidRouteChildren: mainLayoutExamsExamidRouteChildren = {
  mainLayoutExamsExamidAdminAuthRoute:
    mainLayoutExamsExamidAdminAuthRouteWithChildren,
  mainLayoutExamsExamidSessionAuthRoute:
    mainLayoutExamsExamidSessionAuthRouteWithChildren,
}

const mainLayoutExamsExamidRouteWithChildren =
  mainLayoutExamsExamidRoute._addFileChildren(
    mainLayoutExamsExamidRouteChildren,
  )

interface mainLayoutExamsRouteChildren {
  mainLayoutExamsAdminAuthRoute: typeof mainLayoutExamsAdminAuthRouteWithChildren
  mainLayoutExamsExamidRoute: typeof mainLayoutExamsExamidRouteWithChildren
}

const mainLayoutExamsRouteChildren: mainLayoutExamsRouteChildren = {
  mainLayoutExamsAdminAuthRoute: mainLayoutExamsAdminAuthRouteWithChildren,
  mainLayoutExamsExamidRoute: mainLayoutExamsExamidRouteWithChildren,
}

const mainLayoutExamsRouteWithChildren = mainLayoutExamsRoute._addFileChildren(
  mainLayoutExamsRouteChildren,
)

interface mainLayoutRouteChildren {
  mainLayoutIndexRoute: typeof mainLayoutIndexRoute
  mainLayoutExamsRoute: typeof mainLayoutExamsRouteWithChildren
  mainLayoutProfileIndexRoute: typeof mainLayoutProfileIndexRoute
}

const mainLayoutRouteChildren: mainLayoutRouteChildren = {
  mainLayoutIndexRoute: mainLayoutIndexRoute,
  mainLayoutExamsRoute: mainLayoutExamsRouteWithChildren,
  mainLayoutProfileIndexRoute: mainLayoutProfileIndexRoute,
}

const mainLayoutRouteWithChildren = mainLayoutRoute._addFileChildren(
  mainLayoutRouteChildren,
)

interface mainRouteChildren {
  mainLayoutRoute: typeof mainLayoutRouteWithChildren
}

const mainRouteChildren: mainRouteChildren = {
  mainLayoutRoute: mainLayoutRouteWithChildren,
}

const mainRouteWithChildren = mainRoute._addFileChildren(mainRouteChildren)

export interface FileRoutesByFullPath {
  '/': typeof mainLayoutIndexRoute
  '/signin': typeof authSigninIndexRoute
  '/exams': typeof mainLayoutExamsAdminAuthRouteWithChildren
  '/profile': typeof mainLayoutProfileIndexRoute
  '/exams/$exam_id': typeof mainLayoutExamsExamidSessionAuthRouteWithChildren
  '/exams/': typeof mainLayoutExamsAdminAuthIndexRoute
  '/exams/$exam_id/': typeof mainLayoutExamsExamidAdminAuthIndexRoute
  '/exams/new': typeof mainLayoutExamsAdminAuthNewIndexRoute
  '/exams/$exam_id/session': typeof mainLayoutExamsExamidSessionAuthSessionIndexRoute
}

export interface FileRoutesByTo {
  '/signin': typeof authSigninIndexRoute
  '/': typeof mainLayoutIndexRoute
  '/exams': typeof mainLayoutExamsAdminAuthIndexRoute
  '/profile': typeof mainLayoutProfileIndexRoute
  '/exams/$exam_id': typeof mainLayoutExamsExamidAdminAuthIndexRoute
  '/exams/new': typeof mainLayoutExamsAdminAuthNewIndexRoute
  '/exams/$exam_id/session': typeof mainLayoutExamsExamidSessionAuthSessionIndexRoute
}

export interface FileRoutesById {
  __root__: typeof rootRoute
  '/(main)': typeof mainRouteWithChildren
  '/(main)/_layout': typeof mainLayoutRouteWithChildren
  '/(auth)/signin/': typeof authSigninIndexRoute
  '/(main)/_layout/': typeof mainLayoutIndexRoute
  '/(main)/_layout/exams': typeof mainLayoutExamsRouteWithChildren
  '/(main)/_layout/exams/_adminAuth': typeof mainLayoutExamsAdminAuthRouteWithChildren
  '/(main)/_layout/profile/': typeof mainLayoutProfileIndexRoute
  '/(main)/_layout/exams/$exam_id': typeof mainLayoutExamsExamidRouteWithChildren
  '/(main)/_layout/exams/$exam_id/_adminAuth': typeof mainLayoutExamsExamidAdminAuthRouteWithChildren
  '/(main)/_layout/exams/$exam_id/_sessionAuth': typeof mainLayoutExamsExamidSessionAuthRouteWithChildren
  '/(main)/_layout/exams/_adminAuth/': typeof mainLayoutExamsAdminAuthIndexRoute
  '/(main)/_layout/exams/$exam_id/_adminAuth/': typeof mainLayoutExamsExamidAdminAuthIndexRoute
  '/(main)/_layout/exams/_adminAuth/new/': typeof mainLayoutExamsAdminAuthNewIndexRoute
  '/(main)/_layout/exams/$exam_id/_sessionAuth/session/': typeof mainLayoutExamsExamidSessionAuthSessionIndexRoute
}

export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/'
    | '/signin'
    | '/exams'
    | '/profile'
    | '/exams/$exam_id'
    | '/exams/'
    | '/exams/$exam_id/'
    | '/exams/new'
    | '/exams/$exam_id/session'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/signin'
    | '/'
    | '/exams'
    | '/profile'
    | '/exams/$exam_id'
    | '/exams/new'
    | '/exams/$exam_id/session'
  id:
    | '__root__'
    | '/(main)'
    | '/(main)/_layout'
    | '/(auth)/signin/'
    | '/(main)/_layout/'
    | '/(main)/_layout/exams'
    | '/(main)/_layout/exams/_adminAuth'
    | '/(main)/_layout/profile/'
    | '/(main)/_layout/exams/$exam_id'
    | '/(main)/_layout/exams/$exam_id/_adminAuth'
    | '/(main)/_layout/exams/$exam_id/_sessionAuth'
    | '/(main)/_layout/exams/_adminAuth/'
    | '/(main)/_layout/exams/$exam_id/_adminAuth/'
    | '/(main)/_layout/exams/_adminAuth/new/'
    | '/(main)/_layout/exams/$exam_id/_sessionAuth/session/'
  fileRoutesById: FileRoutesById
}

export interface RootRouteChildren {
  mainRoute: typeof mainRouteWithChildren
  authSigninIndexRoute: typeof authSigninIndexRoute
}

const rootRouteChildren: RootRouteChildren = {
  mainRoute: mainRouteWithChildren,
  authSigninIndexRoute: authSigninIndexRoute,
}

export const routeTree = rootRoute
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()

/* ROUTE_MANIFEST_START
{
  "routes": {
    "__root__": {
      "filePath": "__root.tsx",
      "children": [
        "/(main)",
        "/(auth)/signin/"
      ]
    },
    "/(main)": {
      "filePath": "(main)",
      "children": [
        "/(main)/_layout"
      ]
    },
    "/(main)/_layout": {
      "filePath": "(main)/_layout.tsx",
      "parent": "/(main)",
      "children": [
        "/(main)/_layout/",
        "/(main)/_layout/exams",
        "/(main)/_layout/profile/"
      ]
    },
    "/(auth)/signin/": {
      "filePath": "(auth)/signin/index.tsx"
    },
    "/(main)/_layout/": {
      "filePath": "(main)/_layout/index.tsx",
      "parent": "/(main)/_layout"
    },
    "/(main)/_layout/exams": {
      "filePath": "(main)/_layout/exams",
      "parent": "/(main)/_layout",
      "children": [
        "/(main)/_layout/exams/_adminAuth",
        "/(main)/_layout/exams/$exam_id"
      ]
    },
    "/(main)/_layout/exams/_adminAuth": {
      "filePath": "(main)/_layout/exams/_adminAuth.tsx",
      "parent": "/(main)/_layout/exams",
      "children": [
        "/(main)/_layout/exams/_adminAuth/",
        "/(main)/_layout/exams/_adminAuth/new/"
      ]
    },
    "/(main)/_layout/profile/": {
      "filePath": "(main)/_layout/profile/index.tsx",
      "parent": "/(main)/_layout"
    },
    "/(main)/_layout/exams/$exam_id": {
      "filePath": "(main)/_layout/exams/$exam_id",
      "parent": "/(main)/_layout/exams",
      "children": [
        "/(main)/_layout/exams/$exam_id/_adminAuth",
        "/(main)/_layout/exams/$exam_id/_sessionAuth"
      ]
    },
    "/(main)/_layout/exams/$exam_id/_adminAuth": {
      "filePath": "(main)/_layout/exams/$exam_id/_adminAuth.tsx",
      "parent": "/(main)/_layout/exams/$exam_id",
      "children": [
        "/(main)/_layout/exams/$exam_id/_adminAuth/"
      ]
    },
    "/(main)/_layout/exams/$exam_id/_sessionAuth": {
      "filePath": "(main)/_layout/exams/$exam_id/_sessionAuth.tsx",
      "parent": "/(main)/_layout/exams/$exam_id",
      "children": [
        "/(main)/_layout/exams/$exam_id/_sessionAuth/session/"
      ]
    },
    "/(main)/_layout/exams/_adminAuth/": {
      "filePath": "(main)/_layout/exams/_adminAuth.index.tsx",
      "parent": "/(main)/_layout/exams/_adminAuth"
    },
    "/(main)/_layout/exams/$exam_id/_adminAuth/": {
      "filePath": "(main)/_layout/exams/$exam_id/_adminAuth.index.tsx",
      "parent": "/(main)/_layout/exams/$exam_id/_adminAuth"
    },
    "/(main)/_layout/exams/_adminAuth/new/": {
      "filePath": "(main)/_layout/exams/_adminAuth.new/index.tsx",
      "parent": "/(main)/_layout/exams/_adminAuth"
    },
    "/(main)/_layout/exams/$exam_id/_sessionAuth/session/": {
      "filePath": "(main)/_layout/exams/$exam_id/_sessionAuth.session/index.tsx",
      "parent": "/(main)/_layout/exams/$exam_id/_sessionAuth"
    }
  }
}
ROUTE_MANIFEST_END */
