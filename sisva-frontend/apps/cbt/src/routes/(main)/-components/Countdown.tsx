import { useInterval } from "ahooks";
import { useRef, useState } from "react";

interface CountdownProps {
  duration: number; // Countdown duration in seconds
  onComplete?: () => void; // Callback when countdown reaches 0 (with 1s delay)
}

/**
 * @param duration - Countdown duration in seconds
 */

export function Countdown({ duration, onComplete }: CountdownProps) {
  const [timeLeft, setTimeLeft] = useState(duration);
  const onCompleteHasRunRef = useRef(false);

  useInterval(() => {
    if (timeLeft > 0) {
      setTimeLeft((prev) => prev - 1);
    } else {
      if (!onCompleteHasRunRef.current) onComplete?.();
      onCompleteHasRunRef.current = true;
    }
  }, 1000);

  return timeLeft;
}
