import { DotsVertical } from "@untitled-ui/icons-react";
import { Popover, theme } from "antd";
import type { ReactNode } from "react";

export function GroupHeader({
  text,
  popoverContent,
}: {
  text: string;
  popoverContent?: ReactNode;
}) {
  const token = theme.useToken().token;

  return (
    <div className="relative w-full px-4 py-3 rounded-md flex justify-between bg-neutral-300">
      <div
        className="absolute left-0 top-1/2 transform -translate-y-1/2 w-[3px] h-5 rounded-r-full"
        style={{
          backgroundColor: token.colorPrimary,
        }}
      />
      <div className="flex items-center gap-2">
        <span className="font-medium text-neutral-800">{text}</span>
      </div>
      {popoverContent && (
        <Popover trigger="click" content={popoverContent}>
          <DotsVertical
            width={20}
            height={20}
            className="text-neutral-700 cursor-pointer"
          />
        </Popover>
      )}
    </div>
  );
}
