import { useDeleteSessionDataFromCookies } from "@sisva/hooks/query/useAuth";
import {
  useCurrentUser,
  useNotificationAPI,
  useSchool,
} from "@sisva/providers";
import { Image } from "@sisva/ui";
import { Link, useMatchRoute, useNavigate } from "@tanstack/react-router";
import { File06, Home05, LogOut04, User01 } from "@untitled-ui/icons-react";
import { useToggle } from "ahooks";
import { Modal, theme } from "antd";
import { atom, useAtom } from "jotai";
import type { ReactNode } from "react";

export const navbarRightElementAtom = atom<React.ReactNode>(null);
export const navbarMiddleElementAtom = atom<React.ReactNode>(null);

export default function NavBar() {
  const currentUser = useCurrentUser();
  const school = useSchool();
  const token = theme.useToken().token;

  const [rightElement] = useAtom(navbarRightElementAtom);
  const [middleElement] = useAtom(navbarMiddleElementAtom);

  const matchRoute = useMatchRoute();

  function getHomeColor() {
    if (matchRoute({ to: "/" })) return token.colorPrimaryActive;
    return token.colorPrimary;
  }

  function getUserColor() {
    if (matchRoute({ to: "/profile" }) || matchRoute({ to: "/profile" }))
      return token.colorPrimaryActive;
    return token.colorPrimary;
  }

  const studentNavbarItems = (
    <>
      <Link
        className="flex flex-col items-center font-medium"
        to="/"
        style={{
          color: getHomeColor(),
        }}
      >
        <Home05 fill={getHomeColor()} />
        <div>Beranda</div>
      </Link>
      <Link
        className="flex flex-col items-center font-medium"
        to="/profile"
        style={{
          color: getUserColor(),
        }}
      >
        <User01 fill={getUserColor()} />
        <div>Profil</div>
      </Link>
    </>
  );

  const adminNavbarItems = (
    <>
      <Link
        className="flex flex-col items-center font-medium"
        to="/exams"
        style={{
          color: token.colorPrimary,
        }}
      >
        <File06 />
        <div>Daftar Ujian</div>
      </Link>
      <LogoutButton
        renderTrigger={(onClick) => {
          return (
            <div
              className="flex flex-col items-center font-medium cursor-pointer"
              onClick={onClick}
              style={{
                color: token.colorPrimary,
              }}
            >
              <LogOut04 />
              <div>Keluar</div>
            </div>
          );
        }}
      />
    </>
  );

  const navbarItems =
    currentUser.type === "student" ? studentNavbarItems : adminNavbarItems;

  return (
    <div className="h-24 z-20 relative min-h-24 sm:shadow-md flex items-center justify-between px-8 shadow-[0_-4px_6px_-1px_rgba(0,0,0,0.1)] sm:rounded-t-none rounded-t-2xl">
      <div className="absolute top-1/2 -translate-y-1/2 flex items-center gap-4 left-1/2 -translate-x-1/2">
        {middleElement}
      </div>
      <div className="sm:flex items-center gap-6 hidden">
        <div className="h-16 w-16 relative">
          <Link to="/">
            <Image
              alt="Logo sekolah"
              fill
              className="object-contain"
              src={school.logo_url || ""}
            />
          </Link>
        </div>
        <div className="font-bold text-lg">{school.name}</div>
      </div>
      <div className="flex items-center gap-8 sm:justify-end justify-evenly flex-1">
        {rightElement ? rightElement : navbarItems}
      </div>
    </div>
  );
}

function LogoutButton({
  renderTrigger,
}: {
  renderTrigger?: (onClick: () => void) => ReactNode;
}) {
  const notification = useNotificationAPI();
  const school = useSchool();

  const navigate = useNavigate();

  const { mutate: deleteSessionDataFromCookies } =
    useDeleteSessionDataFromCookies({
      onSuccess() {
        notification.success({
          message: "Berhasil keluar dari akun",
        });
        navigate({ to: "/signin", search: { school_code: school.code } });
      },
    });

  const [open, { toggle }] = useToggle();
  return (
    <>
      {renderTrigger?.(toggle)}
      <Modal
        title="Keluar dari akun?"
        open={open}
        onOk={() => deleteSessionDataFromCookies()}
        onCancel={toggle}
      ></Modal>
    </>
  );
}
