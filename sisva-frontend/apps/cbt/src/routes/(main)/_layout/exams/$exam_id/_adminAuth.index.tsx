import { createFileRoute } from "@tanstack/react-router";
import { number, object, parse, pipe, transform, unknown } from "valibot";

import ExamDetailPage from "./-components/ExamDetailPage";

export const Route = createFileRoute(
  "/(main)/_layout/exams/$exam_id/_adminAuth/"
)({
  params: {
    parse(params) {
      const parsedParam = parse(
        object({
          exam_id: pipe(unknown(), transform(Number), number()),
        }),
        params
      );
      return parsedParam;
    },
  },
  head() {
    return {
      meta: [
        {
          title: "<PERSON>jian | Sisva",
        },
      ],
    };
  },
  component: ExamDetailPage,
});
