import { Link } from "@tanstack/react-router";
import { useBoolean } from "ahooks";
import { Button, Modal } from "antd";
import type { ReactNode } from "react";

export default function ExitButton({
  renderTrigger,
}: {
  renderTrigger?: (onClick: () => void) => ReactNode;
}) {
  const [open, { toggle }] = useBoolean();

  const app = document.getElementById("app");

  return (
    <>
      {renderTrigger ? (
        renderTrigger(toggle)
      ) : (
        <Button
          onClick={() => {
            toggle();
          }}
          variant="solid"
          color="primary"
        >
          Keluar
        </Button>
      )}
      <Modal
        getContainer={app ?? undefined}
        title="Keluar Ujian"
        open={open}
        onCancel={() => {
          toggle();
        }}
        footer={null}
      >
        <div>
          Apakah Anda yakin ingin meninggalkan halaman ujian? Semua jawaban Anda
          akan disimpan
        </div>
        <div className="pt-4 flex flex-col gap-4">
          <div className="flex justify-end w-full gap-3">
            <Button
              onClick={() => {
                toggle();
              }}
            >
              Batal
            </Button>
            <Link to="/">
              <Button variant="solid" color="primary">
                Keluar
              </Button>
            </Link>
          </div>
        </div>
      </Modal>
    </>
  );
}
