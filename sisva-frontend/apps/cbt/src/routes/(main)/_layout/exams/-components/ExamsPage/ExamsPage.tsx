import { useExamsWithClasses } from "@sisva/hooks/query/exam/useExams";
import { unique } from "@sisva/utils";
import { Link } from "@tanstack/react-router";
import { Edit03, Plus, Trash01 } from "@untitled-ui/icons-react";
import {
  Button,
  DatePicker,
  Form,
  Input,
  Select,
  Table,
  type TableProps,
  Tag,
  theme,
} from "antd";
import dayjs from "dayjs";
import { type ISortBy, sort } from "fast-sort";
import fuzzysort from "fuzzysort";
import { useForm } from "react-hook-form";
import { FormItem } from "react-hook-form-antd";

import DeleteExamButton from "../DeleteExamButton";

function fastSortComparator<T>(getValueFn: ISortBy<T>) {
  return (a: T, b: T) => {
    const result = sort([a, b]).asc(getValueFn);
    return result[0] === a ? -1 : 1;
  };
}

export default function ExamsPage() {
  const { data: exams = [] } = useExamsWithClasses();
  const token = theme.useToken().token;

  const { control, watch, setValue } = useForm({
    defaultValues: {
      saerchText: "",
      studentGroupName: null,
      subjectName: null,
      date_range: null,
    },
  });

  const defferedText = watch("saerchText");
  const studentGroupName = watch("studentGroupName");
  const subjectName = watch("subjectName");
  const date_range = watch("date_range");

  const start_date_filter = date_range?.[0] ?? null;
  const end_date_filter = date_range?.[1] ?? null;

  const filteredExamsByDate = exams.filter((exam) => {
    if (start_date_filter && end_date_filter) {
      const startTime = dayjs(exam.start_time, "DD/MM/YYYY h:mm A Z");
      return (
        startTime.isBefore(end_date_filter) &&
        startTime.isAfter(start_date_filter)
      );
    }
    return true;
  });

  const studentGroupNames = unique(
    filteredExamsByDate
      .map((exam) => {
        return exam.classes.map((class_) => {
          return class_.student_group_name;
        });
      })
      .flat()
  );

  const subjectNames = unique(
    filteredExamsByDate
      .map((exam) => {
        return exam.classes.map((class_) => {
          return class_.subject_name;
        });
      })
      .flat()
  );

  const filteredExams = fuzzysort
    .go(
      defferedText,
      filteredExamsByDate
        .filter((exam) => {
          return (
            !studentGroupName ||
            exam.classes
              .map((class_) => {
                return class_.student_group_name;
              })
              .includes(studentGroupName)
          );
        })
        .filter((exam) => {
          return (
            !subjectName ||
            exam.classes
              .map((class_) => {
                return class_.subject_name;
              })
              .includes(subjectName)
          );
        }),
      {
        keys: ["name"],
        all: !defferedText,
      }
    )
    .map((result) => result.obj);

  const columns: TableProps<(typeof filteredExams)[number]>["columns"] = [
    {
      title: "Nama Ujian",
      minWidth: 150,
      sorter: fastSortComparator((item) => item.name),
      dataIndex: "name",
    },
    {
      title: "Kelas",
      dataIndex: "class_ids",
      render(_, record) {
        return record.classes.map((class_) => (
          <Tag color={token.colorPrimaryText} key={class_.id}>
            {class_.student_group_name}
          </Tag>
        ));
      },
    },
    {
      title: "Mata Pelajaran",
      minWidth: 200,
      dataIndex: "class_ids",
      sorter: fastSortComparator((item) => item.classes[0]?.subject_name),
      render(_, record) {
        return record.classes[0] && record.classes[0].subject_name;
      },
    },
    {
      key: "date",
      title: "Tanggal",
      dataIndex: "start_time",
      minWidth: 120,
      defaultSortOrder: "descend",
      sorter: fastSortComparator((item) =>
        dayjs(item.start_time, "DD/MM/YYYY h:mm A Z").toDate()
      ),
      render(_, record) {
        return dayjs(record.start_time, "DD/MM/YYYY h:mm A Z").format(
          "DD/MM/YYYY"
        );
      },
    },
    {
      key: "startTime",
      minWidth: 100,
      title: "Jam Mulai",
      sorter: fastSortComparator((item) => {
        const time = dayjs(item.start_time, "DD/MM/YYYY h:mm A Z");
        return dayjs()
          .set("hour", time.hour())
          .set("minute", time.minute())
          .set("second", 0)
          .toDate();
      }),
      dataIndex: "start_time",
      render(_, record) {
        return dayjs(record.start_time, "DD/MM/YYYY h:mm A Z").format("HH:MM");
      },
    },
    {
      minWidth: 100,
      title: "Jam Selesai",
      dataIndex: "end_time",
      sorter: fastSortComparator((item) => {
        const time = dayjs(item.end_time, "DD/MM/YYYY h:mm A Z");
        return dayjs()
          .set("hour", time.hour())
          .set("minute", time.minute())
          .set("second", 0)
          .toDate();
      }),
      render(_, record) {
        return dayjs(record.end_time, "DD/MM/YYYY h:mm A Z").format("HH:MM");
      },
    },
    {
      title: "Status",
      minWidth: 100,
      render(_, record) {
        const startTime = dayjs(record.start_time, "DD/MM/YYYY h:mm A Z");
        const endTime = dayjs(record.end_time, "DD/MM/YYYY h:mm A Z");
        if (startTime.isAfter(dayjs())) return <Tag>Belum dimulai</Tag>;
        if (startTime.isBefore(dayjs()) && endTime.isAfter(dayjs()))
          return <Tag color="warning">Sedang berlangsung</Tag>;
        if (endTime.isBefore(dayjs()))
          return <Tag color="success">Selesai</Tag>;
      },
    },
    {
      title: "Aksi",
      minWidth: 100,
      render(_, record) {
        return (
          <div className="flex gap-2">
            <Link to="/exams/$exam_id" params={{ exam_id: record.id }}>
              <Button variant="outlined" icon={<Edit03 width={20} />}></Button>
            </Link>
            <DeleteExamButton
              exam_id={record.id}
              renderTrigger={(onClick) => {
                return (
                  <Button
                    onClick={onClick}
                    variant="outlined"
                    color="danger"
                    icon={<Trash01 width={20} />}
                  ></Button>
                );
              }}
            />
          </div>
        );
      },
    },
  ];

  return (
    <div className="flex flex-col items-center px-4 grow">
      <div className="size-full max-w-5xl py-8 flex flex-col gap-4 thick-scrollbar">
        <div className="flex justify-between">
          <h2>Daftar Ujian</h2>
          <Link to="/exams/new">
            <Button icon={<Plus width={20} />} color="primary" variant="solid">
              Buat
            </Button>
          </Link>
        </div>
        <div className="flex gap-4 justify-between overflow-auto pb-2">
          <Form className="contents">
            <div className="min-w-40">
              <FormItem
                control={control}
                name="saerchText"
                className="contents"
              >
                <Input placeholder="Cari ujian" />
              </FormItem>
            </div>
            <div className="flex gap-4">
              <div className="min-w-56">
                <FormItem
                  control={control}
                  name="date_range"
                  className="contents"
                >
                  <DatePicker.RangePicker
                    format="DD/MM/YYYY"
                    onChange={() => {
                      setValue("studentGroupName", null);
                    }}
                    className="w-full"
                  />
                </FormItem>
              </div>
              <div className="min-w-48">
                <FormItem
                  control={control}
                  name="subjectName"
                  className="contents"
                >
                  <Select
                    placeholder="Mata pelajaran"
                    allowClear
                    options={subjectNames.map((name) => {
                      return {
                        label: name,
                        value: name,
                      };
                    })}
                  />
                </FormItem>
              </div>
              <div className="min-w-40">
                <FormItem
                  control={control}
                  name="studentGroupName"
                  className="contents"
                >
                  <Select
                    placeholder="Kelas"
                    allowClear
                    options={studentGroupNames.map((name) => {
                      return {
                        label: name,
                        value: name,
                      };
                    })}
                  />
                </FormItem>
              </div>
            </div>
          </Form>
        </div>
        <Table
          tableLayout="auto"
          dataSource={filteredExams}
          columns={columns}
          rowKey={(exam) => {
            return exam.id;
          }}
          bordered
          scroll={{ y: "50svh", x: "auto" }}
        />
      </div>
    </div>
  );
}
