import { valibotResolver } from "@hookform/resolvers/valibot";
import {
  useClass,
  useClassesWithSubjectAndTeacherAndStudentGroup,
} from "@sisva/hooks/query/academic/useClasses";
import { useSelectedPeriod } from "@sisva/hooks/query/academic/usePeriods";
import {
  useCreateTeachingPlan,
  useTeachingPlans,
} from "@sisva/hooks/query/classroom/useTeachingPlans";
import {
  useCreateExam,
  useExamWithClasses,
  useUpdateExam,
} from "@sisva/hooks/query/exam/useExams";
import { useNotificationAPI } from "@sisva/providers";
import { examTypeOptions } from "@sisva/types/dropdownOptions";
import type { ExamType } from "@sisva/types/types";
import { unique, uniqueBy } from "@sisva/utils";
import { Link, useNavigate } from "@tanstack/react-router";
import {
  Button,
  DatePicker,
  Form,
  Input,
  InputNumber,
  Select,
  Switch,
  TimePicker,
} from "antd";
import type { Dayjs } from "dayjs";
import dayjs from "dayjs";
import { useForm } from "react-hook-form";
import { FormItem } from "react-hook-form-antd";
import {
  array,
  boolean,
  custom,
  forward,
  includes,
  type InferInput,
  type InferOutput,
  literal,
  minLength,
  minValue,
  nonEmpty,
  nullable,
  number,
  object,
  optional,
  partialCheck,
  pipe,
  string,
  transform,
  union,
  url,
} from "valibot";

const quiz: ExamType = "quiz";
const survey: ExamType = "survey";
const external: ExamType = "external";

function dayjsSchema(message: string | undefined = undefined) {
  return custom<Dayjs>((input) => {
    if (!input) return false;
    return dayjs.isDayjs(input);
  }, message);
}

const schema = ({ allowPastStartTime } = { allowPastStartTime: false }) =>
  pipe(
    object({
      name: pipe(string(), nonEmpty("Nama ujian harus diisi")),
      description: optional(string()),
      entry_code: pipe(string(), nonEmpty("Kode ujian harus diisi")),
      student_group_ids: pipe(
        array(number()),
        minLength(1, "Kelas harus dipilih")
      ),
      subject_id: pipe(
        nullable(number()),
        number("Mata pelajaran harus dipilih")
      ),
      is_anti_cheat: boolean(),
      is_shuffle_question: boolean(),
      display_question_num: pipe(
        number(),
        minValue(1, "Jumlah soal harus lebih dari 0")
      ),
      type: union([literal(quiz), literal(survey), literal(external)]),
      date: pipe(
        nullable(dayjsSchema()),
        dayjsSchema("Tanggal ujian harus diisi")
      ),
      clock: pipe(
        nullable(array(dayjsSchema())),
        array(dayjsSchema(), "Jam harus diisi"),
        minLength(2)
      ),
      external_link: optional(
        union([
          pipe(
            literal(""),
            transform(() => undefined)
          ),
          pipe(
            string(),
            nonEmpty("Link harus diisi"),
            url("Link tidak valid"),
            includes("google.com", "Link harus berupa link google form")
          ),
        ])
      ),
    }),
    forward(
      partialCheck(
        [["type"], ["external_link"]],
        ({ type, external_link }) => {
          if (type === "external" && !external_link) return false;
          return true;
        },
        "Link harus diisi"
      ),
      ["external_link"]
    ),
    forward(
      partialCheck(
        [["clock"], ["date"]],
        ({ clock, date }) => {
          if (allowPastStartTime) return true;
          const startClock = clock[0]!;

          const start_time = date
            .set("hour", startClock.hour())
            .set("minute", startClock.minute())
            .set("second", 0);

          if (dayjs().isAfter(start_time)) return false;

          return true;
        },
        "Tanggal dan/atau jam ujian telah lewat"
      ),
      ["date"]
    )
  );

type SchemaOutput = InferOutput<ReturnType<typeof schema>>;
type SchemaInput = InferInput<ReturnType<typeof schema>>;

export default function ExamForm({
  exam_id,
  class_id,
  onCancelButton,
  onEditSuccess,
  teaching_plan_ids,
}: {
  exam_id?: number;
  class_id?: number;
  onCancelButton?: () => void;
  onEditSuccess?: () => void;
  teaching_plan_ids?: number[];
}) {
  const notification = useNotificationAPI();
  const navigate = useNavigate();
  const { data: selectedPeriod } = useSelectedPeriod();
  const { data: classes = [] } =
    useClassesWithSubjectAndTeacherAndStudentGroup();
  const { data: exam } = useExamWithClasses({ exam_id });
  const { data: class_ } = useClass(class_id);

  const { data: teachingPlans = [] } = useTeachingPlans();
  const filteredTeachingPlans = teachingPlans.filter((teachingPlan) =>
    teaching_plan_ids?.includes(teachingPlan.id)
  );
  const class_ids = unique(filteredTeachingPlans.map((plan) => plan.class_id));
  const classesFromTeachingPlanIdsProp = classes.filter((class_) =>
    class_ids.includes(class_.id)
  );

  const hasStarted = exam
    ? dayjs(exam?.start_time, "DD/MM/YYYY h:mm A Z").isBefore(dayjs())
    : false;

  const { control, handleSubmit, watch } = useForm<
    SchemaInput,
    unknown,
    SchemaOutput
  >({
    values: {
      name: exam?.name ?? "",
      description: exam?.description ?? "",
      entry_code: exam?.entry_code ?? "",
      student_group_ids: (() => {
        if (exam) {
          return exam?.classes.map((item) => item.student_group_id);
        }
        if (teaching_plan_ids) {
          return classesFromTeachingPlanIdsProp.map(
            (item) => item.student_group_id
          );
        }
        if (class_) {
          return [class_.student_group_id];
        }
        return [];
      })(),
      subject_id:
        exam?.classes[0]?.subject_id ??
        classesFromTeachingPlanIdsProp[0]?.subject_id ??
        class_?.subject_id ??
        null,
      is_anti_cheat: exam?.is_anti_cheat ?? false,
      is_shuffle_question: exam?.is_shuffle_question ?? false,
      display_question_num: exam?.display_question_num ?? 0,
      external_link: exam?.external_link ?? "",
      type: exam?.type ?? quiz,
      date: exam?.start_time
        ? dayjs(exam.start_time, "DD/MM/YYYY h:mm A Z")
        : null,
      clock: exam
        ? [
            dayjs(exam.start_time, "DD/MM/YYYY h:mm A Z"),
            dayjs(exam.end_time, "DD/MM/YYYY h:mm A Z"),
          ]
        : null,
    },
    resolver: valibotResolver(
      schema({
        allowPastStartTime: !!exam_id && hasStarted,
      })
    ),
  });

  const student_group_ids = watch("student_group_ids");
  const subject_id = watch("subject_id");
  const type = watch("type");

  const availableStudentGroups = uniqueBy(
    classes
      .map((class_) => class_.student_group)
      .filter((item) => item !== undefined)
      .filter((studentGroup) =>
        exam_id ? true : studentGroup.period_id === selectedPeriod?.id
      ),
    (item) => item.id
  );

  const availableSubjects = uniqueBy(
    classes
      .filter((class_) => student_group_ids.includes(class_.student_group_id))
      .map((class_) => class_.subject)
      .filter((item) => item !== undefined),
    (item) => item.id
  );

  const { mutate: createExam } = useCreateExam();
  const { mutate: updateExam } = useUpdateExam();
  const { mutateAsync: createTeachingPlan } = useCreateTeachingPlan();

  const onFinish = handleSubmit(async (value) => {
    const class_ids = classes
      .filter(
        (class_) =>
          student_group_ids.includes(class_.student_group_id) &&
          class_.subject_id === subject_id
      )
      .map((class_) => class_.id);

    const teachingPlanIds: number[] =
      exam?.teaching_plan_ids ?? teaching_plan_ids ?? [];
    if (!exam_id && !teaching_plan_ids?.length) {
      for (const class_id of class_ids) {
        const data = await createTeachingPlan(
          {
            class_id,
            title: `Rencana pembelajaran untuk ujian ${value.name}`,
            markdown: "",
            teaching_materials: [],
            tasks: [],
            teaching_goal: "",
            teaching_activity: "",
            teaching_scoring: "",
          },
          {
            onSuccess() {
              console.info("Rencana pembelajaran berhasil dibuat");
            },
            onError() {
              console.error("Rencana pembelajaran gagal dibuat");
            },
          }
        );
        teachingPlanIds.push(data.teaching_plan_id);
      }
    }

    const startClock = value.clock[0]!;
    const endClock = value.clock[1]!;

    const payload = {
      name: value.name,
      description: value.description ?? "",
      entry_code: value.entry_code,
      teaching_plan_ids: teachingPlanIds,
      is_anti_cheat: value.is_anti_cheat,
      is_shuffle_question: value.is_shuffle_question,
      display_question_num: value.display_question_num,
      type: value.type,
      start_time: value.date
        .set("hour", startClock.hour())
        .set("minute", startClock.minute())
        .set("second", 0)
        .format("DD/MM/YYYY h:mm A Z"),
      end_time: value.date
        .set("hour", endClock.hour())
        .set("minute", endClock.minute())
        .set("second", 0)
        .format("DD/MM/YYYY h:mm A Z"),
      external_link: value.external_link,
    };

    if (exam_id) {
      updateExam(
        {
          data: payload,
          exam_id,
        },
        {
          onSuccess() {
            notification.success({ message: "Ujian berhasil diperbarui" });
            onEditSuccess?.();
          },
          onError() {
            notification.error({ message: "Ujian gagal diperbarui" });
          },
        }
      );
      return;
    }

    createExam(payload, {
      onSuccess({ exam_id }) {
        notification.success({ message: "Ujian berhasil dibuat" });
        navigate({
          to: "/exams/$exam_id",
          params: { exam_id },
        });
      },
      onError() {
        notification.error({ message: "Ujian gagal dibuat" });
      },
    });
  });

  return (
    <Form layout="vertical" onFinish={onFinish}>
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        <FormItem control={control} name="name" label="Nama Ujian">
          <Input />
        </FormItem>
        <FormItem
          control={control}
          name="type"
          label="Jenis Ujian"
          disabled={!!exam_id}
        >
          <Select options={examTypeOptions} />
        </FormItem>
        <FormItem
          control={control}
          name="student_group_ids"
          label="Kelas"
          disabled={!!exam_id}
        >
          <Select
            mode="multiple"
            options={availableStudentGroups.map((item) => ({
              label: item.name,
              value: item.id,
            }))}
            open={teaching_plan_ids?.length ? false : undefined}
          />
        </FormItem>
        <FormItem
          control={control}
          name="subject_id"
          label="Mata Pelajaran"
          disabled={!!exam_id}
        >
          <Select
            notFoundContent={
              student_group_ids ? undefined : "Pilih kelas terlebih dahulu"
            }
            options={availableSubjects.map((item) => ({
              label: item.name,
              value: item.id,
            }))}
            open={teaching_plan_ids?.length ? false : undefined}
          />
        </FormItem>
        <FormItem
          control={control}
          name="date"
          label="Tanggal Ujian"
          disabled={!!exam_id && hasStarted}
        >
          <DatePicker className="w-full" />
        </FormItem>

        <FormItem
          control={control}
          name="clock"
          label="Jam"
          disabled={!!exam_id && hasStarted}
        >
          <TimePicker.RangePicker
            placeholder={["Jam Mulai", "Jam Selesai"]}
            format="HH:mm"
            showNow={false}
            minuteStep={5}
            className="w-full"
          />
        </FormItem>

        <FormItem
          control={control}
          name="display_question_num"
          label="Jumlah Soal"
          disabled={!!exam_id && hasStarted}
        >
          <InputNumber className="w-full" />
        </FormItem>
        <FormItem control={control} name="entry_code" label="Kode Ujian">
          <Input />
        </FormItem>
        <FormItem
          disabled={type !== "external"}
          control={control}
          name="external_link"
          label="Link Google Form"
        >
          <Input />
        </FormItem>

        <div className="grid grid-cols-2 gap-4">
          <FormItem
            control={control}
            name="is_anti_cheat"
            label="Anti Cheat"
            disabled={!!exam_id && hasStarted}
          >
            <Switch />
          </FormItem>
          <FormItem
            control={control}
            name="is_shuffle_question"
            label="Acak Urutan Soal"
            disabled={!!exam_id && hasStarted}
          >
            <Switch />
          </FormItem>
        </div>
        <FormItem
          control={control}
          name="description"
          label="Deskripsi"
          className="sm:col-span-2"
        >
          <Input.TextArea />
        </FormItem>
      </div>
      <div className="flex justify-end gap-4">
        {onCancelButton ? (
          <Button variant="outlined" color="primary" onClick={onCancelButton}>
            Batal
          </Button>
        ) : (
          <Link to="/exams">
            <Button variant="outlined" color="primary">
              Batal
            </Button>
          </Link>
        )}
        <Button htmlType="submit" variant="solid" color="primary">
          Simpan
        </Button>
      </div>
    </Form>
  );
}
