import { examQueryOptions } from "@sisva/hooks/query/exam/useExams";
import { createFileRoute, notFound, Outlet } from "@tanstack/react-router";
import { number, object, parse, pipe, transform, unknown } from "valibot";

export const Route = createFileRoute(
  "/(main)/_layout/exams/$exam_id/_adminAuth"
)({
  params: {
    parse(params) {
      return parse(
        object({
          exam_id: pipe(unknown(), transform(Number), number()),
        }),
        params
      );
    },
  },
  async beforeLoad({
    context: { currentUser, queryClient },
    params: { exam_id },
  }) {
    if (currentUser.type === "student") throw notFound();
    try {
      const exam = await queryClient.fetchQuery(examQueryOptions(exam_id));
      if (!exam) throw notFound();
    } catch {
      throw notFound();
    }
  },

  component: Outlet,
});
