import { valibotResolver } from "@hookform/resolvers/valibot";
import { useStudentsStudentGroup } from "@sisva/hooks/query/academic/useStudentGroups";
import { useExamWithClasses } from "@sisva/hooks/query/exam/useExams";
import {
  useStartExamSession,
  useSubmitExamSession,
} from "@sisva/hooks/query/exam/useExamSessions";
import { useStudentsExamSubmission } from "@sisva/hooks/query/exam/useExamSubmissions";
import { useCurrentUser, useNotificationAPI } from "@sisva/providers";
import { getRouteApi } from "@tanstack/react-router";
import { useTimeout } from "ahooks";
import { Alert, Button, Form, Input } from "antd";
import dayjs from "dayjs";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { FormItem } from "react-hook-form-antd";
import {
  forward,
  nonEmpty,
  number,
  object,
  partialCheck,
  pipe,
  string,
} from "valibot";

import { Countdown } from "#/routes/(main)/-components/Countdown";

import TimeLeft from "./TimeLeft";
import { MAX_ATTEMPT } from "./useAntiCheat";

export default function InputCodeScreen({
  onEntrySuccess,
}: {
  onEntrySuccess?: () => void;
}) {
  const currentUser = useCurrentUser();
  const notification = useNotificationAPI();
  const routeApi = getRouteApi(
    "/(main)/_layout/exams/$exam_id/_sessionAuth/session/"
  );
  const navigate = routeApi.useNavigate();
  const { exam_id } = routeApi.useParams();
  const { data: exam, isLoading: L2 } = useExamWithClasses({ exam_id });
  const { data: studentGroup } = useStudentsStudentGroup(currentUser.id);
  const class_ = exam?.classes.find(
    (class_) => class_?.student_group_id === studentGroup?.id
  );

  const { data: examSubmission, isLoading: L1 } = useStudentsExamSubmission({
    exam_id,
    student_id: currentUser.id,
  });

  const isLoading = L1 || L2;

  const hasNotStarted = exam
    ? dayjs(exam.start_time, "DD/MM/YYYY h:mm A Z").isAfter(dayjs())
    : false;
  const hasEnded = exam
    ? dayjs(exam.end_time, "DD/MM/YYYY h:mm A Z").isBefore(dayjs())
    : false;

  const startNum = examSubmission?.start_num ?? 0;
  const submitted = examSubmission?.status === "submitted";
  const noMoreAttempt = startNum >= MAX_ATTEMPT && exam?.is_anti_cheat === true;
  const disabled =
    isLoading || submitted || noMoreAttempt || hasNotStarted || hasEnded;

  // enabled automatic submission if noMoreAttempt and exam status is not submitted
  const enableAutomaticSubmission = !submitted && noMoreAttempt && !isLoading;
  const [showCountdown, setShowCountdown] = useState(false);

  const { mutate: submitExamSession } = useSubmitExamSession({
    onSuccess() {
      notification.success({ message: "Ujian berhasil dikumpulkan" });
      navigate({ to: "/" });
    },
    onError() {
      notification.error({
        message:
          "Ujian gagal dikumpulkan. Refresh halaman ini untuk mencoba kembali",
      });
    },
  });

  // show count down after some delay
  useTimeout(() => {
    if (enableAutomaticSubmission) {
      setShowCountdown(true);
    }
  }, 1000);

  const { control, handleSubmit } = useForm({
    defaultValues: {
      entry_code: "",
      exam_id,
    },
    resolver: valibotResolver(
      pipe(
        object({
          entry_code: pipe(string(), nonEmpty("Kode ujian harus diisi")),
          exam_id: number(),
        }),
        forward(
          partialCheck(
            [["entry_code"]],
            ({ entry_code: entry_code }) => {
              return entry_code === exam?.entry_code;
            },
            "Kode ujian tidak cocok"
          ),
          ["entry_code"]
        )
      )
    ),
  });

  const { mutate: startExamSession } = useStartExamSession({
    onSuccess() {
      notification.success({ message: "Ujian berhasil dimulai" });
      onEntrySuccess?.();
    },
    onError() {
      notification.error({ message: "Ujian gagal dimulai" });
    },
  });

  return (
    <div className="flex flex-col items-center px-4 grow">
      <div className="size-full max-w-5xl py-8 flex flex-col gap-4">
        <div className="flex flex-col gap-2"></div>

        <div className="size-full flex flex-col gap-4">
          <div className="flex flex-col gap-8 thick-scrollbar items-center justify-center flex-1">
            {
              <TimeLeft
                exam_id={exam_id}
                render={({ color, timeLeft }) => {
                  return (
                    <div
                      className="flex items-center gap-4 md:hidden"
                      style={{
                        color: color,
                      }}
                    >
                      <div className="text-lg lg:text-xl">Waktu tersisa</div>
                      <div className="font-medium text-2xl lg:text-4xl">
                        {timeLeft}
                      </div>
                    </div>
                  );
                }}
              />
            }
            {disabled && !isLoading && (
              <Alert
                className="max-w-2xl w-full"
                description={(() => {
                  if (submitted) return "Kamu telah menyelesaikan ujian ini";
                  if (showCountdown)
                    return (
                      <div>
                        Kamu telah mencapai jumlah maksimal kesempatan untuk
                        memulai ujian ini. Ujian ini akan{" "}
                        <span className="font-semibold">
                          dikumpulkan otomatis
                        </span>{" "}
                        dalam{" "}
                        <span className="font-semibold">
                          <Countdown
                            duration={10}
                            onComplete={() => {
                              submitExamSession({ exam_id, note: "" });
                            }}
                          />{" "}
                          detik
                        </span>
                      </div>
                    );
                  if (noMoreAttempt)
                    return "Kamu telah mencapai jumlah maksimal kesempatan untuk memulai ujian ini";
                  if (hasNotStarted) return "Ujian ini belum dimulai";
                  if (hasEnded) return "Ujian ini telah berakhir";
                })()}
                type="warning"
              />
            )}
            <div className="flex flex-col gap-2 w-full max-w-2xl">
              <div className="flex gap-4">
                <div className="w-32">Nama Ujian</div>
                <div>:</div>
                <div>{exam?.name}</div>
              </div>
              <div className="flex gap-4">
                <div className="w-32">Mata Pelajaran</div>
                <div>:</div>
                <div>{class_?.subject_name}</div>
              </div>
              <div className="flex gap-4">
                <div className="w-32">Guru</div>
                <div>:</div>
                <div>{class_?.teacher_name}</div>
              </div>
              <div className="flex gap-4">
                <div className="w-32">Tanggal Ujian</div>
                <div>:</div>
                <div>
                  {exam &&
                    dayjs(exam.start_time, "DD/MM/YYYY h:mm A Z").format(
                      "DD/MM/YYYY"
                    )}
                </div>
              </div>
              <div className="flex gap-4">
                <div className="w-32">Waktu Mulai</div>
                <div>:</div>
                <div>
                  {exam &&
                    dayjs(exam.start_time, "DD/MM/YYYY h:mm A Z").format(
                      "h:mm A"
                    )}
                </div>
              </div>
              <div className="flex gap-4">
                <div className="w-32">Waktu Selesai</div>
                <div>:</div>
                <div>
                  {exam &&
                    dayjs(exam.end_time, "DD/MM/YYYY h:mm A Z").format(
                      "h:mm A"
                    )}
                </div>
              </div>
            </div>
            {exam?.is_anti_cheat && (
              <Alert
                className="max-w-2xl"
                showIcon
                message="Perhatian"
                description="Jangan keluar dari tab, membuka tab baru, atau meninggalkan jendela fullscreen selama ujian. Melakukan hal tersebut akan memicu sistem anti-cheat"
                type="warning"
              />
            )}

            <div className="flex flex-col gap-2">
              <Form
                onFinish={handleSubmit((value) => {
                  if (exam && exam.is_anti_cheat) {
                    return startExamSession(value);
                  }

                  //HACK: keeping this until this issue fixed https://github.com/zetvies/sisva-demo/issues/225
                  if (!examSubmission) {
                    return startExamSession(value);
                  }

                  onEntrySuccess?.();
                })}
                className="contents"
              >
                <FormItem
                  control={control}
                  name="entry_code"
                  label="Kode ujian"
                  disabled={disabled}
                >
                  <Input placeholder="Masukan kode ujian" />
                </FormItem>
                <Button
                  htmlType="submit"
                  variant="solid"
                  color="primary"
                  disabled={disabled}
                >
                  Mulai ujian
                </Button>
              </Form>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
