import { createFileRoute } from "@tanstack/react-router";
import { number, object, parse, pipe, transform, unknown } from "valibot";

import SessionPage from "./-components/SessionPage";

export const Route = createFileRoute(
  "/(main)/_layout/exams/$exam_id/_sessionAuth/session/"
)({
  params: {
    parse(params) {
      const parsedParam = parse(
        object({
          exam_id: pipe(unknown(), transform(Number), number()),
        }),
        params
      );
      return parsedParam;
    },
  },
  head() {
    return {
      meta: [
        {
          title: "<PERSON><PERSON><PERSON> | <PERSON><PERSON><PERSON>",
        },
      ],
    };
  },
  component: SessionPage,
});
