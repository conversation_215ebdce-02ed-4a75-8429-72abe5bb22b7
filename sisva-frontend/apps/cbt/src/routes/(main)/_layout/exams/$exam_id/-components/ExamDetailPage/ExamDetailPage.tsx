import { useExam } from "@sisva/hooks/query/exam/useExams";
import { getRouteApi } from "@tanstack/react-router";
import type { TabsProps } from "antd";
import { Tabs } from "antd";
import dayjs from "dayjs";

import InfoDasarTab from "./InfoDasarTab";
import JawabanSiswaTab from "./JawabanSiswaTab";
import SoalTab from "./SoalTab";

dayjs.locale("id");

export default function ExamDetailPage() {
  const routeApi = getRouteApi("/(main)/_layout/exams/$exam_id/_adminAuth/");
  const { exam_id } = routeApi.useParams();
  const { data: exam } = useExam({
    exam_id,
  });

  const items: TabsProps["items"] = [
    {
      key: "1",
      label: <span className="font-medium">Info Dasar</span>,
      children: <InfoDasarTab />,
    },
    {
      key: "2",
      label: <span className="font-medium">Soal</span>,
      children: <SoalTab />,
    },
    {
      key: "3",
      label: <span className="font-medium"><PERSON><PERSON><PERSON>wa</span>,
      children: <JawabanSiswaTab />,
    },
  ]
    // hide soal tab if exam is external
    .filter((item) => {
      if (exam?.type === "external" && item.key === "2") return false;
      return true;
    });

  return (
    <div className="flex flex-col items-center px-4 grow">
      <div className="size-full max-w-5xl py-8 flex flex-col gap-4">
        <Tabs defaultActiveKey="1" items={items} centered />
      </div>
    </div>
  );
}
