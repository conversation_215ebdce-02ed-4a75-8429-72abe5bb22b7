import { useExamWithClasses } from "@sisva/hooks/query/exam/useExams";
import { useSubmitExamSession } from "@sisva/hooks/query/exam/useExamSessions";
import { useStudentsExamSubmission } from "@sisva/hooks/query/exam/useExamSubmissions";
import { useCurrentUser, useNotificationAPI } from "@sisva/providers";
import { type ReactNode, useNavigate } from "@tanstack/react-router";
import { useInterval } from "ahooks";
import { theme } from "antd";
import dayjs from "dayjs";
import { useAtom } from "jotai";
import { useRef, useState } from "react";

import { hasOnTimeoutRunAtom } from "./SessionPage";

export default function TimeLeft({
  exam_id,
  render,
}: {
  exam_id: number;
  render?: (props: {
    color: string | undefined;
    timeLeft: string;
  }) => ReactNode;
}) {
  const currentUser = useCurrentUser();
  const token = theme.useToken().token;
  const [timeLeft, setTimeLeft] = useState("");
  const notification = useNotificationAPI();
  const navigate = useNavigate();

  // useAtom insteaf of useRef because this components is rendered in multiple place
  const [hasOnTimeoutRun, setHasOnTimeoutRun] = useAtom(hasOnTimeoutRunAtom);
  const enableOnTimeOutRef = useRef(false);
  const [seconds, setSeconds] = useState(0);
  const { data: exam } = useExamWithClasses({ exam_id });
  const endTime = !exam ? null : dayjs(exam.end_time, "DD/MM/YYYY h:mm A Z");
  //  for debugging
  // .subtract(17, "hours")
  // .subtract(33, "minutes");
  //

  const { data: examSubmission } = useStudentsExamSubmission({
    exam_id,
    student_id: currentUser.id,
  });

  const { mutate: submitExamSession } = useSubmitExamSession({
    onSuccess() {
      notification.success({ message: "Ujian berhasil dikumpulkan" });
      navigate({ to: "/" });
    },
    onError() {
      notification.error({ message: "Ujian gagal dikumpulkan" });
    },
  });

  function onTimeout() {
    if (
      examSubmission &&
      exam &&
      examSubmission.status !== "submitted" &&
      exam.is_anti_cheat === true
    ) {
      submitExamSession({ exam_id, note: "" });
    }
  }

  useInterval(() => {
    const [diff, hours, seconds] = (() => {
      if (!endTime) return [0, 0, 0];
      if (dayjs().isAfter(endTime)) return [0, 0, 0];

      // enable onTimeOut if endTime exist and haven't been passed
      enableOnTimeOutRef.current = true;
      return [
        endTime.diff(dayjs()),
        endTime.diff(dayjs(), "hours"),
        endTime.diff(dayjs(), "seconds"),
      ];
    })();

    setTimeLeft(hours + dayjs.duration(diff).format(" : mm : ss"));
    setSeconds(seconds);

    if (seconds === 0 && !hasOnTimeoutRun && enableOnTimeOutRef.current) {
      onTimeout?.();
      setHasOnTimeoutRun(true);
    }
  }, 1000);

  const color = seconds <= 60 ? token.colorErrorText : undefined;

  return (
    timeLeft &&
    (render ? (
      render({ color, timeLeft })
    ) : (
      <div
        className="md:flex items-center gap-4 hidden"
        style={{
          color: color,
        }}
      >
        <div className="text-lg lg:text-xl">Waktu tersisa</div>
        <div className="font-medium text-2xl lg:text-4xl">{timeLeft}</div>
      </div>
    ))
  );
}
