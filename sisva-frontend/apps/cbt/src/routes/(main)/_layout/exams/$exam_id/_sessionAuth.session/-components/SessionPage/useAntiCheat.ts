import {
  useDocumentVisibility,
  useEventListener,
  useFullscreen,
  useHover,
  useKeyPress,
} from "ahooks";
import { useEffect, useRef } from "react";

import { env } from "#/env";

export const MAX_ATTEMPT = 3;

export default function useAntiCheat({
  onViolation,
  enabled = false,
}: {
  onViolation?: () => void;
  enabled?: boolean;
}) {
  const app = document.getElementById("app");

  const [isFullscreen, { toggleFullscreen, enterFullscreen, exitFullscreen }] =
    useFullscreen(app, {
      pageFullscreen: false,
    });

  const documentVisibility = useDocumentVisibility();
  const isHovering = useHover(app);

  //HACK: isHovering is false initially until mouseenter event triggered at least once.
  useEffect(() => {
    app?.dispatchEvent(new MouseEvent("mouseenter"));
  }, [app]);

  const prevFullscreen = useRef(isFullscreen);
  const prevDocumentVisibility = useRef(documentVisibility);
  const prevIsHovering = useRef(isHovering);

  useEffect(() => {
    if (!enabled) return;

    const isExitFullscreen =
      prevFullscreen.current === true && isFullscreen === false;
    const isBecomeNotVisible =
      prevDocumentVisibility.current === "visible" &&
      documentVisibility !== "visible";
    const isBecomeNotHovering =
      prevIsHovering.current === true && isHovering === false;

    if (isExitFullscreen || isBecomeNotVisible || isBecomeNotHovering) {
      console.info("Violation detected");
      onViolation?.();
    }

    prevFullscreen.current = isFullscreen;
    prevDocumentVisibility.current = documentVisibility;
    prevIsHovering.current = isHovering;
  }, [documentVisibility, enabled, isFullscreen, isHovering, onViolation]);

  // disable right click
  useEventListener(
    "contextmenu",
    (e) => {
      if (!enabled) return;
      e.preventDefault();
    },
    { target: app }
  );

  // disable keypress
  useKeyPress(
    // https://github.com/alibaba/hooks/blob/master/packages/hooks/src/useKeyPress/index.ts#L21
    // NOTE: some keys are not working due to normal browser behavior
    [
      "f12", // prevent opening dev tools
      "f11", // prevent toggling fullscreen. NOT WORKING for exit, NOT WORKING for enter sometimes
      "meta", // MacOS Command key / Windows key
      "ctrl.shift.i", // Open DevTools
      "ctrl.shift.j", // Open DevTools (Console)
      "ctrl.shift.c", // Open Element Inspector
      "ctrl.shift.k", // Open DevTools (Firefox)
      "ctrl.u", // View page source
      "ctrl.s", // Save page
      "ctrl.p", // Print page
      "ctrl.h", // History page
      "ctrl.w", // Close tab. NOT WORKING
      "ctrl.f", // Open search
      "ctrl.r", // Reload page. NOT WORKING
      "ctrl.n", // Open new window. NOT WORKING
      "ctrl.t", // Open new tab. NOT WORKING
      "ctrl.o", // Open file
      "ctrl.l", // Open location
      "ctrl.add", // Zoom in. NOT WORKING
      "ctrl.subtract", // Zoom out. NOT WORKING
    ].filter((string) => {
      // allow during development
      if (env.DEV && string === "f12") return false;
      return true;
    }),
    (e) => {
      if (!enabled) return;
      e.preventDefault();
    }
  );

  return {
    toggleFullscreen,
    enterFullscreen,
    exitFullscreen,
  };
}
