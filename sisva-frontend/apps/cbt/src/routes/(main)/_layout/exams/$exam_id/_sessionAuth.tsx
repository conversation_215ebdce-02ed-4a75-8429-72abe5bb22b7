import { classesQeuryOptions } from "@sisva/hooks/query/academic/useClasses";
import { studentInStudentGroupsQueryOptions } from "@sisva/hooks/query/academic/useStudentGroups";
import { teachingPlansQueryOptions } from "@sisva/hooks/query/classroom/useTeachingPlans";
import { examQueryOptions } from "@sisva/hooks/query/exam/useExams";
import { createFileRoute, notFound, Outlet } from "@tanstack/react-router";
import { number, object, parse, pipe, transform, unknown } from "valibot";

export const Route = createFileRoute(
  "/(main)/_layout/exams/$exam_id/_sessionAuth"
)({
  params: {
    parse(params) {
      return parse(
        object({
          exam_id: pipe(unknown(), transform(Number), number()),
        }),
        params
      );
    },
  },
  async beforeLoad({
    context: { currentUser, queryClient },
    params: { exam_id },
  }) {
    if (currentUser.type !== "student") throw notFound();
    try {
      const exam = await queryClient.fetchQuery(examQueryOptions(exam_id));
      const classes = await queryClient.fetchQuery(classesQeuryOptions);
      const teachingPlans = await queryClient.fetchQuery(
        teachingPlansQueryOptions
      );
      const studentInStudentGroups = await queryClient.fetchQuery(
        studentInStudentGroupsQueryOptions
      );

      const studentsStudentGroupId = studentInStudentGroups.find(
        (item) => item.student_id === currentUser.id
      );

      const allowedClassIds = classes
        .filter((item) => {
          if (currentUser.type === "student") {
            return (
              item.student_group_id === studentsStudentGroupId?.student_group_id
            );
          } else {
            return item.teacher_id === currentUser.id;
          }
        })
        .map((item) => item.id);

      const class_ids = teachingPlans
        .filter((plan) => exam.teaching_plan_ids.includes(plan.id))
        .map((plan) => plan.class_id);

      if (!class_ids.every((class_id) => allowedClassIds.includes(class_id))) {
        throw notFound();
      }
    } catch {
      throw notFound();
    }
  },

  component: Outlet,
});
