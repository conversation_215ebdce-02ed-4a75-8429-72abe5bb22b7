import { useClasses } from "@sisva/hooks/query/academic/useClasses";
import { useTeachingPlans } from "@sisva/hooks/query/classroom/useTeachingPlans";
import { getRouteApi } from "@tanstack/react-router";

import ExamForm from "../../-components/ExamForm";

export default function NewExamPage() {
  const { data: teachingPlans = [] } = useTeachingPlans();
  const { data: classes = [] } = useClasses();
  const { teaching_plan_ids, class_id } = getRouteApi(
    "/(main)/_layout/exams/_adminAuth/new/"
  ).useSearch();

  const validTeachingPlanIds = teachingPlans.map((item) => item.id);
  const validClassIds = classes.map((item) => item.id);

  const isValidTeachingPlanIds =
    !!teaching_plan_ids &&
    teaching_plan_ids.length > 0 &&
    teaching_plan_ids.every((id) => validTeachingPlanIds.includes(id));

  const isValidClassId =
    !!class_id && class_id > 0 && validClassIds.includes(class_id);

  return (
    <div className="flex flex-col items-center px-4 grow">
      <div className="size-full max-w-5xl py-8 flex flex-col gap-4">
        <h2>Buat Ujian</h2>
        <div>
          <ExamForm
            teaching_plan_ids={
              isValidTeachingPlanIds ? teaching_plan_ids : undefined
            }
            class_id={isValidClassId ? class_id : undefined}
          />
        </div>
      </div>
    </div>
  );
}
