import { useSubmitExamSession } from "@sisva/hooks/query/exam/useExamSessions";
import { useStudentsExamSubmission } from "@sisva/hooks/query/exam/useExamSubmissions";
import { useCurrentUser, useNotificationAPI } from "@sisva/providers";
import { getRouteApi } from "@tanstack/react-router";
import { AlertTriangle } from "@untitled-ui/icons-react";
import { useFullscreen } from "ahooks";
import { useAtom } from "jotai";
import { createPortal } from "react-dom";

import { Countdown } from "#/routes/(main)/-components/Countdown";

import { screenAtom } from "./SessionPage";
import { MAX_ATTEMPT } from "./useAntiCheat";

export default function WarningScreen() {
  const app = document.getElementById("app");
  const [, setScreen] = useAtom(screenAtom);

  const currentUser = useCurrentUser();
  const [, { exitFullscreen }] = useFullscreen(app);

  const routeApi = getRouteApi(
    "/(main)/_layout/exams/$exam_id/_sessionAuth/session/"
  );
  const navigate = routeApi.useNavigate();
  const notification = useNotificationAPI();
  const { exam_id } = routeApi.useParams();

  const { data: examSubmission } = useStudentsExamSubmission({
    exam_id,
    student_id: currentUser.id,
  });

  const { mutate: submitExamSession } = useSubmitExamSession({
    onSuccess() {
      notification.success({ message: "Ujian berhasil dikumpulkan" });
      navigate({ to: "/" });
      exitFullscreen();
    },
    onError() {
      notification.error({
        message:
          "Ujian gagal dikumpulkan. Refresh halaman ini untuk mencoba kembali",
      });
    },
  });

  const remaingingAttempt = examSubmission
    ? MAX_ATTEMPT - examSubmission.start_num
    : "";

  const messsage =
    remaingingAttempt === 0 ? (
      <span>
        Kamu <span className="font-bold">tidak memiliki</span> kesempatan untuk
        memulai kembali ujian ini. Ujian akan dikumpulkan otomatis dalam{" "}
        <Countdown
          duration={10}
          onComplete={() => {
            submitExamSession({ exam_id, note: "" });
          }}
        />{" "}
        detik
      </span>
    ) : (
      <span>
        Kamu memiliki{" "}
        <span className="font-bold">{remaingingAttempt} kali</span> kesempatan
        untuk memulai kembali ujian ini.
      </span>
    );

  if (app)
    return createPortal(
      <div className="bg-red-500 absolute size-full top-0 text-white left-0 z-30 flex flex-col items-center justify-center">
        <AlertTriangle className="text-white w-32 h-32" />
        <div className="text-xl max-w-2xl text-center p-4">
          Kamu terdeteksi telah meningggalkan halaman ujian. {messsage}
        </div>
        {remaingingAttempt !== 0 && (
          <div
            onClick={() => {
              setScreen("input_code");
            }}
            className="underline cursor-pointer"
          >
            Kembali ke halaman ujian
          </div>
        )}
      </div>,
      app
    );
}
