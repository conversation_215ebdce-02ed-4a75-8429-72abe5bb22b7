import { useExamWithClasses } from "@sisva/hooks/query/exam/useExams";
import { getExamTypeText } from "@sisva/types/types";
import { getRouteApi } from "@tanstack/react-router";
import { useToggle } from "ahooks";
import { Button, Tag, theme } from "antd";
import dayjs from "dayjs";

import ExamForm from "../../../-components/ExamForm";

export default function InfoDasarTab() {
  const token = theme.useToken().token;
  const routeApi = getRouteApi("/(main)/_layout/exams/$exam_id/_adminAuth/");
  const { exam_id } = routeApi.useParams();
  const { data: exam } = useExamWithClasses({
    exam_id,
  });

  const [edit, { toggle }] = useToggle();

  const examDetail = (
    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
      <div>
        <div className="font-medium"><PERSON><PERSON></div>
        <div>{exam?.name}</div>
      </div>
      <div>
        <div className="font-medium"><PERSON><PERSON></div>
        <div>{exam && getExamTypeText(exam.type)}</div>
      </div>
      <div>
        <div className="font-medium">Kelas</div>
        <div>
          {exam?.classes.map((class_) => (
            <Tag color={token.colorPrimaryText} key={class_.id}>
              {class_.student_group_name}
            </Tag>
          ))}
        </div>
      </div>
      <div>
        <div className="font-medium">Mata Pelajaran</div>
        <div>{exam?.classes[0]?.subject_name}</div>
      </div>
      <div>
        <div className="font-medium">Tanggal Ujian</div>
        <div>
          {exam &&
            dayjs(exam.start_time, "DD/MM/YYYY h:mm A Z").format("DD/MM/YYYY")}
        </div>
      </div>
      <div className="grid grid-cols-2 gap-4">
        <div>
          <div className="font-medium">Jam Mulai</div>
          <div>
            {exam &&
              dayjs(exam.start_time, "DD/MM/YYYY h:mm A Z").format("h:mm A")}
          </div>
        </div>
        <div>
          <div className="font-medium">Jam Selesai</div>
          <div>
            {exam &&
              dayjs(exam.end_time, "DD/MM/YYYY h:mm A Z").format("h:mm A")}
          </div>
        </div>
      </div>
      <div>
        <div className="font-medium">Kode Ujian</div>
        <div>{exam?.entry_code}</div>
      </div>
      <div>
        <div className="font-medium">Jumlah Soal</div>
        <div>{exam?.display_question_num}</div>
      </div>
      {exam?.external_link && (
        <div>
          <div className="font-medium">Link Google Form</div>
          <a className="line-clamp-1">{exam?.external_link}</a>
        </div>
      )}
      <div className="grid grid-cols-2 gap-4">
        <div>
          <div className="font-medium">Anti Cheat</div>
          <div>{exam?.is_anti_cheat ? "Aktif" : "Tidak Aktif"}</div>
        </div>
        <div>
          <div className="font-medium">Acak Urutan Soal</div>
          <div>{exam?.is_shuffle_question ? "Aktif" : "Tidak Aktif"}</div>
        </div>
      </div>
      {exam?.description && (
        <div>
          <div className="font-medium">Deksripsi</div>
          <div>{exam?.description}</div>
        </div>
      )}
    </div>
  );

  return (
    <div className="flex flex-col gap-4">
      <div className="flex justify-between gap-4">
        <h2>Info Dasar</h2>
      </div>

      <div className="size-full flex flex-col gap-4">
        {!edit && examDetail}
        {edit && (
          <ExamForm
            exam_id={exam_id}
            onCancelButton={toggle}
            onEditSuccess={toggle}
          />
        )}
        {!edit && (
          <div className="flex justify-end">
            <Button variant="solid" color="primary" onClick={toggle}>
              Edit
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}
