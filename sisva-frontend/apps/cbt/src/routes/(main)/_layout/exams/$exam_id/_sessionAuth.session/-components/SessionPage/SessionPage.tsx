import { useExamWithClasses } from "@sisva/hooks/query/exam/useExams";
import { getRouteApi } from "@tanstack/react-router";
import dayjs from "dayjs";
import durationPlugin from "dayjs/plugin/duration";
import { useAtom, useSetAtom } from "jotai";
import { useEffect } from "react";
dayjs.extend(durationPlugin);

import { atom } from "jotai";

import { navbarMiddleElementAtom } from "#/routes/(main)/-components/NavBar";

import ExamExternalScreen from "./ExamExternalScreen";
import ExamScreen from "./ExamScreen";
import InputCodeScreen from "./InputCodeScreen";
import TimeLeft from "./TimeLeft";
import useAntiCheat from "./useAntiCheat";
import WarningScreen from "./WarningScreen";

const disableAntiCheatPassword = "dev" + dayjs().format("DDHH"); // dev + <current date> + <current hour>, example: dev2811
export const disableAntiCheatAtom = atom(false);
export const screenAtom = atom<"input_code" | "exam" | "warning">("input_code");
export const hasOnTimeoutRunAtom = atom(false);

export default function SessionPage() {
  const [screen, setScreen] = useAtom(screenAtom);
  const setMiddleElement = useSetAtom(navbarMiddleElementAtom);
  const setHasOnTimeoutRun = useSetAtom(hasOnTimeoutRunAtom);
  const [disableAntiCheat, setDisableAntiCheat] = useAtom(disableAntiCheatAtom);

  const routeApi = getRouteApi(
    "/(main)/_layout/exams/$exam_id/_sessionAuth/session/"
  );
  const { exam_id } = routeApi.useParams();

  const { data: exam } = useExamWithClasses({ exam_id });

  const { enterFullscreen } = useAntiCheat({
    enabled: false,
  });

  useEffect(() => {
    // set countdown timer on the navbar
    setMiddleElement(<TimeLeft exam_id={exam_id} />);

    return () => {
      setMiddleElement(null);
      setScreen("input_code");
      setHasOnTimeoutRun(false);
      setDisableAntiCheat(false);
    }; // Cleanup, reset atom state on unmount
  }, [
    exam_id,
    setDisableAntiCheat,
    setHasOnTimeoutRun,
    setMiddleElement,
    setScreen,
  ]);

  // developer backdoor to disable anti-cheat
  useEffect(() => {
    //@ts-expect-error we don't need type safety here
    window.disableAntiCheat = (password: string) => {
      if (password === disableAntiCheatPassword) {
        setDisableAntiCheat(true);
        console.info("[Anti-Cheat Disabled]");
      } else {
        console.info("[Wrong password]");
      }
    };
  }, [setDisableAntiCheat]);

  if (screen === "input_code")
    return (
      <InputCodeScreen
        onEntrySuccess={() => {
          if (exam && exam.is_anti_cheat === true && !disableAntiCheat) {
            enterFullscreen();
          }
          setScreen("exam");
        }}
      />
    );
  if (screen === "exam") {
    if (exam?.type === "external") return <ExamExternalScreen />;
    return <ExamScreen />;
  }
  if (screen === "warning") return <WarningScreen />;
}
