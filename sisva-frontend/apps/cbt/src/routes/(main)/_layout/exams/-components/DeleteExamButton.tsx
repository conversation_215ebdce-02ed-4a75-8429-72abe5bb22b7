import {
  useDeleteExam,
  useExamWithClasses,
} from "@sisva/hooks/query/exam/useExams";
import { useExamSubmissions } from "@sisva/hooks/query/exam/useExamSubmissions";
import { useNotificationAPI } from "@sisva/providers";
import { useToggle } from "ahooks";
import { Alert, Modal, Tag, theme } from "antd";
import dayjs from "dayjs";
import type { ReactNode } from "react";

export default function DeleteExamButton({
  exam_id,
  renderTrigger,
}: {
  exam_id: number | undefined;
  renderTrigger?: (onClick: () => void) => ReactNode;
}) {
  const token = theme.useToken().token;
  const notification = useNotificationAPI();
  const [open, { toggle }] = useToggle();
  const { data: exam } = useExamWithClasses({
    exam_id,
  });
  const { data: examSubmissions } = useExamSubmissions({ exam_id });

  const disabled = !!examSubmissions?.length;

  const { mutate: deleteExam } = useDeleteExam({
    onSuccess() {
      notification.success({
        message: "Ujian berhasil dihapus",
      });
      toggle();
    },
    onError() {
      notification.error({
        message: "Ujian gagal dihapus",
      });
    },
  });

  return (
    <>
      {renderTrigger?.(toggle)}
      <Modal
        okText="Hapus"
        okButtonProps={{
          variant: "outlined",
          color: "danger",
          disabled,
        }}
        title="Hapus ujian"
        open={open}
        onOk={() => exam_id && deleteExam(exam_id)}
        onCancel={toggle}
      >
        <div className="flex flex-col gap-4">
          {disabled && (
            <Alert
              message="Ujian ini tidak dapat dihapus karena pernah digunakan"
              type="warning"
            />
          )}
          <div className="flex flex-col gap-2">
            <div>
              <div className="font-semibold">Nama Ujian</div>
              <div>{exam?.name}</div>
            </div>
            <div>
              <div className="font-semibold">Kelas</div>
              <div>
                {exam?.classes.map((class_) => (
                  <Tag color={token.colorPrimaryText} key={class_.id}>
                    {class_?.student_group_name}
                  </Tag>
                ))}
              </div>
            </div>
            <div>
              <div className="font-semibold">Mata Pelajaran</div>
              <div>{exam?.classes[0]?.subject_name}</div>
            </div>
            <div>
              <div className="font-semibold">Tanggal</div>
              <div>
                {dayjs(exam?.start_time, "DD/MM/YYYY h:mm A Z").format(
                  "DD/MM/YYYY"
                )}
              </div>
            </div>
          </div>
        </div>
      </Modal>
    </>
  );
}
