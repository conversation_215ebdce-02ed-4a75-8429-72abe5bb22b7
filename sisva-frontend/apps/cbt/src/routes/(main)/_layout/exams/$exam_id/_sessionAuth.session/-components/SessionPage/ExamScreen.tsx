import { valibotResolver } from "@hookform/resolvers/valibot";
import { useExamQuestions } from "@sisva/hooks/query/exam/useExamQuestions";
import { useExamWithClasses } from "@sisva/hooks/query/exam/useExams";
import { useSubmitExamSession } from "@sisva/hooks/query/exam/useExamSessions";
import {
  useExamSubmissionAnswers,
  useSetExamSubmissionAnswers,
  useStudentsExamSubmission,
} from "@sisva/hooks/query/exam/useExamSubmissions";
import { useGetFileUrl } from "@sisva/hooks/utils";
import { useCurrentUser, useNotificationAPI } from "@sisva/providers";
import type { ExamQuestion } from "@sisva/types/apiTypes";
import { hashCode } from "@sisva/utils";
import { deepEqual, getRouteApi } from "@tanstack/react-router";
import {
  ChevronLeft,
  ChevronRight,
  Edit02,
  Flag01,
  Grid01,
  LogIn04,
} from "@untitled-ui/icons-react";
import { useBoolean, useFullscreen, useInterval, useMemoizedFn } from "ahooks";
import {
  Alert,
  Button,
  Checkbox,
  Empty,
  Form,
  Image,
  Input,
  Modal,
  Radio,
  Skeleton,
  Slider,
  Table,
  Tag,
  theme,
} from "antd";
import type { ColumnsType } from "antd/es/table";
import dayjs from "dayjs";
import { sort } from "fast-sort";
import { useAtom, useSetAtom } from "jotai";
import { type ReactNode, useEffect, useRef, useState } from "react";
import { type Control, type Path, useForm, useWatch } from "react-hook-form";
import { FormItem } from "react-hook-form-antd";
import { boolean, literal, number, object, pipe, string } from "valibot";

import { navbarRightElementAtom } from "#/routes/(main)/-components/NavBar";

import ExitButton from "./ExitButton";
import { disableAntiCheatAtom, screenAtom } from "./SessionPage";
import TimeLeft from "./TimeLeft";
import useAntiCheat from "./useAntiCheat";

type FormValues = Record<string, string | null | number>;

export default function ExamScreen() {
  const currentUser = useCurrentUser();
  const app = document.getElementById("app");
  const token = theme.useToken().token;
  const routeApi = getRouteApi(
    "/(main)/_layout/exams/$exam_id/_sessionAuth/session/"
  );
  const { exam_id } = routeApi.useParams();
  const notification = useNotificationAPI();

  const [, setScreen] = useAtom(screenAtom);
  const [disableAntiCheat] = useAtom(disableAntiCheatAtom);
  const [selectedIndex, setSelectedIndex] = useState(0);
  const lastSaved = useRef("");

  const getUrl = useGetFileUrl();

  const { data: allExamQuestions = [], isLoading: L1 } = useExamQuestions({
    exam_id,
  });
  const { data: examSubmissionAnswers = [], isLoading: L2 } =
    useExamSubmissionAnswers({ exam_id, user_id: currentUser.id });
  const { data: exam, isLoading: L3 } = useExamWithClasses({
    exam_id,
  });

  const { data: examSubmission, isLoading: L4 } = useStudentsExamSubmission({
    exam_id,
    student_id: currentUser.id,
  });

  const isLoading = L1 || L2 || L3 || L4;

  const [flaggedQuestionIds, setFlaggedQuestionIds] = useState<string[]>([]);
  const questionRefs = useRef<Record<string, HTMLDivElement | null>>({});
  const lastSubmittedValues = useRef<FormValues | null>(null);

  const examQuestions = (() => {
    if (!exam) return [];

    // pick the first N questions (defined by `exam.display_question_num`)
    // If shuffle is enabled, sort using a deterministic hash based on user ID
    if (exam.is_shuffle_question) {
      return sort(allExamQuestions)
        .asc((question) => hashCode(question.id + currentUser.id))
        .slice(0, exam.display_question_num);
    }
    return sort(allExamQuestions)
      .asc((question) => question.id)
      .slice(0, exam.display_question_num);
  })();

  const formValues: FormValues = {};
  examQuestions.forEach((question) => {
    const answer = examSubmissionAnswers.find(
      (answer) => answer.question_id === question.id
    );
    const value = (() => {
      if (question.type === "text" || question.type === "scale") {
        return answer?.text_value ?? null;
      }
      if (question.type === "option") {
        return answer?.option_id_values?.[0] ?? null;
      }
      return null;
    })();

    formValues[question.id] = value;
  });

  const { control, handleSubmit } = useForm({
    values: formValues,
  });

  const { mutate: setExamSubmissionAnswers } = useSetExamSubmissionAnswers({
    onSuccess() {
      lastSaved.current = dayjs().format("DD/MM/YYYY HH:mm:ss");
      console.info("Jawaban berhasil disimpan", lastSaved.current);
    },
    onError() {
      console.info(
        "Jawaban gagal disimpan",
        dayjs().format("DD/MM/YYYY HH:mm:ss")
      );
      console.info("Jawaban terakhir disimpan pada waktu", lastSaved.current);
      notification.error({
        message: `Terjadi masalah saat menyimpan jawaban, jawaban terakhir disimpan pada waktu ${lastSaved.current}`,
        duration: -1,
      });
    },
  });

  const saveAnswers = useMemoizedFn((force: boolean = false) => {
    handleSubmit((values) => {
      if (deepEqual(values, lastSubmittedValues.current) && !force) {
        return; // Skip mutation if values haven't changed
      }

      const data = Object.entries(values)
        .map(([question_id, value]) => {
          const question = examQuestions.find(
            (question) => question.id === question_id
          );
          if (!question) return undefined;
          if (question.type === "text" || question.type === "scale") {
            return {
              question_id,
              text_value: value?.toString(),
            };
          }
          if (question.type === "option") {
            return {
              question_id,
              option_id_values: value ? [value.toString()] : null,
            };
          }
        })
        .filter((item) => item !== undefined);
      setExamSubmissionAnswers(
        { data, exam_id, user_id: currentUser.id },
        {
          onSuccess() {
            lastSubmittedValues.current = values;
          },
        }
      );
    })();
  });

  function handleFlag(question_id: string) {
    if (flaggedQuestionIds.includes(question_id)) {
      setFlaggedQuestionIds([
        ...flaggedQuestionIds.filter((id) => id !== question_id),
      ]);
    } else {
      setFlaggedQuestionIds([...flaggedQuestionIds, question_id]);
    }
  }

  useInterval(() => {
    if (isLoading) return;
    saveAnswers();
  }, 10000);

  useAntiCheat({
    enabled: exam?.is_anti_cheat && !disableAntiCheat,
    onViolation() {
      saveAnswers(true);
      setScreen("warning");
    },
  });

  const setRightElement = useSetAtom(navbarRightElementAtom);
  const maxIndex = examQuestions.length - 1;

  useEffect(() => {
    setRightElement(
      <>
        <Button
          color="default"
          variant="outlined"
          size="large"
          className="flex sm:hidden"
          disabled={selectedIndex === 0}
          onClick={() => {
            setSelectedIndex((prev) => prev - 1);
          }}
          icon={<ChevronLeft />}
        ></Button>
        <div className="block sm:hidden">
          {selectedIndex + 1}/{maxIndex + 1}
        </div>
        <ExitButton
          renderTrigger={(onClick) => {
            return (
              <div
                onClick={() => {
                  saveAnswers(true);
                  onClick();
                }}
                className="sm:flex cursor-pointer flex-col items-center font-bold hidden"
                style={{
                  color: token.colorPrimary,
                }}
              >
                <LogIn04 />
                <div>Keluar</div>
              </div>
            );
          }}
        />
        <Button
          color="default"
          variant="outlined"
          size="large"
          className="flex sm:hidden"
          onClick={() => {
            setSelectedIndex((prev) => prev + 1);
          }}
          disabled={selectedIndex === maxIndex}
          icon={<ChevronRight />}
        ></Button>
      </>
    );
    return () => setRightElement(null); // Cleanup on unmount
  }, [
    maxIndex,
    selectedIndex,
    setRightElement,
    saveAnswers,
    token.colorPrimary,
  ]);

  const selectedQuestion = examQuestions[selectedIndex];

  return (
    <>
      {/* DESKTOP */}

      <div className="md:flex flex-col items-center px-4 grow hidden">
        <div className="size-full max-w-5xl py-8 flex flex-col gap-4">
          <div className="flex flex-col gap-2"></div>

          <div className="size-full flex flex-col gap-4">
            <div className="flex flex-col gap-4 thick-scrollbar">
              {(() => {
                if (isLoading) {
                  return <Skeleton active />;
                }

                if (examSubmission?.status === "submitted") {
                  return <Empty description="Ujian telah dikumpulkan" />;
                }

                if (examQuestions.length === 0) {
                  return <Empty description="Tidak ada soal yang disediakan" />;
                }

                return (
                  <div className="flex items-start gap-4">
                    <div className="flex-1 flex flex-col gap-4">
                      {examQuestions.map((question, i) => {
                        return (
                          <div
                            className="flex gap-4 items-start"
                            key={question.id}
                            ref={(el) => {
                              questionRefs.current[question.id] = el;
                            }}
                          >
                            <QuestionFlag
                              onClick={() => handleFlag(question.id)}
                              flagged={flaggedQuestionIds.includes(question.id)}
                              index={i}
                              control={control}
                              question_id={question.id}
                            />
                            <Form className="contents">
                              <div
                                key={question.id}
                                className="shadow-md flex flex-col gap-4 p-4 rounded-md border-solid border-neutral-200 border flex-1"
                              >
                                {question.image_url && (
                                  <Image
                                    src={getUrl(question.image_url)}
                                    alt="Gambar untuk soal ujian"
                                    width={80}
                                    height={80}
                                    preview={{
                                      getContainer: app ?? undefined,
                                    }}
                                    className="min-w-20 object-cover"
                                  />
                                )}
                                <div>{question.text}</div>
                                {question.type === "text" && (
                                  <FormItem
                                    control={control}
                                    name={question.id}
                                  >
                                    <Input.TextArea placeholder="Isi jawaban" />
                                  </FormItem>
                                )}

                                {question.type === "option" && (
                                  <FormItem
                                    control={control}
                                    name={question.id}
                                  >
                                    <Radio.Group
                                      className="flex flex-col gap-2"
                                      options={question.option_detail.options.map(
                                        (option) => ({
                                          value: option.id,
                                          label: option.text,
                                        })
                                      )}
                                    />
                                  </FormItem>
                                )}
                                {question.type === "scale" && (
                                  <div className="px-10">
                                    <FormItem
                                      control={control}
                                      name={question.id}
                                    >
                                      <Slider
                                        marks={{
                                          1: (
                                            <div className="flex flex-col items-center gap-1">
                                              <div>1</div>
                                              <div className="w-24 text-center leading-none text-xs p-0.5 line-clamp-2">
                                                {
                                                  question.scale_detail
                                                    .min_label
                                                }
                                              </div>
                                            </div>
                                          ),
                                          2: "2",
                                          3: "3",
                                          4: "4",
                                          5: (
                                            <div className="flex flex-col items-center gap-1">
                                              <div>5</div>
                                              <div className="w-24 text-center leading-none text-xs p-0.5 line-clamp-2">
                                                {
                                                  question.scale_detail
                                                    .max_label
                                                }
                                              </div>
                                            </div>
                                          ),
                                        }}
                                        min={1}
                                        max={5}
                                        step={1}
                                        className="max-w-64"
                                      />
                                    </FormItem>
                                  </div>
                                )}
                              </div>
                            </Form>
                          </div>
                        );
                      })}
                    </div>
                    <div className="flex flex-col gap-4 w-64 border border-solid border-neutral-200 shadow-md rounded-md p-4 sticky top-4">
                      <div className="grid grid-cols-4 gap-2 overflow-auto max-h-[40svh] pe-2">
                        {examQuestions.map((question, i) => (
                          <QuestionOverviewCell
                            key={question.id}
                            question_id={question.id}
                            index={i}
                            control={control}
                            flagged={flaggedQuestionIds.includes(question.id)}
                            onClick={() => {
                              questionRefs.current[question.id]?.scrollIntoView(
                                {
                                  behavior: "smooth",
                                  block: "center",
                                }
                              );
                            }}
                          />
                        ))}
                      </div>
                      <RekapitulasiButton
                        exam_id={exam_id}
                        control={control}
                        questions={examQuestions.map((question, i) => ({
                          ...question,
                          index: i,
                        }))}
                        flaggedQuestionIds={flaggedQuestionIds}
                        onEditClick={(exam_id) => {
                          questionRefs.current[exam_id]?.scrollIntoView({
                            behavior: "smooth",
                            block: "center",
                          });
                        }}
                        renderTrigger={(onClick) => (
                          <Button
                            onClick={() => {
                              saveAnswers(true);
                              onClick();
                            }}
                            variant="solid"
                            color="primary"
                          >
                            Selesaikan Ujian
                          </Button>
                        )}
                      />
                    </div>
                  </div>
                );
              })()}
            </div>
          </div>
        </div>
      </div>

      {/* MOBILE */}

      <div className="flex flex-col flex-1 md:hidden">
        <div className="flex p-6 justify-between gap-4 items-center h-24">
          <Button
            color={(() => {
              const flagged =
                selectedQuestion &&
                flaggedQuestionIds.includes(selectedQuestion.id);
              if (flagged) return "danger";
              return "default";
            })()}
            onClick={() => {
              if (selectedQuestion) handleFlag(selectedQuestion.id);
            }}
            variant="outlined"
            size="large"
            icon={<Flag01 />}
          ></Button>
          <TimeLeft
            exam_id={exam_id}
            render={({ color, timeLeft }) => {
              return (
                <div
                  className="flex flex-col items-center gap-1"
                  style={{
                    color: color,
                  }}
                >
                  <div className="">Waktu tersisa</div>
                  <div className="font-medium text-xl">{timeLeft}</div>
                </div>
              );
            }}
          />
          <OverviewButton
            exam_id={exam_id}
            control={control}
            questions={examQuestions.map((question, i) => ({
              ...question,
              index: i,
            }))}
            flaggedQuestionIds={flaggedQuestionIds}
            renderTrigger={(onClick) => (
              <Button
                onClick={() => {
                  saveAnswers(true);
                  onClick();
                }}
                color="default"
                variant="outlined"
                size="large"
                icon={<Grid01 />}
              ></Button>
            )}
            onEditClick={(exam_id) => {
              setSelectedIndex(
                examQuestions.findIndex((question) => question.id === exam_id)
              );
            }}
          />
        </div>
        <div className="flex flex-col flex-1 overflow-auto">
          {(() => {
            return (
              selectedQuestion && (
                <Form className="contents">
                  <div
                    key={selectedQuestion.id}
                    className="flex flex-col gap-4 flex-1 justify-center"
                  >
                    <div className="h-[40svh] sm:h-[25svh] shadow-lg p-4 flex flex-col">
                      <div className="border border-solid border-neutral-300 p-4 size-full rounded-md">
                        <div className="size-full overflow-auto flex flex-col gap-2">
                          {selectedQuestion.image_url && (
                            <Image
                              src={getUrl(selectedQuestion.image_url)}
                              alt="Gambar untuk soal ujian"
                              width={100}
                              height={100}
                              preview={{
                                getContainer: app ?? undefined,
                              }}
                              className="min-w-20 object-cover"
                            />
                          )}
                          <div>{selectedQuestion.text}</div>
                        </div>
                      </div>
                    </div>
                    <div className="flex-1 p-4">
                      {selectedQuestion.type === "text" && (
                        <FormItem control={control} name={selectedQuestion.id}>
                          <Input.TextArea placeholder="Isi jawaban" rows={10} />
                        </FormItem>
                      )}

                      {selectedQuestion.type === "option" && (
                        <div className="p-4">
                          <FormItem
                            control={control}
                            name={selectedQuestion.id}
                          >
                            <Radio.Group
                              className="flex flex-col gap-2"
                              options={selectedQuestion.option_detail.options.map(
                                (option) => ({
                                  value: option.id,
                                  label: option.text,
                                })
                              )}
                            />
                          </FormItem>
                        </div>
                      )}
                      {selectedQuestion.type === "scale" && (
                        <div className="p-4 px-10">
                          <FormItem
                            control={control}
                            name={selectedQuestion.id}
                          >
                            <Slider
                              marks={{
                                1: (
                                  <div className="flex flex-col items-center gap-1">
                                    <div>1</div>
                                    <div className="w-24 text-center leading-none text-xs p-0.5 line-clamp-2">
                                      {selectedQuestion.scale_detail.min_label}
                                    </div>
                                  </div>
                                ),
                                2: "2",
                                3: "3",
                                4: "4",
                                5: (
                                  <div className="flex flex-col items-center gap-1">
                                    <div>5</div>
                                    <div className="w-24 text-center leading-none text-xs p-0.5 line-clamp-2">
                                      {selectedQuestion.scale_detail.max_label}
                                    </div>
                                  </div>
                                ),
                              }}
                              min={1}
                              max={5}
                              step={1}
                            />
                          </FormItem>
                        </div>
                      )}
                    </div>
                  </div>
                </Form>
              )
            );
          })()}
        </div>
        <div className="sm:flex md:hidden p-8 gap-4 justify-between items-center hidden shadow-[0_-4px_6px_-1px_rgba(0,0,0,0.1)] rounded-xl">
          <Button
            color="default"
            variant="outlined"
            size="large"
            disabled={selectedIndex === 0}
            onClick={() => {
              setSelectedIndex((prev) => prev - 1);
            }}
            icon={<ChevronLeft />}
          ></Button>
          <div>
            {selectedIndex + 1}/{maxIndex + 1}
          </div>
          <Button
            color="default"
            variant="outlined"
            size="large"
            onClick={() => {
              setSelectedIndex((prev) => prev + 1);
            }}
            disabled={selectedIndex === maxIndex}
            icon={<ChevronRight />}
          ></Button>
        </div>
      </div>
    </>
  );
}

function RekapitulasiButton<T extends FormValues>({
  questions,
  flaggedQuestionIds,
  control,
  exam_id,
  onEditClick,
  renderTrigger,
}: {
  questions: (ExamQuestion & { index: number })[];
  flaggedQuestionIds: string[];
  control: Control<T>;
  exam_id: number;
  onEditClick?: (exam_id: string) => void;
  renderTrigger?: (onClick: () => void) => ReactNode;
}) {
  const routeApi = getRouteApi(
    "/(main)/_layout/exams/$exam_id/_sessionAuth/session/"
  );
  const navigate = routeApi.useNavigate();
  const currentUser = useCurrentUser();
  const [open, { toggle }] = useBoolean();
  const notification = useNotificationAPI();
  const answers = useWatch({ control });

  const app = document.getElementById("app");
  const [, { exitFullscreen }] = useFullscreen(app);

  const [, setDisableAntiCheat] = useAtom(disableAntiCheatAtom);

  const ready =
    Object.values(answers).every((answer) => !!answer) &&
    flaggedQuestionIds.length === 0;

  const { control: submitControl, handleSubmit } = useForm({
    defaultValues: {
      force: ready,
      exam_id,
      user_id: currentUser.id,
      note: "",
    },
    resolver: valibotResolver(
      object({
        force: pipe(boolean(), literal(true, "Harus dicentang")),
        exam_id: number(),
        user_id: string(),
        note: string(),
      })
    ),
  });

  const { mutate: submitExamSession } = useSubmitExamSession({
    onSuccess() {
      notification.success({ message: "Ujian berhasil dikumpulkan" });
      navigate({ to: "/" });
      toggle();

      // disable anti-cheat before exiting fullscreen
      setDisableAntiCheat(true);
      setTimeout(() => {
        exitFullscreen();
        setDisableAntiCheat(false);
      }, 1000);
    },
    onError() {
      notification.error({ message: "Ujian gagal dikumpulkan" });
    },
  });

  const columns: ColumnsType<(typeof questions)[number]> = [
    {
      title: "No",
      dataIndex: "index",
      render: (value) => {
        return (
          <div className="flex items-center gap-1">
            <div>{value + 1}</div>
          </div>
        );
      },
      width: 80,
    },
    {
      title: "Status",
      dataIndex: "id",
      render: (id: string) => {
        const answer = answers[id];
        const flagged = flaggedQuestionIds.includes(id);

        return (
          <div className="flex items-center gap-1">
            {(() => {
              if (answer && flagged)
                return (
                  <div className="flex gap-2 items-center">
                    <div>
                      <Tag color="warning">Tersimpan</Tag> (ditandai)
                    </div>{" "}
                    <Flag01 className="w-4" />
                  </div>
                );
              if (answer) return <Tag color="default">Tersimpan</Tag>;
              if (flagged) return <Tag color="warning">Ditandai</Tag>;
              return <Tag color="error">Belum dijawab</Tag>;
            })()}
          </div>
        );
      },
    },
    {
      title: "Aksi",
      render: (_, record) => {
        return (
          <div className="flex gap-2 ">
            <Button
              onClick={() => {
                toggle();
                onEditClick?.(record.id);
              }}
              variant="outlined"
              icon={<Edit02 width={20} />}
            ></Button>
          </div>
        );
      },
      width: 80,
    },
  ];
  return (
    <>
      {renderTrigger ? (
        renderTrigger(toggle)
      ) : (
        <Button
          onClick={() => {
            toggle();
          }}
          variant="solid"
          color="primary"
        >
          Selesaikan Ujian
        </Button>
      )}
      <Modal
        getContainer={app ?? undefined}
        title={<div className="flex justify-center">Rekapitulasi Jawaban</div>}
        open={open}
        onCancel={() => {
          toggle();
        }}
        footer={null}
        width={800}
      >
        <div className="pt-4 flex flex-col gap-4">
          <Table
            rowKey={(record) => record.id}
            bordered
            columns={columns}
            dataSource={questions}
            scroll={{
              y: 300,
            }}
            pagination={false}
          />
          <Alert
            message={
              <div>
                Pastikan semua jawaban telah diisi dengan{" "}
                <span className="font-semibold">lengkap</span> dan{" "}
                <span className="font-semibold">benar</span>. Setelah
                dikumpulkan, Anda{" "}
                <span className="font-semibold">tidak dapat</span> mengedit atau
                mengakses kembali jawaban Anda.
              </div>
            }
            type="warning"
          />

          <Form
            onFinish={handleSubmit((values) => {
              submitExamSession(values);
            })}
            layout="vertical"
            requiredMark={false}
          >
            {!ready && (
              <FormItem
                control={submitControl}
                name="force"
                valuePropName="checked"
              >
                <Checkbox>
                  Terdapat soal yang belum dijawab atau ditandai. Yakin ingin
                  mengumpulkan?
                </Checkbox>
              </FormItem>
            )}
            <div className="flex justify-end w-full gap-3">
              <Button
                onClick={() => {
                  toggle();
                }}
              >
                Batal
              </Button>
              <Button htmlType="submit" variant="solid" color="primary">
                Akhiri Ujian
              </Button>
            </div>
          </Form>
        </div>
      </Modal>
    </>
  );
}

function OverviewButton<T extends FormValues>({
  questions,
  flaggedQuestionIds,
  control,
  exam_id,
  renderTrigger,
  onEditClick,
}: {
  questions: (ExamQuestion & { index: number })[];
  flaggedQuestionIds: string[];
  control: Control<T>;
  exam_id: number;
  renderTrigger?: (onClick: () => void) => ReactNode;
  onEditClick?: (exam_id: string) => void;
}) {
  const [open, { toggle }] = useBoolean();

  const app = document.getElementById("app");

  return (
    <>
      {renderTrigger ? (
        renderTrigger(toggle)
      ) : (
        <Button
          onClick={() => {
            toggle();
          }}
          variant="solid"
          color="primary"
        >
          Selesaikan Ujian
        </Button>
      )}
      <Modal
        getContainer={app ?? undefined}
        open={open}
        closeIcon={false}
        onCancel={() => {
          toggle();
        }}
        footer={null}
      >
        <div className="flex flex-col gap-4 border border-solid border-neutral-200 shadow-md rounded-md p-4">
          <div className="grid grid-cols-4 gap-2 overflow-auto max-h-[50svh] pe-2">
            {questions.map((question, i) => (
              <QuestionOverviewCell
                key={question.id}
                question_id={question.id as Path<T>}
                index={i}
                control={control}
                flagged={flaggedQuestionIds.includes(question.id)}
                onClick={() => {
                  toggle();
                  onEditClick?.(question.id);
                }}
              />
            ))}
          </div>
        </div>
        <div className="pt-4 flex flex-col gap-4">
          <div className="flex justify-end w-full gap-3">
            <ExitButton
              renderTrigger={(onClick) => {
                return (
                  <Button
                    onClick={() => {
                      toggle();
                      onClick();
                    }}
                  >
                    Keluar
                  </Button>
                );
              }}
            />
            <Button
              onClick={() => {
                toggle();
              }}
            >
              Kembali
            </Button>
            <RekapitulasiButton
              exam_id={exam_id}
              control={control}
              questions={questions.map((question, i) => ({
                ...question,
                index: i,
              }))}
              flaggedQuestionIds={flaggedQuestionIds}
              onEditClick={(exam_id) => {
                onEditClick?.(exam_id);
              }}
              renderTrigger={(onClick) => (
                <Button
                  onClick={() => {
                    toggle();
                    onClick();
                  }}
                  variant="solid"
                  color="primary"
                >
                  Selesaikan Ujian
                </Button>
              )}
            />
          </div>
        </div>
      </Modal>
    </>
  );
}

function QuestionFlag<T extends FormValues>({
  index,
  control,
  question_id,
  onClick,
  flagged,
}: {
  index: number;
  control: Control<T>;
  question_id: Path<T>;
  onClick: () => void;
  flagged: boolean;
}) {
  const answer = useWatch({ control, name: question_id });
  const token = theme.useToken().token;

  return (
    <div
      onClick={onClick}
      className="p-4 flex flex-col gap-2 rounded-md shadow-md border-solid border-neutral-200 border w-48 cursor-pointer"
    >
      <div className="font-semibold">Soal {index + 1}</div>
      <div>{answer ? "Jawaban tersimpan" : "Belum dijawab"}</div>
      <div
        className="flex gap-2 items-center"
        style={{
          color: flagged ? token.colorErrorText : token.colorTextDescription,
        }}
      >
        <Flag01 className="w-5" />
        {flagged ? "Soal ditandai" : "Tandai Soal"}
      </div>
    </div>
  );
}

function QuestionOverviewCell<T extends FormValues>({
  index,
  control,
  question_id,
  onClick,
  flagged,
}: {
  index: number;
  control: Control<T>;
  question_id: Path<T>;
  onClick: () => void;
  flagged: boolean;
}) {
  const answer = useWatch({ control, name: question_id });
  const token = theme.useToken().token;
  return (
    <div
      onClick={onClick}
      className="border cursor-pointer text-xl border-solid border-neutral-300 rounded-md flex flex-col items-center overflow-hidden"
    >
      <div className="p-2">{index + 1}</div>
      <div
        className="w-full h-4"
        style={{
          backgroundColor: (() => {
            if (answer && flagged) return token.colorWarningText;
            if (answer) return token.colorSuccessText;
            if (flagged) return token.colorErrorText;
            return token.colorTextDescription;
          })(),
        }}
      />
    </div>
  );
}
