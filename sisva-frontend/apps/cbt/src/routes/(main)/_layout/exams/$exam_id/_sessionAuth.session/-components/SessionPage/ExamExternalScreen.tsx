import { valibotResolver } from "@hookform/resolvers/valibot";
import { useExamWithClasses } from "@sisva/hooks/query/exam/useExams";
import { useSubmitExamSession } from "@sisva/hooks/query/exam/useExamSessions";
import { useStudentsExamSubmission } from "@sisva/hooks/query/exam/useExamSubmissions";
import { useCurrentUser, useNotificationAPI } from "@sisva/providers";
import { getRouteApi } from "@tanstack/react-router";
import { ArrowBlockLeft, Grid01, LogOut04 } from "@untitled-ui/icons-react";
import { useBoolean, useFullscreen } from "ahooks";
import { Alert, Button, Empty, Form, Modal, Skeleton, theme } from "antd";
import { useAtom, useSetAtom } from "jotai";
import { type ReactNode, useEffect } from "react";
import { useForm } from "react-hook-form";
import { boolean, literal, number, object, pipe, string } from "valibot";

import { navbarRightElementAtom } from "#/routes/(main)/-components/NavBar";

import ExitButton from "./ExitButton";
import { disableAntiCheatAtom, screenAtom } from "./SessionPage";
import TimeLeft from "./TimeLeft";
import useAntiCheat from "./useAntiCheat";

export default function ExamExternalScreen() {
  const currentUser = useCurrentUser();
  const token = theme.useToken().token;
  const routeApi = getRouteApi(
    "/(main)/_layout/exams/$exam_id/_sessionAuth/session/"
  );
  const { exam_id } = routeApi.useParams();
  const [, setScreen] = useAtom(screenAtom);
  const [disableAntiCheat] = useAtom(disableAntiCheatAtom);

  const { data: exam, isLoading: L1 } = useExamWithClasses({
    exam_id,
  });

  const { data: examSubmission, isLoading: L2 } = useStudentsExamSubmission({
    exam_id,
    student_id: currentUser.id,
  });

  const isLoading = L1 || L2;

  useAntiCheat({
    enabled: exam?.is_anti_cheat && !disableAntiCheat,
    onViolation() {
      setScreen("warning");
    },
  });

  const setRightElement = useSetAtom(navbarRightElementAtom);

  useEffect(() => {
    setRightElement(
      <>
        <ExitButton
          renderTrigger={(onClick) => {
            return (
              <>
                <div
                  onClick={onClick}
                  className="hidden sm:flex cursor-pointer flex-col items-center font-bold"
                  style={{
                    color: token.colorPrimary,
                  }}
                >
                  <LogOut04 />
                  <div>Keluar</div>
                </div>
                <Button
                  color="default"
                  onClick={onClick}
                  variant="outlined"
                  size="large"
                  className="sm:hidden"
                  icon={<ArrowBlockLeft />}
                ></Button>
              </>
            );
          }}
        />
        <TimeLeft
          exam_id={exam_id}
          render={({ color, timeLeft }) => {
            return (
              <div
                className="sm:hidden flex flex-col items-center gap-1 justify-center"
                style={{
                  color: color,
                }}
              >
                <div className="">Waktu tersisa</div>
                <div className="font-medium text-xl">{timeLeft}</div>
              </div>
            );
          }}
        />

        <RekapitulasiButton
          exam_id={exam_id}
          renderTrigger={(onClick) => (
            <Button
              onClick={() => {
                onClick();
              }}
              color="default"
              variant="outlined"
              size="large"
              icon={<Grid01 />}
              className="sm:hidden"
            ></Button>
          )}
        />
      </>
    );
    return () => setRightElement(null); // Cleanup on unmount
  }, [exam_id, setRightElement, token.colorPrimary]);

  return (
    <div className="flex flex-col items-center px-4 grow">
      <div className="size-full max-w-5xl py-4 md:py-8 flex flex-col gap-4">
        <div className="size-full flex flex-col gap-4">
          <div className="flex flex-col gap-4 thick-scrollbar flex-1">
            {(() => {
              if (isLoading) {
                return <Skeleton active />;
              }

              if (examSubmission?.status === "submitted") {
                return <Empty description="Ujian telah dikumpulkan" />;
              }

              return (
                <div className="flex flex-col-reverse md:flex-row md:items-start gap-4 flex-1">
                  <div className="flex-1 flex flex-col gap-4 self-stretch border border-solid border-neutral-200 shadow-md rounded-md">
                    {exam?.external_link && (
                      <iframe
                        src={processUrl(exam.external_link)}
                        className="border-0 size-full"
                      />
                    )}
                  </div>
                  <div className="hidden md:flex flex-col gap-4 md:w-64 sm:contents border border-solid border-neutral-200 shadow-md rounded-md p-4 sticky top-4">
                    <RekapitulasiButton
                      exam_id={exam_id}
                      renderTrigger={(onClick) => (
                        <Button
                          onClick={() => {
                            onClick();
                          }}
                          variant="solid"
                          color="primary"
                        >
                          Selesaikan Ujian
                        </Button>
                      )}
                    />
                    <TimeLeft
                      exam_id={exam_id}
                      render={({ color, timeLeft }) => {
                        return (
                          <div
                            className="md:hidden flex flex-row items-center gap-2 justify-center"
                            style={{
                              color: color,
                            }}
                          >
                            <div className="">Waktu tersisa</div>
                            <div className="font-medium text-xl">
                              {timeLeft}
                            </div>
                          </div>
                        );
                      }}
                    />
                  </div>
                </div>
              );
            })()}
          </div>
        </div>
      </div>
    </div>
  );
}

function RekapitulasiButton({
  exam_id,
  renderTrigger,
}: {
  exam_id: number;
  renderTrigger?: (onClick: () => void) => ReactNode;
}) {
  const routeApi = getRouteApi(
    "/(main)/_layout/exams/$exam_id/_sessionAuth/session/"
  );
  const navigate = routeApi.useNavigate();
  const currentUser = useCurrentUser();
  const [open, { toggle }] = useBoolean();
  const notification = useNotificationAPI();

  const app = document.getElementById("app");
  const [, { exitFullscreen }] = useFullscreen(app);
  const [, setDisableAntiCheat] = useAtom(disableAntiCheatAtom);

  const ready = true;

  const { handleSubmit } = useForm({
    defaultValues: {
      force: ready,
      exam_id,
      user_id: currentUser.id,
      note: "",
    },
    resolver: valibotResolver(
      object({
        force: pipe(boolean(), literal(true, "Harus dicentang")),
        exam_id: number(),
        user_id: string(),
        note: string(),
      })
    ),
  });

  const { mutate: submitExamSession } = useSubmitExamSession({
    onSuccess() {
      notification.success({ message: "Ujian berhasil dikumpulkan" });
      navigate({ to: "/" });
      toggle();
      // disable anti-cheat before exiting fullscreen
      setDisableAntiCheat(true);
      setTimeout(() => {
        exitFullscreen();
        setDisableAntiCheat(false);
      }, 1000);
    },
    onError() {
      notification.error({ message: "Ujian gagal dikumpulkan" });
    },
  });

  return (
    <>
      {renderTrigger ? (
        renderTrigger(toggle)
      ) : (
        <Button
          onClick={() => {
            toggle();
          }}
          variant="solid"
          color="primary"
        >
          Selesaikan Ujian
        </Button>
      )}
      <Modal
        getContainer={app ?? undefined}
        title={<div className="flex justify-center">Rekapitulasi Jawaban</div>}
        open={open}
        onCancel={() => {
          toggle();
        }}
        footer={null}
      >
        <div className="pt-4 flex flex-col gap-4">
          <Alert
            message={
              <div>
                Pastikan semua jawaban telah diisi dengan{" "}
                <span className="font-semibold">lengkap</span> dan{" "}
                <span className="font-semibold">benar</span>. Setelah
                dikumpulkan, Anda{" "}
                <span className="font-semibold">tidak dapat</span> mengedit atau
                mengakses kembali jawaban Anda.
              </div>
            }
            type="warning"
          />

          <Form
            onFinish={handleSubmit((values) => {
              submitExamSession(values);
            })}
            layout="vertical"
            requiredMark={false}
          >
            <div className="flex justify-end w-full gap-3">
              <Button
                onClick={() => {
                  toggle();
                }}
              >
                Batal
              </Button>
              <Button htmlType="submit" variant="solid" color="primary">
                Akhiri Ujian
              </Button>
            </div>
          </Form>
        </div>
      </Modal>
    </>
  );
}

function processUrl(originalURL: string) {
  try {
    const url = new URL(originalURL);

    // Check if the URL is from Google Forms
    if (
      url.hostname.includes("docs.google.com") &&
      url.pathname.includes("/forms/")
    ) {
      // Add or modify the 'embedded' query parameter to 'true'
      url.searchParams.set("embedded", "true");
      return url.toString();
    }

    // If not a Google Form URL, return the original URL
    return originalURL;
  } catch (error) {
    console.error("Error processing URL:", error);
    return originalURL;
  }
}
