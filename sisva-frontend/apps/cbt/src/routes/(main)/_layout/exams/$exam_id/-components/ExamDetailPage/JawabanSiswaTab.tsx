import { valibotResolver } from "@hookform/resolvers/valibot";
import { useStudentsStudentGroup } from "@sisva/hooks/query/academic/useStudentGroups";
import { useExamQuestions } from "@sisva/hooks/query/exam/useExamQuestions";
import { useExam, useExamWithClasses } from "@sisva/hooks/query/exam/useExams";
import {
  useSetExamScore,
  useStudentsExamScore,
  useStudentsExamScoreDetail,
} from "@sisva/hooks/query/exam/useExamScores";
import { useResetExamSession } from "@sisva/hooks/query/exam/useExamSessions";
import {
  useExamSubmissionAnswers,
  useStudentsExamSubmission,
} from "@sisva/hooks/query/exam/useExamSubmissions";
import { useStudentsByClassIds } from "@sisva/hooks/query/user/useStudents";
import { useUser } from "@sisva/hooks/query/user/useUsers";
import { useGetFileUrl } from "@sisva/hooks/utils";
import { useNotificationAPI } from "@sisva/providers";
import type { ExamQuestion } from "@sisva/types/apiTypes";
import { AvatarWithAcronymByID } from "@sisva/ui";
import { cn, roundTo, sum } from "@sisva/utils";
import { getRouteApi } from "@tanstack/react-router";
import { Check, RefreshCcw01, SearchSm } from "@untitled-ui/icons-react";
import { useToggle } from "ahooks";
import {
  Alert,
  Button,
  Divider,
  Empty,
  Form,
  Image,
  Input,
  InputNumber,
  Modal,
  Radio,
  Slider,
  theme,
} from "antd";
import dayjs from "dayjs";
import { sort } from "fast-sort";
import fuzzysort from "fuzzysort";
import { type ReactNode, useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { FormItem } from "react-hook-form-antd";
import {
  array,
  type InferInput,
  type InferOutput,
  maxValue,
  minValue,
  nonEmpty,
  nullable,
  number,
  object,
  pipe,
  string,
} from "valibot";

export default function JawabanSiswaTab() {
  const token = theme.useToken().token;
  const routeApi = getRouteApi("/(main)/_layout/exams/$exam_id/_adminAuth/");
  const { exam_id } = routeApi.useParams();
  const { data: exam } = useExamWithClasses({
    exam_id,
  });

  const hasEnded = exam
    ? dayjs(exam.end_time, "DD/MM/YYYY h:mm A Z").isBefore(dayjs())
    : true;

  const [selectedStudentId, setSelectedStudentId] = useState<
    string | undefined
  >();

  const { data: students = [], isLoading: L1 } = useStudentsByClassIds(
    exam?.class_ids ?? []
  );
  const selectedStudent = students.find(
    (student) => student.id === selectedStudentId
  );

  const { data: selectedStudentExamSubmission } = useStudentsExamSubmission({
    exam_id,
    student_id: selectedStudent?.id,
  });

  const firstStudentId = students[0]?.id;
  useEffect(() => {
    if (firstStudentId) setSelectedStudentId(firstStudentId);
  }, [firstStudentId]);

  const { control, watch } = useForm({
    defaultValues: {
      searchText: "",
    },
  });

  const searchText = watch("searchText");

  const filteredStudents = fuzzysort
    .go(searchText, students, {
      keys: ["name"],
      all: !searchText,
    })
    .map((data) => data.obj);

  return (
    <div className="flex flex-col gap-4">
      <div className="flex justify-between gap-4">
        <h2>Jawaban Siswa</h2>
      </div>
      {!hasEnded && (
        <Alert showIcon type="warning" message="Ujian ini belum berakhir" />
      )}

      <div className="size-full flex flex-col gap-4">
        <div
          className={cn(
            "flex rounded-lg shadow-md flex-col md:flex-row border border-solid border-neutral-200"
          )}
        >
          <div className="md:w-96 w-full border-[0] border-r border-solid border-neutral-300">
            <div className="p-4 font-semibold border-[0] border-b border-solid border-neutral-300">
              Daftar Siswa
            </div>
            <div className="p-4 border-[0] border-b border-solid border-neutral-300">
              <Form>
                <FormItem
                  control={control}
                  name="searchText"
                  className="contents"
                >
                  <Input
                    placeholder="Cari siswa"
                    suffix={<SearchSm color={token.colorTextLabel} />}
                  />
                </FormItem>
              </Form>
            </div>
            <div className="h-[50svh] overflow-auto">
              {filteredStudents.length > 0 ? (
                filteredStudents.map((student) => {
                  return (
                    <div
                      key={student.id}
                      className="flex gap-2 items-center p-4 text-sm border-[0] border-b border-solid border-neutral-300 cursor-pointer hover:bg-neutral-100 last:border-b-0"
                      onClick={() => {
                        setSelectedStudentId(student.id);
                      }}
                    >
                      <AvatarWithAcronymByID user_id={student.id} />
                      <div className="flex-1">
                        <div>{student.name}</div>
                        <div className="text-xs text-neutral-400">
                          <StudentGroupName student_id={student.id} />
                        </div>
                      </div>
                      <div className="flex items-center gap-0.5">
                        <ExamScore exam_id={exam_id} student_id={student.id} />
                        <div>/</div>
                        <div>100</div>
                      </div>
                    </div>
                  );
                })
              ) : (
                <div className="flex flex-col justify-center h-full">
                  <Empty />
                </div>
              )}
            </div>
          </div>
          <div className="flex flex-col gap-4 p-4 flex-1 h-[calc(50svh+122px)] overflow-auto">
            <div className="font-semibold">
              {selectedStudent?.name} -{" "}
              {selectedStudent && (
                <StudentGroupName student_id={selectedStudent.id} />
              )}
            </div>

            {selectedStudentExamSubmission ? (
              <div className="text-sm flex flex-col gap-4">
                <div className="text-neutral-600">
                  {selectedStudentExamSubmission.status === "submitted" &&
                    dayjs(
                      selectedStudentExamSubmission?.submit_time,
                      "DD/MM/YYYY h:mm A Z"
                    ).format("dddd DD/MM/YYYY h:mm A")}{" "}
                  {selectedStudentExamSubmission.status === "ongoing" && (
                    <span>(sedang berlangsung)</span>
                  )}
                </div>
                {selectedStudent && exam?.is_anti_cheat && (
                  <div>
                    <ResetExamSessionButton
                      exam_id={exam_id}
                      student_id={selectedStudent.id}
                      renderTrigger={(onClick) => {
                        return (
                          <Button
                            color="danger"
                            size="small"
                            variant="filled"
                            onClick={onClick}
                            icon={<RefreshCcw01 width={14} />}
                          >
                            Reset sesi ujian
                          </Button>
                        );
                      }}
                    />
                  </div>
                )}
              </div>
            ) : (
              <div className="text-neutral-600 text-sm">
                Belum mengikuti ujian
              </div>
            )}
            <Divider />

            {selectedStudent && (
              <ExamScoreForm
                student_id={selectedStudent.id}
                exam_id={exam_id}
              />
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

function StudentGroupName({ student_id }: { student_id: string }) {
  const { data: studentGroup } = useStudentsStudentGroup(student_id);

  return studentGroup?.name;
}

function ExamScore({
  exam_id,
  student_id,
}: {
  exam_id: number;
  student_id: string;
}) {
  const { data: examScore } = useStudentsExamScore({
    exam_id,
    student_id,
  });

  return examScore ? (
    roundTo(examScore.value, 2)
  ) : (
    <div className="border border-solid w-5 h-3 border-black rounded-sm"></div>
  );
}

function ExamScoreForm({
  student_id,
  exam_id,
}: {
  student_id: string;
  exam_id: number;
}) {
  const token = theme.useToken().token;
  const notification = useNotificationAPI();
  const getUrl = useGetFileUrl();
  const { data: examScore } = useStudentsExamScore({
    exam_id,
    student_id,
  });
  const { data: examScoreDetail } = useStudentsExamScoreDetail({
    exam_id: examScore?.exam_id,
    student_id: examScore?.user_id,
  });

  const { data: exam } = useExam({ exam_id });
  const { data: examQuestions = [] } = useExamQuestions({ exam_id });
  const { data: examSubmission } = useStudentsExamSubmission({
    exam_id,
    student_id: student_id,
  });
  const { data: submissionAnswers = [] } = useExamSubmissionAnswers({
    exam_id,
    user_id: student_id,
    enabled: !!examSubmission,
  });

  const questionIds = submissionAnswers.map((item) => item.question_id);

  const filteredExamQuestions = sort(
    examQuestions.filter((question) => questionIds.includes(question.id))
  ).asc((item) => item.id);

  const totalWeight = sum(filteredExamQuestions.map((item) => item.weight));

  const scoreSchema = object({
    student_id: pipe(string(), nonEmpty("Siswa harus dipilih")),
    feedback: pipe(string()),
    value: pipe(
      nullable(number()),
      number("Nilai harus diisi"),
      minValue(0, "Nilai minimum adalah 0"),
      maxValue(100, "Nilai maxium adalah 100")
    ),
    question_points: array(
      object({
        question_id: string(),
        value: number(),
      })
    ),
  });

  type SchemaInput = InferInput<typeof scoreSchema>;
  type SchemaOutput = InferOutput<typeof scoreSchema>;

  const { control, handleSubmit, setValue, reset, watch, clearErrors } =
    useForm<SchemaInput, unknown, SchemaOutput>({
      values: {
        student_id: student_id,
        value: examScore?.value ? roundTo((examScore.value, 2)) : null,
        feedback: examScore?.feedback ?? "",
        question_points: filteredExamQuestions.map((question) => {
          const scoreDetails = examScoreDetail?.find(
            ({ question_id }) => question_id === question.id
          );

          if (question.type !== "option")
            return {
              question_id: question.id,
              value: scoreDetails?.value ?? 0,
            };
          const correctAnswer = question.option_detail.options
            .filter((option) => option.correct)
            .map((option) => option.id);
          const answer =
            submissionAnswers
              .find((answer) => answer.question_id === question.id)
              ?.option_id_values?.filter((item) => item !== null) ?? [];
          const correct = answer.some((option_id) =>
            correctAnswer.includes(option_id)
          );

          return {
            question_id: question.id,
            value: scoreDetails?.value ?? (correct ? question.weight : 0),
          };
        }),
      },
      resolver: valibotResolver(scoreSchema),
    });

  const totalValue = sum(watch("question_points").map((item) => item.value));

  useEffect(() => {
    const value = (totalValue / totalWeight) * 100;

    setValue("value", isNaN(value) ? null : roundTo(value, 2));
  }, [setValue, totalValue, totalWeight]);

  function isAnswered({ question }: { question: ExamQuestion }) {
    const submissionAnswer = submissionAnswers.find(
      (answer) => answer.question_id === question.id
    );

    if (!submissionAnswer) return false;
    if (
      (!submissionAnswer.text_value && question.type === "text") ||
      (!submissionAnswer.text_value && question.type === "scale") ||
      (!submissionAnswer.option_id_values?.some((value) => value) &&
        question.type === "option")
    ) {
      return false;
    }
    return true;
  }

  const [numberOfUnevaluatedQuestion, indexes] = (() => {
    const indexes: number[] = [];
    const unevaluatedQuestions = filteredExamQuestions.filter(
      (question, index) => {
        if (question.type === "option") return false;
        const value = watch(`question_points.${index}.value`);
        if (value !== 0) return false;
        if (!isAnswered({ question })) return false;

        indexes.push(index);
        return true;
      }
    );

    return [unevaluatedQuestions.length, indexes];
  })();

  const { mutate: setExamScore } = useSetExamScore();

  const onFinish = handleSubmit((values) => {
    setExamScore(
      {
        exam_id,
        user_id: student_id,
        data: {
          feedback: values.feedback,
          details: values.question_points,
          value: values.value,
        },
      },
      {
        onSuccess() {
          notification.success({ message: "Nilai ujian berhasil diperbarui" });
        },
        onError() {
          notification.error({ message: "Nilai ujian gagal diperbarui" });
        },
      }
    );
  });

  return (
    <Form onFinish={onFinish} layout="vertical">
      {numberOfUnevaluatedQuestion > 0 && (
        <div className="pb-4">
          <Alert
            message={
              <span>
                Terdapat{" "}
                <span className="font-semibold">
                  {numberOfUnevaluatedQuestion}
                </span>{" "}
                soal yang belum dinilai, soal no:{" "}
                <span className="font-semibold">
                  {indexes.map((index) => index + 1).join(", ")}
                </span>
              </span>
            }
            type="warning"
          />
        </div>
      )}
      <FormItem
        control={control}
        name="value"
        label={<div className="font-semibold">Nilai</div>}
      >
        <InputNumber min={0} max={100} readOnly={exam?.type !== "external"} />
      </FormItem>
      <FormItem
        control={control}
        name="feedback"
        label={<div className="font-semibold">Komentar penilaian</div>}
      >
        <Input.TextArea placeholder="Tulis komentar penilaian di sini" />
      </FormItem>
      <div>
        <Button htmlType="submit" variant="solid" color="primary">
          Simpan Nilai
        </Button>
      </div>
      <Divider />
      <div className="flex flex-col gap-4">
        {filteredExamQuestions.map((question, index) => {
          const submissionAnswer = submissionAnswers.find(
            (answer) => answer.question_id === question.id
          );
          const answered = isAnswered({ question });
          return (
            <div key={question.id} className="flex gap-4 items-start">
              <div className="shadow-sm flex flex-col gap-4 p-4 rounded-md border-solid border-neutral-200 border flex-1">
                <div className="font-semibold text-lg">{index + 1}.</div>
                {!answered && <Alert message="Belum dijawab" type="warning" />}
                {question.image_url && (
                  <Image
                    src={getUrl(question.image_url)}
                    alt="Gambar untuk soal ujian"
                    width={80}
                    height={80}
                    className="min-w-20 object-cover"
                  />
                )}
                <div>{question.text}</div>
                {question.type === "text" && (
                  <Input.TextArea
                    readOnly
                    disabled={!answered}
                    className="cursor-not-allowed"
                    value={submissionAnswer?.text_value ?? undefined}
                  />
                )}
                {question.type === "option" && (
                  <Radio.Group
                    disabled={!answered}
                    className="flex flex-col gap-2"
                    options={question.option_detail.options.map((option) => ({
                      value: option.id,
                      label: option.correct ? (
                        <div className="p-1.5 rounded-sm bg-green-200 cursor-not-allowed">
                          {option.text}
                        </div>
                      ) : (
                        <div className="p-1.5 rounded-sm cursor-not-allowed">
                          {option.text}
                        </div>
                      ),
                    }))}
                    value={submissionAnswer?.option_id_values?.[0] ?? undefined}
                  />
                )}
                {question.type === "scale" && (
                  <div className="px-10 pb-8">
                    <Slider
                      disabled={!answered}
                      //@ts-expect-error allow null value
                      value={
                        isNaN(Number(submissionAnswer?.text_value))
                          ? null
                          : Number(submissionAnswer?.text_value)
                      }
                      marks={{
                        1: (
                          <div className="flex flex-col items-center gap-1">
                            <div>1</div>
                            <div className="w-24 text-center leading-none text-xs p-0.5 line-clamp-2">
                              {question.scale_detail.min_label}
                            </div>
                          </div>
                        ),
                        2: "2",
                        3: "3",
                        4: "4",
                        5: (
                          <div className="flex flex-col items-center gap-1">
                            <div>5</div>
                            <div className="w-24 text-center leading-none text-xs p-0.5 line-clamp-2">
                              {question.scale_detail.max_label}
                            </div>
                          </div>
                        ),
                      }}
                      min={1}
                      max={5}
                      step={1}
                      className="max-w-64 cursor-not-allowed"
                    />
                  </div>
                )}
              </div>
              <div className="p-4 border border-solid border-neutral-200 shadow-sm rounded-md flex flex-col gap-2">
                <div className="flex gap-2 justify-between pb-2">
                  Poin
                  {answered && question.type !== "option" && (
                    <Button
                      size="small"
                      color="primary"
                      variant="outlined"
                      icon={<Check width={16} />}
                      onClick={() =>
                        setValue(
                          `question_points.${index}.value`,
                          question.weight
                        )
                      }
                    ></Button>
                  )}
                </div>
                <FormItem
                  control={control}
                  name={`question_points.${index}.value`}
                  className="contents"
                  disabled={!answered}
                >
                  <InputNumber
                    readOnly={question.type === "option"}
                    className={cn("w-16", {
                      "cursor-not-allowed": question.type === "option",
                    })}
                    min={0}
                    max={question.weight}
                  />
                </FormItem>
                <div className="self-end text-xs text-neutral-500">
                  Bobot: {question.weight}
                </div>
              </div>
            </div>
          );
        })}
      </div>
    </Form>
  );
}

function ResetExamSessionButton({
  exam_id,
  student_id,
  renderTrigger,
}: {
  exam_id: number;
  student_id: string;
  renderTrigger?: (onClick: () => void) => ReactNode;
}) {
  const notification = useNotificationAPI();
  const [open, { toggle }] = useToggle();
  const { data: exam } = useExamWithClasses({
    exam_id,
  });
  const { data: student } = useUser(student_id);

  const { mutate: resetExamSession } = useResetExamSession();

  function onOk() {
    resetExamSession(
      {
        exam_id,
        data: {
          user_id: student_id,
        },
      },
      {
        onSuccess() {
          notification.success({
            message: "Sesi ujian berhasil direset",
          });
          toggle();
        },
        onError() {
          notification.error({
            message: "Sesi ujian gagal direset",
          });
        },
      }
    );
  }

  return (
    <>
      {renderTrigger?.(toggle)}
      <Modal
        okText="Reset"
        okButtonProps={{
          variant: "outlined",
        }}
        title="Reset sesi ujian"
        open={open}
        onOk={onOk}
        onCancel={toggle}
      >
        <div className="flex flex-col gap-4">
          <div className="flex flex-col gap-2">
            <div>
              <div className="font-semibold">Siswa</div>
              <div>{student?.name}</div>
            </div>
            <div>
              <div className="font-semibold">Nama Ujian</div>
              <div>{exam?.name}</div>
            </div>

            <div>
              <div className="font-semibold">Mata Pelajaran</div>
              <div>{exam?.classes[0]?.subject_name}</div>
            </div>
            <div>
              <div className="font-semibold">Tanggal</div>
              <div>
                {dayjs(exam?.start_time, "DD/MM/YYYY h:mm A Z").format(
                  "DD/MM/YYYY"
                )}
              </div>
            </div>
          </div>
        </div>
      </Modal>
    </>
  );
}
