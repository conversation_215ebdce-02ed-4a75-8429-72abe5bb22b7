import { createFileRoute, notFound, redirect } from "@tanstack/react-router";

import HomePage from "./-components/HomePage";

export const Route = createFileRoute("/(main)/_layout/")({
  beforeLoad({ context: { currentUser } }) {
    if (currentUser.type !== "student") throw redirect({ to: "/exams" });
  },
  head() {
    return {
      meta: [
        {
          title: "Beranda | Sisva",
        },
      ],
    };
  },
  component: HomePage,
});
