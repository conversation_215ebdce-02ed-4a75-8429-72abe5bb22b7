import { useDeleteSessionDataFromCookies } from "@sisva/hooks/query/useAuth";
import { useCurrentUser, useNotificationAPI } from "@sisva/providers";
import { useSchool } from "@sisva/providers";
import { AvatarWithAcronym } from "@sisva/ui";
import { useNavigate } from "@tanstack/react-router";
import { Lock01, LogOut04, User01, Users01 } from "@untitled-ui/icons-react";
import { useToggle } from "ahooks";
import { Divider, Modal, theme } from "antd";
import { useState } from "react";

import TabsLabel from "#/routes/(main)/-components/TabsLabel";

import PasswordSection from "./PasswordSection";
import StudentsBiodataSection from "./StudentsBiodataSection";
import WaliMuridSection from "./WaliMuridSection";

export default function ProfilePage() {
  const currentUser = useCurrentUser();
  const navigate = useNavigate();
  const school = useSchool();
  const notification = useNotificationAPI();
  const [currentTab, setCurrentTab] = useState<
    "biodata" | "guardian" | "security"
  >("biodata");
  const [open, { toggle }] = useToggle();

  const { mutate: deleteSessionDataFromCookies } =
    useDeleteSessionDataFromCookies({
      onSuccess() {
        notification.success({
          message: "Berhasil keluar dari akun",
        });
        navigate({ to: "/signin", search: { school_code: school.code } });
      },
    });

  return (
    <>
      <Modal
        title="Keluar dari akun?"
        open={open}
        onOk={() => deleteSessionDataFromCookies()}
        onCancel={toggle}
      ></Modal>
      <div className="size-full flex flex-col items-center px-4">
        <div className="size-full max-w-5xl pt-8 flex flex-col md:flex-row gap-8">
          <div className="md:w-96 w-full flex flex-col gap-8 md:min-w-96">
            <ProfilePrimaryCard />
            <div className="flex flex-col shadow-md rounded-lg p-4">
              <div className="flex flex-col gap-4">
                <TabsLabel
                  Icon={User01}
                  title="Biodata"
                  description="Informasi biodata akunmu"
                  active={currentTab === "biodata"}
                  onClick={() => setCurrentTab("biodata")}
                />
                {currentUser.type === "student" && (
                  <TabsLabel
                    Icon={Users01}
                    title="Wali Murid"
                    description="Informasi wali muridmu"
                    active={currentTab === "guardian"}
                    onClick={() => setCurrentTab("guardian")}
                  />
                )}
                <TabsLabel
                  Icon={Lock01}
                  title="Keamanan"
                  description="Pengaturan password akunmu"
                  active={currentTab === "security"}
                  onClick={() => setCurrentTab("security")}
                />
              </div>
              <Divider />
              <TabsLabel
                Icon={LogOut04}
                title="Sign Out"
                description="Keluar dari akun"
                onClick={toggle}
              />
            </div>
          </div>
          <div className="grow pb-4 md:overflow-auto pe-2">
            {(() => {
              switch (currentTab) {
                case "biodata":
                  return currentUser.type === "student" ? (
                    <StudentsBiodataSection />
                  ) : null;
                case "guardian":
                  return <WaliMuridSection />;
                case "security":
                  return <PasswordSection />;
              }
            })()}
          </div>
        </div>
      </div>
    </>
  );
}

function ProfilePrimaryCard() {
  const currentUser = useCurrentUser();
  const token = theme.useToken().token;
  return (
    <div
      style={{
        backgroundColor: token.colorPrimary,
      }}
      className="text-white p-6 flex flex-col items-center justify-center rounded-xl overflow-hidden relative gap-2"
    >
      <div className="size-40 absolute rounded-xl bg-gradient-to-r from-slate-50/0 to-slate-50/10 rotate-45 -left-12 bottom-0" />
      <div className="size-48 absolute rounded-3xl bg-gradient-to-r from-slate-50/0 to-slate-50/10 rotate-[135deg] -right-12 -bottom-16" />
      <div className="text-lg font-bold">Profilku</div>
      <AvatarWithAcronym
        name={currentUser.name}
        uri={currentUser.profile_image_uri}
        size={64}
      />
      <div className="text-lg font-bold">{currentUser.name}</div>
      <div className="text-sm">@{currentUser.username}</div>
    </div>
  );
}
