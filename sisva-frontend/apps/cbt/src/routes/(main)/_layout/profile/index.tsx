import { createFileRoute, notFound } from "@tanstack/react-router";

import ProfilePage from "./-components/ProfilePage";

export const Route = createFileRoute("/(main)/_layout/profile/")({
  beforeLoad({ context: { currentUser } }) {
    if (currentUser.type !== "student") throw notFound();
  },
  head() {
    return {
      meta: [
        {
          title: "Profil | Sisva",
        },
      ],
    };
  },
  component: ProfilePage,
});
