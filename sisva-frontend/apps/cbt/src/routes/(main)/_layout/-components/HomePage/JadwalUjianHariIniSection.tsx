import {
  type StudentsExam,
  useStudentsExams,
} from "@sisva/hooks/query/exam/useExams";
import { useCurrentUser } from "@sisva/providers";
import { cn } from "@sisva/utils";
import { Link } from "@tanstack/react-router";
import { Empty, Skeleton, theme } from "antd";
import dayjs from "dayjs";
import id from "dayjs/locale/id";
import { sort } from "fast-sort";
dayjs.locale(id);

const todayMidnigth = dayjs("00:00", "HH:MM");

export default function JadwalUjianHariIniSection() {
  const currentUser = useCurrentUser();
  const { data: exams = [], isLoading } = useStudentsExams(currentUser.id);
  const filteredExams = sort(
    exams.filter((exam) => {
      const startTime = dayjs(exam.start_time, "DD/MM/YYYY h:mm A Z");
      return (
        startTime.isAfter(todayMidnigth) &&
        startTime.isBefore(todayMidnigth.add(1, "d"))
      );
    })
  ).asc((exam) => dayjs(exam.start_time, "DD/MM/YYYY h:mm A Z"));

  const firstExamEndsAfterNow = sort(
    filteredExams.filter((exam) => {
      const endTime = dayjs(exam.end_time, "DD/MM/YYYY h:mm A Z");
      return endTime.isAfter(dayjs());
    })
  ).asc((exam) => dayjs(exam.start_time, "DD/MM/YYYY h:mm A Z"))[0];

  return (
    <div className="shadow-xl rounded-lg p-4 gap-4 flex flex-col">
      <div className="font-semibold">Jadwal ujian hari ini</div>
      <div className="overflow-auto max-h-64 pe-2 size-full flex flex-col gap-4">
        <div className="flex flex-col flex-1">
          {(() => {
            if (isLoading) return <Skeleton active />;
            if (filteredExams.length === 0)
              return (
                <div className="flex flex-col items-center justify-center flex-1">
                  <Empty description="Tidak ada ujian untuk hari ini" />
                </div>
              );

            return filteredExams.map((exam) => (
              <ExamCard
                key={exam.id}
                exam={exam}
                active={exam.id === firstExamEndsAfterNow?.id}
              />
            ));
          })()}
        </div>
      </div>
    </div>
  );
}

function ExamCard({ exam, active }: { exam: StudentsExam; active: boolean }) {
  const token = theme.useToken().token;

  return (
    <div className="flex gap-4 items-center border-0 border-solid border-b border-neutral-300 py-4">
      <div className="w-20">
        {dayjs(exam.start_time, "DD/MM/YYYY h:mm A Z").format("h:mm A")}
      </div>
      <Link
        to="/exams/$exam_id/session"
        params={{ exam_id: exam.id }}
        className="contents"
      >
        <div
          style={{
            backgroundColor: active ? token.colorPrimary : undefined,
          }}
          className={cn(
            " text-xs p-2  flex-1 rounded-md flex flex-col gap-1 relative overflow-hidden shadow-md",
            {
              "text-white": active,
              "border-neutral-200 border-solid border": !active,
            }
          )}
        >
          <div className="size-32 absolute rounded-full bg-gradient-to-r from-slate-50/10 to-slate-50/0 rotate-45 right-0 bottom-6" />

          <div className="text-base font-semibold">{exam.name}</div>
          <div>{exam.class?.subject_name}</div>
          <div>{exam.class?.teacher_name}</div>
        </div>
      </Link>
    </div>
  );
}
