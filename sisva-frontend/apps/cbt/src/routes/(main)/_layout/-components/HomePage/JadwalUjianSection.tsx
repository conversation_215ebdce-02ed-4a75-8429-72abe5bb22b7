import {
  type StudentsExam,
  useStudentsExams,
} from "@sisva/hooks/query/exam/useExams";
import { useStudentsExamSubmission } from "@sisva/hooks/query/exam/useExamSubmissions";
import { useCurrentUser } from "@sisva/providers";
import { Link } from "@tanstack/react-router";
import { File06, SearchSm } from "@untitled-ui/icons-react";
import { DatePicker, Empty, Form, Input, Select, Skeleton } from "antd";
import { Card, Divider, theme } from "antd";
import dayjs from "dayjs";
import { sort } from "fast-sort";
import fuzzysort from "fuzzysort";
import { Fragment, useDeferredValue } from "react";
import { useForm } from "react-hook-form";
import { FormItem } from "react-hook-form-antd";

import { GroupHeader } from "#/routes/(main)/-components/GroupHeader";

export default function JadwalUjianSection() {
  const currentUser = useCurrentUser();
  const token = theme.useToken().token;

  const { data: exams = [], isLoading } = useStudentsExams(currentUser.id);

  const { control, watch, setValue } = useForm({
    defaultValues: {
      searchText: "",
      subject_name: null,
      date_range: [dayjs().subtract(7, "d"), dayjs()],
    },
  });

  const searctText = watch("searchText");
  const subject_name = watch("subject_name");
  const date_range = watch("date_range");

  const start_date_filter = date_range?.[0] ?? null;
  const end_date_filter = date_range?.[1] ?? null;

  const defferedText = useDeferredValue(searctText);

  const filteredExamsByDate = exams
    .filter((exam) => {
      if (start_date_filter && end_date_filter) {
        const startTime = dayjs(exam.start_time, "DD/MM/YYYY h:mm A Z");
        return (
          startTime.isBefore(end_date_filter) &&
          startTime.isAfter(start_date_filter)
        );
      }
      return false;
    })
    .map((exam) => ({
      ...exam,
      date_id: dayjs(exam.start_time, "DD/MM/YYYY h:mm A Z").format(
        "DD/MM/YYYY"
      ),
    }));

  const subjectNames = [
    ...new Set(
      filteredExamsByDate
        .map((exam) => exam.class?.subject_name)
        .filter((item) => item !== undefined)
    ),
  ];

  const filteredExams = fuzzysort
    .go(
      defferedText,
      filteredExamsByDate.filter(
        (exam) => !subject_name || exam.class?.subject_name === subject_name
      ),
      {
        keys: ["name", "class.subject_name", "class.teacher_name"],
        all: !defferedText,
      }
    )
    .map((result) => result.obj);

  const groupedExamsByDateId = sort(
    Object.entries(Object.groupBy(filteredExams, (exam) => exam.date_id))
  ).desc(([date_id]) => dayjs(date_id, "DD/MM/YYYY"));

  return (
    <div className="rounded-xl shadow-xl w-full max-w-5xl p-4 sm:p-8 gap-4 flex flex-col min-h-96">
      <Form className="contents thick-scrollbar">
        <div className="flex gap-2 overflow-auto pe-1 flex-col sm:flex-row lg:items-center">
          <FormItem control={control} name="searchText" className="contents">
            <Input
              placeholder="Cari"
              suffix={<SearchSm color={token.colorTextLabel} />}
            />
          </FormItem>
          <FormItem control={control} name="date_range" className="contents">
            <DatePicker.RangePicker
              format="DD/MM/YYYY"
              onChange={() => {
                setValue("subject_name", null);
              }}
              className="w-full"
            />
          </FormItem>
          <FormItem control={control} name="subject_name" className="contents">
            <Select
              options={subjectNames.map((item) => ({
                label: item,
                value: item,
              }))}
              placeholder="Mata Pelajaran"
              allowClear
              showSearch
              className="min-w-48"
            />
          </FormItem>
        </div>
      </Form>
      <div className="flex flex-col gap-4 flex-1">
        {(() => {
          if (isLoading) return <Skeleton active />;
          if (filteredExams.length === 0)
            return (
              <div className="flex flex-col justify-center items-center flex-1">
                <Empty
                  description={
                    date_range
                      ? "Ujian tidak ditemukan"
                      : "Pilih tanggal terlebih dahulu"
                  }
                />
              </div>
            );
          return groupedExamsByDateId.map(([date_id, exams = []]) => {
            return (
              <Fragment key={date_id}>
                <GroupHeader
                  text={dayjs(date_id, "DD/MM/YYYY").format("dddd, DD/MM/YYYY")}
                />
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 items-start">
                  {sort(exams)
                    .asc((exam) =>
                      dayjs(exam.start_time, "DD/MM/YYYY h:mm A Z")
                    )
                    .map((exam) => (
                      <JadwalUjianCard exam={exam} key={exam.id} />
                    ))}
                </div>
              </Fragment>
            );
          });
        })()}
      </div>
    </div>
  );
}

function JadwalUjianCard({ exam }: { exam: StudentsExam }) {
  const token = theme.useToken().token;
  const currentUser = useCurrentUser();

  const { data: examSubmission } = useStudentsExamSubmission({
    exam_id: exam.id,
    student_id: currentUser.id,
  });

  const submitted = examSubmission?.status === "submitted";
  const ongoing = examSubmission?.status === "ongoing";
  const hasNotStarted = dayjs(exam.start_time, "DD/MM/YYYY h:mm A Z").isAfter(
    dayjs()
  );
  const hasEnded = dayjs(exam.end_time, "DD/MM/YYYY h:mm A Z").isBefore(
    dayjs()
  );

  const [statusText, color] = (() => {
    if (submitted) return ["Dikumpulkan", token.colorSuccess];
    if (hasNotStarted) return ["Belum dimulai", token.colorPrimary];
    if (hasEnded && ongoing)
      return ["Berakhir (belum dikumpulkan)", token.colorError];
    if (hasEnded) return ["Berakhir", token.colorError];
    if (ongoing) return ["Sedang berlangsung", token.colorWarning];
    return ["Siap dimulai", token.colorWarning];
  })();

  return (
    <Link
      to="/exams/$exam_id/session"
      params={{ exam_id: exam.id }}
      className="contents"
    >
      <Card
        cover={
          <div
            className="h-20"
            style={{
              backgroundColor: token.colorPrimary,
            }}
          />
        }
      >
        <div className=" flex flex-col gap-4">
          <div className="flex flex-col gap-1">
            <div
              className="rounded-full px-2 py-1 w-fit"
              style={{
                backgroundColor: token.colorPrimary,
              }}
            >
              <span className="text-white text-sm font-semibold line-clamp-1">
                {exam.class?.subject_name}
              </span>
            </div>
            <div className="text-neutral-500">{exam.class?.teacher_name}</div>
          </div>
          <div className="flex gap-2 items-center">
            <File06 color={token.colorPrimary} className="min-w-6" />
            <span className="text-base font-medium uppercase">{exam.name}</span>
          </div>
          <div className="flex items-center justify-between">
            <div className="flex flex-col gap-2">
              <span className="text-sm font-semibold">Tanggal Ujian</span>
              <span className="text-sm text-neutral-500">
                {dayjs(exam.start_time, "DD/MM/YYYY h:mm A Z").format(
                  "DD/MM/YYYY"
                )}
              </span>
            </div>
            <div className="flex flex-col gap-2">
              <span className="text-sm font-semibold">Mulai</span>
              <span className="text-sm text-neutral-500">
                {dayjs(exam.start_time, "DD/MM/YYYY h:mm A Z").format("h:mm A")}
              </span>
            </div>
            <div className="flex flex-col gap-2">
              <span className="text-sm font-semibold">Selesai</span>
              <span className="testtext-sm text-neutral-500">
                {dayjs(exam.end_time, "DD/MM/YYYY h:mm A Z").format("h:mm A")}
              </span>
            </div>
            <div className="flex flex-col gap-2"></div>
          </div>
          <Divider
            className="my-2 h-[2px] rounded-full opacity-50"
            style={{ backgroundColor: token.colorPrimary }}
          />
          <div className="flex items-center gap-2">
            <div
              className="w-5 h-5 rounded-full opacity-70"
              style={{ backgroundColor: color }}
            />
            <span className="text-sm text-neutral-700">{statusText}</span>
          </div>
        </div>
      </Card>
    </Link>
  );
}
