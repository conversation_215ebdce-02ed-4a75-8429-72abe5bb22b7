import {
  type StudentsExam,
  useStudentsExams,
} from "@sisva/hooks/query/exam/useExams";
import { useCurrentUser } from "@sisva/providers";
import { Link } from "@tanstack/react-router";
import { Empty, Skeleton, theme } from "antd";
import dayjs from "dayjs";
import id from "dayjs/locale/id";
dayjs.locale(id);

export default function JadwalUjianMendatangSection() {
  const currentUser = useCurrentUser();
  const { data: exams = [], isLoading } = useStudentsExams(currentUser.id);
  const filteredExams = exams.filter((exam) => {
    return dayjs(exam.start_time, "DD/MM/YYYY h:mm A Z").isAfter(dayjs());
  });

  return (
    <div className="shadow-xl rounded-lg p-4 gap-4 flex flex-col">
      <div className="font-semibold"><PERSON><PERSON><PERSON> yang akan datang</div>
      <div className="overflow-auto max-h-64 pe-2 size-full flex flex-col gap-4">
        <div className="flex flex-col gap-4 flex-1">
          {(() => {
            if (isLoading) return <Skeleton active />;
            if (filteredExams.length === 0)
              return (
                <div className="flex flex-col items-center justify-center flex-1">
                  <Empty description="Tidak ada ujian" />
                </div>
              );

            return filteredExams.map((exam) => (
              <ExamCard key={exam.id} exam={exam} />
            ));
          })()}
        </div>
      </div>
    </div>
  );
}

function ExamCard({ exam }: { exam: StudentsExam }) {
  const token = theme.useToken().token;

  return (
    <Link
      to="/exams/$exam_id/session"
      params={{ exam_id: exam.id }}
      className="contents"
    >
      <div
        style={{
          backgroundColor: token.colorPrimary,
        }}
        className="text-white text-xs p-2 rounded-md flex flex-col gap-1 relative overflow-hidden"
      >
        <div className="size-32 absolute rounded-full bg-gradient-to-r from-slate-50/10 to-slate-50/0 rotate-45 right-0 bottom-6" />

        <div className="text-base font-semibold">{exam.name}</div>
        <div>{exam.class?.subject_name}</div>
        <div>{exam.class?.teacher_name}</div>
        <div className="flex justify-between pt-1 items-center">
          <div>Waktu Pelaksanaan</div>
          <div className="flex gap-2 items-center">
            <div>
              {dayjs(exam.start_time, "DD/MM/YYYY h:mm A Z").format(
                "dddd, DD/MM/YYYY h:mm A"
              )}
            </div>
          </div>
        </div>
      </div>
    </Link>
  );
}
