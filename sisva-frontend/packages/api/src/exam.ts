import type { Exam, ExamOptionQuestion, ExamScaleQuestion, ExamScore, ExamSubmissionAnswer, ExamTextQuestion } from "@sisva/types/apiTypes";
import type { AxiosInstance, AxiosRequestConfig } from "axios";
import axios from "axios";

import { getBearerToken, getSchoolId, getUserId } from "./utils";
export type ExamQuestionPayload = Prettify<
  | Optional<
      Omit<ExamOptionQuestion, "option_detail"> & {
        option_detail: {
          text: string;
          correct?: boolean;
        };
      },
      "id"
    >
  | Optional<ExamTextQuestion, "id">
  | Optional<ExamScaleQuestion, "id">
>;

const ExamAPI = new (class {
  api: AxiosInstance;
  constructor() {
    this.api = axios.create({
      baseURL: "https://api-staging.sisva.id/exam/v1",
    });
  }

  getRequestConfig(additionalHeaders: Record<string, string> = {}): AxiosRequestConfig {
    const defaultHeaders = {
      "X-Sisva-Source": "classroom.test",
      "X-Sisva-UserID": getUserId(),
      "X-Sisva-SchoolID": getSchoolId(),
      Authorization: `Bearer ${getBearerToken()}`,
    };

    return {
      headers: {
        ...defaultHeaders,
        ...additionalHeaders,
      },
    };
  }

  getExams({
    with_teaching_plan_detail,
    class_id,
    teacher_id,
    student_id,
    period_id,
  }: {
    with_teaching_plan_detail?: boolean;
    class_id?: number | string;
    teacher_id?: string;
    student_id?: string;
    period_id?: number | string;
  } = {}) {
    const params = new URLSearchParams({
      with_teaching_plan_detail: with_teaching_plan_detail?.toString() ?? "",
      class_id: class_id?.toString() ?? "",
      teacher_id: teacher_id?.toString() ?? "",
      student_id: student_id?.toString() ?? "",
      period_id: period_id?.toString() ?? "",
    }).toString();
    return this.api.get<{ data: Exam[] }>(`/exams?${params}`, this.getRequestConfig());
  }

  getExam({ exam_id, with_teaching_plan_detail }: { exam_id: number | string; with_teaching_plan_detail?: boolean }) {
    const params = new URLSearchParams({
      with_teaching_plan_detail: with_teaching_plan_detail?.toString() ?? "",
    }).toString();
    return this.api.get<{ data: Exam }>(`/exams/${exam_id}?${params}`, this.getRequestConfig());
  }

  deleteExam({ exam_id }: { exam_id: number | string }) {
    return this.api.delete(`/exams/${exam_id}`, this.getRequestConfig());
  }

  createExam(data: Omit<Exam, "id" | "teaching_plan_details">) {
    return this.api.post(`/exams`, data, this.getRequestConfig());
  }

  updateExam({ exam_id, data }: { exam_id: number | string; data: Omit<Exam, "id" | "teaching_plan_details"> }) {
    return this.api.patch(`/exams/${exam_id}`, data, this.getRequestConfig());
  }

  getExamQuestions({ exam_id }: { exam_id: number | string }) {
    return this.api.get(`/exams/${exam_id}/questions`, this.getRequestConfig());
  }

  setExamQuestions({ exam_id, data }: { exam_id: number | string; data: ExamQuestionPayload[] }) {
    return this.api.put(`/exams/${exam_id}/questions`, data, this.getRequestConfig());
  }

  resetExamSession({ exam_id, data }: { exam_id: number | string; data: { user_id: string } }) {
    return this.api.post(`/exams/${exam_id}/session/reset`, data, this.getRequestConfig());
  }

  startExamSession({ exam_id, entry_code }: { exam_id: number | string; entry_code: string }) {
    return this.api.post(`/exams/${exam_id}/session/start`, { entry_code }, this.getRequestConfig());
  }

  submitExamSession({ exam_id, note }: { exam_id: number | string; note: string }) {
    return this.api.post(`/exams/${exam_id}/session/submit`, { note }, this.getRequestConfig());
  }

  getExamSubmissions({ exam_id, user_id }: { exam_id: number | string; user_id?: string }) {
    const params = new URLSearchParams({
      user_id: user_id ?? "",
    }).toString();
    return this.api.get(`/exams/${exam_id}/submissions?${params}`, this.getRequestConfig());
  }

  setSubmissionAnswers({ exam_id, data, user_id }: { exam_id: number | string; data: ExamSubmissionAnswer[]; user_id: string }) {
    return this.api.put(`/exams/${exam_id}/submissions/${user_id}/answers`, data, this.getRequestConfig());
  }

  getSubmissionAnswers({ exam_id, user_id }: { exam_id: number | string; user_id: string }) {
    return this.api.get(`/exams/${exam_id}/submissions/${user_id}/answers`, this.getRequestConfig());
  }

  getScores({ exam_id, user_id }: { exam_id: number | string; user_id?: string }) {
    const params = new URLSearchParams({
      user_id: user_id ?? "",
    }).toString();
    return this.api.get<{ data: ExamScore[] }>(`/exams/${exam_id}/scores?${params}`, this.getRequestConfig());
  }

  setScore({
    exam_id,
    user_id,
    data,
  }: {
    exam_id: number | string;
    data: {
      feedback: string;
      details?: {
        question_id: string;
        value: number;
      }[];
      value?: number;
    };
    user_id: string;
  }) {
    return this.api.put(`/exams/${exam_id}/scores/${user_id}`, data, this.getRequestConfig());
  }

  generateScore({ exam_id, user_id }: { exam_id: number | string; user_id: string }) {
    return this.api.post(`/exams/${exam_id}/scores/${user_id}/generate`, {}, this.getRequestConfig());
  }
})();

export default ExamAPI;
