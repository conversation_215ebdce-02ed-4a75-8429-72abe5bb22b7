import type { User } from "@sisva/types/apiTypes";
import type { AxiosInstance, AxiosRequestConfig } from "axios";
import axios from "axios";

import { getBearerToken, getSchoolId, getUserId } from "./utils";

const UsersAPI = new (class {
  api: AxiosInstance;
  constructor() {
    this.api = axios.create({
      baseURL: "https://api-staging.sisva.id/user/v1",
    });
  }

  getRequestConfig(
    additionalHeaders: Record<string, string> = {},
    option?: {
      token: string;
      user_id: string;
      school_id: string;
    }
  ): AxiosRequestConfig {
    const defaultHeaders = {
      "X-Sisva-Source": "tenant.user.test",
      "X-Sisva-UserID": option?.user_id ?? getUserId(),
      "X-Sisva-SchoolID": option?.school_id ?? getSchoolId(),
      Authorization: `Bearer ${option?.token ?? getBearerToken()}`,
    };

    return {
      headers: {
        ...defaultHeaders,
        ...additionalHeaders,
      },
    };
  }
  createUser(payload) {
    return this.api.post("/users", payload, this.getRequestConfig());
  }

  getAllUsers(params: string = "staff,teacher,student") {
    return this.api.get<{ data: User[] }>(`/users?types=${params}`, this.getRequestConfig());
  }

  getUserById(id: string, option?: { token: string; user_id: string; school_id: string }) {
    return this.api.get(`/users/${id}`, this.getRequestConfig({}, option));
  }
  getUserByIdWhenLogin({ userId, schoolId, token }: { userId: string; schoolId: string | number; token: string }) {
    const headers = {
      "X-Sisva-Source": "tenant.user.test",
      "X-Sisva-UserID": userId,
      "X-Sisva-SchoolID": schoolId,
      Authorization: `Bearer ${token}`,
    };
    return this.api.get(`/users/${userId}`, {
      headers,
    });
  }

  updateUserById(payload, id: string) {
    return this.api.patch(`/users/${id}`, payload, this.getRequestConfig());
  }

  getCurrentUser() {
    const userId = getUserId();
    if (!userId) throw new Error("User ID not found");
    return this.getUserById(userId);
  }
})();
export default UsersAPI;
