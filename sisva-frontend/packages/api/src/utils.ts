import Cookies from "js-cookie";

const prefix = "sisva";

export const COOKIES_KEY = {
  token: `${prefix}_token`,
  user_id: `${prefix}_user_id`,
  school_id: `${prefix}_school_id`,
  username: `${prefix}_username`,
};

export const getBearerToken = () => {
  return Cookies.get(COOKIES_KEY.token);
};

export const getUserId = () => {
  return Cookies.get(COOKIES_KEY.user_id);
};

export const getSchoolId = () => {
  return Cookies.get(COOKIES_KEY.school_id);
};
