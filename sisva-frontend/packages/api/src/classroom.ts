import type { Task, TeachingPlan } from "@sisva/types/apiTypes";
import type { AxiosInstance, AxiosRequestConfig } from "axios";
import axios from "axios";

import { getBearerToken, getSchoolId, getUserId } from "./utils";

const ClassroomAPI = new (class {
  api: AxiosInstance;
  constructor() {
    this.api = axios.create({
      baseURL: "https://api-staging.sisva.id/classroom/v1",
    });
  }

  getRequestConfig(additionalHeaders: Record<string, string> = {}): AxiosRequestConfig {
    const defaultHeaders = {
      "X-Sisva-Source": "classroom.test",
      "X-Sisva-UserID": getUserId(),
      "X-Sisva-SchoolID": getSchoolId(),
      Authorization: `Bearer ${getBearerToken()}`,
    };

    return {
      headers: {
        ...defaultHeaders,
        ...additionalHeaders,
      },
    };
  }

  getTasks({ class_id, period_id }: { class_id?: number | string; period_id?: number | string } = {}) {
    const params = new URLSearchParams({
      class_id: class_id?.toString() ?? "",
      period_id: period_id?.toString() ?? "",
    }).toString();
    return this.api.get<{ data: Task[] }>(`/tasks?${params}`, this.getRequestConfig());
  }

  getTask({ task_id }: { task_id: number | string }) {
    return this.api.get<{ data: Task }>(`/tasks/${task_id}`, this.getRequestConfig());
  }

  createTask(payload: {
    name: string;
    description: string;
    class_id: number;
    deadline: string;
    attachment_file_uri: string;
    allow_submission: boolean;
    allow_overdue_submission: boolean;
  }) {
    return this.api.post("/tasks", payload, this.getRequestConfig());
  }

  updateTask(
    payload: {
      name: string;
      description: string;
      class_id: number;
      deadline: string;
      attachment_file_uri: string;
      allow_submission: boolean;
      allow_overdue_submission: boolean;
    },
    task_id: number | string
  ) {
    return this.api.put(`/tasks/${task_id}`, payload, this.getRequestConfig());
  }

  deleteTask(task_id: number | string) {
    return this.api.delete(`/tasks/${task_id}`, this.getRequestConfig());
  }

  getScoresByTaskId({ task_id }: { task_id: number | string }) {
    return this.api.get(`/tasks/${task_id}/scores`, this.getRequestConfig());
  }

  getScoresByClassId({ class_id }: { class_id: number | string }) {
    const params = new URLSearchParams({
      class_id: class_id?.toString() ?? "",
    }).toString();
    return this.api.get(`/tasks/scores?${params}`, this.getRequestConfig());
  }

  getTeachingMaterials({
    class_id,
    subject_id,
    curriculum_id,
    study_program_id,
  }: {
    class_id?: number | string;
    subject_id?: number | string;
    curriculum_id?: number | string;
    study_program_id?: number | string;
  }) {
    const params = new URLSearchParams({
      class_id: class_id?.toString() ?? "",
      subject_id: subject_id?.toString() ?? "",
      curriculum_id: curriculum_id?.toString() ?? "",
      study_program_id: study_program_id?.toString() ?? "",
    }).toString();

    return this.api.get(`/teaching_materials?${params}`, this.getRequestConfig());
  }

  getTeachingMaterial({ id }: { id: number | string }) {
    return this.api.get(`/teaching_materials/${id}`, this.getRequestConfig());
  }

  createTeachingMaterial(payload: { subject_id: number | string; description: string; attachment_file_uri: string; grade: string }) {
    return this.api.post("/teaching_materials", payload, this.getRequestConfig());
  }

  updateTeachingMaterial(
    payload: {
      subject_id: number | string;
      grade: string;
      description: string;
      attachment_file_uri: string;
    },
    teaching_material_id: number | string
  ) {
    return this.api.patch(`/teaching_materials/${teaching_material_id}`, payload, this.getRequestConfig());
  }

  deleteTeachingMaterial(teaching_material_id: number | string) {
    return this.api.delete(`/teaching_materials/${teaching_material_id}`, this.getRequestConfig());
  }

  getTeachingPlans({ class_id }: { class_id?: number | string } = {}) {
    const params = new URLSearchParams({
      class_id: class_id?.toString() ?? "",
    }).toString();
    return this.api.get<{ data: TeachingPlan[] }>(`/teaching_plans?${params}`, this.getRequestConfig());
  }

  createTeachingPlan(payload: {
    title: string;
    markdown: string;
    class_id: number;
    teaching_materials: {
      id: number;
    }[];
    tasks: {
      id: number;
    }[];
    teaching_goal: string;
    teaching_activity: string;
    teaching_scoring: string;
  }) {
    return this.api.post<{ data: number }>("/teaching_plans", payload, this.getRequestConfig());
  }

  updateTeachingPlan(
    payload: {
      title: string;
      markdown: string;
      class_id: number;
      teaching_materials: {
        id: number;
      }[];
      tasks: {
        id: number;
      }[];
      teaching_goal: string;
      teaching_activity: string;
      teaching_scoring: string;
    },
    teaching_plan_id: number
  ) {
    return this.api.patch(`/teaching_plans/${teaching_plan_id}`, payload, this.getRequestConfig());
  }

  deleteTeachingPlan({ id }: { id: number | string }) {
    return this.api.delete(`/teaching_plans/${id}`, this.getRequestConfig());
  }

  setScore({
    task_id,
    value,
    student_id,
    feedback,
  }: {
    task_id: number | string;
    value: number;
    student_id: number | string;
    feedback?: string;
  }) {
    return this.api.put(`/tasks/${task_id}/scores`, { value, student_id, feedback }, this.getRequestConfig());
  }

  getSubmissions({ task_id }: { task_id: number | string }) {
    return this.api.get(`/tasks/${task_id}/submissions`, this.getRequestConfig());
  }

  setSubmission({
    task_id,
    student_id,
    attachment_file_uri,
    note,
  }: {
    task_id: number | string;
    student_id: number | string;
    attachment_file_uri: string;
    note?: string;
  }) {
    return this.api.put(`/tasks/${task_id}/submissions`, { student_id, note, attachment_file_uri }, this.getRequestConfig());
  }
})();

export default ClassroomAPI;
