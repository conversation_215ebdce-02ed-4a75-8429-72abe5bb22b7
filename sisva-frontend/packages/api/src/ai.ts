import type { AxiosInstance, AxiosRequestConfig } from "axios";
import axios from "axios";

import { getBearerToken, getSchoolId, getUserId } from "./utils";

const defaultSource = "ai.test";

export type EditTeachingPlanSectionPayload = {
  sectionType: "title" | "description" | "teachingGoal" | "teachingActivity" | "teachingScoring" | "tasks" | "teachingMaterials";
  currentContent: string;
  instructions: string;
};

export type GenerateTeachingPlanSectionPayload = {
  sectionType: "title" | "description" | "teachingGoal" | "teachingActivity" | "teachingScoring" | "tasks" | "teachingMaterials";
  title?: string;
  subject?: string;
  className?: string;
  specifications?: string;
};

export type GenerateExamQuestionsPayload = {
  subjectName: string;
  questionCount: number;
  name?: string;
  className?: string;
  description?: string;
  optionQuestionCount?: number;
  textQuestionCount?: number;
  scaleQuestionCount?: number;
};

export type GenerateSingleExamQuestionPayload = {
  questionType: "option" | "text" | "scale";
  subjectName: string;
  name?: string;
  className?: string;
  description?: string;
  customization?: string;
};

export type EditExamQuestionPayload = {
  currentQuestion: {
    text: string;
    type: "option" | "text" | "scale";
    weight: number;
    option_detail?: {
      options: Array<{
        text: string;
        correct: boolean;
      }>;
    };
    scale_detail?: {
      min_label: string;
      max_label: string;
    };
  };
  instructions: string;
  subjectName?: string;
  className?: string;
  examName?: string;
  examDescription?: string;
};

export type EditTaskPayload = {
  currentTask: {
    name: string;
    description: string;
  };
  instructions: string;
  className?: string;
  planTitle?: string;
  subjectName?: string;
};

export type AIEditedExamQuestion = {
  text: string;
  type: "option" | "text" | "scale";
  weight: number;
  option_detail?: {
    options: Array<{
      text: string;
      correct: boolean;
    }>;
  };
  scale_detail?: {
    min_label: string;
    max_label: string;
  };
};

const AIAPI = new (class {
  api: AxiosInstance;
  constructor() {
    this.api = axios.create({
      baseURL: "http://localhost:8080/api/ai", //change this to sisva deployment after everything is done
    });
  }

  getRequestConfig(additionalHeaders: Record<string, string> = {}): AxiosRequestConfig {
    const defaultHeaders = {
      "X-Sisva-Source": defaultSource,
      "X-Sisva-UserID": getUserId(),
      "X-Sisva-SchoolID": getSchoolId(),
      Authorization: `Bearer ${getBearerToken()}`,
      "Content-Type": "application/json",
    };
    console.log("Default Headers being sent:", defaultHeaders);
    return {
      headers: {
        ...defaultHeaders,
        ...additionalHeaders,
      },
    };
  }

  /**
   * Generate a teaching plan using AI
   */
  generateTeachingPlan(payload: { title: string; specifications?: string }) {
    return this.api.post<{
      description: string;
      teachingGoal: string;
      teachingActivity: string;
      teachingScoring: string;
      tasks: Array<{
        name: string;
        description: string;
      }>;
      teachingMaterials: Array<{
        title: string;
        content: string;
      }>;
    }>("/teaching-plan", payload, this.getRequestConfig());
  }

  /**
   * Generate a task using AI
   */
  generateTask(payload: { title: string; className?: string; planTitle?: string; subjectName?: string; specifications?: string }) {
    return this.api.post<{
      name: string;
      description: string;
    }>("/task", payload, this.getRequestConfig());
  }

  /**
   * Edit a task using AI
   */
  editTask(payload: EditTaskPayload) {
    return this.api.post<{
      name: string;
      description: string;
    }>("/edit-task", payload, this.getRequestConfig());
  }

  /**
   * Generate exam questions using AI
   */
  generateExamQuestions(payload: GenerateExamQuestionsPayload) {
    return this.api
      .post<{
        questions: Array<{
          text: string;
          type: "option" | "text" | "scale";
          weight: number;
          option_detail?: {
            options: Array<{
              text: string;
              correct: boolean;
            }>;
          };
          scale_detail?: {
            min_label: string;
            max_label: string;
          };
        }>;
      }>("/exam-questions", payload, this.getRequestConfig())
      .then((response) => {
        console.log("Response received:", response.data);
        console.log("Headers received:", response.headers);
        return response.data;
      });
  }

  /**
   * Generate a single exam question using AI
   */
  generateSingleExamQuestion(payload: GenerateSingleExamQuestionPayload) {
    return this.api.post<{
      text: string;
      type: "option" | "text" | "scale";
      weight: number;
      option_detail?: {
        options: Array<{
          text: string;
          correct: boolean;
        }>;
      };
      scale_detail?: {
        min_label: string;
        max_label: string;
      };
    }>("/single-exam-question", payload, this.getRequestConfig());
  }

  /**
   * Generate teaching materials using AI
   */
  generateTeachingMaterials(payload: { title: string; subject: string; specifications?: string }) {
    return this.api.post<{
      title: string;
      content: string;
    }>("/teaching-materials", payload, this.getRequestConfig());
  }

  /**
   * Edit a section of a teaching plan using AI
   */
  editTeachingPlanSection(payload: EditTeachingPlanSectionPayload) {
    return this.api.post<string>("/edit-teaching-plan-section", payload, this.getRequestConfig());
  }

  /**
   * Generate a specific section of a teaching plan using AI
   */
  generateTeachingPlanSection(payload: GenerateTeachingPlanSectionPayload) {
    return this.api.post<string>("/generate-teaching-plan-section", payload, this.getRequestConfig());
  }

  /**
   * Edit an exam question using AI
   */
  editExamQuestion(payload: EditExamQuestionPayload) {
    return this.api.post<AIEditedExamQuestion>("/edit-exam-question", payload, this.getRequestConfig());
  }
})();

export default AIAPI;
