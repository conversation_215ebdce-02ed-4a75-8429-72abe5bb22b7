import type { Report, ReportScore, Template } from "@sisva/types/apiTypes";
import type { AxiosInstance, AxiosRequestConfig } from "axios";
import axios from "axios";

import { getBearerToken, getSchoolId, getUserId } from "./utils";

export type CreateReportPayload = Omit<Report, "id" | "status">;

const ReportAPI = new (class {
  api: AxiosInstance;
  constructor() {
    this.api = axios.create({
      baseURL: "https://api-staging.sisva.id/report/v1",
    });
  }

  getRequestConfig(additionalHeaders: Record<string, string> = {}): AxiosRequestConfig {
    const defaultHeaders = {
      "X-Sisva-Source": "classroom.test",
      "X-Sisva-UserID": getUserId(),
      "X-Sisva-SchoolID": getSchoolId(),
      Authorization: `Bearer ${getBearerToken()}`,
    };

    return {
      headers: {
        ...defaultHeaders,
        ...additionalHeaders,
      },
    };
  }

  getTemplates() {
    return this.api.get<{ data: Template[] }>(`/templates`, this.getRequestConfig());
  }

  getTemplate({ template_id }: { template_id: number | string }) {
    return this.api.get<{ data: Template }>(`/templates/${template_id}`, this.getRequestConfig());
  }

  getReports() {
    return this.api.get<{ data: Report[] }>(`/reports`, this.getRequestConfig());
  }

  getReport({ report_id }: { report_id: string | number }) {
    return this.api.get<{ data: Report }>(`/reports/${report_id}`, this.getRequestConfig());
  }

  createReport(payload: CreateReportPayload) {
    return this.api.post(`/reports`, payload, this.getRequestConfig());
  }

  updateReport(payload: Report) {
    return this.api.patch(`/reports/${payload.id}`, payload, this.getRequestConfig());
  }

  deleteReport({ report_id }: { report_id: string | number }) {
    return this.api.delete(`/reports/${report_id}`, this.getRequestConfig());
  }

  getStudentScores({ report_id }: { report_id: string | number }) {
    return this.api.get<{ data: ReportScore[] }>(`/student-scores?report_id=${report_id}`, this.getRequestConfig());
  }

  setStudentScores(payload: ReportScore[]) {
    return this.api.put(`/student-scores`, payload, this.getRequestConfig());
  }

  deleteStudentScores(payload: { report_id: string | number; class_id?: string | number; student_id?: string }) {
    return this.api.delete(`/student-scores`, {
      headers: this.getRequestConfig().headers,
      data: payload,
    });
  }
})();

export default ReportAPI;
