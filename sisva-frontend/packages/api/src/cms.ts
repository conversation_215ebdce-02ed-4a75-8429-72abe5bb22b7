import type { AxiosInstance, AxiosRequestConfig } from "axios";
import axios from "axios";

import { getBearerToken, getSchoolId, getUserId } from "./utils";

const defaultSource = "cms.test";

const CMSAPI = new (class {
  api: AxiosInstance;
  constructor() {
    this.api = axios.create({
      baseURL: "https://api-staging.sisva.id/tenant/v1",
    });
  }

  getRequestConfig(additionalHeaders: Record<string, string> = {}): AxiosRequestConfig {
    const defaultHeaders = {
      "X-Sisva-Source": defaultSource,
      "X-Sisva-UserID": getUserId(),
      "X-Sisva-SchoolID": getSchoolId(),
      Authorization: `Bearer ${getBearerToken()}`,
    };

    return {
      headers: {
        ...defaultHeaders,
        ...additionalHeaders,
      },
    };
  }

  getSchoolById(id: string) {
    const headers = { "X-Sisva-Source": defaultSource };
    return this.api.get(`/schools/${id}`, { headers });
  }
  getSchoolByCode(code: string) {
    const headers = {
      "X-Sisva-Source": defaultSource,
    };
    return this.api.get(`/schools?code=${code}`, { headers });
  }
  editSchoolById(id, payload) {
    return this.api.patch(`/schools/${id}`, payload, this.getRequestConfig());
  }
  getCurrentSchool() {
    const schoolId = getSchoolId();
    if (!schoolId) throw new Error("School ID not found");
    return this.getSchoolById(schoolId);
  }
})();
export default CMSAPI;
