import type { AxiosInstance, AxiosRequestConfig } from "axios";
import axios from "axios";

import { getBearerToken, getSchoolId, getUserId } from "./utils";

const AttendanceAPI = new (class {
  api: AxiosInstance;
  constructor() {
    this.api = axios.create({
      baseURL: "https://api-staging.sisva.id/attendance/v1",
    });
  }

  getRequestConfig(additionalHeaders: Record<string, string> = {}): AxiosRequestConfig {
    const defaultHeaders = {
      "X-Sisva-Source": "attendance.test",
      "X-Sisva-UserID": getUserId(),
      "X-Sisva-SchoolID": getSchoolId(),
      Authorization: `Bearer ${getBearerToken()}`,
    };

    return {
      headers: {
        ...defaultHeaders,
        ...additionalHeaders,
      },
    };
  }

  getStaffAttendanceByDateId(date_id) {
    return this.api.get(`/staff?date_id=${date_id}`, this.getRequestConfig());
  }

  getStudentAttendanceByDateId(date_id) {
    return this.api.get(`/students?date_id=${date_id}`, this.getRequestConfig());
  }

  getStudentClassAttendanceByDateId(date_id) {
    return this.api.get(`/classes/students?date_id=${date_id}`, this.getRequestConfig());
  }

  createStaffAttendance(id, payload) {
    return this.api.put(`/staff/${id}`, payload, this.getRequestConfig());
  }

  createStudentAttendance(id, payload) {
    return this.api.put(`/students/${id}`, payload, this.getRequestConfig());
  }

  updateStudentClassAttendance({
    class_id,
    student_id,
    date_id,
    status,
  }: {
    class_id: number | string;
    student_id: string;
    date_id: number;
    status: string;
  }) {
    return this.api.put(
      `/classes/${class_id}/students/${student_id}`,
      {
        date_id,
        status,
      },
      this.getRequestConfig()
    );
  }
})();

export default AttendanceAPI;
