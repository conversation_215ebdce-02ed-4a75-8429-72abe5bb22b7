import type { AxiosInstance, AxiosRequestConfig } from "axios";
import axios from "axios";

import { getBearerToken, getSchoolId, getUserId } from "./utils";

const FinanceAPI = new (class {
  api: AxiosInstance;
  constructor() {
    this.api = axios.create({
      baseURL: "https://api-staging.sisva.id/finance/v1",
    });
  }

  getRequestConfig(additionalHeaders: Record<string, string> = {}): AxiosRequestConfig {
    const defaultHeaders = {
      "X-Sisva-Source": "finance.test",
      "X-Sisva-UserID": getUserId(),
      "X-Sisva-SchoolID": getSchoolId(),
      Authorization: `Bearer ${getBearerToken()}`,
    };

    return {
      headers: {
        ...defaultHeaders,
        ...additionalHeaders,
      },
    };
  }

  updateInvoice(id, payload) {
    return this.api.patch(`/invoices/${id}`, payload, this.getRequestConfig());
  }

  updateBill(id, payload) {
    return this.api.patch(`/bills/${id}`, payload, this.getRequestConfig());
  }

  updatePaymentProof(id, payload) {
    return this.api.patch(`/invoices/${id}/payment-proof`, payload, this.getRequestConfig());
  }

  createBill(payload) {
    return this.api.post("/bills", payload, this.getRequestConfig());
  }

  createUserBill(payload) {
    return this.api.post("/user-bills", payload, this.getRequestConfig());
  }
  createInvoice(payload) {
    return this.api.post("/invoices", payload, this.getRequestConfig());
  }

  getAllInvoices({ bill_id, user_id }: { bill_id?: string | number; user_id?: string | number }) {
    const params = new URLSearchParams({
      bill_id: bill_id?.toString() ?? "",
      user_id: user_id?.toString() ?? "",
    }).toString();
    return this.api.get(`/invoices?${params}`, this.getRequestConfig());
  }

  getAllBills() {
    return this.api.get(`/bills`, this.getRequestConfig());
  }

  getBillById(id) {
    return this.api.get(`/bills/${id}`, this.getRequestConfig());
  }

  getInvoiceById(id) {
    return this.api.get(`/invoices/${id}`, this.getRequestConfig());
  }

  getAllUserBill({ bill_id }: { bill_id?: string | number } = {}) {
    const params = new URLSearchParams({
      bill_id: bill_id?.toString() ?? "",
    }).toString();
    return this.api.get(`/user-bills?${params}`, this.getRequestConfig());
  }

  deleteInvoice(id) {
    return this.api.delete(`/invoices/${id}`, this.getRequestConfig());
  }

  deleteBill(id) {
    return this.api.delete(`/bills/${id}`, this.getRequestConfig());
  }

  deleteUserBill(id) {
    return this.api.delete(`/user-bills/${id}`, this.getRequestConfig());
  }
})();

export default FinanceAPI;
