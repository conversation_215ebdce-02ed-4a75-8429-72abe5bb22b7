{
  "extends": "@sisva/typescript-config/tsconfig.base.json",
  "compilerOptions": {
    // --- strictness ---
    "strict": false,
    "strictNullChecks": true,
    "strictBindCallApply": true,
    "strictBuiltinIteratorReturn": true,
    "strictPropertyInitialization": true,
    "noImplicitThis": true,
    "useUnknownInCatchVariables": true,

    // --- TODO: make this true later ---
    "noImplicitAny": false,
    "strictFunctionTypes": true,
    "noUncheckedIndexedAccess": true,
    // --- strictness ---

    "outDir": "dist",
    "rootDir": "src"
  },
  "include": ["src", "utils.d.ts"],
  "exclude": ["node_modules", "dist"]
}
