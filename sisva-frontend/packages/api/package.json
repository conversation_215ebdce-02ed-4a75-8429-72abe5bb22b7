{"name": "@sisva/api", "version": "0.0.0", "private": true, "scripts": {"lint": "eslint ./src", "lint:fix": "eslint ./src --fix", "typecheck": "tsc --noEmit", "build": "tsup", "dev": "tsup --watch"}, "exports": {"./ai": "./dist/ai.js", "./academic": "./dist/academic.js", "./attendance": "./dist/attendance.js", "./auth": "./dist/auth.js", "./classroom": "./dist/classroom.js", "./cms": "./dist/cms.js", "./exam": "./dist/exam.js", "./files": "./dist/files.js", "./finance": "./dist/finance.js", "./reports": "./dist/reports.js", "./users": "./dist/users.js", "./utils": "./dist/utils.js"}, "devDependencies": {"@sisva/eslint-config": "workspace:*", "@sisva/typescript-config": "workspace:*", "@types/node": "^22.13.0", "@types/react": "19.0.8", "@types/react-dom": "19.0.3", "eslint": "^9.21.0", "tsup": "8.3.0", "typescript": "5.7.3"}, "dependencies": {"@types/js-cookie": "^3.0.6", "@sisva/types": "workspace:*", "axios": "^1.8.1", "js-cookie": "^3.0.5", "react": "^19.0.0", "react-dom": "^19.0.0"}}