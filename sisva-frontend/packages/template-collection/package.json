{"name": "@sisva/template-collection", "version": "0.0.0", "private": true, "scripts": {"lint": "eslint ./src", "lint:fix": "eslint ./src --fix", "typecheck": "tsc --noEmit", "build": "tsup", "dev": "tsup --watch"}, "exports": {".": "./dist/index.js", "./css/*": "./src/css/*"}, "devDependencies": {"@sisva/eslint-config": "workspace:*", "@sisva/typescript-config": "workspace:*", "@types/js-cookie": "^3.0.6", "@types/node": "^22.13.0", "@types/react": "19.0.8", "@types/react-dom": "19.0.3", "eslint": "^9.21.0", "tsup": "8.3.0", "typescript": "5.7.3"}, "dependencies": {"@sisva/api": "workspace:*", "@sisva/types": "workspace:*", "clsx": "^2.1.1", "currency.js": "^2.0.4", "dayjs": "^1.11.13", "dayjs-range-extend": "^1.0.4", "js-cookie": "^3.0.5", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^3.0.2", "valibot": "1.1.0"}}