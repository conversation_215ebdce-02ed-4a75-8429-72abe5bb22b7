export function template1Input(input: Template1Input) {
  return input;
}

type Template1Input = {
  student: {
    name: string;
    detail: {
      grade: string;
    };
    nik: string;
  };
  study_program: {
    code: string;
  };
  period: {
    name: string;
  };
  kkm: number;
  class: Record<
    ClassKey,
    {
      subject_name: string;
      teacher_name: string;
      score_num_1: number;
      score_num_2: number;
      score_text_1: string;
      score_text_2: string;
      score_grade_1: string;
    }
  >;
  score_sum_1: number;
  score_sum_2: number;
  score_avg_1: number;
  score_avg_2: number;
  attendance: Record<
    AttendanceKey,
    {
      count: number;
    }
  >;
  rank: number;
  student_group: {
    size: number;
  };
  user: Record<
    UserKey,
    {
      name: string;
      nik: string;
    }
  >;
};

type ClassKey =
  | "qurdis"
  | "aqidah"
  | "fiqih"
  | "ski"
  | "ppkn"
  | "bindo"
  | "barab"
  | "mtk"
  | "sindo"
  | "bing"
  | "sbu"
  | "pjok"
  | "pkwu"
  | "keterampilanagama"
  | "mtk"
  | "biologi"
  | "fisika"
  | "kimia"
  | "sosiologi"
  | "informatika"
  | "rpl"
  | "multimedia";

type AttendanceKey = "present" | "sick" | "leave" | "absent";

type UserKey = "walikelas" | "kepalamadrasah";

const placeholder_json = [
  {
    key: "student",
    kind: {
      key: "student",
      id: "$student_id",
    },
  },
  {
    key: "study_program",
    kind: {
      key: "study_program",
      id: "$study_program_id",
    },
  },
  {
    key: "kkm",
    kind: {
      key: "kkm",
      id: "$school_id",
    },
  },
  {
    key: "rank",
    kind: {
      key: "student_rank",
      id: "$student_id",
    },
  },
  {
    key: "student_group",
    kind: {
      key: "student_group",
      id: "$student_group_id",
    },
  },
  {
    key: "attendance",
    kind: {
      key: "attendance",
      id: "student_id",
    },
  },
  {
    key: "period",
    kind: {
      key: "period",
      id: "$period_id",
    },
  },
  {
    key: "score_sum_1",
    kind: {
      key: "score_sum_1",
    },
  },
  {
    key: "score_sum_2",
    kind: {
      key: "score_sum_2",
    },
  },
  {
    key: "score_avg_1",
    kind: {
      key: "score_avg_1",
    },
  },
  {
    key: "score_avg_1",
    kind: {
      key: "score_avg_1",
    },
  },
  {
    key: "user",
    kind: {
      key: "user",
      user: {
        walikelas: {
          id: "asdf-asdf-asdf-asdf",
        },
        kepalamadrasah: {
          id: "zxcv-zxcv-zxcv-zxcv",
        },
      },
    },
  },
  {
    key: "class",
    kind: {
      key: "class",
      class: {
        qurdis: {
          teacher_id: "asdf-asdf-asdf-asdf",
          subject_id: 58,
          student_group_id: "$student_group_id",
        },
        aqidah: {
          teacher_id: "asdf-asdf-asdf-asdf",
          subject_id: 58,
          student_group_id: "$student_group_id",
        },
        fiqih: {
          teacher_id: "asdf-asdf-asdf-asdf",
          subject_id: 58,
          student_group_id: "$student_group_id",
        },
        ski: {
          teacher_id: "asdf-asdf-asdf-asdf",
          subject_id: 58,
          student_group_id: "$student_group_id",
        },
        ppkn: {
          teacher_id: "asdf-asdf-asdf-asdf",
          subject_id: 58,
          student_group_id: "$student_group_id",
        },
        bindo: {
          teacher_id: "asdf-asdf-asdf-asdf",
          subject_id: 58,
          student_group_id: "$student_group_id",
        },
        barab: {
          teacher_id: "asdf-asdf-asdf-asdf",
          subject_id: 58,
          student_group_id: "$student_group_id",
        },
        mtk: {
          teacher_id: "asdf-asdf-asdf-asdf",
          subject_id: 58,
          student_group_id: "$student_group_id",
        },
        sindo: {
          teacher_id: "asdf-asdf-asdf-asdf",
          subject_id: 58,
          student_group_id: "$student_group_id",
        },
        bing: {
          teacher_id: "asdf-asdf-asdf-asdf",
          subject_id: 58,
          student_group_id: "$student_group_id",
        },
        sbu: {
          teacher_id: "asdf-asdf-asdf-asdf",
          subject_id: 58,
          student_group_id: "$student_group_id",
        },
        pjok: {
          teacher_id: "asdf-asdf-asdf-asdf",
          subject_id: 58,
          student_group_id: "$student_group_id",
        },
        pkwu: {
          teacher_id: "asdf-asdf-asdf-asdf",
          subject_id: 58,
          student_group_id: "$student_group_id",
        },
        keteampilanagama: {
          teacher_id: "asdf-asdf-asdf-asdf",
          subject_id: 58,
          student_group_id: "$student_group_id",
        },
        biologi: {
          teacher_id: "asdf-asdf-asdf-asdf",
          subject_id: 58,
          student_group_id: "$student_group_id",
        },
        fisika: {
          teacher_id: "asdf-asdf-asdf-asdf",
          subject_id: 58,
          student_group_id: "$student_group_id",
        },
        kimia: {
          teacher_id: "asdf-asdf-asdf-asdf",
          subject_id: 58,
          student_group_id: "$student_group_id",
        },
        sosiologi: {
          teacher_id: "asdf-asdf-asdf-asdf",
          subject_id: 58,
          student_group_id: "$student_group_id",
        },
        informatika: {
          teacher_id: "asdf-asdf-asdf-asdf",
          subject_id: 58,
          student_group_id: "$student_group_id",
        },
        rpl: {
          teacher_id: "asdf-asdf-asdf-asdf",
          subject_id: 58,
          student_group_id: "$student_group_id",
        },
        multimedia: {
          teacher_id: "asdf-asdf-asdf-asdf",
          subject_id: 58,
          student_group_id: "$student_group_id",
        },
      },
    },
  },
];
