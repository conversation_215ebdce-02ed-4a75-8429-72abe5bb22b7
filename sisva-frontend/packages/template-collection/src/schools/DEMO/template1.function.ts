import type * as jspdf from "jspdf";
import type * as jspdf_autotable from "jspdf-autotable";

type JsPDF = typeof jspdf;
type JsPDFAutotable = typeof jspdf_autotable;

export default async function template1Function(param: {
  jspdf: JsPDF;
  jspdf_autotable: JsPDFAutotable;
}) {
  const { jspdf, jspdf_autotable } = param;
  const doc = new jspdf.jsPDF({
    orientation: "portrait",
    unit: "mm",
    format: "a4",
  });
  const pageWidth = doc.internal.pageSize.getWidth();
  const pageHeight = doc.internal.pageSize.getHeight();
  const marginTop = 16;
  const marginBottom = 16;
  const marginLeft = 16;
  const marginRight = 16;
  const headerHeight = 45;

  async function getBase64ImageFromURL(url: string) {
    const res = await fetch(url);
    const blob = await res.blob();
    return new Promise<string | null | undefined>((resolve, reject) => {
      const reader = new FileReader();
      reader.onloadend = () => {
        const result = reader.result;
        if (typeof result === "string") {
          resolve(result);
        }
        reject(new Error("Failed to read image"));
      };
      reader.onerror = reject;
      reader.readAsDataURL(blob);
    });
  }

  async function drawHeader() {
    const imageUrl =
      "https://api-staging.sisva.id/file/v1/files/17385688788c0aeba187082f605cde.jpe?school_id=eb3e09e2-a599-4687-90c5-05d3903852cb";
    const base64Img = await getBase64ImageFromURL(imageUrl);
    doc.setFont("helvetica", "bold");
    doc.setFontSize(14);

    const totalPages = doc.getNumberOfPages();
    for (let i = 1; i <= totalPages; i++) {
      let y = marginTop - 8;
      const x = pageWidth / 2;
      const lineHeight = 5;
      const imageWidth = 30;
      const imageHeight = 30;

      doc.setPage(i);
      doc.addImage(
        base64Img ?? "",
        "JPEG",
        x - imageWidth / 2,
        y,
        imageWidth,
        imageHeight
      );
      y += imageHeight + lineHeight;
      doc.text("LAPORAN HASIL BELAJAR PENILAIAN TENGAH SEMESTER GENAP", x, y, {
        align: "center",
      });
      y += lineHeight;
      doc.text("MADRASAH ALIYAH AL-JIHAD", x, y, {
        align: "center",
      });
      y += lineHeight;
      doc.text("TAHUN PELAJARAN 2024/2025", x, y, {
        align: "center",
      });
    }
  }

  function drawFooter() {
    const y = pageHeight - 8;
    const x = pageWidth - 8;
    const totalPages = doc.getNumberOfPages();
    doc.setFont("helvetica", "normal");
    doc.setFontSize(9);
    for (let i = 1; i <= totalPages; i++) {
      doc.setPage(i);
      doc.text(`Halaman ${i}/${totalPages}`, x, y, { align: "right" });
    }
  }

  function drawBiodata() {
    doc.setFontSize(11);
    const data = [
      {
        title: "Nama Peserta Didik:",
        value: "M ISMAIL IRWAN",
      },
      {
        title: "Kelas:",
        value: "XII",
      },
      {
        title: "Nomor Induk:",
        value: "123123123",
      },
      {
        title: "Program:",
        value: "IPA",
      },
    ];

    const titleWidth = 45;
    const column1X = marginLeft;
    const column2X = 120;
    const lineHeight = 5;
    let y = marginTop + headerHeight + 5;
    let x = column1X;
    let currentColumn = 1;
    for (const item of data) {
      doc.setFont("helvetica", "bold");
      doc.text(item.title, x, y);
      x += titleWidth;
      doc.setFont("helvetica", "normal");
      doc.text(item.value, x, y);
      if (currentColumn === 2) {
        y += lineHeight;
      }
      x = currentColumn === 1 ? column2X : column1X;
      currentColumn = currentColumn === 1 ? 2 : 1;
    }
    return { endY: y };
  }

  function drawScoreTable({ startY }: { startY: number }) {
    let endY = 0;

    const body: jspdf_autotable.RowInput[] = [];
    for (let i = 1; i <= 16; i++) {
      body.push([
        { content: i, rowSpan: 2 },
        { content: "Al Qur'an Hadis", styles: { halign: "left" } },
        { content: "75", rowSpan: 2 },
        { content: "85", rowSpan: 2 },
        { content: "Delapan Puluh Lima", rowSpan: 2 },
        { content: "92", rowSpan: 2 },
        { content: "Sembilan Puluh Dua", rowSpan: 2 },
        { content: "A", rowSpan: 2 },
      ]);
      body.push([
        { content: "Siti Maryam, S. Ud", styles: { halign: "left" } },
      ]);
    }

    body.push([
      {
        content: "JUMLAH NILAI PRESTASI HASIL BELAJAR",
        colSpan: 3,
        styles: { fontStyle: "bold" },
      },
      { content: "1264", styles: { fontStyle: "bold" } },
      { content: "" },
      { content: "1425", styles: { fontStyle: "bold" } },
    ]);

    body.push([
      { content: "RATA - RATA", colSpan: 3, styles: { fontStyle: "bold" } },
      { content: "66.53", styles: { fontStyle: "bold" } },
      { content: "" },
      { content: "74", styles: { fontStyle: "bold" } },
    ]);

    jspdf_autotable.autoTable(doc, {
      theme: "plain",
      headStyles: {
        halign: "center",
        valign: "middle",
        lineColor: 10,
        lineWidth: 0.1,
      },
      bodyStyles: {
        halign: "center",
        valign: "middle",
        lineColor: 10,
        lineWidth: 0.1,
        cellPadding: 2,
      },
      head: [
        [
          { content: "No", rowSpan: 3 },
          { content: "Mata Pelajaran", rowSpan: 3, styles: { cellWidth: 40 } },
          { content: "KKM", rowSpan: 3 },
          { content: "Nilai Hasil Belajar", colSpan: 5 },
        ],
        [
          { content: "Pengetahuan", colSpan: 2 },
          { content: "Keterampilan", colSpan: 2 },
          { content: "Sikap" },
        ],
        [
          { content: "Angka" },
          { content: "Huruf" },
          { content: "Angka" },
          { content: "Huruf" },
          { content: "Predikat" },
        ],
      ],
      body: body,
      margin: {
        top: marginTop + headerHeight,
        bottom: marginBottom,
        left: marginLeft,
        right: marginRight,
      },
      startY: startY,
      didDrawPage: ({ cursor }) => {
        endY = cursor?.y ?? 0;
      },
    });
    return { endY };
  }

  function drawAttendanceTable({ startY }: { startY: number }) {
    let endY = 0;
    let endX = 0;
    jspdf_autotable.autoTable(doc, {
      tableWidth: 75,
      theme: "plain",
      headStyles: {
        valign: "middle",
        lineColor: 10,
        lineWidth: 0.1,
      },
      bodyStyles: {
        valign: "middle",
        lineColor: 10,
        lineWidth: 0.1,
      },
      head: [
        [
          { content: "No" },
          { content: "Alasan Ketidakhadiran" },
          { content: "Hari" },
        ],
      ],
      body: [
        [{ content: "1" }, { content: "Sakit" }, { content: "2" }],
        [{ content: "1" }, { content: "Izin" }, { content: "2" }],
        [{ content: "1" }, { content: "Tanpa Keterangan" }, { content: "2" }],
      ],
      margin: {
        top: marginTop + headerHeight,
        bottom: marginBottom,
        left: marginLeft,
        right: marginRight,
      },
      startY: startY,
      didDrawPage: ({ cursor }) => {
        endY = cursor?.y ?? 0;
        endX = cursor?.x ?? 0;
      },
    });
    return { endY, endX, startY };
  }

  function drawPeringkat({
    startX,
    startY,
  }: {
    startX: number;
    startY: number;
  }) {
    let y = startY;
    const lineHeight = 5;
    doc.text("Peringkat ke 10", startX, y);
    y += lineHeight;
    doc.text("dari 26 siswa", startX, y);

    return { endY: y };
  }

  function drawNote({ startX, startY }: { startX: number; startY: number }) {
    let y = startY;
    const lineHeight = 5;
    const title = "Catatan Wali Kelas";
    doc.text(title, startX, y);
    const underlineY = y + 0.4;
    const textWidth = doc.getTextWidth(title);
    doc.line(startX, underlineY, startX + textWidth, underlineY); // underline
    y += lineHeight;
    doc.setFont("helvetica", "italic");
    doc.text(
      "Tingkatkan semangat belajar, tingkatkan jam belajarmu di rumah dan tingkatkan prestasimu.",
      startX,
      y
    );
    return { endY: y };
  }

  function drawDate({ startX, startY }: { startX: number; startY: number }) {
    doc.setFont("helvetica", "normal");
    let y = startY;
    const dateText = "Depok, Maret 2025";
    const lineHeight = 5;
    const textWidth = doc.getTextWidth(dateText);
    const x = startX - textWidth;
    doc.text(dateText, x, y);
    y += lineHeight;
    doc.text("Mengetahui,", x, y);

    return { endY: y };
  }

  function drawSignature() {
    doc.setFont("helvetica", "normal");
    const lineHeight = 5;
    const gap = 20;
    const startY = pageHeight - marginBottom - (lineHeight * 1 + gap);
    let y = startY;
    const columnWidht = (pageWidth - (marginLeft + marginRight)) / 3;
    const halfColumnWidth = columnWidht / 2;
    const column1Center = marginLeft + halfColumnWidth;
    const column2Center = marginLeft + columnWidht + halfColumnWidth;
    const column3Center = marginLeft + columnWidht * 2 + halfColumnWidth;
    doc.text("Orang Tua/Wali", column1Center, y, { align: "center" });
    doc.text("Wali Kelas", column2Center, y, { align: "center" });
    doc.text("Kepala Madrasah", column3Center, y, { align: "center" });
    y += gap;
    doc.text(".................", column1Center, y, { align: "center" });
    doc.setFont("helvetica", "bold");
    doc.text("Muslim, S.Ag", column2Center, y, { align: "center" });
    doc.text("Ratna Dewi, S.Kom., M.M", column3Center, y, { align: "center" });
    doc.setFont("helvetica", "normal");
    y += lineHeight;
    doc.text("NIP. 198005032007102000", column3Center, y, { align: "center" });
    return { endY: y, startY };
  }

  const { endY: endYBiodata } = drawBiodata();
  const { endY: endYScoreTable } = drawScoreTable({ startY: endYBiodata });
  let startYPeringkat = endYScoreTable + 8;
  if (endYScoreTable > 160) {
    doc.addPage("a4", "portrait");
    startYPeringkat = marginTop + headerHeight;
  }
  const {
    endX: endXAttendanceTable,
    endY: endYAttendanceTable,
    startY: startYAttendanceTable,
  } = drawAttendanceTable({ startY: startYPeringkat });
  drawPeringkat({
    startX: endXAttendanceTable + 10,
    startY: startYAttendanceTable + 3,
  });
  drawNote({
    startX: marginLeft,
    startY: endYAttendanceTable + 8,
  });

  const { startY: startYSignature } = drawSignature();
  drawDate({
    startX: pageWidth - marginRight,
    startY: startYSignature - 16,
  });

  await drawHeader();
  drawFooter();

  // for debugging
  function drawRules(enable: boolean = false) {
    if (!enable) return;
    const gap = 10;
    const color = "#f0f0f0";
    doc.setDrawColor(color);
    doc.setLineWidth(0.1);
    const totalPages = doc.getNumberOfPages();

    for (let i = 1; i <= totalPages; i++) {
      doc.setPage(i);
      for (let y = gap; y < pageHeight; y += gap) {
        doc.line(0, y, pageWidth, y);
      }
      for (let x = gap; x < pageWidth; x += gap) {
        doc.line(x, 0, x, pageHeight);
      }
    }
  }
  drawRules(false);

  return doc.output("blob");
}
