{
  // reference: https://www.totaltypescript.com/tsconfig-cheat-sheet

  "compilerOptions": {
    // --- base options --
    "esModuleInterop": true,
    "skipLibCheck": true,
    "target": "ES2022",
    "allowJs": true,
    "checkJs": true,
    "resolveJsonModule": true,
    "moduleDetection": "force",
    "isolatedModules": true,
    "verbatimModuleSyntax": true,
    // --- base options --

    // --- strictness ---
    "strict": true,
    "strictNullChecks": true,
    "strictBindCallApply": true,
    "strictBuiltinIteratorReturn": true,
    "strictPropertyInitialization": true,
    "noImplicitThis": true,
    "useUnknownInCatchVariables": true,
    "noImplicitAny": true,
    "strictFunctionTypes": true,
    "noUncheckedIndexedAccess": true,
    // --- strictness ---

    // Emit d.ts files, set to true if you're building for a library
    "declaration": true,
    // Caching shenanigans
    "composite": true,
    // Generates a source map for .d.ts files which map back to the original .ts source file.
    // This will allow editors such as VS Code to go to the original .ts file when using features like Go to Definition.
    "declarationMap": true,
    // Generate .tsbuildinfo, makes subsequent builds faster
    "incremental": true,
    "tsBuildInfoFile": "tsbuildinfo.json",
    // You very likely want "nodenext" for modern Node.js projects and "preserve" or "esnext" for code that will be bundled.
    "module": "esnext",
    // Set to "bundler" if you're using bundlers like webpack or rollup (vite)
    "moduleResolution": "bundler",
    // If your code runs in the DOM:
    "lib": ["dom", "dom.iterable", "esnext"],

    "jsx": "react-jsx"
  }
}
