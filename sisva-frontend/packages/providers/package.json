{"name": "@sisva/providers", "version": "0.0.0", "private": true, "scripts": {"lint": "eslint ./src", "lint:fix": "eslint ./src --fix", "typecheck": "tsc --noEmit", "build": "tsup", "dev": "tsup --watch"}, "exports": {".": "./dist/index.js"}, "devDependencies": {"@sisva/eslint-config": "workspace:*", "@sisva/typescript-config": "workspace:*", "@types/node": "^22.13.0", "@types/react": "19.0.8", "@types/react-dom": "19.0.3", "eslint": "^9.21.0", "tsup": "8.3.0", "typescript": "5.7.3"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^6.4.6", "@mui/material": "^6.4.6", "@mui/x-date-pickers": "^7.27.1", "@sisva/types": "workspace:*", "antd": "^5.24.2", "dayjs": "^1.11.13", "react": "^19.0.0", "react-dom": "^19.0.0"}}