import { idID } from "@mui/material/locale";
import { createTheme, ThemeProvider } from "@mui/material/styles";
import type { ReactNode } from "react";

export function MUIThemeProvider({
  themeConfig,
  children,
}: {
  themeConfig: Record<string, unknown>;
  children: ReactNode;
}) {
  const theme = createTheme(themeConfig, idID);

  return <ThemeProvider theme={theme}>{children}</ThemeProvider>;
}
