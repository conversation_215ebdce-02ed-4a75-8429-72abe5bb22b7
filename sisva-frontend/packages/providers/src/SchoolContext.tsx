import type { School } from "@sisva/types/apiTypes";
import type { ReactNode } from "react";
import { createContext, useContext } from "react";

export type SchoolWithMoreData = School & {
  landing_image_url: string | undefined;
  logo_url: string | undefined;
};

const SchoolContext = createContext<SchoolWithMoreData | null>(null);

export function SchoolProvider({
  children,
  school,
}: {
  school: SchoolWithMoreData;
  children: ReactNode;
}) {
  return (
    <SchoolContext.Provider value={school}>{children}</SchoolContext.Provider>
  );
}

export function useSchool() {
  const schoolData = useContext(SchoolContext);
  if (!schoolData) {
    throw new Error("useSchool must be used within a SchoolProvider");
  }
  return schoolData;
}
