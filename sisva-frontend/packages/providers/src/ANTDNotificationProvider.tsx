import { notification } from "antd";
import type { ReactElement } from "react";
import { createContext, useContext } from "react";

type Props = {
  children: ReactElement;
};

type Context = ReturnType<typeof notification.useNotification>[0];

const NotificationAPI = createContext<Context | null>(null);

export const ANTDNotificationAPIProvider = ({ children }: Props) => {
  const [api, contextHolder] = notification.useNotification();

  return (
    <NotificationAPI.Provider value={api}>
      {contextHolder}
      {children}
    </NotificationAPI.Provider>
  );
};

export const useNotificationAPI = () => {
  const context = useContext(NotificationAPI);

  if (!context)
    throw new Error(
      "NotificationAPI must be called from within the NotificationAPIProvider"
    );

  return context;
};
