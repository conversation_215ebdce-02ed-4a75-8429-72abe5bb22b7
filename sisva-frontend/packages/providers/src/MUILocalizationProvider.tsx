import "dayjs/locale/id";

import { LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import dayjs from "dayjs";
import customParseFormat from "dayjs/plugin/customParseFormat";
import type { ReactNode } from "react";

dayjs.extend(customParseFormat);

export const MUILocalizationProvider = ({
  children,
}: {
  children: ReactNode;
}) => {
  return (
    <LocalizationProvider dateAdapter={AdapterDayjs} adapterLocale="id">
      {children}
    </LocalizationProvider>
  );
};
