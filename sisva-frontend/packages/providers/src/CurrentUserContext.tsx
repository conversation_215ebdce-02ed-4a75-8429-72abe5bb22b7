import type { User } from "@sisva/types/apiTypes";
import type { ReactNode } from "react";
import { createContext, useContext } from "react";

const CurrentUserContext = createContext<User | null>(null);

export function CurrentUserProvider({
  children,
  currentUser,
}: {
  currentUser: User;
  children: ReactNode;
}) {
  return (
    <CurrentUserContext.Provider value={currentUser}>
      {children}
    </CurrentUserContext.Provider>
  );
}

export function useCurrentUser() {
  const currentUser = useContext(CurrentUserContext);
  if (!currentUser) {
    throw new Error("useCurrentUser must be used within a CurrentUserContext");
  }
  return currentUser;
}
