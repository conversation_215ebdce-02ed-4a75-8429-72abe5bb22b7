import { spawnSync } from "child_process";
import { defineConfig } from "tsup";

export default defineConfig((options) => ({
  entry: ["src/**/*.{ts,tsx}"],
  format: "esm",
  outDir: "dist",
  splitting: false,
  outExtension() {
    return {
      js: `.js`,
    };
  },
  async onSuccess() {
    const { status, error } = spawnSync("pnpm", [
      "exec",
      "tsc",
      "--emitDeclarationOnly",
    ]);
    if (error) console.error(error);
    if (status === 0) {
      console.log("✅ declaration files generated");
    } else {
      console.log("❌ failed to generate declaration files");
    }
  },
  ...options,
}));
