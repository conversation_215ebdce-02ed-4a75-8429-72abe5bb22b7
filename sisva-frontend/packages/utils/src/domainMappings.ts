export type Target = "administration" | "classroom" | "guardian" | "cbt";
type DomainMapping = Record<string, { code: string; target: Target }>;

export const domainMappings: DomainMapping = (() => {
  const mappings: DomainMapping = {};

  const addDomains = (code: string, items: [string, Target][]) => {
    items.forEach(([domain, target]) => {
      mappings[domain] = { code, target };
    });
  };

  addDomains("TIMURGROWTH", [
    ["examgrowth.timurnetwork.org", "cbt"],
    ["admingrowth.timurnetwork.org", "administration"],
    ["classroom.timurnetwork.org", "classroom"],
    ["localhost:3011", "administration"],
    ["localhost:3012", "guardian"],
    ["localhost:3013", "classroom"],
    ["localhost:3014", "cbt"],
  ]);

  // for testing
  // addDomains("DEMO", [
  //   ["localhost:3004", "cbt"],
  //   ["localhost:3003", "classroom"],
  // ]);

  return mappings;
})();

const defaultDomain: Record<Target, string> = {
  administration: "administration.sisva.id",
  classroom: "classroom.sisva.id",
  guardian: "guardian.sisva.id",
  cbt: "exam.sisva.id",
};

const defaultDomainDev: Record<Target, string> = {
  administration: "localhost:3001",
  guardian: "localhost:3002",
  classroom: "localhost:3003",
  cbt: "localhost:3004",
};

export function getDomain(param: { isDev: boolean; target: Target }) {
  if (param.isDev) {
    return defaultDomainDev[param.target];
  }

  const domainMapping = domainMappings[window.location.host];
  const domain = Object.entries(domainMappings).find(
    ([_, { code, target }]) =>
      code === domainMapping?.code && target === param.target
  )?.[0];

  if (domain) return domain;

  return defaultDomain[param.target];
}
