// check https://stackoverflow.com/questions/15549823/oadate-to-milliseconds-timestamp-in-javascript

/**
 * Convert a Microsoft OADate to ECMAScript Date
 */
export function dateFromOADate(oaDate: number) {
  // Treat integer part is whole days
  const days = Math.trunc(oaDate);
  // Treat decimal part as part of 24hr day, always +ve
  const ms = Math.abs((oaDate - days) * 8.64e7);
  // Add days and add ms
  return new Date(1899, 11, 30 + days, 0, 0, 0, ms);
}
