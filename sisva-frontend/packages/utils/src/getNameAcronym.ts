/**
 * Converts a full name into its acronym
 */
export const getNameAcronym = (name: string) => {
  // <PERSON>le empty or null input
  if (!name || typeof name !== "string") return "";

  // Split the name into words
  const words = name.trim().split(/\s+/);

  // If only one word, return first two characters
  if (words.length === 1) {
    return words[0]!.substring(0, 2).toUpperCase();
  }

  // Get the first letter of each word and convert to uppercase
  const acronym = words.map((word) => word[0]!.toUpperCase()).join("");

  return acronym;
};
