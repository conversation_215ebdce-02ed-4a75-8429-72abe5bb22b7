/** Segment array into array of array with defined length
 * @example const data = [
 * {name: "A"},
 * {name: "B"},
 * {name: "C"}
 * ];
 *
 * const segmented = segmentArray(data, 2);
 * console.log(segmented); // [[{name: "A"}, {name: "B"}], [{name: "C"}]]
 */
export function segmentArray<T>(arr: T[], segmentLength: number): T[][] {
  return arr.reduce((result: T[][], item: T, index: number) => {
    const segmentIndex = Math.floor(index / segmentLength);

    if (!result[segmentIndex]) {
      result[segmentIndex] = [];
    }

    result[segmentIndex].push(item);
    return result;
  }, []);
}
