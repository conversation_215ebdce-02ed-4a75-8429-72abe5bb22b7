import AcademicAPI from "@sisva/api/academic";
import type {
  Class,
  ClassSchedule,
  NonLearningSchedules,
  Period,
  SchoolSchedules,
  StudentGroup,
} from "@sisva/types/apiTypes";
import type { Day } from "@sisva/types/types";
import dayjs from "dayjs";
import customParseFormat from "dayjs/plugin/customParseFormat";
import dayjsRange from "dayjs-range-extend";

dayjs.extend(customParseFormat);
const TIME_FORMAT = "h:mm A Z";

type Schedule =
  // for class schedule
  | {
      id: string; // to exclude from otherSchedules, use crypto.randomUUID()
      non_learning_schedule_id?: undefined;
      class_schedule_id?: number; // to exclude from all class schedules, used to edit existing class schedule
      student_group_id: number;
      teacher_id: string;
      period_id: number;
      school_schedule_id?: undefined;
      start_time: string;
      end_time: string;
      day: Day;
    }
  // for non-learning schedule
  | {
      id: string; // to exclude from otherSchedules, use crypto.randomUUID()
      non_learning_schedule_id?: number; // to exclude from all non-learning schedules, used to edit existing non-learning schedule
      class_schedule_id?: undefined;
      student_group_id?: undefined;
      teacher_id?: undefined;
      period_id?: undefined;
      school_schedule_id: number;
      start_time: string;
      end_time: string;
      day?: undefined;
    };

/**
 * passing school_schedule_id will assume the schedule as non-learning schedule,
 * otherwise it will assume it as class schedule
 */
export async function checkScheduleConflict({
  schedule,
  otherSchedules = [],
}: {
  schedule: Schedule;
  otherSchedules?: Schedule[];
}) {
  const scheduleRange = dayjsRange(
    dayjs(schedule.start_time, TIME_FORMAT),
    dayjs(schedule.end_time, TIME_FORMAT)
  );

  const periods: Period[] = (await AcademicAPI.getAllPeriod()).data.data;
  const periodIds = periods.map((period) => period.id);

  const classSchedules: ClassSchedule[] = (
    await (async () => {
      const arrayOfClassSchedules: ClassSchedule[][] = [];
      for (const id of periodIds) {
        arrayOfClassSchedules.push(
          (await AcademicAPI.getAllClassSchedules({ period_id: id })).data.data
        );
      }
      return arrayOfClassSchedules.flat();
    })()
  ).filter(
    (item) =>
      !schedule.class_schedule_id || item.id !== schedule.class_schedule_id
  );

  const nonLearningSchedules: NonLearningSchedules[] = (
    await (async () => {
      const arrayOfClassSchedules: NonLearningSchedules[][] = [];
      for (const id of periodIds) {
        arrayOfClassSchedules.push(
          (await AcademicAPI.getAllNonLearningSchedules({ period_id: id })).data
            .data
        );
      }
      return arrayOfClassSchedules.flat();
    })()
  ).filter(
    (item) =>
      !schedule.non_learning_schedule_id ||
      item.id !== schedule.non_learning_schedule_id
  );

  const schoolSchedules: SchoolSchedules[] = await (async () => {
    const arrayOfSchoolSchedules: SchoolSchedules[][] = [];
    for (const id of periodIds) {
      arrayOfSchoolSchedules.push(
        (await AcademicAPI.getAllSchoolSchedules({ period_id: id })).data.data
      );
    }
    return arrayOfSchoolSchedules.flat();
  })();

  const classes: Class[] = (await AcademicAPI.getAllClasses()).data.data;
  const studentGroups: StudentGroup[] = (await AcademicAPI.getAllStudentGroup())
    .data.data;

  const classSchedulesWithMoreData = classSchedules.map((classSchedule) => {
    const class_ = classes.find(
      (class_) => class_.id === classSchedule.class_id
    );
    const studentGroup = studentGroups.find(
      (studentGroup) => studentGroup.id === class_?.student_group_id
    );
    return {
      ...classSchedule,
      student_group: studentGroup,
      type: "class_schedule" as const,
      unique_id: `${classSchedule.id} - class_schedule`,
    };
  });

  const nonLearningSchedulesWithMoreData = nonLearningSchedules.map(
    (nonLearningSchedule) => {
      const schoolSchedule = schoolSchedules.find(
        (schoolSchedule) =>
          schoolSchedule.id === nonLearningSchedule.school_schedule_id
      );
      return {
        ...nonLearningSchedule,
        school_schedule: schoolSchedule,
        type: "non_learning_schedule" as const,
        unique_id: `${nonLearningSchedule.id} - non_learning_schedule`,
      };
    }
  );

  const otherSchedulesWithMoreData = otherSchedules.map((otherSchedule) => ({
    ...otherSchedule,
    type: "other_schedule" as const,
    unique_id: `${otherSchedule.id} - non_learning_schedule`,
  }));

  const studentGroup = studentGroups.find(
    (studentGroup) => studentGroup.id === schedule.student_group_id
  );

  const classSchedulesToCheckStudentGroupConflict = studentGroup
    ? classSchedulesWithMoreData.filter(
        (classSchedule) =>
          classSchedule.student_group?.id === schedule.student_group_id &&
          classSchedule.day === schedule.day
      )
    : classSchedulesWithMoreData.filter(
        (classSchedule) =>
          classSchedule.school_schedule_id === schedule.school_schedule_id
      );

  const classSchedulesToCheckTeacherConflict = studentGroup
    ? classSchedulesWithMoreData.filter(
        (classSchedule) =>
          classSchedule.teacher_id === schedule.teacher_id &&
          classSchedule.student_group?.period_id === schedule.period_id &&
          classSchedule.day === schedule.day
      )
    : classSchedulesWithMoreData.filter(
        (classSchedule) =>
          classSchedule.school_schedule_id === schedule.school_schedule_id
      );

  const nonLearningSchedulesToCheckConflict = studentGroup
    ? nonLearningSchedulesWithMoreData.filter(
        (nonLearningSchedule) =>
          nonLearningSchedule.school_schedule?.period_id ===
            schedule.period_id &&
          nonLearningSchedule.day === schedule.day &&
          nonLearningSchedule.school_schedule?.grade === studentGroup?.grade &&
          nonLearningSchedule.study_program_id ===
            studentGroup?.study_program_id
      )
    : nonLearningSchedulesWithMoreData.filter(
        (nonLearningSchedule) =>
          nonLearningSchedule.school_schedule_id === schedule.school_schedule_id
      );

  const otherSchedulesToCheckConflict = studentGroup
    ? otherSchedulesWithMoreData.filter(
        (otherSchedule) =>
          otherSchedule.id !== schedule.id &&
          otherSchedule.period_id === schedule.period_id &&
          otherSchedule.day === schedule.day &&
          (otherSchedule.student_group_id === schedule.student_group_id ||
            otherSchedule.teacher_id === schedule.teacher_id)
      )
    : otherSchedulesWithMoreData.filter(
        (otherSchedule) =>
          otherSchedule.id !== schedule.id &&
          otherSchedule.school_schedule_id === schedule.school_schedule_id
      );

  type ConflictingSchedule =
    | (typeof classSchedulesWithMoreData)[number]
    | (typeof nonLearningSchedulesWithMoreData)[number]
    | (typeof otherSchedulesWithMoreData)[number];

  let hasConflict = false;
  const conflictingSchedules: ConflictingSchedule[] = [];

  classSchedulesToCheckStudentGroupConflict.forEach((classSchedule) => {
    const classScheduleRange = dayjsRange(
      dayjs(classSchedule.start_time, TIME_FORMAT),
      dayjs(classSchedule.end_time, TIME_FORMAT)
    );
    if (classScheduleRange.isOverlap(scheduleRange)) {
      hasConflict = true;
      conflictingSchedules.push(classSchedule);
    }
  });

  classSchedulesToCheckTeacherConflict.forEach((classSchedule) => {
    const classScheduleRange = dayjsRange(
      dayjs(classSchedule.start_time, TIME_FORMAT),
      dayjs(classSchedule.end_time, TIME_FORMAT)
    );
    if (classScheduleRange.isOverlap(scheduleRange)) {
      hasConflict = true;
      conflictingSchedules.push(classSchedule);
    }
  });

  nonLearningSchedulesToCheckConflict.forEach((nonLearningSchedule) => {
    const nonLearningScheduleRange = dayjsRange(
      dayjs(nonLearningSchedule.start_time, TIME_FORMAT),
      dayjs(nonLearningSchedule.end_time, TIME_FORMAT)
    );
    if (nonLearningScheduleRange.isOverlap(scheduleRange)) {
      hasConflict = true;
      conflictingSchedules.push(nonLearningSchedule);
    }
  });

  otherSchedulesToCheckConflict.forEach((otherSchedule) => {
    const otherScheduleRange = dayjsRange(
      dayjs(otherSchedule.start_time, TIME_FORMAT),
      dayjs(otherSchedule.end_time, TIME_FORMAT)
    );
    if (otherScheduleRange.isOverlap(scheduleRange)) {
      hasConflict = true;
      conflictingSchedules.push(otherSchedule);
    }
  });

  class ScheduleConflictError extends Error {
    conflictingSchedules: ConflictingSchedule[];

    constructor(conflictingSchedules: ConflictingSchedule[]) {
      super("Schedule conflict detected.");
      this.conflictingSchedules = conflictingSchedules;
      this.name = "ScheduleConflictError";
    }

    static fromConflicts(conflictingSchedules: ConflictingSchedule[]) {
      return new ScheduleConflictError(conflictingSchedules);
    }
  }

  function isScheduleConflictError(
    error: unknown
  ): error is ScheduleConflictError {
    return error instanceof ScheduleConflictError;
  }

  const uniqueIds = [
    ...new Set(conflictingSchedules.map((item) => item.unique_id)),
  ];

  return {
    hasConflict,
    conflictingSchedules,
    error: hasConflict
      ? ScheduleConflictError.fromConflicts(
          uniqueIds.map(
            (id) => conflictingSchedules.find((item) => item.unique_id === id)!
          )
        )
      : null,
    isScheduleConflictError,
  };
}

export type ConflictingSchedule = Awaited<
  ReturnType<typeof checkScheduleConflict>
>["conflictingSchedules"][number];

export type ScheduleConflictError = NonNullable<
  Awaited<ReturnType<typeof checkScheduleConflict>>["error"]
>;

export function isScheduleConflictError(
  error: unknown
): false | ScheduleConflictError {
  if (
    typeof error === "object" &&
    error !== null &&
    "name" in error &&
    error.name === "ScheduleConflictError"
  ) {
    return error as ScheduleConflictError;
  } else {
    return false;
  }
}
