const romanMap = {
  I: 1,
  V: 5,
  X: 10,
  L: 50,
  C: 100,
  D: 500,
  M: 1000,
};

type Roman = keyof typeof romanMap;

// https://leetcode.com/problems/roman-to-integer/solutions/2109445/nice-and-clean-ts-js/
export function romanToInt(input: string): number {
  if (!isRomanString(input)) return 0;
  const integers = input.split("").map((c) => romanMap[c as Roman]);

  return integers.reduce((acc, x, i) => {
    const num = integers[i + 1];
    if (!num) return acc + x;
    return x < num ? acc - x : acc + x;
  }, 0);
}

// https://www.geeksforgeeks.org/validating-roman-numerals-using-regular-expression/
export function isRomanString(str: string) {
  const regex = new RegExp(
    /^M{0,3}(CM|CD|D?C{0,3})(XC|XL|L?X{0,3})(IX|IV|V?I{0,3})$/
  );

  return regex.test(str);
}
