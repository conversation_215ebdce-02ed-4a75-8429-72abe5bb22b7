/**
 * Merges two arrays of objects, combining properties from both arrays.
 * Allows for duplicates and preserves all properties.
 *
 * @template T The first type of objects in the arrays
 * @template U The second type of objects in the arrays
 * @param array1 First array of objects
 * @param array2 Second array of objects
 * @returns Merged array of objects with combined properties
 */
export function mergeArrays<
  T extends Record<string, unknown>,
  U extends Record<string, unknown>,
>(array1: T[], array2: U[]) {
  if (!array1.length || !array2.length) {
    return [...array1, ...array2] as ((T & Partial<U>) | (U & Partial<T>))[];
  }
  const keys1 = Object.keys(array1[0] ?? {});
  const keys2 = Object.keys(array2[0] ?? {});
  return [
    ...array1.map((item1) => {
      for (const key of keys2) {
        (item1 as Record<string, unknown>)[key] = item1[key] ?? undefined;
      }
      return item1;
    }),

    ...array2.map((item2) => {
      for (const key of keys1) {
        (item2 as Record<string, unknown>)[key] = item2[key] ?? undefined;
      }
      return item2;
    }),
  ] as ((T & Partial<U>) | (U & Partial<T>))[];
}
