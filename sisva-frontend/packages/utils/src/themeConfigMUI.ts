export const themeConfigMUI = {
  typography: {
    fontFamily: "Kumbh Sans",
    fontSize: 13,
  },
  palette: {
    primary: {
      main: "#008CD5",
    },
    // please use text.primary / text.secondary https://mui.com/material-ui/customization/default-theme/
    base: {
      main: "#98A2B3",
      base100: "#101828",
      base90: "#1D2939",
      base80: "#344054",
      base70: "#475467",
      base60: "#667085",
      base50: "#98A2B3",
      base40: "#D0D5DD",
      base30: "#EAECF0",
      base20: "#F2F4F7",
      base10: "#F9FAFB",
    },
  },
  breakpoints: {
    values: {
      xs: 0,
      sm: 320,
      ms: 375,
      md: 640,
      ml: 768,
      lg: 1024,
      xl: 1280,
      xxl: 1536,
    },
  },
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          textTransform: "none",
          minWidth: 0,
          fontSize: 14,
        },
      },
    },
    MuiInputBase: {
      styleOverrides: {
        root: {
          fontSize: 14,
        },
      },
    },
    // MuiPaper: {
    //   styleOverrides: {
    //     root: {
    //       borderRadius: 0,
    //     },
    //   },
    // },
    MuiInputLabel: {
      styleOverrides: {
        root: {
          fontSize: 13,
        },
      },
    },
  },
};
