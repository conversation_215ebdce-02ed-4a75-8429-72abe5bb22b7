import {
  array,
  type InferOutput,
  literal,
  number,
  object,
  optional,
  safeParse,
  union,
} from "valibot";

import { getDomain, type Target } from "./domainMappings";

//  ───────────────────────────────── CBT ─────────────────────────────────
const createExamSchema = object({
  to: literal("createExam"),
  class_id: optional(number()),
});

const createExamWithPlansSchema = object({
  to: literal("createExamWithExistingTeachingPlanIds"),
  teaching_plan_ids: array(number()),
});

const viewExamSchema = object({
  to: literal("viewExam"),
  exam_id: number(),
});

const examSessionSchema = object({
  to: literal("examSession"),
  exam_id: number(),
});

export const cbtRedirectSchema = union([
  createExamSchema,
  createExamWithPlansSchema,
  viewExamSchema,
  examSessionSchema,
]);

type CBTRedirectPayload = InferOutput<typeof cbtRedirectSchema>;

//  ────────────────────────────── Classroom ──────────────────────────────
const viewClassSchema = object({
  to: literal("viewClass"),
  class_id: number(),
});

export const classroomRedirectSchema = union([viewClassSchema]);

type ClassroomRedirectPayload = InferOutput<typeof classroomRedirectSchema>;

//   ──────────────────────────────────────────────────────────────────────

type RedirectPayload = CBTRedirectPayload | ClassroomRedirectPayload;
type RedirectSchema = typeof cbtRedirectSchema | typeof classroomRedirectSchema;

export function encodeRedirectPayload(payload: RedirectPayload): string {
  const json = JSON.stringify(payload);
  const base64 = btoa(json);
  const safeBase64 = encodeURIComponent(base64);
  return safeBase64;
}

export function decodeRedirectPayload<T extends RedirectSchema>(
  encoded: string,
  schema: T
): InferOutput<T> | null {
  try {
    const base64 = decodeURIComponent(encoded);
    const json = atob(base64);
    const rawPayload = JSON.parse(json);

    const result = safeParse(schema, rawPayload);

    if (result.success) {
      return result.output;
    } else {
      console.error("Invalid redirect payload:", result.issues);
      return null;
    }
  } catch (error) {
    console.error("Failed to decode redirect payload:", error);
    return null;
  }
}

export function createRedirectUrl({
  payload,
  isDev,
}: {
  payload: RedirectPayload;
  isDev: boolean;
}) {
  function getTarget(): Target {
    const cbtResult = safeParse(cbtRedirectSchema, payload);
    if (cbtResult.success) return "cbt";
    const classroomResult = safeParse(classroomRedirectSchema, payload);
    if (classroomResult.success) return "classroom";

    return "administration";
  }
  const redirectPayload = encodeRedirectPayload(payload);
  const protocol = isDev ? "http" : "https";

  const url = new URL(
    protocol + "://" + getDomain({ isDev, target: getTarget() })
  );
  url.searchParams.append("redirect", redirectPayload);
  return url.href;
}
