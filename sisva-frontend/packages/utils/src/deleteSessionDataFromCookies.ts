import { COOKIES_KEY } from "@sisva/api/utils";
import Cookies from "js-cookie";
import { parse } from "tldts";

export function deleteSessionDataFromCookies() {
  const parseHost = parse(window.location.host);
  const domain = parseHost.domain;

  const options = {
    path: "/",
    domain: domain ? "." + domain : undefined,
  };

  Cookies.remove(COOKIES_KEY.token, options);
  Cookies.remove(COOKIES_KEY.user_id, options);
  Cookies.remove(COOKIES_KEY.school_id, options);
  Cookies.remove(COOKIES_KEY.username, options);
}
