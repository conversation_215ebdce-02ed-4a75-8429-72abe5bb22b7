import type {
  Attendance,
  ClassType,
  Day,
  ExamSubmissionStatus,
  ExamType,
  InvoiceStatus,
  PeriodStatus,
  Permission,
  ReportStatus,
  StudyProgramStatus,
  SubjectType,
  UserRole,
  UserType,
} from "./types";

export type Grade =
  //* actually could be any string
  | "I"
  | "II"
  | "III"
  | "IV"
  | "V"
  | "VI"
  | "VII"
  | "VIII"
  | "IX"
  | "X"
  | "XI"
  | "XII";

export type Session = {
  username: string;
  user_id: string;
  school_id: string;
  token: string;
};

export type User = {
  id: string;
  username: string;
  nik: string;
  name: string;
  type: UserType;
  detail: {
    json_text: string;

    // * these only exist for students
    grade: Grade | "";
    study_program_id: number;
    guardian_id: string;
    //
  };
  profile_image_uri: string;
  roles: UserRole[];
  permissions: Permission[];
  status: "active" | "inactive";
};

export type School = {
  abbreviation: string;
  additional_json_text: string;
  code: string;
  education_level: string;
  education_ownership_type: string;
  education_type: string;
  id: string;
  identifier_type: string;
  identifier_value: string;
  landing_image_uri: string | null | undefined | "";
  logo_uri: string | null | undefined | "";
  name: string;
  theme_json_text: string;
};

/* -------------------------------------------------------------------------- */
/*                                  Academic                                  */
/* -------------------------------------------------------------------------- */

export type Extracurricular = {
  id: number;
  name: string;
  teacher_id: string;
  teacher_name: string;
};

export type ExtracurricularMember = {
  extracurricular_id: number;
  extracurricular_name: string;
  student_id: string;
  student_name: string;
};

export type Subject = {
  id: number;
  name: string;
  type: SubjectType;
  study_program_id: number;
  study_program_name: string;
  curriculum_id: number;
  curriculum_name: string;
};

export type SubjectTeacher = {
  teacher_id: string;
  teacher_name: string;
  subject_id: number;
  subject_name: string;
  grade: Grade;
  subject_detail: {
    id: number;
    name: string;
    type: string; //? mandatory etc ???
    study_program_id: number;
    study_program_name: string;
    curriculum_id: number;
    curriculum_name: string;
  };
};

export type StudentGroup = {
  id: number;
  name: string;
  type: ClassType; //? homeroom, elective ???
  period_id: number;
  period_name: string;
  study_program_id: number;
  study_program_name: string;
  grade: Grade;
  detail: {
    homeroom_teacher_id: string;
    homeroom_teacher_name: string;
  };
};

export type StudentInStudentGroup = {
  student_id: string;
  student_name: string;
  student_group_id: number;
  student_group_name: string;
};

export type Period = {
  id: number;
  name: string;
  start_time: string;
  end_time: string;
  status: PeriodStatus;
  study_programs:
    | {
        id: number;
        code: string;
      }[]
    | null;
};

export type Curriculum = {
  id: number;
  name: string;
  description: string;
  study_programs:
    | {
        id: number;
        code: string;
      }[]
    | null;
  total_subjects: number;
  passing_grade: number;
};

export type Syllabus = {
  id: number;
  file_uri: string;
  study_program_id: number;
  study_program_name: string;
  curriculum_id: number;
  curriculum_name: string;
  subject_id: number;
  subject_name: string;
  grade: Grade;
};

export type PeriodCurriculum = {
  period_id: number;
  period_name: string;
  study_program_id: number;
  study_program_name: string;
  grade: Grade;
  curriculum_id: number;
  curriculum_name: string;
};

export type StudyProgram = {
  id: number;
  name: string;
  code: string;
  status: StudyProgramStatus;
  grades: Grade[] | null;
};

export type ClassSchedule = {
  id: number;
  class_id: number;
  class_name: string;
  school_schedule_id: number;
  study_program_id: number;
  teacher_id: string;
  teacher_name: string;
  subject_id: number;
  subject_name: string;
  grade: Grade;
  day: Day;
  start_time: string;
  end_time: string;
};

export type NonLearningSchedules = {
  id: number;
  name: string;
  school_schedule_id: number;
  study_program_id: number;
  grade: Grade;
  day: Day;
  start_time: string;
  end_time: string;
};

export type Class = {
  id: number;
  name: string;
  student_group_id: number;
  student_group_name: string;
  subject_id: number;
  subject_name: string;
  teacher_id: string;
  teacher_name: string;
};

// rename to SchoolSchedule later
export type SchoolSchedules = {
  id: number;
  period_id: number;
  study_program_id: number;
  grade: Grade;
  status: string; // ? active, inactive ???
  day: Day;
  start_time: string;
  end_time: string;
};

export type Announcement = {
  id: number;
  title: string;
  image_uri: string;
  text: string;
  target_user_types: UserType[];
  create_time: string;
};

export type Credit = {
  duration_minutes: number;
  create_time: string;
};

export type Entity = {
  id: number;
  user_id: string;
  type: "headmaster" | "vice_headmaster";
};

/* -------------------------------------------------------------------------- */
/*                                  Attendance                                */
/* -------------------------------------------------------------------------- */

export type StudentAttendance = {
  date_id: number;
  student_id: string;
  student_name: string;
  status: Attendance;
};

export type StaffAttendance = {
  date_id: number;
  staff_id: string;
  staff_name: string;
  status: Attendance;
};

export type StudentClassAttendance = {
  date_id: number;
  student_id: string;
  student_name: string;
  class_id: number;
  status: Attendance;
};

/* -------------------------------------------------------------------------- */
/*                                   Finance                                  */
/* -------------------------------------------------------------------------- */

export type Invoice = {
  id: number;
  user_bill_id: number;
  status: InvoiceStatus;
  amount: number;
  note: string;
  payment_proof: {
    invoice_id: number;
    uri: string;
    note: string;
    update_by: string;
    update_time: string;
  };
};

export type Bill = {
  id: number;
  custom_id: string;
  name: string;
  status: string; // ? draft, ... ???
  target_user_types: UserType[];
  amount: number;
  deadline: string;
  description: string;
};

export type UserBill = {
  id: number;
  bill_id: number;
  user_id: string;
};

/* -------------------------------------------------------------------------- */
/*                                  Classroom                                 */
/* -------------------------------------------------------------------------- */

export type Task = {
  id: number;
  name: string;
  description: string;
  class_id: number;
  start_time: string;
  deadline: string;
  allow_submission: boolean;
  allow_overdue_submission: boolean;
  attachment_file_uri: string;
};

export type Score = {
  task_id: number;
  student_id: string;
  value: number;
  feedback: string;
};

export type TeachingMaterial = {
  id: number;
  description: string;
  attachment_file_uri: string;
  subject_id: number;
  subject_name: string;
  grade: Grade;
  curriculum_id: number;
  curriculum_name: string;
  study_program_id: number;
  study_program_name: string;
  create_by: string;
};

export type TeachingPlan = {
  id: number;
  title: string;
  class_id: number;
  markdown: string;
  teaching_materials:
    | {
        id: number;
        description: string;
        attachment_file_uri: string;
      }[]
    | null;
  tasks:
    | {
        id: number;
        name: string;
        deadline: string;
        attachment_file_uri: string;
      }[]
    | null;
  teaching_goal: string;
  teaching_activity: string;
  teaching_scoring: string;
};

export type Submission = {
  task_id: number;
  submission_time: string;
  student_id: string;
  attachment_file_uri: string;
  note: string;
};

/* -------------------------------------------------------------------------- */
/*                                    CBT                                     */
/* -------------------------------------------------------------------------- */

export type Exam = {
  id: number;
  name: string;
  description: string;
  entry_code: string;
  teaching_plan_ids: number[];
  is_anti_cheat: boolean;
  is_shuffle_question: boolean;
  display_question_num: number;
  teaching_plan_details:
    | {
        id: number;
        title: string;
        class_id: number;
        class_name: string;
        student_group_id: number;
        student_group_name: string;
        subject_id: number;
        subject_name: string;
      }[]
    | null
    | undefined;
  type: ExamType;
  start_time: string;
  end_time: string;
  external_link?: string | undefined | null;
};

export type ExamOptionQuestion = {
  id: string;
  text: string;
  type: "option";
  weight: number;
  image_url?: string;
  option_detail: {
    options: {
      id: string;
      text: string;
      correct?: boolean;
    }[];
  };
};

export type ExamTextQuestion = {
  id: string;
  text: string;
  image_url?: string;
  type: "text";
  weight: number;
};

export type ExamScaleQuestion = {
  id: string;
  text: string;
  image_url?: string;
  type: "scale";
  weight: number;
  scale_detail: {
    min_label: string;
    max_label: string;
  };
};

export type ExamQuestion =
  | ExamOptionQuestion
  | ExamTextQuestion
  | ExamScaleQuestion;

export type ExamSubmission = {
  exam_id: number;
  user_id: string;
  start_num: number;
  note?: string;
  status: ExamSubmissionStatus;
  start_time: string;
  submit_time: string;
};

export type ExamSubmissionAnswer = {
  question_id: string;
  option_id_values?: string[] | null | null[];
  text_value?: string | null;
};

export type ExamScore = {
  exam_id: number;
  user_id: string;
  value: number;
  feedback?: string;
  details?:
    | {
        question_id: string;
        value: number;
        weighted_value: number;
      }[]
    | null;
};

/* -------------------------------------------------------------------------- */
/*                                  Report                                    */
/* -------------------------------------------------------------------------- */

export type Template = {
  id: number;
  pdf_generator_fn_text: string;
  name: string;
  placeholder: Array<{
    key: string;
    kind: string | unknown;
  }>;
  ref_url: string;
  description?: string;
};

export type Report = {
  id: number;
  name: string;
  template_id: number;
  period_id: number;
  study_program_id?: number;
  grade?: string;
  status: ReportStatus;
};

export type ReportScore = {
  student_id: string;
  report_id: number;
  class_id: number;
  scores: {
    name: string;
    value_num: number | null;
    value_text: string | null;
  }[];
};
