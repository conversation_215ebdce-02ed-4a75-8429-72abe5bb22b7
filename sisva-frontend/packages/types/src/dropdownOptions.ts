import type {
  Attendance,
  AttendanceText,
  Day,
  DayText,
  EducationLevel,
  EducationLevelText,
  ExamType,
  ExamTypeText,
  Gender,
  GenderText,
  Guardian,
  GuardianText,
  IncomeLevel,
  IncomeLevelText,
  InvoiceStatus,
  InvoiceStatusText,
  LifeStatus,
  LifeStatusText,
  Nationality,
  NationalityText,
  PeriodStatus,
  PeriodStatusText,
  Permission,
  PermissionText,
  Relationship,
  RelationshipText,
  Religion,
  ReligionText,
  ReportStatus,
  ReportStatusText,
  SchoolEducationLevel,
  SchoolEducationLevelText,
  SchoolOwnershipType,
  SchoolOwnershipTypeText,
  SchoolType,
  SchoolTypeText,
  UserType,
  UserTypeText,
} from "./types";

export const genderOptions: { label: GenderText; value: Gender }[] = [
  {
    label: "Laki-laki",
    value: "male",
  },
  {
    label: "Perempuan",
    value: "female",
  },
];

export const nationalityOptions: {
  label: NationalityText;
  value: Nationality;
}[] = [
  {
    label: "Warga Negara Indonesia",
    value: "wni",
  },
  {
    label: "Warga Negara Asing",
    value: "wna",
  },
];

export const religionOptions: { label: ReligionText; value: Religion }[] = [
  {
    label: "Islam",
    value: "islam",
  },
  {
    label: "Kristen Protestan",
    value: "christian_protestant",
  },
  {
    label: "Kristen Katolik",
    value: "christian_catholic",
  },
  {
    label: "Hindu",
    value: "hindu",
  },
  {
    label: "Buddha",
    value: "buddha",
  },
  {
    label: "Konghucu",
    value: "konghucu",
  },
];

export const roleOptions: { label: UserTypeText; value: UserType }[] = [
  {
    label: "Staf",
    value: "staff",
  },
  {
    label: "Guru",
    value: "teacher",
  },
  {
    label: "Siswa",
    value: "student",
  },
  {
    label: "Wali",
    value: "student_guardian",
  },
];

export const permissionsOptions: {
  label: PermissionText;
  value: Permission;
}[] = [
  {
    label: "Sekolah",
    value: "manage_school",
  },
  {
    label: "Karyawan",
    value: "manage_staff",
  },
  {
    label: "Akademik",
    value: "manage_academic",
  },
  {
    label: "Siswa",
    value: "manage_student",
  },
  {
    label: "Informasi",
    value: "manage_information",
  },
  {
    label: "Keuangan",
    value: "manage_finance",
  },
  {
    label: "Rapot",
    value: "manage_report",
  },
];

export const guardianOptions: { label: GuardianText; value: Guardian }[] = [
  {
    label: "Ayah",
    value: "father",
  },
  {
    label: "Ibu",
    value: "mother",
  },
  {
    label: "Wali",
    value: "guardian",
  },
  {
    label: "Murid",
    value: "student",
  },
];

export const educationLevelOptions: {
  label: EducationLevelText;
  value: EducationLevel;
}[] = [
  {
    label: "SD",
    value: "elementary",
  },
  {
    label: "SMP",
    value: "junior_high",
  },
  {
    label: "SMA/SMK/MA",
    value: "senior_high",
  },
  {
    label: "S1/D3/D4",
    value: "undergraduate",
  },
  {
    label: "S2",
    value: "graduate",
  },
  {
    label: "S3",
    value: "doctorate",
  },
];

export const incomeLevelOptions: {
  label: IncomeLevelText;
  value: IncomeLevel;
}[] = [
  {
    label: "0 - Rp1.000.000",
    value: "0-1",
  },
  {
    label: "Rp1.000.000 - Rp10.000.000",
    value: "1-10",
  },
  {
    label: "Rp10.000.000 - Rp50.000.000",
    value: "10-50",
  },
  {
    label: "Rp50.000.000 - Rp100.000.000",
    value: "50-100",
  },
  {
    label: "Rp100.000.000+",
    value: "100+",
  },
];

export const lifeStatusOptions: { label: LifeStatusText; value: LifeStatus }[] =
  [
    {
      label: "Masih Hidup",
      value: "alive",
    },
    {
      label: "Meninggal Dunia",
      value: "dead",
    },
  ];

export const relationshipOptions: {
  label: RelationshipText;
  value: Relationship;
}[] = [
  {
    label: "Orang Tua",
    value: "parents",
  },
  {
    label: "Kakak/Adik",
    value: "siblings",
  },
  {
    label: "Saudara",
    value: "family",
  },
  {
    label: "Teman",
    value: "friends",
  },
  {
    label: "Suami/Istri",
    value: "spouse",
  },
  {
    label: "Lainnya",
    value: "others",
  },
];

export const invoiceStatusOptions: {
  label: InvoiceStatusText;
  value: InvoiceStatus;
}[] = [
  {
    label: "Pending",
    value: "pending",
  },
  {
    label: "Verifikasi",
    value: "inreview",
  },
  {
    label: "Lunas",
    value: "done",
  },
];

export const periodStatusOptions: {
  label: PeriodStatusText;
  value: PeriodStatus;
}[] = [
  {
    label: "Belum Aktif",
    value: "inactive",
  },
  {
    label: "Aktif",
    value: "active",
  },
  {
    label: "Selesai",
    value: "finished",
  },
];

export const attendanceStatusOptions: {
  label: AttendanceText;
  value: Attendance;
}[] = [
  {
    label: "Alpha",
    value: "absent",
  },
  {
    label: "Hadir",
    value: "present",
  },
  {
    label: "Izin",
    value: "leave",
  },
  {
    label: "Sakit",
    value: "sick",
  },
];

export const schoolTypeOptions: {
  label: SchoolTypeText;
  value: SchoolType;
}[] = [
  {
    label: "Umum",
    value: "general",
  },
  {
    label: "Madrasah",
    value: "madrasa",
  },
  {
    label: "Lainnya",
    value: "other",
  },
];

export const schoolEducationLevelOptions: {
  label: SchoolEducationLevelText;
  value: SchoolEducationLevel;
}[] = [
  {
    label: "SD/MI",
    value: "elementary",
  },
  {
    label: "SMP/MTS",
    value: "junior_high",
  },
  {
    label: "SMA/SMK/MA",
    value: "senior_high",
  },
  {
    label: "Lainnya",
    value: "other",
  },
];

export const schoolOwnershipTypeOptions: {
  label: SchoolOwnershipTypeText;
  value: SchoolOwnershipType;
}[] = [
  {
    label: "Negeri",
    value: "public",
  },
  {
    label: "Swasta",
    value: "private",
  },
];

export const dayOptions: {
  label: DayText;
  value: Day;
}[] = [
  {
    label: "Senin",
    value: 1,
  },
  {
    label: "Selasa",
    value: 2,
  },
  {
    label: "Rabu",
    value: 3,
  },
  {
    label: "Kamis",
    value: 4,
  },
  {
    label: "Jumat",
    value: 5,
  },
  {
    label: "Sabtu",
    value: 6,
  },
  {
    label: "Minggu",
    value: 7,
  },
];

export const examTypeOptions: {
  label: ExamTypeText;
  value: ExamType;
}[] = [
  {
    label: "Kuis",
    value: "quiz",
  },
  {
    label: "Survey",
    value: "survey",
  },
  {
    label: "Eksternal",
    value: "external",
  },
];

export const reportStatusOptions: {
  label: ReportStatusText;
  value: ReportStatus;
}[] = [
  {
    label: "Draft",
    value: "draft",
  },
  {
    label: "Pengisian",
    value: "filling",
  },
  {
    label: "Terbit",
    value: "published",
  },
];
