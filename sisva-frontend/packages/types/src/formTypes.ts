import type { InferType } from "yup";
import { array, number, object, ref, string } from "yup";

export const updatePasswordSchema = object({
  current_password: string().required("Password wajib diisi."),
  new_password: string()
    .required("Password wajib diisi.")
    .min(8, "Password minimal 8 karakter"),
  confirm_password: string()
    .required("Konfirmasi password wajib diisi.")
    .oneOf([ref("new_password")], "Password tidak sama"),
});
export type UpdatePasswordForm = InferType<typeof updatePasswordSchema>;

export const resetPasswordSchema = object({
  new_password: string()
    .required("Password wajib diisi.")
    .min(8, "Password minimal 8 karakter"),
  confirm_password: string()
    .required("Konfirmasi password wajib diisi.")
    .oneOf([ref("new_password")], "Password tidak sama"),
});
export type ResetPasswordForm = InferType<typeof resetPasswordSchema>;

export const createGuardianSchema = object({
  name: string().required("Nama wajib diisi."),
  password: string()
    .required("Password wajib diisi.")
    .min(8, "Password minimal 8 karakter"),
  confirm_password: string()
    .required("Konfirmasi password wajib diisi.")
    .oneOf([ref("password")], "Password tidak sama"),
});
export type CreateGuardianForm = InferType<typeof createGuardianSchema>;

export const updateGuardianSchema = object({
  name: string().required("Nama wajib diisi."),
  nik: string()
    .nullable()
    .matches(/^[0-9]+$/, {
      message: "NIK harus berupa angka.",
      excludeEmptyString: true,
    }),
  email: string().email("Email tidak valid."),
  phone: string()
    .nullable()
    .matches(/^[0-9]+$/, {
      message: "Nomor telepon harus berupa angka.",
      excludeEmptyString: true,
    }),
  gender: string(),
  religion: string(),
  nationality: string(),
  address: string(),
  profile_image_uri: string(),
  occupation: string(),
  education_level: string(),
  income_level: string(),
  life_status: string(),
  birth_year: string(),
});
export type UpdateGuardianForm = InferType<typeof updateGuardianSchema>;

export const updateStudentsGuardianSchema = object({
  guardian_id: string().required("Guardian ID wajib diisi."),
  student_ids: array()
    .of(
      object({
        label: string().required(),
        id: string().required(),
      })
    )
    .required(),
});
export type UpdateStudentsGuardianForm = InferType<
  typeof updateStudentsGuardianSchema
>;

export const createStaffSchema = object({
  name: string().required("Nama wajib diisi."),
  type: string().required("Tipe wajib diisi."),
  permissions: array()
    .of(string().required("Akses wajib diisi."))
    .required("Akses wajib diisi.")
    .min(1, "Minimum satu akses diperlukan."),
  password: string()
    .required("Password wajib diisi.")
    .min(8, "Password minimal 8 karakter"),
  confirm_password: string()
    .required("Konfirmasi password wajib diisi.")
    .oneOf([ref("password")], "Password tidak sama"),
});
export type CreateStaffForm = InferType<typeof createStaffSchema>;

export const updateStaffSchema = object({
  name: string().required("Nama wajib diisi."),
  nik: string()
    .nullable()
    .matches(/^[0-9]+$/, {
      message: "NIK harus berupa angka.",
      excludeEmptyString: true,
    }),
  nuptk: string()
    .nullable()
    .matches(/^[0-9]+$/, {
      message: "NUPTK harus berupa angka.",
      excludeEmptyString: true,
    }),
  permissions: array()
    .of(string().required("Akses wajib diisi."))
    .required("Akses wajib diisi.")
    .min(1, "Minimum satu akses diperlukan."),
  email: string().email("Email tidak valid."),
  phone: string()
    .nullable()
    .matches(/^[0-9]+$/, {
      message: "Nomor telepon harus berupa angka.",
      excludeEmptyString: true,
    }),
  gender: string(),
  religion: string(),
  nationality: string(),
  address: string(),
  profile_image_uri: string(),
});
export type UpdateStaffForm = InferType<typeof updateStaffSchema>;

export const createStudentSchema = object({
  name: string().required("Nama wajib diisi."),
  password: string()
    .required("Password wajib diisi.")
    .min(8, "Password minimal 8 karakter"),
  confirm_password: string()
    .required("Konfirmasi password wajib diisi.")
    .oneOf([ref("password")], "Password tidak sama"),
});
export type CreateStudentForm = InferType<typeof createStudentSchema>;

export const updateStudentSchema = object({
  name: string().required("Nama wajib diisi."),
  nik: string()
    .nullable()
    .matches(/^[0-9]+$/, {
      message: "NIK harus berupa angka.",
      excludeEmptyString: true,
    }),
  email: string().email("Email tidak valid."),
  phone: string()
    .nullable()
    .matches(/^[0-9]+$/, {
      message: "Nomor telepon harus berupa angka.",
      excludeEmptyString: true,
    }),
  gender: string(),
  religion: string(),
  nationality: string(),
  address: string(),
  profile_image_uri: string(),
  study_program_id: number(),
  grade: string(),
});
export type UpdateStudentForm = InferType<typeof updateStudentSchema>;

export const billSchema = object({
  custom_id: string().notRequired(),
  name: string().required("Nama harus diisi"),
  status: string().notRequired(),
  target_user_types: array()
    .min(1, "Target harus dipilih")
    .required("Target harus dipilih"),
  amount: number()
    .min(1, "Harga harus diisi")
    .typeError("Harga harus berupa angka")
    .required("Harga harus diisi"),
  deadline: string().required("Batas waktu harus diisi"),
  description: string().notRequired(),
});
export type BillSchemaForm = InferType<typeof billSchema>;

export const invoiceSchema = object({
  user_bill_id: number()
    .required("Tagihan pengguna wajib diisi")
    .notOneOf([0], "Tagihan pengguna wajib diisi"),
  status: string().required("Status wajib diisi"),
  amount: number()
    .min(1, "Harga harus diisi")
    .typeError("Harga harus berupa angka")
    .required("Harga harus diisi"),
  note: string(),
  payment_proof_uri: string(),
  payment_proof_note: string(),
});
export type InvoiceSchemaForm = InferType<typeof invoiceSchema>;

export const studentGroupSchema = object({
  name: string().required("Nama wajib diisi"),
  type: string().required("Tipe wajib dipilih"),
  period_id: number()
    .required("Periode wajib dipilih")
    .notOneOf([0], "Periode wajib diisi"),
  study_program_id: number()
    .required("Program Studi wajib dipilih")
    .notOneOf([0], "Program Studi wajib dipilih"),
  grade: string().required("Tingkatan wajib dipilih"),
  homeroom_teacher_id: string().required("Wali kelas wajib dipilih"),
});
export type StudentGroupSchemaForm = InferType<typeof studentGroupSchema>;

export const schoolSchema = object({
  name: string().required("Nama sekolah wajib diisi"),
  abbreviation: string(),
  education_level: string().required("Tingkatan sekolah wajib dipilih"),
  education_ownership_type: string().required(
    "Kepemilikan sekolah wajib dipilih"
  ),
  education_type: string().required("Jenis sekolah wajib dipilih"),
  identifier_type: string(),
  identifier_value: string(),
  landing_image_uri: string().required("Gambar latar tidak boleh kosong"),
  logo_uri: string().required("Logo sekolah tidak boleh kosong"),
  theme_json_text: string(),
  email: string().email("Email tidak valid."),
  phone: string()
    .nullable()
    .matches(/^[0-9]+$/, {
      message: "Nomor telepon harus berupa angka.",
      excludeEmptyString: true,
    }),
  address: string(),
});
export type SchoolSchemaForm = InferType<typeof schoolSchema>;
