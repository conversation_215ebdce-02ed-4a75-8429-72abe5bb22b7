import { match } from "ts-pattern";

export type SortDirection = "ascending" | "descending" | "";

export type Permission =
  | "manage_school"
  | "manage_staff"
  | "manage_academic"
  | "manage_student"
  | "manage_report"
  | "manage_information"
  | "manage_finance";
export type PermissionText =
  | "Sekolah"
  | "Karyawan"
  | "Akademik"
  | "Siswa"
  | "Rapot"
  | "Informasi"
  | "Keuangan";
export function getPermissions(data: {
  manage_school: boolean;
  manage_staff: boolean;
  manage_academic: boolean;
  manage_student: boolean;
  manage_report: boolean;
  manage_information: boolean;
  manage_finance: boolean;
}): Permission[] {
  const permissions: Permission[] = [];
  if (data.manage_school) permissions.push("manage_school");
  if (data.manage_staff) permissions.push("manage_staff");
  if (data.manage_academic) permissions.push("manage_academic");
  if (data.manage_student) permissions.push("manage_student");
  if (data.manage_report) permissions.push("manage_report");
  if (data.manage_information) permissions.push("manage_information");
  if (data.manage_finance) permissions.push("manage_finance");
  return permissions;
}
export function getPermmissionText(permission: Permission): PermissionText {
  switch (permission) {
    case "manage_school":
      return "Sekolah";
    case "manage_staff":
      return "Karyawan";
    case "manage_academic":
      return "Akademik";
    case "manage_student":
      return "Siswa";
    case "manage_report":
      return "Rapot";
    case "manage_information":
      return "Informasi";
    case "manage_finance":
      return "Keuangan";
  }
}

export type UserType =
  | "staff"
  | "teacher"
  | "student"
  | "student_guardian"
  | "management";
export type UserTypeText = "Staf" | "Guru" | "Siswa" | "Wali" | "Manajer";
export function getUserType(text: UserTypeText): UserType {
  switch (text) {
    case "Staf":
      return "staff";
    case "Guru":
      return "teacher";
    case "Siswa":
      return "student";
    case "Wali":
      return "student_guardian";
    case "Manajer":
      return "management";
  }
}
export function getUserTypeText(type: UserType): UserTypeText {
  switch (type) {
    case "staff":
      return "Staf";
    case "teacher":
      return "Guru";
    case "student":
      return "Siswa";
    case "student_guardian":
      return "Wali";
    case "management":
      return "Manajer";
  }
}

export type UserRole =
  | "staff"
  | "teacher"
  | "student"
  | "student_guardian"
  | "admin";

export type Gender = "male" | "female" | "others";
export type GenderText = "Laki-laki" | "Perempuan" | "Lainnya";
export function getGender(text: GenderText): Gender {
  switch (text) {
    case "Laki-laki":
      return "male";
    case "Perempuan":
      return "female";
    case "Lainnya":
      return "others";
  }
}
export function getGenderText(gender: Gender): GenderText {
  switch (gender) {
    case "male":
      return "Laki-laki";
    case "female":
      return "Perempuan";
    case "others":
      return "Lainnya";
  }
}

export type Nationality = "wni" | "wna";
export type NationalityText = "Warga Negara Indonesia" | "Warga Negara Asing";
export function getNationality(text: NationalityText): Nationality {
  switch (text) {
    case "Warga Negara Indonesia":
      return "wni";
    case "Warga Negara Asing":
      return "wna";
  }
}
export function getNationalityText(nationality: Nationality): NationalityText {
  switch (nationality) {
    case "wni":
      return "Warga Negara Indonesia";
    case "wna":
      return "Warga Negara Asing";
  }
}

export type Religion =
  | "islam"
  | "christian_protestant"
  | "christian_catholic"
  | "hindu"
  | "buddha"
  | "konghucu"
  | "others";
export type ReligionText =
  | "Islam"
  | "Kristen Protestan"
  | "Kristen Katolik"
  | "Hindu"
  | "Buddha"
  | "Konghucu"
  | "Lainnya";
export function getReligion(text: ReligionText): Religion {
  switch (text) {
    case "Islam":
      return "islam";
    case "Kristen Protestan":
      return "christian_protestant";
    case "Kristen Katolik":
      return "christian_catholic";
    case "Hindu":
      return "hindu";
    case "Buddha":
      return "buddha";
    case "Konghucu":
      return "konghucu";
    case "Lainnya":
      return "others";
  }
}
export function getReligionText(text: Religion): ReligionText {
  switch (text) {
    case "islam":
      return "Islam";
    case "christian_protestant":
      return "Kristen Protestan";
    case "christian_catholic":
      return "Kristen Katolik";
    case "hindu":
      return "Hindu";
    case "buddha":
      return "Buddha";
    case "konghucu":
      return "Konghucu";
    case "others":
      return "Lainnya";
  }
}

export type Guardian = "father" | "mother" | "guardian" | "student";
export type GuardianText = "Ayah" | "Ibu" | "Wali" | "Murid";
export function getGuardian(text: GuardianText): Guardian {
  switch (text) {
    case "Ayah":
      return "father";
    case "Ibu":
      return "mother";
    case "Wali":
      return "guardian";
    case "Murid":
      return "student";
  }
}
export function getGuardianText(text: Guardian): GuardianText {
  switch (text) {
    case "father":
      return "Ayah";
    case "mother":
      return "Ibu";
    case "guardian":
      return "Wali";
    case "student":
      return "Murid";
  }
}

export type EducationLevel =
  | "elementary"
  | "junior_high"
  | "senior_high"
  | "undergraduate"
  | "graduate"
  | "doctorate"
  | "other";
export type EducationLevelText =
  | "SD"
  | "SMP"
  | "SMA/SMK/MA"
  | "S1/D3/D4"
  | "S2"
  | "S3"
  | "Lainnya";
export function getEducationLevel(text: EducationLevelText): EducationLevel {
  switch (text) {
    case "SD":
      return "elementary";
    case "SMP":
      return "junior_high";
    case "SMA/SMK/MA":
      return "senior_high";
    case "S1/D3/D4":
      return "undergraduate";
    case "S2":
      return "graduate";
    case "S3":
      return "doctorate";
    case "Lainnya":
      return "other";
  }
}
export function getEducationLevelText(
  level: EducationLevel
): EducationLevelText {
  switch (level) {
    case "elementary":
      return "SD";
    case "junior_high":
      return "SMP";
    case "senior_high":
      return "SMA/SMK/MA";
    case "undergraduate":
      return "S1/D3/D4";
    case "graduate":
      return "S2";
    case "doctorate":
      return "S3";
    case "other":
      return "Lainnya";
  }
}

export type IncomeLevel = "0-1" | "1-10" | "10-50" | "50-100" | "100+";
export type IncomeLevelText =
  | "0 - Rp1.000.000"
  | "Rp1.000.000 - Rp10.000.000"
  | "Rp10.000.000 - Rp50.000.000"
  | "Rp50.000.000 - Rp100.000.000"
  | "Rp100.000.000+";
export function getIncomeLevel(text: IncomeLevelText): IncomeLevel {
  switch (text) {
    case "0 - Rp1.000.000":
      return "0-1";
    case "Rp1.000.000 - Rp10.000.000":
      return "1-10";
    case "Rp10.000.000 - Rp50.000.000":
      return "10-50";
    case "Rp50.000.000 - Rp100.000.000":
      return "50-100";
    case "Rp100.000.000+":
      return "100+";
  }
}
export function getIncomeLevelText(level: IncomeLevel): IncomeLevelText {
  switch (level) {
    case "0-1":
      return "0 - Rp1.000.000";
    case "1-10":
      return "Rp1.000.000 - Rp10.000.000";
    case "10-50":
      return "Rp10.000.000 - Rp50.000.000";
    case "50-100":
      return "Rp50.000.000 - Rp100.000.000";
    case "100+":
      return "Rp100.000.000+";
  }
}

export type LifeStatus = "alive" | "dead";
export type LifeStatusText = "Masih Hidup" | "Meninggal Dunia";
export function getLifeStatus(text: LifeStatusText): LifeStatus {
  switch (text) {
    case "Masih Hidup":
      return "alive";
    case "Meninggal Dunia":
      return "dead";
  }
}
export function getLifeStatusText(status: LifeStatus): LifeStatusText {
  switch (status) {
    case "alive":
      return "Masih Hidup";
    case "dead":
      return "Meninggal Dunia";
  }
}

export type Relationship =
  | "parents"
  | "siblings"
  | "family"
  | "friends"
  | "spouse"
  | "others";
export type RelationshipText =
  | "Orang Tua"
  | "Kakak/Adik"
  | "Saudara"
  | "Teman"
  | "Suami/Istri"
  | "Lainnya";
export function getRelationship(text: RelationshipText): Relationship {
  switch (text) {
    case "Orang Tua":
      return "parents";
    case "Kakak/Adik":
      return "siblings";
    case "Saudara":
      return "family";
    case "Teman":
      return "friends";
    case "Suami/Istri":
      return "spouse";
    case "Lainnya":
      return "others";
  }
}
export function getRelationshipText(
  relationship: Relationship
): RelationshipText {
  switch (relationship) {
    case "parents":
      return "Orang Tua";
    case "siblings":
      return "Kakak/Adik";
    case "family":
      return "Saudara";
    case "friends":
      return "Teman";
    case "spouse":
      return "Suami/Istri";
    case "others":
      return "Lainnya";
  }
}

export type SubjectType = "mandatory" | "optional";
export type SubjectTypeText = "Wajib" | "Pilihan";
export function getSubjectType(text: SubjectTypeText): SubjectType {
  switch (text) {
    case "Wajib":
      return "mandatory";
    case "Pilihan":
      return "optional";
  }
}

export type PeriodStatus = "active" | "inactive" | "finished" | "cancelled";
export type PeriodStatusText =
  | "Aktif"
  | "Belum Aktif"
  | "Selesai"
  | "Dibatalkan";
export function getPeriodStatus(text: PeriodStatusText): PeriodStatus {
  switch (text) {
    case "Aktif":
      return "active";
    case "Belum Aktif":
      return "inactive";
    case "Selesai":
      return "finished";
    case "Dibatalkan":
      return "cancelled";
  }
}
export function getPeriodStatusText(status: PeriodStatus): PeriodStatusText {
  switch (status) {
    case "active":
      return "Aktif";
    case "inactive":
      return "Belum Aktif";
    case "finished":
      return "Selesai";
    case "cancelled":
      return "Dibatalkan";
  }
}

export type ClassType = "homeroom" | "moving";
export type ClassTypeText = "Kelas Wajib" | "Kelas Pilihan";
export function getClassType(text: ClassTypeText): ClassType {
  switch (text) {
    case "Kelas Wajib":
      return "homeroom";
    case "Kelas Pilihan":
      return "moving";
  }
}

export type Attendance = "present" | "sick" | "leave" | "absent";
export type AttendanceText = "Hadir" | "Sakit" | "Izin" | "Alpha";
export type AttendanceTextAbbr = "H" | "S" | "I" | "A";
export function getAttendance(
  text: AttendanceText | AttendanceTextAbbr
): Attendance {
  switch (text) {
    case "H":
      return "present";
    case "S":
      return "sick";
    case "I":
      return "leave";
    case "A":
      return "absent";
    // ----
    case "Hadir":
      return "present";
    case "Sakit":
      return "sick";
    case "Izin":
      return "leave";
    case "Alpha":
      return "absent";
    // ----
    default:
      return "present";
  }
}
export function getAttendanceText(
  text: Attendance | AttendanceTextAbbr
): AttendanceText {
  return match(text)
    .returnType<AttendanceText>()
    .with("present", "H", () => "Hadir")
    .with("sick", "S", () => "Sakit")
    .with("leave", "I", () => "Izin")
    .with("absent", "A", () => "Alpha")
    .exhaustive();
}

export type MonthText =
  | "Januari"
  | "Februari"
  | "Maret"
  | "April"
  | "Mei"
  | "Juni"
  | "Juli"
  | "Agustus"
  | "September"
  | "Oktober"
  | "November"
  | "Desember";
export function getMonthNumber(text: MonthText): string {
  switch (text) {
    case "Januari":
      return "01";
    case "Februari":
      return "02";
    case "Maret":
      return "03";
    case "April":
      return "04";
    case "Mei":
      return "05";
    case "Juni":
      return "06";
    case "Juli":
      return "07";
    case "Agustus":
      return "08";
    case "September":
      return "09";
    case "Oktober":
      return "10";
    case "November":
      return "11";
    case "Desember":
      return "12";
  }
}

function getKeyFromValue<T extends Record<string | number, string>>(
  record: T,
  value: T[keyof T]
): keyof T {
  const entry = Object.entries(record).find(([_, v]) => v === value);
  if (!entry) {
    throw new Error(`Value "${value}" not found in record`);
  }

  // Handle numeric keys by converting them back to numbers if needed
  const key = entry[0];
  return isNaN(Number(key)) ? key : (Number(key) as keyof T);
}

//TODO: refactor all above to be something like this
const invoiceStatusLabels = {
  pending: "Pending",
  inreview: "Verifikasi",
  done: "Lunas",
} as const;
export type InvoiceStatus = keyof typeof invoiceStatusLabels;
export type InvoiceStatusText = (typeof invoiceStatusLabels)[InvoiceStatus];
export function getInvoiceStatusText(status: InvoiceStatus) {
  return invoiceStatusLabels[status];
}
export function getInvoiceStatus(statusText: InvoiceStatusText) {
  return getKeyFromValue(invoiceStatusLabels, statusText);
}

export type StudyProgramStatus = "active" | "inactive";

const dayLabels = {
  1: "Senin",
  2: "Selasa",
  3: "Rabu",
  4: "Kamis",
  5: "Jumat",
  6: "Sabtu",
  7: "Minggu",
} as const;
export type Day = keyof typeof dayLabels;
export type DayText = (typeof dayLabels)[Day];
export function getDay(text: DayText) {
  return getKeyFromValue(dayLabels, text);
}
export function getDayText(day: Day) {
  return dayLabels[day];
}

const schoolTypeLabels = {
  general: "Umum",
  madrasa: "Madrasah",
  other: "Lainnya",
} as const;
export type SchoolType = keyof typeof schoolTypeLabels;
export type SchoolTypeText = (typeof schoolTypeLabels)[SchoolType];
export function getSchoolTypeText(key: SchoolType) {
  return schoolTypeLabels[key];
}
export function getSchoolType(text: SchoolTypeText) {
  return getKeyFromValue(schoolTypeLabels, text);
}

const schoolEducationLevelLabels = {
  elementary: "SD/MI",
  junior_high: "SMP/MTS",
  senior_high: "SMA/SMK/MA",
  other: "Lainnya",
} as const;
export type SchoolEducationLevel = keyof typeof schoolEducationLevelLabels;
export type SchoolEducationLevelText =
  (typeof schoolEducationLevelLabels)[SchoolEducationLevel];
export function getSchoolEducationLevelText(key: SchoolEducationLevel) {
  return schoolEducationLevelLabels[key];
}
export function getSchoolEducationLevel(text: SchoolEducationLevelText) {
  return getKeyFromValue(schoolEducationLevelLabels, text);
}

const schoolOwnershipTypeLabels = {
  public: "Negeri",
  private: "Swasta",
} as const;
export type SchoolOwnershipType = keyof typeof schoolOwnershipTypeLabels;
export type SchoolOwnershipTypeText =
  (typeof schoolOwnershipTypeLabels)[SchoolOwnershipType];
export function getSchoolOwnershipTypeText(key: SchoolOwnershipType) {
  return schoolOwnershipTypeLabels[key];
}
export function getSchoolOwnershipType(text: SchoolOwnershipTypeText) {
  return getKeyFromValue(schoolOwnershipTypeLabels, text);
}

const examTypeLabels = {
  quiz: "Kuis",
  survey: "Survey",
  external: "Eksternal",
} as const;

export type ExamType = keyof typeof examTypeLabels;
export type ExamTypeText = (typeof examTypeLabels)[ExamType];
export function getExamTypeText(key: ExamType) {
  return examTypeLabels[key];
}
export function getExamType(text: ExamTypeText) {
  return getKeyFromValue(examTypeLabels, text);
}

export type ExamSubmissionStatus = "ongoing" | "submitted";

const reportStatusLabels = {
  draft: "Draft",
  filling: "Pengisian",
  published: "Terbit",
} as const;
export type ReportStatus = keyof typeof reportStatusLabels;
export type ReportStatusText = (typeof reportStatusLabels)[ReportStatus];
export function getReportStatusText(key: ReportStatus) {
  return reportStatusLabels[key];
}
export function getReportStatus(text: ReportStatusText) {
  return getKeyFromValue(reportStatusLabels, text);
}
