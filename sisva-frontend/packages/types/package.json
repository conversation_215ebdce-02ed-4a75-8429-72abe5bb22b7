{"name": "@sisva/types", "version": "0.0.0", "private": true, "scripts": {"lint": "eslint ./src", "lint:fix": "eslint ./src --fix", "typecheck": "tsc --noEmit", "build": "tsup", "dev": "tsup --watch"}, "exports": {"./apiTypes": "./dist/apiTypes.js", "./dropdownOptions": "./dist/dropdownOptions.js", "./formTypes": "./dist/formTypes.js", "./types": "./dist/types.js", "./utils": {"types": "./src/utils.d.ts"}}, "devDependencies": {"@sisva/eslint-config": "workspace:*", "@sisva/typescript-config": "workspace:*", "@types/node": "^22.13.0", "@types/react": "19.0.8", "@types/react-dom": "19.0.3", "eslint": "^9.21.0", "tsup": "8.3.0", "typescript": "5.7.3"}, "dependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "ts-pattern": "^5.6.2", "yup": "^1.6.1"}}