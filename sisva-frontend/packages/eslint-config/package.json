{"name": "@sisva/eslint-config", "version": "0.0.0", "type": "module", "private": true, "exports": {"./base": "./base.js", "./react-internal": "./react-internal.js"}, "devDependencies": {"@eslint/js": "^9.21.0", "eslint": "^9.21.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-only-warn": "^1.1.0", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-simple-import-sort": "^12.1.1", "eslint-plugin-turbo": "^2.4.0", "globals": "^15.15.0", "typescript": "^5.7.3", "typescript-eslint": "^8.24.0"}}