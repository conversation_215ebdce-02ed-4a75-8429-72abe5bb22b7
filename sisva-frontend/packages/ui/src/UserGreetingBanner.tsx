import { useStudentsStudentGroup } from "@sisva/hooks/query/academic/useStudentGroups";
import { useTeachersSubjectTeachers } from "@sisva/hooks/query/academic/useSubjectTeachers";
import { useGuardiansStudents } from "@sisva/hooks/query/user/useGuardians";
import { useCurrentUser } from "@sisva/providers";
import { Skeleton, theme } from "antd";

import { AvatarWithAcronymByID } from "./AvatarWithAcronym";

export function UserGreetingBanner() {
  const currentUser = useCurrentUser();
  const token = theme.useToken().token;

  // this will be [] if the user is not a guardian
  const { data: students = [], isLoading } = useGuardiansStudents(
    currentUser.id
  );

  // this will be [] if the user is not a teacher
  const { data: subjectTeachers = [] } = useTeachersSubjectTeachers(
    currentUser.id
  );

  // this will be undefined if the user is not a student
  const { data: studentsStudentGroup } = useStudentsStudentGroup(
    currentUser.id
  );

  return (
    <div
      className="flex flex-col sm:flex-row items-center gap-4 w-full max-w-5xl text-white px-4 py-6 rounded-xl relative overflow-hidden"
      style={{
        backgroundColor: token.colorPrimary,
      }}
    >
      <div className="size-64 absolute rounded-full bg-gradient-to-r from-slate-50/10 to-slate-50/0 rotate-45 right-64 bottom-6" />
      <div className="size-40 absolute rounded-xl bg-gradient-to-r from-slate-50/0 to-slate-50/10 rotate-45 -left-4 bottom-0" />
      <div className="size-40 absolute rounded-xl bg-gradient-to-r from-slate-50/10 to-slate-50/0 rotate-45 -right-8 -bottom-20" />

      <AvatarWithAcronymByID user_id={currentUser.id} size={64} />
      <div>
        <div className="font-bold text-lg">Halo {currentUser.name}! 👋</div>
        {(() => {
          if (isLoading) return <Skeleton active paragraph={false} />;
          if (students.length > 0) {
            return (
              <div>
                Wali murid {students.map((student) => student.name).join(", ")}
              </div>
            );
          }
          if (subjectTeachers.length > 0) {
            return (
              <div>
                Guru{" "}
                {subjectTeachers
                  .map((subjectTeacher) => subjectTeacher.subject_name)
                  .join(", ")}
              </div>
            );
          }
          if (studentsStudentGroup) {
            return <div>Siswa {studentsStudentGroup.name}</div>;
          }
        })()}
      </div>
    </div>
  );
}
