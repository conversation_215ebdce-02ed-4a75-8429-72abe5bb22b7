import { Avatar } from "@mui/material";
import { useUser } from "@sisva/hooks/query/user/useUsers";
import { useSchool } from "@sisva/providers";
import { getFileUrl } from "@sisva/utils";
import { getNameAcronym } from "@sisva/utils";

export function AvatarWithAcronym({
  uri,
  size = 40,
  name = "X",
}: {
  uri?: string;
  size?: number;
  name?: string;
}) {
  const school_id = useSchool()?.id;
  const acronym = getNameAcronym(name);
  return (
    <Avatar
      src={getFileUrl(uri, school_id) || undefined}
      sx={{
        width: size,
        height: size,
      }}
    >
      {acronym[0]}
    </Avatar>
  );
}

export function AvatarWithAcronymByID({
  user_id,
  size,
}: {
  user_id?: string;
  size?: number;
}) {
  const { data: userData } = useUser(user_id);

  return (
    <AvatarWithAcronym
      size={size}
      uri={userData?.profile_image_uri}
      name={userData?.name}
    />
  );
}
