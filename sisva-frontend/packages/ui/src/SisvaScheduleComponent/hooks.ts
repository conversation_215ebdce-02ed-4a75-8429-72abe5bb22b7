import { useClassesWithSubjectAndTeacherAndStudentGroup } from "@sisva/hooks/query/academic/useClasses";
import {
  useStudentGroupsClassSchedules,
  useStudentsClassSchedules,
  useTeachersClassSchedules,
} from "@sisva/hooks/query/academic/useClassSchedules";
import {
  useStudentGroupsNonLearningSchedules,
  useStudentsNonLearningSchedules,
} from "@sisva/hooks/query/academic/useNonLearningSchedules";
import { useSelectedPeriod } from "@sisva/hooks/query/academic/usePeriods";
import { useUser } from "@sisva/hooks/query/user/useUsers";
import { mergeArrays } from "@sisva/utils";
import dayjs from "dayjs";
import isoWeek from "dayjs/plugin/isoWeek";
import { sort } from "fast-sort";
import { match } from "ts-pattern";

dayjs.extend(isoWeek);

export function useSisvaSchedulesComponent({
  currentView,
  user_id,
  student_group_id,
  period_id,
}: {
  currentView: "Week" | "Day";
  user_id?: string;
  student_group_id?: number;
  period_id?: number;
}) {
  const { data: user } = useUser(user_id);

  const { data: selectedPeriod, isLoading: L1 } = useSelectedPeriod();

  // this will be [] if user.type is teacher or undefined
  const { data: studentsNonLearningSchedules = [], isLoading: L2 } =
    useStudentsNonLearningSchedules({
      student_id: user?.id,
      period_id: period_id ?? selectedPeriod?.id,
    });

  // this will be [] if userType is teacher or undefined
  const { data: studentsClassSchedules = [], isLoading: L3 } =
    useStudentsClassSchedules({
      student_id: user?.id,
      period_id: period_id ?? selectedPeriod?.id,
    });

  // this will be [] if student_group_id is not defined
  const { data: studentGroupsNonLearningSchedules = [], isLoading: L5 } =
    useStudentGroupsNonLearningSchedules({
      student_group_id: student_group_id,
    });

  // this will be [] if student_group_id is not defined
  const { data: studentGroupsClassSchedules = [], isLoading: L6 } =
    useStudentGroupsClassSchedules({
      student_group_id: student_group_id,
    });

  // this will be [] if userType is not teacher or undefined
  const { data: teachersClassSchedules = [], isLoading: L4 } =
    useTeachersClassSchedules({
      teacher_id: user?.id,
      period_id: period_id ?? selectedPeriod?.id,
    });

  const { data: classes = [], isLoading: L7 } =
    useClassesWithSubjectAndTeacherAndStudentGroup();

  const selectedClassSchedules = match(user?.type)
    .with("student", () => studentsClassSchedules)
    .with("teacher", () => teachersClassSchedules)
    .otherwise(() => studentGroupsClassSchedules);

  const selectedNonLearningSchedules = user_id
    ? studentsNonLearningSchedules
    : studentGroupsNonLearningSchedules;

  const bothSchedules = mergeArrays(
    selectedClassSchedules,
    selectedNonLearningSchedules
  );

  const schedulesData = bothSchedules.map((schedule) => {
    const type = schedule.class_id
      ? ("class-schedule" as const)
      : ("non-learning-schedule" as const);
    const class_ = classes.find((item) => item.id === schedule.class_id);

    return {
      ...schedule,
      start_time_date_obj: dayjs(schedule.start_time, "h:mm A Z")
        .isoWeekday(schedule.day)
        .toDate(),
      end_time_date_obj: dayjs(schedule.end_time, "h:mm A Z")
        .isoWeekday(schedule.day)
        .toDate(),
      start_time_monday: dayjs(schedule.start_time, "h:mm A Z").isoWeekday(1),
      end_time_monday: dayjs(schedule.end_time, "h:mm A Z").isoWeekday(1),
      name: schedule.name ?? schedule.subject_name,
      unique_id: `${schedule.id}-${schedule.class_id}`,
      class: class_,
      type,
    };
  });

  const uniqueIdToDelete: string[] = [];
  const schedulesDataWithConsecutiveCount = schedulesData
    .map((schedule) => {
      if (currentView === "Day")
        return {
          ...schedule,
          consecutive_count: 1,
        };

      let consecutiveCount = 1;
      let currentSchedule = schedule;

      while (currentSchedule) {
        const sameScheduleButNextDay = schedulesData.find(
          (schedule2) =>
            schedule2.start_time === currentSchedule.start_time &&
            schedule2.end_time === currentSchedule.end_time &&
            schedule2.name === currentSchedule.name &&
            schedule2.day === currentSchedule.day + 1
        );
        if (sameScheduleButNextDay) {
          consecutiveCount++;
          uniqueIdToDelete.push(sameScheduleButNextDay.unique_id);
          currentSchedule = sameScheduleButNextDay;
        } else {
          break;
        }
      }

      return {
        ...schedule,
        consecutive_count: consecutiveCount,
      };
    })
    .filter((schedule) => !uniqueIdToDelete.includes(schedule.unique_id));

  const startHour = sort(schedulesData)
    .asc("start_time_monday")[0]
    ?.start_time_monday?.format("HH:mm");

  const endHour = sort(schedulesData)
    .desc("end_time_monday")[0]
    ?.end_time_monday?.format("HH:mm");

  return {
    schedulesData: schedulesDataWithConsecutiveCount,
    startHour,
    endHour,
    isLoading: L1 || L2 || L3 || L4 || L5 || L6 || L7,
  };
}

export type ScheduleData = ReturnType<
  typeof useSisvaSchedulesComponent
>["schedulesData"][number];
