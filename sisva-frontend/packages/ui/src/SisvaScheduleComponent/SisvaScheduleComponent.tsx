import { useUser } from "@sisva/hooks/query/user/useUsers";
import type { Day as DayType } from "@sisva/types/types";
import { syncfusionLocaleID } from "@sisva/utils";
import { cn } from "@sisva/utils";
import { L10n, loadCldr } from "@syncfusion/ej2-base";
import idGregorian from "@syncfusion/ej2-cldr-data/main/id/ca-gregorian.json";
import idNumberData from "@syncfusion/ej2-cldr-data/main/id/numbers.json";
import idtimeZoneData from "@syncfusion/ej2-cldr-data/main/id/timeZoneNames.json";
import idNumberingSystem from "@syncfusion/ej2-cldr-data/supplemental/numberingSystems.json";
import {
  Day,
  Inject,
  ScheduleComponent,
  Week,
} from "@syncfusion/ej2-react-schedule";
import { Empty, Skeleton } from "antd";
import dayjs from "dayjs";
import id from "dayjs/locale/id";
import type { ReactNode } from "react";

import { type ScheduleData, useSisvaSchedulesComponent } from "./hooks";

loadCldr(idNumberData, idtimeZoneData, idGregorian, idNumberingSystem);
L10n.load(syncfusionLocaleID);

/**
 * pass either student_group_id or user_id
 */
export function SisvaScheduleComponent({
  user_id,
  student_group_id,
  period_id, // if passed, this component will use this period_id instead of 'selectedPeriod.id' from useSelectedPeriod
  currentView = "Week",
  timeScale = { enable: true, interval: 60, slotCount: 5 },
  selectedDate,
  overwriteStartHour,
  overwriteEndHour,
  renderCell,
}: {
  user_id?: string;
  student_group_id?: number;
  period_id?: number;
  currentView?: "Week" | "Day";
  overwriteStartHour?: string;
  overwriteEndHour?: string;
  timeScale?: {
    enable: true;
    interval: number;
    slotCount: number;
  };
  selectedDate?: DayType;
  renderCell?: ({
    scheduleData,
    defaultRender,
  }: {
    scheduleData: ScheduleData;
    defaultRender: ReactNode;
  }) => ReactNode;
}) {
  const { data: user } = useUser(user_id);
  const { schedulesData, endHour, startHour, isLoading } =
    useSisvaSchedulesComponent({
      currentView,
      user_id,
      student_group_id,
      period_id,
    });

  if (isLoading) return <Skeleton active />;

  if (schedulesData.length === 0) {
    return (
      <div className="size-full flex flex-col justify-center">
        <Empty />
      </div>
    );
  }

  return (
    <ScheduleComponent
      key={JSON.stringify(schedulesData)}
      currentView={currentView}
      selectedDate={
        selectedDate ? dayjs().isoWeekday(selectedDate).toDate() : undefined
      }
      eventSettings={{
        allowAdding: false,
        allowDeleting: false,
        allowEditing: false,
        dataSource: schedulesData,
        fields: {
          subject: {
            name: "name",
          },
          startTime: {
            name: "start_time_date_obj",
          },
          endTime: {
            name: "end_time_date_obj",
          },
          id: "unique_id",
        },
        template: (data: (typeof schedulesData)[number]) => {
          const defaultRender = (
            <div
              className={cn(
                "text-black font-kumbh text-wrap p-2 size-full rounded-lg flex flex-col",
                {
                  "justify-center": !data.class_id,
                }
              )}
              style={{
                backgroundColor: data.class_id ? "#ACDEE7" : "#FFDBCB",
              }}
            >
              <div
                className={cn("text-base font-medium", {
                  "text-center": !data.class_id && currentView === "Week",
                })}
              >
                {data.name}
              </div>
              <div className="text-sm font-light">
                {user?.type === "teacher"
                  ? data.class?.student_group_name
                  : data.teacher_name}
              </div>
            </div>
          );
          return (
            renderCell?.({
              scheduleData: data,
              defaultRender,
            }) ?? defaultRender
          );
        },
      }}
      locale="id"
      showHeaderBar={false}
      showQuickInfo={false}
      firstDayOfWeek={1}
      startHour={overwriteStartHour ?? startHour}
      endHour={overwriteEndHour ?? endHour}
      timeScale={timeScale}
      dateHeaderTemplate={({ date }: { date: Date }) => {
        return (
          <div className="flex font-bold justify-center text-base">
            {dayjs(date).locale(id).format("dddd")}
          </div>
        );
      }}
      eventRendered={({
        data,
        element,
      }: {
        data: (typeof schedulesData)[number];
        element: HTMLElement;
      }) => {
        element.style.width = `${100 * data.consecutive_count}%`;
        element.style.backgroundColor = "transparent";
      }}
      headerIndentTemplate={(props: { className: string[] }) => {
        return props.className[0] == "e-header-cells" ? (
          <div className="flex justify-center font-kumbh text-base">Jam</div>
        ) : null;
      }}
      allowSwiping={false}
    >
      <Inject services={[Week, Day]} />
    </ScheduleComponent>
  );
}
