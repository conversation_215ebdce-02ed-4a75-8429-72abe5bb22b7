import clsx from "clsx";
import React from "react";

interface ImageProps extends React.ImgHTMLAttributes<HTMLImageElement> {
  src: string;
  alt: string;
  fill?: boolean;
  className?: string;
  loading?: "lazy" | "eager";
}

export const Image: React.FC<ImageProps> = ({
  src,
  alt,
  fill,
  className,
  loading = "lazy",
  ...props
}) => {
  return (
    <img
      src={src}
      alt={alt}
      className={clsx(
        fill && "absolute inset-0 w-full h-full object-cover",
        className
      )}
      loading={loading}
      {...props}
    />
  );
};
