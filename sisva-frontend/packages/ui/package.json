{"name": "@sisva/ui", "version": "0.0.0", "private": true, "scripts": {"lint": "eslint ./src", "lint:fix": "eslint ./src --fix", "typecheck": "tsc --noEmit", "build": "tsup", "dev": "tsup --watch"}, "exports": {".": "./dist/index.js", "./css/*": "./src/css/*"}, "devDependencies": {"@sisva/eslint-config": "workspace:*", "@sisva/typescript-config": "workspace:*", "@types/node": "^22.13.0", "@types/react": "19.0.8", "@types/react-dom": "19.0.3", "eslint": "^9.21.0", "tsup": "8.3.0", "typescript": "5.7.3"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^6.4.6", "@mui/material": "^6.4.6", "@sisva/api": "workspace:*", "@sisva/hooks": "workspace:*", "@sisva/providers": "workspace:*", "@sisva/types": "workspace:*", "@sisva/utils": "workspace:*", "@syncfusion/ej2-base": "^26.0.0", "@syncfusion/ej2-buttons": "^26.0.0", "@syncfusion/ej2-calendars": "^26.0.0", "@syncfusion/ej2-cldr-data": "^26.0.0", "@syncfusion/ej2-dropdowns": "^26.0.0", "@syncfusion/ej2-inputs": "^26.0.0", "@syncfusion/ej2-lists": "^26.0.0", "@syncfusion/ej2-navigations": "^26.0.0", "@syncfusion/ej2-popups": "^26.0.0", "@syncfusion/ej2-react-schedule": "^26.0.0", "@syncfusion/ej2-splitbuttons": "^26.0.0", "antd": "^5.24.2", "clsx": "^2.1.1", "dayjs": "^1.11.13", "fast-sort": "^3.4.1", "react": "^19.0.0", "react-dom": "^19.0.0", "ts-pattern": "^5.6.2"}}