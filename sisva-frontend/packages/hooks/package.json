{"name": "@sisva/hooks", "version": "0.0.0", "private": true, "scripts": {"lint": "eslint ./src", "lint:fix": "eslint ./src --fix", "typecheck": "tsc --noEmit", "build": "tsup", "dev": "tsup --watch"}, "exports": {"./query/academic/useAnnouncements": "./dist/query/academic/useAnnouncements.js", "./query/academic/useClassSchedules": "./dist/query/academic/useClassSchedules.js", "./query/academic/useClasses": "./dist/query/academic/useClasses.js", "./query/academic/useCredits": "./dist/query/academic/useCredits.js", "./query/academic/useCurriculums": "./dist/query/academic/useCurriculums.js", "./query/academic/useEntities": "./dist/query/academic/useEntities.js", "./query/academic/useExtracurricularMembers": "./dist/query/academic/useExtracurricularMembers.js", "./query/academic/useExtracurriculars": "./dist/query/academic/useExtracurriculars.js", "./query/academic/useNonLearningSchedules": "./dist/query/academic/useNonLearningSchedules.js", "./query/academic/usePeriods": "./dist/query/academic/usePeriods.js", "./query/academic/useSchoolSchedules": "./dist/query/academic/useSchoolSchedules.js", "./query/academic/useStudentGroups": "./dist/query/academic/useStudentGroups.js", "./query/academic/useStudyPrograms": "./dist/query/academic/useStudyPrograms.js", "./query/academic/useSubjectTeachers": "./dist/query/academic/useSubjectTeachers.js", "./query/academic/useSubjects": "./dist/query/academic/useSubjects.js", "./query/academic/useSyllabuses": "./dist/query/academic/useSyllabuses.js", "./query/attendance/useAttendance": "./dist/query/attendance/useAttendance.js", "./query/classroom/useAi": "./dist/query/classroom/useAi.js", "./query/classroom/useScores": "./dist/query/classroom/useScores.js", "./query/classroom/useSubmissions": "./dist/query/classroom/useSubmissions.js", "./query/classroom/useTasks": "./dist/query/classroom/useTasks.js", "./query/classroom/useTeachingMaterials": "./dist/query/classroom/useTeachingMaterials.js", "./query/classroom/useTeachingPlans": "./dist/query/classroom/useTeachingPlans.js", "./query/exam/useAi": "./dist/query/exam/useAi.js", "./query/exam/useExamQuestions": "./dist/query/exam/useExamQuestions.js", "./query/exam/useExamScores": "./dist/query/exam/useExamScores.js", "./query/exam/useExamSessions": "./dist/query/exam/useExamSessions.js", "./query/exam/useExamSubmissions": "./dist/query/exam/useExamSubmissions.js", "./query/exam/useExams": "./dist/query/exam/useExams.js", "./query/file/useFiles": "./dist/query/file/useFiles.js", "./query/finance/useBills": "./dist/query/finance/useBills.js", "./query/finance/useInvoices": "./dist/query/finance/useInvoices.js", "./query/finance/useUserBills": "./dist/query/finance/useUserBills.js", "./query/report/useTemplates": "./dist/query/report/useTemplates.js", "./query/report/useReports": "./dist/query/report/useReports.js", "./query/report/useReportScores": "./dist/query/report/useReportScores.js", "./query/user/useGuardians": "./dist/query/user/useGuardians.js", "./query/user/useStaffTeachers": "./dist/query/user/useStaffTeachers.js", "./query/user/useStudents": "./dist/query/user/useStudents.js", "./query/user/useTeachers": "./dist/query/user/useTeachers.js", "./query/user/useUsers": "./dist/query/user/useUsers.js", "./query/useAuth": "./dist/query/useAuth.js", "./query/useSchools": "./dist/query/useSchools.js", "./utils": "./dist/utils/index.js"}, "devDependencies": {"@sisva/eslint-config": "workspace:*", "@sisva/typescript-config": "workspace:*", "@types/js-cookie": "^3.0.6", "@types/node": "^22.13.0", "@types/react": "19.0.8", "@types/react-dom": "19.0.3", "eslint": "^9.21.0", "tsup": "8.3.0", "typescript": "5.7.3"}, "dependencies": {"@sisva/api": "workspace:*", "@sisva/providers": "workspace:*", "@sisva/types": "workspace:*", "@sisva/utils": "workspace:*", "@tanstack/react-query": "^5.66.11", "ahooks": "^3.8.4", "fast-sort": "^3.4.1", "js-cookie": "^3.0.5", "react": "^19.0.0", "react-dom": "^19.0.0", "tldts": "^7.0.7", "yup": "^1.6.1"}}