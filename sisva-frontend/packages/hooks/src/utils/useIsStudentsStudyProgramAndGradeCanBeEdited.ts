import { useStudentInStudentGroups } from "../query/academic/useStudentGroups";

export function useIsStudentsStudyProgramAndGradeCanBeEdited(
  student_id: string
) {
  const { data: studentInStudentGroups = [] } = useStudentInStudentGroups();

  // * if student has any student group, then this student's study_program_id and grade can't be edited
  return !studentInStudentGroups.map((x) => x.student_id).includes(student_id);
}
