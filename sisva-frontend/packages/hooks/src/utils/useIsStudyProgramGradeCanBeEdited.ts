import { usePeriodCurriculums } from "../query/academic/usePeriods";
import { useSubjects } from "../query/academic/useSubjects";
import { useStudents } from "../query/user/useStudents";

export function useIsStudyProgramGradeCanBeEdited(study_program_id: number) {
  const { data: students = [] } = useStudents();
  const { data: periodCurriculums = [] } = usePeriodCurriculums();
  const { data: subjects = [] } = useSubjects();

  // * if any of the students, periodCurriculums, or subjects has the studyProgram.id, then it can't be edited
  return !(
    students
      .map((student) => student.detail.study_program_id)
      .includes(study_program_id) ||
    periodCurriculums
      .map((periodCurriculum) => periodCurriculum.study_program_id)
      .includes(study_program_id) ||
    subjects
      .map((subject) => subject.study_program_id)
      .includes(study_program_id)
  );
}
