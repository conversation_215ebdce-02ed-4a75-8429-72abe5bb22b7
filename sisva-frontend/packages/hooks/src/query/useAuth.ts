import Auth<PERSON><PERSON> from "@sisva/api/auth";
import <PERSON>AP<PERSON> from "@sisva/api/users";
import { COOKIES_KEY } from "@sisva/api/utils";
import type { Session, User } from "@sisva/types/apiTypes";
import { deleteSessionDataFromCookies } from "@sisva/utils";
import type { QueryClient } from "@tanstack/react-query";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import Cookies from "js-cookie";
import { parse } from "tldts";

export function useWriteSessionDataToCookies({
  onSuccess,
  onError,
}: {
  onSuccess?: ({ user }: { user: User }) => void;
  onError?: (error: Error) => void;
} = {}) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({
      username,
      password,
      school_id,
    }: {
      username: string;
      password: string;
      school_id: string;
    }) => {
      const session = (await AuthAPI.login({ username, password, school_id }))
        .data.data as Session;
      const expirationTime = new Date(Date.now() + 24 * 60 * 60 * 1000);

      const parseHost = parse(window.location.host);
      const domain = parseHost.domain;
      const options = {
        expires: expirationTime,
        path: "/",
        domain: domain ? "." + domain : undefined,
      };

      Cookies.set(COOKIES_KEY.token, session.token, options);
      Cookies.set(COOKIES_KEY.user_id, session.user_id, options);
      Cookies.set(COOKIES_KEY.school_id, session.school_id, options);
      Cookies.set(COOKIES_KEY.username, session.username, options);

      const user = (await UsersAPI.getCurrentUser()).data.data;
      return user;
    },
    async onSuccess(data) {
      invalidateCurrentUser(queryClient);
      invalidateCurrentSchool(queryClient);
      onSuccess?.({ user: data });
    },
    onError(error) {
      onError?.(error);
    },
  });
}

export function invalidateCurrentUser(queryClient: QueryClient) {
  queryClient.invalidateQueries({ queryKey: ["users", "current-user"] });
}

export function invalidateCurrentSchool(queryClient: QueryClient) {
  queryClient.invalidateQueries({ queryKey: ["school", "current-school"] });
}

export function useDeleteSessionDataFromCookies({
  onSuccess,
  onError,
}: {
  onSuccess?: () => void;
  onError?: () => void;
} = {}) {
  return useMutation({
    mutationFn: async () => {
      deleteSessionDataFromCookies();
    },
    onSuccess,
    onError,
  });
}
