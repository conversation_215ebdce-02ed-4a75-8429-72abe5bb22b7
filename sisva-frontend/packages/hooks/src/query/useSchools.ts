import CMSAPI from "@sisva/api/cms";
import { useSchool } from "@sisva/providers";
import type { School } from "@sisva/types/apiTypes";
import type { SchoolSchemaForm } from "@sisva/types/formTypes";
import { queryOptions, useQuery } from "@tanstack/react-query";
import { useMutation } from "@tanstack/react-query";
import { useLocalStorageState } from "ahooks";

export function useSchoolByCode({ school_code }: { school_code: string }) {
  return useQuery<School>({
    queryKey: ["school", { school_code }],
    queryFn: async () => (await CMSAPI.getSchoolByCode(school_code)).data.data,
  });
}

export function useWriteSchoolToLocalStorage({
  onSuccess,
  onError,
}: {
  onSuccess?: (school: School) => void;
  onError?: () => void;
} = {}) {
  const [, setSchool] = useLocalStorageState("schoolData");

  return useMutation({
    mutationFn: async (school_code: string) => {
      const school = (await CMSAPI.getSchoolByCode(school_code)).data
        .data as School;
      setSchool(school);
      return school;
    },
    onSuccess: (school) => {
      onSuccess?.(school);
    },
    onError: () => {
      onError?.();
    },
  });
}

export function useUpdateSchool({
  onSuccess,
  onError,
}: {
  onSuccess?: () => void;
  onError?: () => void;
} = {}) {
  const school = useSchool();
  return useMutation({
    mutationFn: async ({
      landing_image_uri,
      logo_uri,
      name,
      theme_json_text,
      education_type,
      education_ownership_type,
      education_level,
      abbreviation,
      email,
      identifier_type,
      address,
      identifier_value,
      phone,
    }: SchoolSchemaForm) => {
      const oldJSONText = (() => {
        try {
          return JSON.parse(school.additional_json_text);
        } catch (error) {
          return {};
        }
      })();
      const payload = {
        landing_image_uri,
        logo_uri,
        name,
        theme_json_text,
        education_type,
        education_ownership_type,
        education_level,
        abbreviation,
        identifier_type,
        identifier_value,
        additional_json_text: JSON.stringify({
          ...oldJSONText,
          email,
          address,
          phone,
        }),
      };
      await CMSAPI.editSchoolById(school.id, payload);
    },
    onSuccess() {
      onSuccess?.();
    },
    onError() {
      onError?.();
    },
  });
}

export const currentSchoolQueryOptions = queryOptions({
  queryKey: ["school", "current-school"],
  async queryFn() {
    return (await CMSAPI.getCurrentSchool()).data.data;
  },
});
