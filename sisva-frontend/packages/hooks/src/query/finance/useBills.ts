import FinanceAPI from "@sisva/api/finance";
import type { Bill } from "@sisva/types/apiTypes";
import type { BillSchemaForm } from "@sisva/types/formTypes";
import type { QueryClient } from "@tanstack/react-query";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

import { useInvoice, useInvoicesWithBillAndUser } from "./useInvoices";
import { useUserBill } from "./useUserBills";

export function useBills<T = Bill[]>({
  select,
  enabled,
}: {
  select?: (bills: Bill[]) => T;
  enabled?: boolean;
} = {}) {
  return useQuery<Bill[], unknown, T>({
    queryKey: ["bills"],
    queryFn: async () => (await FinanceAPI.getAllBills()).data.data,
    select,
    enabled,
  });
}

export function invalidateBills(queryClient: QueryClient) {
  queryClient.invalidateQueries({
    queryKey: ["bills"],
  });
}

export const useInvoicesBill = (invoice_id: number) => {
  const { data: invoice } = useInvoice(invoice_id);
  const { data: userBill } = useUserBill(invoice?.user_bill_id);

  return useBill(userBill?.bill_id);
};

export const useBill = (bill_id: number | null | undefined) => {
  return useQuery<Bill>({
    queryKey: ["bills", bill_id],
    queryFn: async () => (await FinanceAPI.getBillById(bill_id)).data.data,
    enabled: !!bill_id,
  });
};

export function useBillsWithInvoices() {
  const { data: invoices = [], isLoading: L1 } = useInvoicesWithBillAndUser();
  return useBills({
    select(bills) {
      return bills.map((bill) => {
        const filteredInvoices = invoices.filter(
          (invoice) => invoice.bill?.id === bill.id
        );
        return {
          ...bill,
          invoices: filteredInvoices,
        };
      });
    },
    enabled: !L1,
  });
}

export type BillWithInvoices = NonNullable<
  ReturnType<typeof useBillsWithInvoices>["data"]
>[number];

export function useBillWithInvoices(bill_id: number | undefined) {
  const { data: invoices = [], isLoading: L1 } = useInvoicesWithBillAndUser();
  return useBills({
    select(bills) {
      return bills
        .map((bill) => {
          const filteredInvoices = invoices.filter(
            (invoice) => invoice.bill?.id === bill.id
          );
          return {
            ...bill,
            invoices: filteredInvoices,
          };
        })
        .find((bill) => bill.id === bill_id);
    },
    enabled: !L1 && !!bill_id,
  });
}

export function useCreateBill({
  onSuccess,
  onError,
}: {
  onSuccess?: () => void;
  onError?: () => void;
} = {}) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({
      deadline,
      target_user_types,
      name,
      amount,
      status,
      description,
      custom_id,
    }: BillSchemaForm) => {
      await FinanceAPI.createBill({
        custom_id,
        name,
        status,
        target_user_types,
        amount,
        deadline,
        description,
      });
    },
    onSuccess() {
      invalidateBills(queryClient);
      onSuccess?.();
    },
    onError() {
      onError?.();
    },
  });
}

export function useUpdateBill({
  onSuccess,
  onError,
}: {
  onSuccess?: () => void;
  onError?: () => void;
} = {}) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({
      bill_id,
      deadline,
      name,
      amount,
      status,
      description,
      custom_id,
    }: BillSchemaForm & {
      bill_id: number;
    }) => {
      await FinanceAPI.updateBill(bill_id, {
        custom_id,
        name,
        status,
        amount,
        deadline,
        description,
      });
    },
    onSuccess() {
      invalidateBills(queryClient);
      onSuccess?.();
    },
    onError() {
      onError?.();
    },
  });
}

export function useDeleteBill({
  onSuccess,
  onError,
}: {
  onSuccess?: () => void;
  onError?: () => void;
} = {}) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (bill_id: number) => {
      await FinanceAPI.deleteBill(bill_id);
    },
    onSuccess() {
      invalidateBills(queryClient);
      onSuccess?.();
    },
    onError() {
      onError?.();
    },
  });
}
