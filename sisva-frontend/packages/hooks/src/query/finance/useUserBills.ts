import FinanceAPI from "@sisva/api/finance";
import type { UserBill } from "@sisva/types/apiTypes";
import type { QueryClient } from "@tanstack/react-query";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { useMutation } from "@tanstack/react-query";

import { useUsersWithBillsAndStudentGroupAndStudyProgram } from "../user/useUsers";
import { useBillsWithInvoices, useBillWithInvoices } from "./useBills";

export const useUserBills = <T = UserBill[]>({
  select,
  enabled,
}: {
  select?: (userBills: UserBill[]) => T;
  enabled?: boolean;
} = {}) => {
  return useQuery<UserBill[], unknown, T>({
    queryKey: ["user-bills"],
    queryFn: async () => (await FinanceAPI.getAllUserBill()).data.data,
    select,
    enabled,
  });
};

export const useDeleteUserBill = ({
  onSuccess,
  onError,
}: {
  onSuccess?: () => void;
  onError?: () => void;
} = {}) => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({ id }: { id: number | string }) => {
      await FinanceAPI.deleteUserBill(id);
    },
    onSuccess() {
      invalidateUserBills(queryClient);
      onSuccess?.();
    },
    onError() {
      onError?.();
    },
  });
};

export const invalidateUserBills = (queryClient: QueryClient) => {
  queryClient.invalidateQueries({ queryKey: ["user-bills"] });
};

export const useUsersUserBills = (user_id: string) => {
  return useUserBills({
    select(userBills) {
      return userBills.filter((userBill) => userBill.user_id === user_id);
    },
  });
};

export const useUserBill = (user_bill_id: number | null | undefined) => {
  return useUserBills({
    select(userBills) {
      return userBills.find((userBill) => userBill.id === user_bill_id);
    },
  });
};

export function useUserBillsWithUserAndBill() {
  const { data: users = [], isLoading: L1 } =
    useUsersWithBillsAndStudentGroupAndStudyProgram();
  const { data: bills = [], isLoading: L2 } = useBillsWithInvoices();

  return useUserBills({
    select(userBills) {
      return userBills.map((userBill) => {
        const user = users.find((user) => user.id === userBill.user_id);
        const bill = bills.find((bill) => bill.id === userBill.bill_id);
        return {
          ...userBill,
          user,
          bill,
        };
      });
    },
    enabled: !(L1 || L2),
  });
}

export type UserBillWithUserAndBill = NonNullable<
  ReturnType<typeof useUserBillsWithUserAndBill>["data"]
>[number];

export function useUserBillWithUserAndBill(user_bill_id: number | undefined) {
  const { data: users = [], isLoading: L1 } =
    useUsersWithBillsAndStudentGroupAndStudyProgram();
  const { data: bills = [], isLoading: L2 } = useBillsWithInvoices();

  return useUserBills({
    select(userBills) {
      return userBills
        .map((userBill) => {
          const user = users.find((user) => user.id === userBill.user_id);
          const bill = bills.find((bill) => bill.id === userBill.bill_id);
          return {
            ...userBill,
            user,
            bill,
          };
        })
        .find((userBill) => userBill.id === user_bill_id);
    },
    enabled: !(L1 || L2) && !!user_bill_id,
  });
}

export function useCreateUserBills({
  onSuccess,
  onError,
  bill_id,
}: {
  onSuccess?: (data: {
    failCount: number;
    userBillCreatedCount: number;
    invoiceCreatedCount: number;
  }) => void;
  onError?: () => void;
  bill_id?: number;
} = {}) {
  const queryClient = useQueryClient();
  const { data: bill } = useBillWithInvoices(bill_id);
  const { data: userBills = [] } = useUserBillsWithUserAndBill();

  return useMutation({
    mutationFn: async ({
      user_options,
    }: {
      user_options: {
        user_id: string;
        create_invoice: boolean;
      }[];
    }) => {
      let failCount = 0;
      let userBillCreatedCount = 0;
      let invoiceCreatedCount = 0;
      for (const user_option of user_options) {
        try {
          const user_bill_id = await (async () => {
            const userBill = userBills.find(
              (userBill) =>
                userBill.user_id === user_option.user_id &&
                userBill.bill_id === bill_id
            );
            if (userBill?.id) return userBill.id;
            const user_bill_id = (
              await FinanceAPI.createUserBill({
                bill_id,
                user_id: user_option.user_id,
              })
            ).data.data;
            userBillCreatedCount += 1;
            return user_bill_id;
          })();

          if (user_option.create_invoice) {
            const userBill = userBills.find(
              (userBill) => userBill.id === user_bill_id
            );
            if (!userBill?.bill?.invoices.length)
              await FinanceAPI.createInvoice({
                user_bill_id,
                status: "pending",
                amount: bill?.amount,
                note: "",
              });
            invoiceCreatedCount += 1;
          }
        } catch (error) {
          failCount += 1;
        }
      }
      return {
        failCount,
        userBillCreatedCount,
        invoiceCreatedCount,
      };
    },
    onSuccess(data) {
      invalidateUserBills(queryClient);
      onSuccess?.(data);
    },
    onError() {
      onError?.();
    },
  });
}
