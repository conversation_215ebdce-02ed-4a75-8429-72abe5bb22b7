import FinanceAPI from "@sisva/api/finance";
import type { Invoice } from "@sisva/types/apiTypes";
import type { InvoiceSchemaForm } from "@sisva/types/formTypes";
import type { QueryClient } from "@tanstack/react-query";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

import { useUsers } from "../user/useUsers";
import { useBills } from "./useBills";
import { useUserBills, useUsersUserBills } from "./useUserBills";

export const useInvoices = <T = Invoice[]>(
  {
    select,
    enabled,
  }: {
    select?: (invoices: Invoice[]) => T;
    enabled?: boolean;
  } = {
    enabled: true,
  }
) => {
  return useQuery<Invoice[], unknown, T>({
    queryKey: ["invoices"],
    queryFn: async () => (await FinanceAPI.getAllInvoices({})).data.data,
    select,
    enabled,
  });
};

export function invalidateInvoices(queryClient: QueryClient) {
  queryClient.invalidateQueries({
    queryKey: ["invoices"],
  });
}

export const useInvoice = (invoice_id: number) => {
  return useQuery<Invoice>({
    queryKey: ["invoices", invoice_id],
    queryFn: async () =>
      (await FinanceAPI.getInvoiceById(invoice_id)).data.data,
    enabled: !!invoice_id,
  });
};

export const useUsersInvoices = (user_id: string) => {
  const { data: userBills = [], isLoading } = useUsersUserBills(user_id);
  return useInvoices({
    select(invoices) {
      return invoices.filter((invoice) =>
        userBills.some((userBill) => userBill.id === invoice.user_bill_id)
      );
    },
    enabled: !isLoading,
  });
};

export const useUsersInvoicesWithBill = (user_id: string) => {
  const { data: userBills = [], isLoading: L1 } = useUsersUserBills(user_id);
  const { data: bills = [], isLoading: L2 } = useBills();

  return useInvoices({
    select(invoices) {
      const filteredInvoices = invoices.filter((invoice) =>
        userBills.some((userBill) => userBill.id === invoice.user_bill_id)
      );
      return filteredInvoices.map((invoice) => {
        const userBill = userBills.find(
          (userBill) => userBill.id === invoice.user_bill_id
        );
        const bill = bills.find((bill) => bill.id === userBill?.bill_id);
        return {
          ...invoice,
          bill,
        };
      });
    },
    enabled: !L1 || L2,
  });
};

export const useInvoicesWithBillAndUser = () => {
  const { data: userBills = [], isLoading: L1 } = useUserBills();
  const { data: bills = [], isLoading: L2 } = useBills();
  const { data: users = [], isLoading: L3 } = useUsers();

  return useInvoices({
    select(invoices) {
      return invoices.map((invoice) => {
        const userBill = userBills.find(
          (userBill) => userBill.id === invoice.user_bill_id
        );
        const bill = bills.find((bill) => bill.id === userBill?.bill_id);
        const user = users.find((user) => user.id === userBill?.user_id);
        return {
          ...invoice,
          bill,
          user,
        };
      });
    },
    enabled: !(L1 || L2 || L3),
  });
};

export const useInvoiceWithBillAndUser = (invoice_id: number | undefined) => {
  const { data: userBills = [], isLoading: L1 } = useUserBills();
  const { data: bills = [], isLoading: L2 } = useBills();
  const { data: users = [], isLoading: L3 } = useUsers();

  return useInvoices({
    select(invoices) {
      return invoices
        .map((invoice) => {
          const userBill = userBills.find(
            (userBill) => userBill.id === invoice.user_bill_id
          );
          const bill = bills.find((bill) => bill.id === userBill?.bill_id);
          const user = users.find((user) => user.id === userBill?.user_id);
          return {
            ...invoice,
            bill,
            user,
          };
        })
        .find((invoice) => invoice.id === invoice_id);
    },
    enabled: !(L1 || L2 || L3) && !!invoice_id,
  });
};

export type InvoiceWithBillAndUser = NonNullable<
  ReturnType<typeof useInvoicesWithBillAndUser>["data"]
>[number];

export function useCreateInvoice({
  onSuccess,
  onError,
}: {
  onSuccess?: () => void;
  onError?: () => void;
} = {}) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({ user_bill_id, amount, note }: InvoiceSchemaForm) => {
      await FinanceAPI.createInvoice({
        user_bill_id,
        status: "pending",
        amount,
        note,
      });
    },
    onSuccess() {
      invalidateInvoices(queryClient);
      onSuccess?.();
    },
    onError() {
      onError?.();
    },
  });
}

export function useUpdateInvoice({
  onSuccess,
  onError,
}: {
  onSuccess?: () => void;
  onError?: () => void;
} = {}) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({
      user_bill_id,
      amount,
      note,
      status,
      payment_proof_uri,
      payment_proof_note,
      invoice_id,
    }: InvoiceSchemaForm & {
      invoice_id: number;
    }) => {
      if (payment_proof_uri || payment_proof_note) {
        await FinanceAPI.updatePaymentProof(invoice_id, {
          uri: payment_proof_uri,
          note: payment_proof_note,
        });
      }

      await FinanceAPI.updateInvoice(invoice_id, {
        user_bill_id,
        status,
        amount,
        note,
      });
    },
    onSuccess() {
      invalidateInvoices(queryClient);
      onSuccess?.();
    },
    onError() {
      onError?.();
    },
  });
}

export const useDeleteInvoice = ({
  onSuccess,
  onError,
}: {
  onSuccess?: () => void;
  onError?: () => void;
} = {}) => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (invoice_id: number) => {
      await FinanceAPI.deleteInvoice(invoice_id);
    },
    onSuccess() {
      invalidateInvoices(queryClient);
      onSuccess?.();
    },
    onError() {
      onError?.();
    },
  });
};
