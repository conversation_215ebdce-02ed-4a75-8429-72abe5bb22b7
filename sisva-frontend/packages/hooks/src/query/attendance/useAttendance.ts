import AttendanceAPI from "@sisva/api/attendance";
import type {
  StaffAttendance,
  StudentAttendance,
  StudentClassAttendance,
} from "@sisva/types/apiTypes";
import type { Attendance } from "@sisva/types/types";
import type { QueryClient, UseQueryOptions } from "@tanstack/react-query";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useQueries } from "@tanstack/react-query";

export function useStudentAttendances<T = StudentAttendance[]>({
  date_id,
  select,
  enabled,
}: {
  date_id: number | string;
  select?: (studentAttendances: StudentAttendance[]) => T;
  enabled?: boolean;
}) {
  return useQuery<StudentAttendance[], unknown, T>({
    queryKey: ["student-attendances", { date_id }],
    queryFn: async () =>
      (await AttendanceAPI.getStudentAttendanceByDateId(date_id)).data.data,
    select,
    enabled,
  });
}

export function useStaffAttendances<T = StaffAttendance[]>({
  date_id,
  select,
  enabled,
}: {
  date_id: number | string;
  select?: (staffAttendance: StaffAttendance[]) => T;
  enabled?: boolean;
}) {
  return useQuery<StaffAttendance[], unknown, T>({
    queryKey: ["staff-attendances", { date_id }],
    queryFn: async () =>
      (await AttendanceAPI.getStaffAttendanceByDateId(date_id)).data.data,
    select,
    enabled,
  });
}

export function useStudentAttendance({
  date_id,
  student_id,
}: {
  date_id: number | string;
  student_id: string | undefined;
}) {
  return useStudentAttendances({
    date_id,
    select(studentAttendances) {
      return studentAttendances.find(
        (attendance) => attendance.student_id === student_id
      );
    },
    enabled: !!student_id,
  });
}

export function useStaffAttendance({
  date_id,
  staff_id,
}: {
  date_id: number | string;
  staff_id: string | undefined;
}) {
  return useStaffAttendances({
    date_id,
    select(staffAttendances) {
      return staffAttendances.find(
        (attendance) => attendance.staff_id === staff_id
      );
    },
    enabled: !!staff_id,
  });
}

export function useUpdateStudentAttendance({
  onSuccess,
  onError,
}: {
  onSuccess?: () => void;
  onError?: () => void;
} = {}) {
  const queryClient = useQueryClient();
  return useMutation({
    async mutationFn({
      student_id,
      date_id,
      status,
    }: {
      student_id: string;
      date_id: number;
      status: Attendance;
    }) {
      const payload = {
        date_id,
        status,
      };
      return (await AttendanceAPI.createStudentAttendance(student_id, payload))
        .data.data;
    },
    onSuccess() {
      onSuccess?.();
      invalidateStudentAttendances(queryClient);
    },
    onError() {
      onError?.();
    },
  });
}

export function useUpdateStudentClassAttendance({
  onSuccess,
  onError,
}: {
  onSuccess?: () => void;
  onError?: () => void;
} = {}) {
  const queryClient = useQueryClient();
  return useMutation({
    async mutationFn({
      student_id,
      date_id,
      status,
      class_id,
    }: {
      student_id: string;
      date_id: number;
      status: Attendance;
      class_id: number;
    }) {
      return (
        await AttendanceAPI.updateStudentClassAttendance({
          class_id,
          date_id,
          status,
          student_id,
        })
      ).data.data;
    },
    onSuccess() {
      onSuccess?.();
      invalidateStudentClassAttendances(queryClient);
    },
    onError() {
      onError?.();
    },
  });
}

export function useUpdateStaffAttendance({
  onSuccess,
  onError,
}: {
  onSuccess?: () => void;
  onError?: () => void;
} = {}) {
  const queryClient = useQueryClient();
  return useMutation({
    async mutationFn({
      staff_id,
      date_id,
      status,
    }: {
      staff_id: string;
      date_id: number;
      status: Attendance;
    }) {
      const payload = {
        date_id,
        status,
      };
      return (await AttendanceAPI.createStaffAttendance(staff_id, payload)).data
        .data;
    },
    onSuccess() {
      onSuccess?.();
      invalidateStaffAttendances(queryClient);
    },
    onError() {
      onError?.();
    },
  });
}

export function useUpdateStudentAttendanceMultiple({
  onSuccess,
  onError,
}: {
  onSuccess?: ({ count }: { count: number }) => void;
  onError?: () => void;
} = {}) {
  const queryClient = useQueryClient();
  return useMutation({
    async mutationFn(
      attendances: {
        student_id: string;
        date_id: number;
        status: Attendance;
      }[]
    ) {
      let count = 0;
      for (const attendance of attendances) {
        await AttendanceAPI.createStudentAttendance(attendance.student_id, {
          date_id: attendance.date_id,
          status: attendance.status,
        });
        count += 1;
      }
      return { count };
    },
    onSuccess({ count }) {
      onSuccess?.({ count });
      invalidateStudentAttendances(queryClient);
    },
    onError() {
      onError?.();
    },
  });
}

export function invalidateStudentAttendances(queryClient: QueryClient) {
  queryClient.invalidateQueries({
    queryKey: ["student-attendances"],
  });
}

export function invalidateStudentClassAttendances(queryClient: QueryClient) {
  queryClient.invalidateQueries({
    queryKey: ["student-class-attendances"],
  });
}

export function invalidateStaffAttendances(queryClient: QueryClient) {
  queryClient.invalidateQueries({
    queryKey: ["staff-attendances"],
  });
}

export const useStudentAttendancesMultiple = ({
  date_ids,
  select,
}: {
  date_ids: number[] | string[];
  select?: (studentAttendances: StudentAttendance[]) => StudentAttendance[];
}) => {
  return useQueries({
    queries: date_ids.map<UseQueryOptions<StudentAttendance[]>>(
      (date_id: string | number) => ({
        queryKey: ["student-attendances", { date_id }],
        queryFn: async () =>
          (await AttendanceAPI.getStudentAttendanceByDateId(date_id)).data.data,
        select,
      })
    ),
    combine: (results) => {
      return {
        data: results.map((result) => result.data ?? []),
        isLoading: results.some((result) => result.isLoading),
        isError: results.some((result) => result.isError),
      };
    },
  });
};

export const useStudentsStudentAttendancesMultiple = ({
  date_codes,
  student_id,
}: {
  date_codes: number[] | string[];
  student_id: string;
}) => {
  return useStudentAttendancesMultiple({
    date_ids: date_codes,
    select(studentAttendances) {
      return studentAttendances.filter(
        (studentAttendance) => studentAttendance.student_id === student_id
      );
    },
  });
};

export function useStudentClassAttendances<T = StudentClassAttendance[]>({
  date_id,
  select,
  enabled,
}: {
  date_id: number | string;
  select?: (studentClassAttendances: StudentClassAttendance[]) => T;
  enabled?: boolean;
}) {
  return useQuery<StudentClassAttendance[], unknown, T>({
    queryKey: ["student-class-attendances", { date_id }],
    queryFn: async () =>
      (await AttendanceAPI.getStudentClassAttendanceByDateId(date_id)).data
        .data,
    select,
    enabled,
  });
}

export const useStudentClassAttendancesByClassId = ({
  date_id,
  class_id,
}: {
  date_id: number | string;
  class_id: number | undefined | null;
}) => {
  return useStudentClassAttendances({
    date_id,
    select(studentClassAttendances) {
      return studentClassAttendances.filter(
        (item) => item.class_id === class_id
      );
    },
    enabled: !!class_id && !!date_id,
  });
};

export function useStudentClassAttendance({
  date_id,
  student_id,
  class_id,
}: {
  date_id: number | string;
  student_id: string | undefined;
  class_id: number;
}) {
  return useStudentClassAttendances({
    date_id,
    select(studentClassAttendances) {
      return studentClassAttendances.find(
        (item) =>
          item.date_id === Number(date_id) &&
          item.student_id === student_id &&
          class_id === class_id
      );
    },
    enabled: !!student_id && !!class_id && !!date_id,
  });
}

export const useStudentClassAttendancesMultiple = (
  date_ids: number[] | string[]
) => {
  return useQueries({
    queries: date_ids.map<UseQueryOptions<StudentClassAttendance[]>>(
      (date_id: string | number) => ({
        queryKey: ["student-class-attendances", { date_id }],
        queryFn: async () =>
          (await AttendanceAPI.getStudentClassAttendanceByDateId(date_id)).data
            .data,
      })
    ),
    combine: (results) => {
      return {
        data: results.map((result) => result.data ?? []),
        isLoading: results.some((result) => result.isLoading),
        isError: results.some((result) => result.isError),
      };
    },
  });
};
