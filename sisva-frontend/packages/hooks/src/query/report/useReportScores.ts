import ReportAPI from "@sisva/api/reports";
import type { ReportScore } from "@sisva/types/apiTypes";
import type { QueryClient } from "@tanstack/react-query";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

export function useReportScores({
  report_id,
}: {
  report_id: number | string | null;
}) {
  return useQuery({
    queryKey: ["report-scores", { report_id }],
    queryFn: async () => {
      return (await ReportAPI.getStudentScores({ report_id: report_id! })).data
        .data;
    },
    enabled: !!report_id,
  });
}

export function invalidateReportScores(queryClient: QueryClient) {
  queryClient.invalidateQueries({
    queryKey: ["report-scores"],
  });
}

export function useSetReportScores() {
  const queryClient = useQueryClient();
  return useMutation({
    async mutationFn(payload: ReportScore[]) {
      return (await ReportAPI.setStudentScores(payload)).data.data;
    },
    onSuccess() {
      invalidateReportScores(queryClient);
    },
  });
}

export function useDeleteReportScores() {
  const queryClient = useQueryClient();
  return useMutation({
    async mutationFn(payload: {
      report_id: string | number;
      class_id?: string | number;
      student_id?: string;
    }) {
      return (await ReportAPI.deleteStudentScores(payload)).data.data;
    },
    onSuccess() {
      invalidateReportScores(queryClient);
    },
  });
}
