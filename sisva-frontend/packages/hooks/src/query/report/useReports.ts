import type { CreateReportPayload } from "@sisva/api/reports";
import ReportAPI from "@sisva/api/reports";
import type { Report } from "@sisva/types/apiTypes";
import type { QueryClient } from "@tanstack/react-query";
import {
  queryOptions,
  useMutation,
  useQuery,
  useQueryClient,
} from "@tanstack/react-query";

import { usePeriods } from "../academic/usePeriods";
import { useStudyPrograms } from "../academic/useStudyPrograms";
import { useTemplates } from "./useTemplates";

export const reportsQueryOptions = queryOptions({
  queryKey: ["reports"],
  queryFn: async () => {
    return (await ReportAPI.getReports()).data.data ?? [];
  },
});

export function useReports() {
  const { data: templates = [] } = useTemplates();
  const { data: periods = [] } = usePeriods();
  const { data: studyPrograms = [] } = useStudyPrograms();
  return useQuery({
    ...reportsQueryOptions,
    select(data) {
      return data.map((report) => {
        const template = templates.find(
          (template) => template.id === report.template_id
        );
        const period = periods.find((period) => period.id === report.period_id);
        const studyProgram = studyPrograms.find(
          (studyProgram) => studyProgram.id === report.study_program_id
        );
        return {
          ...report,
          template: template,
          period: period,
          study_program: studyProgram,
        };
      });
    },
  });
}

export function useReport({
  report_id,
}: {
  report_id: number | undefined | null;
}) {
  const { data: templates = [] } = useTemplates();
  const { data: periods = [] } = usePeriods();
  const { data: studyPrograms = [] } = useStudyPrograms();
  return useQuery({
    queryKey: ["reports", { report_id }],
    queryFn: async () => {
      return (await ReportAPI.getReport({ report_id: report_id! })).data.data;
    },
    enabled: !!report_id,
    select(report) {
      const template = templates.find(
        (template) => template.id === report.template_id
      );
      const period = periods.find((period) => period.id === report.period_id);
      const studyProgram = studyPrograms.find(
        (studyProgram) => studyProgram.id === report.study_program_id
      );
      return {
        ...report,
        template: template,
        period: period,
        study_program: studyProgram,
      };
    },
  });
}

export function invalidateReports(queryClient: QueryClient) {
  queryClient.invalidateQueries({
    queryKey: ["reports"],
  });
}

export function useCreateReport() {
  const queryClient = useQueryClient();
  return useMutation({
    async mutationFn(payload: CreateReportPayload) {
      return (await ReportAPI.createReport(payload)).data.data;
    },
    onSuccess() {
      invalidateReports(queryClient);
    },
  });
}

export function useUpdateReport() {
  const queryClient = useQueryClient();
  return useMutation({
    async mutationFn(payload: Report) {
      return (await ReportAPI.updateReport(payload)).data.data;
    },
    onSuccess() {
      invalidateReports(queryClient);
    },
  });
}

export function useDeleteReport() {
  const queryClient = useQueryClient();
  return useMutation({
    async mutationFn({ report_id }: { report_id: number }) {
      return (await ReportAPI.deleteReport({ report_id })).data.data;
    },
    onSuccess() {
      invalidateReports(queryClient);
    },
  });
}
