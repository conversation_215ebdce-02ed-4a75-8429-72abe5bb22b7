import ReportAPI from "@sisva/api/reports";
import { useQuery } from "@tanstack/react-query";

export function useTemplates() {
  return useQuery({
    queryKey: ["report-templates"],
    queryFn: async () => {
      return (await ReportAPI.getTemplates()).data.data;
    },
  });
}

export function useTemplate({
  template_id,
}: {
  template_id: number | null | undefined;
}) {
  return useQuery({
    queryKey: ["report-templates", { template_id }],
    queryFn: async () => {
      return (await ReportAPI.getTemplate({ template_id: template_id! })).data
        .data;
    },
    enabled: !!template_id,
  });
}
