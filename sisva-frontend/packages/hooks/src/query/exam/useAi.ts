import AIAPI, {
  type EditExamQuestionPayload,
  type GenerateExamQuestionsPayload,
  type GenerateSingleExamQuestionPayload,
} from "@sisva/api/ai";
import { useMutation } from "@tanstack/react-query";

export function useGenerateExamQuestions() {
  return useMutation({
    mutationFn: (payload: GenerateExamQuestionsPayload) =>
      AIAPI.generateExamQuestions(payload).then((res) => res),
  });
}

export function useGenerateSingleExamQuestion() {
  return useMutation({
    mutationFn: (payload: GenerateSingleExamQuestionPayload) =>
      AIAPI.generateSingleExamQuestion(payload).then((res) => res.data),
  });
}

export function useEditExamQuestion() {
  return useMutation({
    mutationFn: (payload: EditExamQuestionPayload) => AIAPI.editExamQuestion(payload).then((res) => res.data),
  });
}
