import ExamAPI from "@sisva/api/exam";
import { useMutation, useQueryClient } from "@tanstack/react-query";

import { invalidateExamSubmissions } from "./useExamSubmissions";

export function useStartExamSession<
  Data extends {
    exam_id: number;
    user_id: string;
    start_num: number;
  },
>({
  onSuccess,
  onError,
}: {
  onSuccess?: (data: Data) => void;
  onError?: () => void;
} = {}) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (payload: { exam_id: number; entry_code: string }) => {
      return (await ExamAPI.startExamSession(payload)).data.data as Data;
    },
    onSuccess(data) {
      invalidateExamSubmissions(queryClient);
      onSuccess?.(data);
    },
    onError() {
      onError?.();
    },
  });
}

export function useSubmitExamSession<
  Data extends {
    exam_id: number;
    user_id: string;
  },
>({
  onSuccess,
  onError,
}: {
  onSuccess?: (data: Data) => void;
  onError?: () => void;
} = {}) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (payload: { exam_id: number; note: string }) => {
      return (await ExamAPI.submitExamSession(payload)).data.data as Data;
    },
    onSuccess(data) {
      invalidateExamSubmissions(queryClient);
      onSuccess?.(data);
    },
    onError() {
      onError?.();
    },
  });
}

export function useResetExamSession<
  Data extends {
    exam_id: number;
    user_id: string;
  },
>() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (payload: {
      exam_id: number;
      data: {
        user_id: string;
      };
    }) => {
      return (await ExamAPI.resetExamSession(payload)).data.data as Data;
    },
    onSuccess(data) {
      invalidateExamSubmissions(queryClient);
    },
    onError() {},
  });
}
