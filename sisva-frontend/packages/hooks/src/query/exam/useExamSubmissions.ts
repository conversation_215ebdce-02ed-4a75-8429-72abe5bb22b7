import ExamAP<PERSON> from "@sisva/api/exam";
import type {
  ExamSubmission,
  ExamSubmissionAnswer,
} from "@sisva/types/apiTypes";
import type { QueryClient } from "@tanstack/react-query";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

export function useExamSubmissions<T = ExamSubmission[]>({
  exam_id,
  select,
  enabled = true,
}: {
  exam_id: number | undefined | null;
  select?: (examSubmissions: ExamSubmission[]) => T;
  enabled?: boolean;
}) {
  return useQuery<ExamSubmission[], unknown, T>({
    queryKey: ["exam-submissions", { exam_id }],
    queryFn: async () => {
      return (await ExamAPI.getExamSubmissions({ exam_id: exam_id! })).data
        .data;
    },
    select,
    enabled: enabled && !!exam_id,
  });
}

export function useStudentsExamSubmission({
  student_id,
  exam_id,
}: {
  student_id: string | undefined | null;
  exam_id: number | undefined | null;
}) {
  return useExamSubmissions({
    exam_id,
    select(examSubmissions) {
      return examSubmissions.find(
        (examSubmission) =>
          examSubmission.exam_id === exam_id &&
          examSubmission.user_id === student_id
      );
    },
    enabled: !!student_id && !!exam_id,
  });
}

export function invalidateExamSubmissions(queryClient: QueryClient) {
  queryClient.invalidateQueries({
    queryKey: ["exam-submissions"],
  });
}

export function invalidateExamSubmissionAnswers(queryClient: QueryClient) {
  queryClient.invalidateQueries({
    queryKey: ["exam-submission-answers"],
  });
}

export function useExamSubmissionAnswers<T = ExamSubmissionAnswer[]>({
  exam_id,
  user_id,
  select,
  enabled,
}: {
  exam_id: number;
  user_id: string;
  select?: (examSubmissions: ExamSubmissionAnswer[]) => T;
  enabled?: boolean;
}) {
  return useQuery<ExamSubmissionAnswer[], unknown, T>({
    queryKey: ["exam-submission-answers", { exam_id, user_id }],
    queryFn: async () => {
      return (await ExamAPI.getSubmissionAnswers({ exam_id, user_id })).data
        .data;
    },
    select,
    enabled,
  });
}

export function useSetExamSubmissionAnswers({
  onSuccess,
  onError,
}: {
  onSuccess?: () => void;
  onError?: () => void;
} = {}) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (payload: {
      exam_id: number;
      user_id: string;
      data: ExamSubmissionAnswer[];
    }) => {
      await ExamAPI.setSubmissionAnswers(payload);
    },
    onSuccess() {
      invalidateExamSubmissionAnswers(queryClient);
      onSuccess?.();
    },
    onError() {
      onError?.();
    },
  });
}
