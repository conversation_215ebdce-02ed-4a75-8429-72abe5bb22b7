import ExamAPI, { type ExamQuestionPayload } from "@sisva/api/exam";
import type { ExamQuestion } from "@sisva/types/apiTypes";
import type { QueryClient } from "@tanstack/react-query";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

export function useExamQuestions<T = ExamQuestion[]>({
  exam_id,
  select,
  enabled,
}: {
  exam_id: number;
  select?: (questions: ExamQuestion[]) => T;
  enabled?: boolean;
}) {
  return useQuery<ExamQuestion[], unknown, T>({
    queryKey: ["exam-questions", { exam_id }],
    queryFn: async () => {
      return (await ExamAPI.getExamQuestions({ exam_id })).data.data;
    },
    select,
    enabled,
  });
}

export function invalidateExamQuestions(queryClient: QueryClient) {
  queryClient.invalidateQueries({
    queryKey: ["exam-questions"],
  });
}

export function useSetExamQuestions() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({
      exam_id,
      data,
    }: {
      exam_id: number;
      data: ExamQuestionPayload[];
    }) => {
      await ExamAPI.setExamQuestions({ exam_id, data });
    },
    onSuccess() {
      invalidateExamQuestions(queryClient);
    },
    onError() {},
  });
}
