import ExamAP<PERSON> from "@sisva/api/exam";
import type { QueryClient } from "@tanstack/react-query";
import {
  queryOptions,
  useMutation,
  useQuery,
  useQueryClient,
} from "@tanstack/react-query";

const examScoresQeuryOptions = ({
  exam_id,
  user_id,
}: {
  exam_id: number;
  user_id?: string;
}) =>
  queryOptions({
    queryKey: ["exam-scores", { exam_id, user_id }],
    queryFn: async () => {
      return (await ExamAPI.getScores({ exam_id, user_id })).data.data;
    },
  });

export function useExamScores({
  exam_id,
}: {
  exam_id: number | null | undefined;
}) {
  return useQuery({
    ...examScoresQeuryOptions({ exam_id: exam_id! }),
    enabled: !!exam_id,
  });
}

export function useStudentsExamScore({
  exam_id,
  student_id,
}: {
  exam_id: number | null | undefined;
  student_id: string | null | undefined;
}) {
  return useQuery({
    ...examScoresQeuryOptions({ exam_id: exam_id! }),
    enabled: !!exam_id && !!student_id,
    select(scores) {
      return scores.find((score) => score.user_id === student_id);
    },
  });
}

// NOTE: we use seperate query for details because BE will return 404 if exam score for certain student doesn't exist.
// before using this, check if exam score for certain student exist with useStudentsExamScore
export function useStudentsExamScoreDetail({
  exam_id,
  student_id,
}: {
  exam_id: number | null | undefined;
  student_id: string | null | undefined;
}) {
  return useQuery({
    ...examScoresQeuryOptions({ exam_id: exam_id!, user_id: student_id! }),
    enabled: !!exam_id && !!student_id,
    select(scores) {
      return scores.find((score) => score.user_id === student_id)?.details;
    },
  });
}

export function invalidateExamScores(queryClient: QueryClient) {
  queryClient.invalidateQueries({
    queryKey: ["exam-scores"],
  });
}

export function useSetExamScore() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (payload: {
      exam_id: number | string;
      data: {
        feedback: string;
        details?: {
          question_id: string;
          value: number;
        }[];
        value?: number;
      };
      user_id: string;
    }) => {
      await ExamAPI.setScore(payload);
    },
    onSuccess() {
      invalidateExamScores(queryClient);
    },
    onError() {},
  });
}

export function useGenerateScore({
  onSuccess,
  onError,
}: {
  onSuccess?: () => void;
  onError?: () => void;
} = {}) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (payload: { exam_id: number; user_id: string }) => {
      await ExamAPI.generateScore(payload);
    },
    onSuccess() {
      invalidateExamScores(queryClient);
      onSuccess?.();
    },
    onError() {
      onError?.();
    },
  });
}
