import ExamAPI from "@sisva/api/exam";
import type { Exam } from "@sisva/types/apiTypes";
import { unique } from "@sisva/utils";
import type { QueryClient } from "@tanstack/react-query";
import {
  queryOptions,
  useMutation,
  useQuery,
  useQueryClient,
} from "@tanstack/react-query";

import { useClassesWithSubjectAndTeacherAndStudentGroup } from "../academic/useClasses";
import { useStudentsStudentGroup } from "../academic/useStudentGroups";
import { useTeachingPlans } from "../classroom/useTeachingPlans";

export const examsQueryOptions = queryOptions({
  queryKey: ["exams"],
  queryFn: async () => {
    return (await ExamAPI.getExams()).data.data;
  },
});

export function useExams() {
  return useQuery({
    ...examsQueryOptions,
  });
}

export function examQueryOptions(exam_id: number | string) {
  return queryOptions({
    queryKey: ["exams", { exam_id }],
    queryFn: async () => {
      return (await ExamAPI.getExam({ exam_id })).data.data;
    },
  });
}

export function useExam<T = Exam>({
  exam_id,
  select,
  enabled,
}: {
  exam_id: number;
  select?: (exam: Exam) => T;
  enabled?: boolean;
}) {
  return useQuery({
    ...examQueryOptions(exam_id),
    select,
    enabled,
  });
}

export function useExamsWithClasses() {
  const { data: classes = [], isLoading: L1 } =
    useClassesWithSubjectAndTeacherAndStudentGroup();
  const { data: teachingPlans = [], isLoading: L2 } = useTeachingPlans();

  return useQuery({
    ...examsQueryOptions,

    select(exams) {
      return exams.map((exam) => {
        const teaching_plans = teachingPlans.filter((teachingPlan) =>
          exam.teaching_plan_ids.includes(teachingPlan.id)
        );
        const class_ids = unique(
          teaching_plans.map((teachingPlan) => teachingPlan.class_id)
        );
        return {
          ...exam,
          teaching_plans,
          class_ids,
          classes: classes.filter((class_) => class_ids.includes(class_.id)),
        };
      });
    },
    enabled: !L1 && !L2,
  });
}

export function useExamWithClasses({
  exam_id,
}: {
  exam_id: number | undefined | null;
}) {
  const { data: classes = [], isLoading: L1 } =
    useClassesWithSubjectAndTeacherAndStudentGroup();
  const { data: teachingPlans = [], isLoading: L2 } = useTeachingPlans();

  return useQuery({
    ...examsQueryOptions,
    select(exams) {
      return exams
        .map((exam) => {
          const teaching_plans = teachingPlans.filter((teachingPlan) =>
            exam.teaching_plan_ids.includes(teachingPlan.id)
          );
          const class_ids = unique(
            teaching_plans.map((teachingPlan) => teachingPlan.class_id)
          );
          return {
            ...exam,
            teaching_plans,
            class_ids,
            classes: classes.filter((class_) => class_ids.includes(class_.id)),
          };
        })
        .find((exam) => exam.id === exam_id);
    },
    enabled: !L1 && !!exam_id && !L2,
  });
}

export type ExamWithClasses = NonNullable<
  ReturnType<typeof useExamsWithClasses>["data"]
>[number];

export function useStudentsExams(student_id: string) {
  const { data: classes = [], isLoading: L1 } =
    useClassesWithSubjectAndTeacherAndStudentGroup();
  const { data: studentGroup, isLoading: L2 } =
    useStudentsStudentGroup(student_id);
  const { data: teachingPlans = [], isLoading: L3 } = useTeachingPlans();

  return useQuery({
    ...examsQueryOptions,

    select(exams) {
      return exams
        .map((exam) => {
          const teaching_plans = teachingPlans.filter((teachingPlan) =>
            exam.teaching_plan_ids.includes(teachingPlan.id)
          );
          const class_ids = unique(
            teaching_plans.map((teachingPlan) => teachingPlan.class_id)
          );

          return {
            ...exam,
            class_ids,
            classes: classes.filter((class_) => class_ids.includes(class_.id)),
          };
        })
        .filter((exam) =>
          exam.classes.some(
            (class_) => class_.student_group?.id === studentGroup?.id
          )
        )
        .map((exam) => {
          const class_ = exam.classes.find(
            (class_) => class_.student_group_id === studentGroup?.id
          );
          return {
            ...exam,
            class: class_,
          };
        });
    },
    enabled: !L1 && !L2 && !L3,
  });
}

export type StudentsExam = NonNullable<
  ReturnType<typeof useStudentsExams>["data"]
>[number];

export function useExamsByClassId(class_id: number) {
  const { data: classes = [], isLoading: L1 } =
    useClassesWithSubjectAndTeacherAndStudentGroup();
  const { data: teachingPlans = [], isLoading: L2 } = useTeachingPlans();

  return useQuery({
    ...examsQueryOptions,

    select(exams) {
      return exams
        .map((exam) => {
          const teaching_plans = teachingPlans.filter((teachingPlan) =>
            exam.teaching_plan_ids.includes(teachingPlan.id)
          );
          const class_ids = unique(
            teaching_plans.map((teachingPlan) => teachingPlan.class_id)
          );

          return {
            ...exam,
            class_ids,
            classes: classes.filter((class_) => class_ids.includes(class_.id)),
            teaching_plans,
          };
        })
        .filter((exam) => exam.class_ids.includes(class_id))
        .map((exam) => {
          const class_ = exam.classes.find((class_) => class_.id === class_id);
          return {
            ...exam,
            class: class_,
          };
        });
    },
    enabled: !L1 && !L2,
  });
}

export function useExamsByTeachingPlanId(
  teaching_plan_id: number | undefined | null
) {
  const { data: classes = [], isLoading: L1 } =
    useClassesWithSubjectAndTeacherAndStudentGroup();
  const { data: teachingPlans = [], isLoading: L2 } = useTeachingPlans();

  return useQuery({
    ...examsQueryOptions,

    select(exams) {
      return exams
        .map((exam) => {
          const teaching_plans = teachingPlans.filter((teachingPlan) =>
            exam.teaching_plan_ids.includes(teachingPlan.id)
          );
          const class_ids = unique(
            teaching_plans.map((teachingPlan) => teachingPlan.class_id)
          );

          return {
            ...exam,
            class_ids,
            classes: classes.filter((class_) => class_ids.includes(class_.id)),
            teaching_plans,
          };
        })
        .filter((exam) => exam.teaching_plan_ids.includes(teaching_plan_id!));
    },
    enabled: !L1 && !L2 && !!teaching_plan_id,
  });
}

export function invalidateExams(queryClient: QueryClient) {
  queryClient.invalidateQueries({
    queryKey: ["exams"],
  });
}

export function useCreateExam() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (payload: Omit<Exam, "id" | "teaching_plan_details">) => {
      const exam_id = (await ExamAPI.createExam(payload)).data.data as number;
      return { exam_id };
    },
    onSuccess() {
      invalidateExams(queryClient);
    },
    onError() {},
  });
}

export function useUpdateExam({
  onSuccess,
  onError,
}: {
  onSuccess?: () => void;
  onError?: () => void;
} = {}) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({
      exam_id,
      data,
    }: {
      exam_id: number;
      data: Omit<Exam, "id" | "teaching_plan_details">;
    }) => {
      await ExamAPI.updateExam({ exam_id, data });
    },
    onSuccess() {
      invalidateExams(queryClient);
      onSuccess?.();
    },
    onError() {
      onError?.();
    },
  });
}

export function useDeleteExam({
  onSuccess,
  onError,
}: {
  onSuccess?: () => void;
  onError?: () => void;
} = {}) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (exam_id: number) => {
      await ExamAPI.deleteExam({ exam_id });
    },
    onSuccess() {
      invalidateExams(queryClient);
      onSuccess?.();
    },
    onError() {
      onError?.();
    },
  });
}
