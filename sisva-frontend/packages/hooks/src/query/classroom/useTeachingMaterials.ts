import ClassroomAPI from "@sisva/api/classroom";
import type { TeachingMaterial } from "@sisva/types/apiTypes";
import type { QueryClient } from "@tanstack/react-query";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

import { useClass } from "../academic/useClasses";
import { useTeachingPlansByClassId } from "./useTeachingPlans";

export function useTeachingMaterials<T = TeachingMaterial[]>({
  select,
  enabled,
}: {
  select?: (teachingMaterials: TeachingMaterial[]) => T;
  enabled?: boolean;
} = {}) {
  return useQuery<TeachingMaterial[], unknown, T>({
    queryKey: ["teaching-materials"],
    queryFn: async () =>
      (await ClassroomAPI.getTeachingMaterials({})).data.data,
    select,
    enabled,
  });
}

export const useTeachingMaterial = (id: string | number | undefined) => {
  return useQuery<TeachingMaterial>({
    queryKey: ["teaching-materials", { id }],
    queryFn: async () =>
      (await ClassroomAPI.getTeachingMaterial({ id: id! })).data.data,
    enabled: !!id,
  });
};

export function useTeachingMaterialsBySubjectId(
  subject_id: number | undefined
) {
  return useTeachingMaterials({
    select: (teachingMaterials) =>
      teachingMaterials.filter((item) => item.subject_id === subject_id),
    enabled: !!subject_id,
  });
}

export function useTeachingMaterialsByTeachingPlansByClassId(
  class_id: number | undefined
) {
  const { data: teachingPlans = [], isLoading: L1 } =
    useTeachingPlansByClassId(class_id);
  const teachingMaterialIds = teachingPlans.flatMap(
    (item) => item.teaching_materials?.map((material) => material.id) ?? []
  );
  return useTeachingMaterials({
    select: (teachingMaterials) =>
      teachingMaterials.filter((item) => teachingMaterialIds.includes(item.id)),
    enabled: !!class_id && !L1,
  });
}

export function useTeachingMaterialsByClassId(class_id: number | undefined) {
  const { data: class_, isLoading: L1 } = useClass(class_id);
  return useTeachingMaterials({
    select: (teachingMaterials) =>
      teachingMaterials.filter(
        (item) => item.subject_id === class_?.subject_id
      ),
    enabled: !!class_id && !L1,
  });
}

export function invalidateTeachingMaterials(queryClient: QueryClient) {
  queryClient.invalidateQueries({
    queryKey: ["teaching-materials"],
  });
}

export function useCreateTeachingMaterials() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (payload: {
      subject_id: number;
      description: string;
      grade: string;
      attachment_file_uri: string;
    }) => {
      const teaching_material_id = (
        await ClassroomAPI.createTeachingMaterial(payload)
      ).data.data as number;
      return { teaching_material_id };
    },
    onSuccess() {
      invalidateTeachingMaterials(queryClient);
    },
    onError() {},
  });
}

export function useUpdateTeachingMaterials({
  onSuccess,
  onError,
}: {
  onSuccess?: () => void;
  onError?: () => void;
} = {}) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (payload: {
      teaching_material_id: number | string;
      subject_id: number;
      description: string;
      grade: string;
      attachment_file_uri: string;
    }) => {
      await ClassroomAPI.updateTeachingMaterial(
        payload,
        payload.teaching_material_id
      );
    },
    onSuccess() {
      invalidateTeachingMaterials(queryClient);
      onSuccess?.();
    },
    onError() {
      onError?.();
    },
  });
}

export function useDeleteTeachingMaterials({
  onSuccess,
  onError,
}: {
  onSuccess?: () => void;
  onError?: () => void;
} = {}) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (teaching_material_id: number | string) => {
      await ClassroomAPI.deleteTeachingMaterial(teaching_material_id);
    },
    onSuccess() {
      invalidateTeachingMaterials(queryClient);
      onSuccess?.();
    },
    onError() {
      onError?.();
    },
  });
}
