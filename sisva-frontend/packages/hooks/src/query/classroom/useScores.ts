import ClassroomAPI from "@sisva/api/classroom";
import type { Score } from "@sisva/types/apiTypes";
import type { QueryClient } from "@tanstack/react-query";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { useMutation } from "@tanstack/react-query";

export function useScoresByTaskId<T = Score[]>(
  task_id: number | string | undefined | null,
  {
    select,
    enabled = true,
  }: {
    select?: (scores: Score[]) => T;
    enabled?: boolean;
  } = {}
) {
  return useQuery<Score[], unknown, T>({
    queryKey: [
      "scores",
      {
        task_id,
      },
    ],
    queryFn: async () =>
      (await ClassroomAPI.getScoresByTaskId({ task_id: task_id! })).data.data,
    select,
    enabled: !!task_id && enabled,
  });
}

export const useScoresByClassId = (class_id: number | string | undefined) => {
  return useQuery<Score[]>({
    queryKey: [
      "scores",
      {
        class_id,
      },
    ],
    queryFn: async () =>
      (await ClassroomAPI.getScoresByClassId({ class_id: class_id! })).data
        .data,
    enabled: !!class_id,
  });
};

export const useSetScore = ({
  onSuccess,
  onError,
}: {
  onSuccess: () => void;
  onError: () => void;
}) => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({
      task_id,
      value,
      student_id,
      feedback,
    }: {
      task_id: number | string;
      value: number;
      student_id: string;
      feedback?: string;
    }) => await ClassroomAPI.setScore({ task_id, value, student_id, feedback }),
    onSuccess: () => {
      invalidateScores(queryClient);
      onSuccess();
    },
    onError: () => {
      onError();
    },
  });
};

export const invalidateScores = (queryClient: QueryClient) => {
  queryClient.invalidateQueries({
    queryKey: ["scores"],
  });
};

export function useStudentScore({
  task_id,
  student_id,
}: {
  task_id: string | number | undefined | null;
  student_id: string | undefined | null;
}) {
  return useScoresByTaskId(task_id, {
    select(scores) {
      return scores.find((score) => score.student_id === student_id);
    },
    enabled: !!task_id && !!student_id,
  });
}
