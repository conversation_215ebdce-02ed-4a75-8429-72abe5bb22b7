import ClassroomAPI from "@sisva/api/classroom";
import type { Submission } from "@sisva/types/apiTypes";
import type { QueryClient } from "@tanstack/react-query";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

import { useStudents } from "../user/useStudents";
import { useScoresByTaskId } from "./useScores";
import { useTasks } from "./useTasks";

export function useSubmissions<T = Submission[]>({
  task_id,
  select,
  enabled = true,
}: {
  task_id: number | string | undefined | null;
  select?: (submissions: Submission[]) => T;
  enabled?: boolean;
}) {
  return useQuery<Submission[], unknown, T>({
    queryKey: ["submissions", { task_id }],
    queryFn: async () => {
      return (await ClassroomAPI.getSubmissions({ task_id: task_id! })).data
        .data;
    },
    select,
    enabled: !!task_id && enabled,
  });
}

export const invalidateSubmissions = ({
  queryClient,
  task_id,
}: {
  queryClient: QueryClient;
  task_id: number | string;
}) => {
  queryClient.invalidateQueries({
    queryKey: ["submissions", { task_id }],
  });
};

export function useSubmissionsWithStudentAndTaskAndScore({
  task_id,
}: {
  task_id: number | string;
}) {
  const { data: students = [] } = useStudents();
  const { data: tasks = [] } = useTasks();
  const { data: scores = [] } = useScoresByTaskId(task_id);
  return useSubmissions({
    task_id,
    select(submissions) {
      return submissions.map((submission) => {
        const student = students.find(
          (student) => student.id === submission.student_id
        );
        const task = tasks.find((task) => task.id === submission.task_id);
        const score = scores.find(
          (score) =>
            score.task_id === task?.id && score.student_id === student?.id
        );
        return {
          ...submission,
          student,
          task,
          score,
        };
      });
    },
    enabled: !!task_id,
  });
}

export function useStudentSubmissionWithStudentAndTaskAndScore({
  student_id,
  task_id,
}: {
  student_id: string | undefined | null;
  task_id: number | string | undefined | null;
}) {
  const { data: students = [] } = useStudents();
  const { data: tasks = [] } = useTasks();
  const { data: scores = [] } = useScoresByTaskId(task_id);
  return useSubmissions({
    task_id,
    select(submissions) {
      return submissions
        .map((submission) => {
          const student = students.find(
            (student) => student.id === submission.student_id
          );
          const task = tasks.find((task) => task.id === submission.task_id);
          const score = scores.find(
            (score) =>
              score.task_id === task?.id && score.student_id === student?.id
          );
          return {
            ...submission,
            student,
            task,
            score,
          };
        })
        .find((submission) => submission.student_id === student_id);
    },
    enabled: !!student_id && !!task_id,
  });
}

export function useSetSubmission({
  task_id,
  onSuccess,
  onError,
}: {
  task_id: number;
  onSuccess: () => void;
  onError: () => void;
}) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({
      student_id,
      attachment_file_uri,
      note,
    }: {
      student_id: string;
      attachment_file_uri: string;
      note: string;
    }) =>
      await ClassroomAPI.setSubmission({
        student_id,
        attachment_file_uri,
        note,
        task_id,
      }),
    onSuccess: () => {
      invalidateSubmissions({
        queryClient,
        task_id,
      });
      onSuccess();
    },
    onError: () => {
      onError();
    },
  });
}
