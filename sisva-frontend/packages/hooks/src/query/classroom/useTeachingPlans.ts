import ClassroomAPI from "@sisva/api/classroom";
import type { QueryClient } from "@tanstack/react-query";
import {
  queryOptions,
  useMutation,
  useQuery,
  useQueryClient,
} from "@tanstack/react-query";

import { useClassesWithSubjectAndTeacherAndStudentGroup } from "../academic/useClasses";
import { useExams, useExamsByTeachingPlanId } from "../exam/useExams";
import { useTasks } from "./useTasks";
import { useTeachingMaterials } from "./useTeachingMaterials";

export const teachingPlansQueryOptions = queryOptions({
  queryKey: ["teaching-plans"],
  queryFn: async () => (await ClassroomAPI.getTeachingPlans()).data.data,
});

export function useTeachingPlans() {
  return useQuery({
    ...teachingPlansQueryOptions,
  });
}

export function useCreateTeachingPlan({
  onSuccess,
  onError,
}: {
  onSuccess?: () => void;
  onError?: () => void;
} = {}) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (payload: {
      title: string;
      markdown: string;
      class_id: number;
      teaching_materials: {
        id: number;
      }[];
      tasks: {
        id: number;
      }[];
      teaching_goal: string;
      teaching_activity: string;
      teaching_scoring: string;
    }) => {
      const teaching_plan_id = (await ClassroomAPI.createTeachingPlan(payload))
        .data.data;
      return { teaching_plan_id };
    },
    onSuccess() {
      invalidateTeachingPlans(queryClient);
      onSuccess?.();
    },
    onError() {
      onError?.();
    },
  });
}

export function useUpdateTeachingPlan({
  onSuccess,
  onError,
}: {
  onSuccess?: () => void;
  onError?: () => void;
} = {}) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (payload: {
      teaching_plan_id: number;
      title: string;
      markdown: string;
      class_id: number;
      teaching_materials: {
        id: number;
      }[];
      tasks: {
        id: number;
      }[];
      teaching_goal: string;
      teaching_activity: string;
      teaching_scoring: string;
    }) => {
      const teaching_plan_id = (
        await ClassroomAPI.updateTeachingPlan(payload, payload.teaching_plan_id)
      ).data.data;
      return { teaching_plan_id };
    },
    onSuccess() {
      invalidateTeachingPlans(queryClient);
      onSuccess?.();
    },
    onError() {
      onError?.();
    },
  });
}

export const useDeleteTeachingPlan = ({
  onSuccess,
  onError,
}: {
  onSuccess: () => void;
  onError: () => void;
}) => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (id: number | string) =>
      await ClassroomAPI.deleteTeachingPlan({ id }),
    onError: () => {
      onError();
    },
    onSuccess: () => {
      onSuccess();
      invalidateTeachingPlans(queryClient);
    },
  });
};

export const invalidateTeachingPlans = (queryClient: QueryClient) => {
  queryClient.invalidateQueries({ queryKey: ["teaching-plans"] });
};

export function useTeachingPlan(teaching_plan_id: number | undefined) {
  return useQuery({
    ...teachingPlansQueryOptions,
    select: (teachingPlans) =>
      teachingPlans.find((plan) => plan.id === teaching_plan_id),
    enabled: !!teaching_plan_id,
  });
}

export function useTeachingPlansByClassId(class_id: number | undefined) {
  const { data: teachingMaterials = [], isLoading: L1 } =
    useTeachingMaterials();
  const { data: tasks = [], isLoading: L2 } = useTasks();
  const { data: classes = [], isLoading: L3 } =
    useClassesWithSubjectAndTeacherAndStudentGroup();
  const { data: exams = [], isLoading: L4 } = useExams();
  const class_ = classes.find((class_) => class_.id === class_id);

  return useQuery({
    ...teachingPlansQueryOptions,
    select(teacingPlans) {
      return teacingPlans
        .filter((plan) => {
          return plan.class_id === class_id;
        })
        .map((plan) => {
          const materialIds =
            plan.teaching_materials?.map((material) => material.id) ?? [];
          const taskIds = plan.tasks?.map((task) => task.id) ?? [];
          return {
            ...plan,
            teaching_materials: teachingMaterials.filter((material) =>
              materialIds.includes(material.id)
            ),
            tasks: tasks
              .filter((task) => taskIds.includes(task.id))
              .map((task) => ({ ...task, class: class_ })),
            exams: exams.filter((exam) =>
              exam.teaching_plan_ids.includes(plan.id)
            ),
          };
        });
    },
    enabled: !!class_id && !L1 && !L2 && !L3 && !L4,
  });
}
