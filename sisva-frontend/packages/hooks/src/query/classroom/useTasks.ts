import ClassroomAPI from "@sisva/api/classroom";
import type { QueryClient } from "@tanstack/react-query";
import {
  queryOptions,
  useMutation,
  useQuery,
  useQueryClient,
} from "@tanstack/react-query";
import { sort } from "fast-sort";

import { useStudentsClasses, useTeachersClasses } from "../academic/useClasses";
import { useTeachingPlans } from "./useTeachingPlans";

export const tasksQueryOptions = ({
  class_id,
  period_id,
}: { class_id?: number; period_id?: number } = {}) =>
  queryOptions({
    queryKey: ["tasks", { class_id, period_id }],
    queryFn: async () =>
      (await ClassroomAPI.getTasks({ class_id, period_id })).data.data,
  });

export function useTasks({
  class_id,
  period_id,
  all,
}: { class_id?: number; period_id?: number; all?: boolean } = {}) {
  const { data: teachingPlans = [], isLoading: L1 } = useTeachingPlans();
  const task_ids = teachingPlans.flatMap(
    (plan) => plan.tasks?.map((task) => task.id) ?? []
  );
  return useQuery({
    ...tasksQueryOptions({ class_id, period_id }),
    select(tasks) {
      return sort(tasks)
        .asc((task) => task.id)
        .filter((task) => (all ? true : task_ids.includes(task.id)));
    },
    enabled: !L1,
  });
}

export function taskQueryOptions(task_id: number | string) {
  return queryOptions({
    queryKey: ["tasks", { task_id }],
    queryFn: async () =>
      (await ClassroomAPI.getTask({ task_id: task_id })).data.data,
  });
}

export const useTask = ({
  task_id,
}: {
  task_id: number | string | undefined;
}) => {
  return useQuery({
    ...taskQueryOptions(task_id!),
    enabled: !!task_id,
  });
};

export const useStudentsTasks = (student_id: string | undefined) => {
  const { data: studentsClasses = [], isLoading: L1 } =
    useStudentsClasses(student_id);
  const { data: teachingPlans = [], isLoading: L2 } = useTeachingPlans();
  const task_ids = teachingPlans.flatMap(
    (plan) => plan.tasks?.map((task) => task.id) ?? []
  );

  return useQuery({
    ...tasksQueryOptions(),
    select(tasks) {
      return sort(tasks)
        .asc((task) => task.id)
        .filter((task) =>
          studentsClasses.some((class_) => class_.id === task.class_id)
        )
        .map((task) => {
          return {
            ...task,
            class: studentsClasses.find(
              (class_) => class_.id === task.class_id
            ),
          };
        })
        .filter((task) => task_ids.includes(task.id));
    },
    enabled: !L1 && !L2 && !!student_id,
  });
};

export type StudentsTask = NonNullable<
  ReturnType<typeof useStudentsTasks>["data"]
>[number];

export function useTeachersTasks(teacher_id: string | undefined) {
  const { data: teachersClasses = [], isLoading: L1 } =
    useTeachersClasses(teacher_id);
  const { data: teachingPlans = [], isLoading: L2 } = useTeachingPlans();
  const task_ids = teachingPlans.flatMap(
    (plan) => plan.tasks?.map((task) => task.id) ?? []
  );

  return useQuery({
    ...tasksQueryOptions(),
    select(tasks) {
      return sort(tasks)
        .asc((task) => task.id)
        .filter((task) =>
          teachersClasses.some((class_) => class_.id === task.class_id)
        )
        .map((task) => {
          return {
            ...task,
            class: teachersClasses.find(
              (class_) => class_.id === task.class_id
            ),
          };
        })
        .filter((task) => task_ids.includes(task.id));
    },
    enabled: !L1 && !L2 && !!teacher_id,
  });
}

export type TeachersTask = NonNullable<
  ReturnType<typeof useTeachersTasks>["data"]
>[number];

export function invalidateTasks(queryClient: QueryClient) {
  queryClient.invalidateQueries({
    queryKey: ["tasks"],
  });
}

export function useCreateTask() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (payload: {
      name: string;
      description: string;
      class_id: number;
      deadline: string;
      attachment_file_uri: string;
      allow_submission: boolean;
      allow_overdue_submission: boolean;
    }) => {
      const task_id = (await ClassroomAPI.createTask(payload)).data
        .data as number;
      return { task_id };
    },
    onSuccess(data) {
      invalidateTasks(queryClient);
    },
    onError() {},
  });
}

export function useUpdateTask({
  onSuccess,
  onError,
}: {
  onSuccess?: (task_id: number) => void;
  onError?: () => void;
} = {}) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (payload: {
      task_id: number;
      name: string;
      description: string;
      class_id: number;
      deadline: string;
      attachment_file_uri: string;
      allow_submission: boolean;
      allow_overdue_submission: boolean;
    }) => {
      return (await ClassroomAPI.updateTask(payload, payload.task_id)).data
        .data;
    },
    onSuccess(data) {
      invalidateTasks(queryClient);
      onSuccess?.(data);
    },
    onError() {
      onError?.();
    },
  });
}

export function useDeleteTask({
  onSuccess,
  onError,
}: {
  onSuccess?: (task_id: number) => void;
  onError?: () => void;
} = {}) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (task_id: number) => {
      return (await ClassroomAPI.deleteTask(task_id)).data.data;
    },
    onSuccess(data) {
      invalidateTasks(queryClient);
      onSuccess?.(data);
    },
    onError() {
      onError?.();
    },
  });
}
