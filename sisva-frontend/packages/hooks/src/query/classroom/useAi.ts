import AIAPI, { type EditTeachingPlanSectionPayload, type GenerateTeachingPlanSectionPayload, type EditTaskPayload } from "@sisva/api/ai";
import { useMutation } from "@tanstack/react-query";

export function useGenerateTeachingPlan() {
  return useMutation({
    mutationFn: (payload: { title: string; specifications?: string }) =>
      AIAPI.generateTeachingPlan(payload).then((res) => res.data),
  });
}

export function useGenerateTask() {
  return useMutation({
    mutationFn: (payload: {
      title: string;
      className?: string;
      planTitle?: string;
      subjectName?: string;
      specifications?: string;
    }) => AIAPI.generateTask(payload).then((res) => res.data),
  });
}

export function useEditTask() {
  return useMutation({
    mutationFn: (payload: EditTaskPayload) =>
      AIAPI.editTask(payload).then((res) => res.data),
  });
}

export function useGenerateTeachingMaterials() {
  return useMutation({
    mutationFn: (payload: {
      title: string;
      subject: string;
      specifications?: string;
    }) => AIAPI.generateTeachingMaterials(payload).then((res) => res.data),
  });
}

export function useEditTeachingPlanSection() {
  return useMutation({
    mutationFn: (payload: EditTeachingPlanSectionPayload) =>
      AIAPI.editTeachingPlanSection(payload).then((res) => res.data),
  });
}

export function useGenerateTeachingPlanSection() {
  return useMutation({
    mutationFn: (payload: GenerateTeachingPlanSectionPayload) =>
      AIAPI.generateTeachingPlanSection(payload).then((res) => res.data),
  });
}
