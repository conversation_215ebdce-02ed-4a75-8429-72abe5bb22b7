import FilesAPI from "@sisva/api/files";
import { useMutation } from "@tanstack/react-query";

export function useUploadFile({
  onSuccess,
  onError,
}: {
  onSuccess?: () => void;
  onError?: () => void;
} = {}) {
  return useMutation({
    async mutationFn(formData: FormData) {
      return (await FilesAPI.uploadFile(formData)).data.data;
    },
    onSuccess() {
      onSuccess?.();
    },
    onError() {
      onError?.();
    },
  });
}
