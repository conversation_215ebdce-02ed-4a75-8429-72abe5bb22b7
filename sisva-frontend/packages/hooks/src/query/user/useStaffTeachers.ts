import UsersAPI from "@sisva/api/users";
import type { User } from "@sisva/types/apiTypes";
import type { CreateStaffForm, UpdateStaffForm } from "@sisva/types/formTypes";
import type { QueryClient } from "@tanstack/react-query";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

import { useStaffAttendances } from "../attendance/useAttendance";
import { invalidateUsers, useUser } from "./useUsers";

export function useStaffTeachers<T = User[]>({
  select,
  enabled,
}: {
  select?: (staffTeachers: User[]) => T;
  enabled?: boolean;
} = {}) {
  return useQuery<User[], unknown, T>({
    queryKey: ["staff", "teachers", "users"],
    queryFn: async () =>
      (await UsersAPI.getAllUsers("staff,teacher")).data.data.filter(
        (student: User) => student.status === "active"
      ),
    select,
    enabled,
  });
}

export function useStaffTeachersWithAttendance({
  date_id,
}: {
  date_id: number | string;
}) {
  const { data: staffAttendance = [], isLoading: L1 } = useStaffAttendances({
    date_id,
  });
  return useStaffTeachers({
    select(staffTeachers) {
      return staffTeachers.map((staffTeacher) => {
        const attendance = staffAttendance.find(
          (attendance) => attendance.staff_id === staffTeacher.id
        );
        return {
          ...staffTeacher,
          attendance,
        };
      });
    },
    enabled: !L1,
  });
}

export type StaffTeachersWithAttendance = NonNullable<
  ReturnType<typeof useStaffTeachersWithAttendance>["data"]
>[number];

export const invalidateStaffTeachers = (queryClient: QueryClient) => {
  queryClient.invalidateQueries({ queryKey: ["staff", "teachers", "users"] });
};

export function useCreateStaff({
  onSuccess,
  onError,
}: {
  onSuccess?: () => void;
  onError?: () => void;
} = {}) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({
      name,
      password,
      type,
      permissions,
    }: CreateStaffForm) => {
      const payload = {
        user: {
          nik: "",
          name: name,
          type: type,
          roles: [type],
          profile_image_uri: "",
          detail: {
            json_text: "",
          },
          permissions: permissions,
        },
        password: password,
      };

      await UsersAPI.createUser(payload);
    },
    onSuccess() {
      invalidateStaffTeachers(queryClient);
      onSuccess?.();
    },
    onError() {
      onError?.();
    },
  });
}

export function useUpdateStaff({
  user_id,
  onSuccess,
  onError,
}: {
  user_id?: string;
  onSuccess?: () => void;
  onError?: () => void;
} = {}) {
  const queryClient = useQueryClient();
  const { data: user } = useUser(user_id);

  return useMutation({
    mutationFn: async ({
      name,
      nik,
      permissions,
      profile_image_uri,
      email,
      address,
      gender,
      nationality,
      nuptk,
      phone,
      religion,
    }: UpdateStaffForm) => {
      if (!user) throw new Error("User not found");

      const oldJSONText = (() => {
        try {
          return JSON.parse(user!.detail.json_text);
        } catch (error) {
          return {};
        }
      })();

      const payload = {
        nik: nik ?? "",
        name: name,
        permissions: permissions,
        detail: {
          json_text: JSON.stringify({
            ...oldJSONText,
            email: email,
            phone: phone ?? "",
            gender: gender,
            religion: religion,
            nationality: nationality,
            address: address,
            nuptk: nuptk,
          }),
        },
        profile_image_uri: profile_image_uri,
      };

      await UsersAPI.updateUserById(payload, user_id!);
    },
    async onSuccess() {
      invalidateUsers(queryClient);
      onSuccess?.();
    },
    onError() {
      onError?.();
    },
  });
}
