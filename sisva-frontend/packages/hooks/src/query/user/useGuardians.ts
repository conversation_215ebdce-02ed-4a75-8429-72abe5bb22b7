import UsersAPI from "@sisva/api/users";
import type { User } from "@sisva/types/apiTypes";
import type {
  CreateGuardianForm,
  UpdateGuardianForm,
} from "@sisva/types/formTypes";
import type { QueryClient } from "@tanstack/react-query";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

import { studentsQeuryOptions, useStudents } from "./useStudents";
import { useUser } from "./useUsers";
import { invalidateUser } from "./useUsers";

export function useGuardians<T = User[]>({
  select,
}: {
  select?: (guardians: User[]) => T;
} = {}) {
  return useQuery<User[], unknown, T>({
    queryKey: ["guardians"],
    queryFn: async () =>
      (await UsersAPI.getAllUsers("student_guardian")).data.data.filter(
        (student: User) => student.status === "active"
      ),
    select,
  });
}

export function useGuardiansStudents(guardian_id: string) {
  return useQuery({
    ...studentsQeuryOptions,
    select: (students) =>
      students.filter((student) => student.detail.guardian_id === guardian_id),
  });
}

export function useCreateGuardian({
  onSuccess,
  onError,
}: {
  onSuccess?: () => void;
  onError?: () => void;
} = {}) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({
      name,
      password,
      confirm_password,
    }: CreateGuardianForm) => {
      const payload = {
        user: {
          nik: "",
          name: name,
          type: "student_guardian",
          roles: ["student_guardian"],
          profile_image_uri: "",
          detail: {
            json_text: "",
          },
          permissions: [],
        },
        password: password,
      };

      await UsersAPI.createUser(payload);
    },
    onSuccess() {
      invalidateGuardians(queryClient);
      onSuccess?.();
    },
    onError() {
      onError?.();
    },
  });
}

export function useUpdateGuardian(
  gaurdian_id: string,
  {
    onSuccess,
    onError,
  }: {
    onSuccess?: () => void;
    onError?: () => void;
  } = {}
) {
  const { data: guardian } = useUser(gaurdian_id);
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({
      name,
      address,
      email,
      gender,
      nik,
      phone,
      profile_image_uri,
      nationality,
      religion,
      education_level,
      occupation,
      birth_year,
      life_status,
      income_level,
    }: UpdateGuardianForm) => {
      const oldJSONText = (() => {
        try {
          return JSON.parse(guardian!.detail.json_text);
        } catch (error) {
          return {};
        }
      })();

      const payload = {
        nik: nik ?? "",
        name: name,
        detail: {
          json_text: JSON.stringify({
            ...oldJSONText,
            email: email,
            phone: phone ?? "",
            gender: gender,
            religion: religion,
            nationality: nationality,
            address: address,
            education_level: education_level,
            occupation: occupation,
            birth_year: birth_year,
            life_status: life_status,
            income_level: income_level,
          }),
        },
        profile_image_uri: profile_image_uri,
      };

      await UsersAPI.updateUserById(payload, gaurdian_id);
    },
    onSuccess() {
      invalidateGuardians(queryClient);
      invalidateUser({ queryClient, user_id: gaurdian_id });
      onSuccess?.();
    },
    onError() {
      onError?.();
    },
  });
}

export function useDeleteGuardian({
  onSuccess,
  onError,
}: {
  onSuccess?: () => void;
  onError?: () => void;
} = {}) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (guardian_id: string) => {
      const payload = {
        status: "deleted",
      };

      await UsersAPI.updateUserById(payload, guardian_id);
    },
    onSuccess() {
      invalidateGuardians(queryClient);
      onSuccess?.();
    },
    onError() {
      onError?.();
    },
  });
}

export function invalidateGuardians(queryClient: QueryClient) {
  queryClient.invalidateQueries({
    queryKey: ["guardians"],
  });
}
