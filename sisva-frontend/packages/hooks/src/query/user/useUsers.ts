import AuthAPI from "@sisva/api/auth";
import UsersAP<PERSON> from "@sisva/api/users";
import type { User } from "@sisva/types/apiTypes";
import type { Grade } from "@sisva/types/apiTypes";
import type { QueryClient } from "@tanstack/react-query";
import {
  queryOptions,
  useMutation,
  useQuery,
  useQueryClient,
} from "@tanstack/react-query";

import {
  useStudentGroups,
  useStudentInStudentGroups,
} from "../academic/useStudentGroups";
import { useStudyPrograms } from "../academic/useStudyPrograms";
import { useBillsWithInvoices } from "../finance/useBills";
import { useUserBills } from "../finance/useUserBills";

export const usersQueryOptions = queryOptions({
  queryKey: ["users"],
  queryFn: async () =>
    (
      await UsersAPI.getAllUsers("staff,teacher,student,student_guardian")
    ).data.data.filter((student: User) => student.status === "active"),
});

export const useUsers = () => {
  return useQuery({
    ...usersQueryOptions,
  });
};

export const useUsersWithoutGuardian = () => {
  return useQuery({
    ...usersQueryOptions,
    select: (users) => users.filter((user) => user.type !== "student_guardian"),
  });
};

export const useUser = (
  user_id: string | undefined | null,
  {
    enabled,
  }: {
    enabled?: boolean;
  } = {
    enabled: true,
  }
) => {
  return useQuery<User>({
    queryKey: ["users", user_id],
    queryFn: async () => (await UsersAPI.getUserById(user_id!)).data.data,
    enabled: !!user_id && enabled,
  });
};

export function useUserByUsername(username: string | undefined) {
  const { data: studentInStudentGroup = [] } = useStudentInStudentGroups();
  const { data: studentGroups = [] } = useStudentGroups();
  return useQuery({
    ...usersQueryOptions,
    select(users) {
      const user = users.find((user) => user.username === username);
      if (!user) return;
      const studentGroupId = studentInStudentGroup.find(
        (item) => item.student_id === user?.id
      )?.student_group_id;
      const studentGroup = studentGroups.find(
        (item) => item.id === studentGroupId
      );
      return {
        ...user,
        // this will be undefined if user is not a student
        student_group: studentGroup,
      };
    },
    enabled: !!username,
  });
}

export const invalidateUser = ({
  queryClient,
  user_id,
}: {
  queryClient: QueryClient;
  user_id: string;
}) => {
  queryClient.invalidateQueries({
    queryKey: ["users", user_id],
  });
};

export const invalidateUsers = (queryClient: QueryClient) => {
  queryClient.invalidateQueries({
    queryKey: ["users"],
  });
};

export const useUpdateUser = ({
  onSuccess,
  onError,
}: {
  onSuccess?: () => void;
  onError?: () => void;
} = {}) => {
  return useMutation({
    mutationFn: async (payload: {
      user_id: string;
      username?: string;
      nik?: string;
      name?: string;
      detail?: {
        json_text?: string;

        // * these only exist for students
        grade?: Grade;
        study_program_id?: number;
        guardian_id?: string;
        //
      };
      profile_image_uri?: string;
      roles?: string[]; //? ???
      permissions?: string[]; //? ???
      status?: string; //? active, inactive ???
    }) => {
      return (await UsersAPI.updateUserById(payload, payload.user_id)).data
        .data;
    },
    onSuccess,
    onError: (error) => {
      console.error(error);
      onError?.();
    },
  });
};

export const useUpdatePassword = ({
  onSuccess,
  onError,
}: {
  onSuccess?: () => void;
  onError?: () => void;
} = {}) => {
  return useMutation({
    mutationFn: async (payload: {
      user_id: string;
      new_password: string;
      current_password: string;
    }) => {
      return (await AuthAPI.changeUserPass(payload)).data.data;
    },
    onSuccess,
    onError: (error) => {
      console.error(error);
      onError?.();
    },
  });
};

export const useResetPassword = ({
  onSuccess,
  onError,
}: {
  onSuccess?: () => void;
  onError?: () => void;
} = {}) => {
  return useMutation({
    mutationFn: async (payload: { user_id: string; new_password: string }) => {
      return (await AuthAPI.resetUserPass(payload)).data.data;
    },
    onSuccess,
    onError: (error) => {
      console.error(error);
      onError?.();
    },
  });
};

export function useDeleteUser({
  onSuccess,
  onError,
}: {
  onSuccess?: () => void;
  onError?: () => void;
} = {}) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (user_id: string) => {
      const payload = {
        status: "deleted",
      };
      await UsersAPI.updateUserById(payload, user_id);
    },
    onSuccess() {
      invalidateUsers(queryClient);
      onSuccess?.();
    },
    onError() {
      onError?.();
    },
  });
}

export function useUsersWithBillsAndStudentGroupAndStudyProgram() {
  const { data: userBills = [] } = useUserBills();
  const { data: bills = [] } = useBillsWithInvoices();
  const { data: studentInStudentGroups = [] } = useStudentInStudentGroups();
  const { data: studentGroups = [] } = useStudentGroups();
  const { data: studyPrograms = [] } = useStudyPrograms();
  return useQuery({
    ...usersQueryOptions,
    select(users) {
      return users.map((user) => {
        const filteredUserBills = userBills.filter(
          (userBill) => userBill.user_id === user.id
        );
        const bill_ids = filteredUserBills.map((userBill) => userBill.bill_id);
        const filteredBills = bills.filter((bill) =>
          bill_ids.includes(bill.id)
        );
        const studentInStudentGroup = studentInStudentGroups.find(
          (studentInStudentGroup) =>
            studentInStudentGroup.student_id === user.id
        );
        const studentGroup = studentGroups.find(
          (studentGroup) =>
            studentGroup.id === studentInStudentGroup?.student_group_id
        );
        const studyProgram = studyPrograms.find(
          (studyProgram) => studyProgram.id === user.detail.study_program_id
        );
        return {
          ...user,
          bills: filteredBills,
          student_group: studentGroup,
          study_program: studyProgram,
        };
      });
    },
  });
}
export type UserWithBillsAndStudentGroupAndStudyProgram = NonNullable<
  ReturnType<typeof useUsersWithBillsAndStudentGroupAndStudyProgram>["data"]
>[number];

export const currentUserQueryOptions = queryOptions({
  queryKey: ["users", "current-user"],
  async queryFn() {
    return (await UsersAPI.getCurrentUser()).data.data;
  },
});
