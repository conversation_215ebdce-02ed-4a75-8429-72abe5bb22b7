import UsersAPI from "@sisva/api/users";
import type { User } from "@sisva/types/apiTypes";
import type {
  CreateStudentForm,
  UpdateStudentForm,
} from "@sisva/types/formTypes";
import type { QueryClient } from "@tanstack/react-query";
import { queryOptions, useQuery } from "@tanstack/react-query";
import { useMutation } from "@tanstack/react-query";
import { useQueryClient } from "@tanstack/react-query";

import { useClass, useClasses } from "../academic/useClasses";
import {
  useStudentGroups,
  useStudentInStudentGroups,
} from "../academic/useStudentGroups";
import { useStudyPrograms } from "../academic/useStudyPrograms";
import {
  useStudentAttendances,
  useStudentClassAttendancesByClassId,
} from "../attendance/useAttendance";
import { useScoresByClassId } from "../classroom/useScores";
import { useTasks } from "../classroom/useTasks";
import { invalidateUser, useUser } from "./useUsers";

export const studentsQeuryOptions = queryOptions({
  queryKey: ["students", "users"],
  queryFn: async () =>
    (await UsersAPI.getAllUsers("student")).data.data.filter(
      (student: User) => student.status === "active"
    ),
});

export function useStudents() {
  return useQuery({
    ...studentsQeuryOptions,
  });
}

export function useUpdateStudentsGuardian({
  onSuccess,
  onError,
}: {
  onSuccess?: () => void;
  onError?: () => void;
} = {}) {
  const queryClient = useQueryClient();

  return useMutation({
    async mutationFn({
      guardian_id,
      student_ids,
    }: {
      guardian_id: string;
      student_ids: string[];
    }) {
      const payload = {
        detail: {
          guardian_id: guardian_id,
        },
      };
      for (const student_id of student_ids) {
        await UsersAPI.updateUserById(payload, student_id);
      }
      return student_ids;
    },
    onSuccess(student_ids) {
      onSuccess?.();
      for (const student_id of student_ids) {
        invalidateUser({
          user_id: student_id,
          queryClient: queryClient,
        });
      }
      invalidateStudents(queryClient);
    },
    onError() {
      onError?.();
    },
  });
}

export function invalidateStudents(queryClient: QueryClient) {
  return queryClient.invalidateQueries({
    queryKey: ["students", "users"],
  });
}

export function useCreateStudent({
  onSuccess,
  onError,
}: {
  onSuccess?: () => void;
  onError?: () => void;
} = {}) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({ name, password }: CreateStudentForm) => {
      const payload = {
        user: {
          nik: "",
          name: name,
          type: "student",
          roles: ["student"],
          profile_image_uri: "",
          detail: {
            json_text: "",
          },
          permissions: [],
        },
        password: password,
      };

      await UsersAPI.createUser(payload);
    },
    onSuccess() {
      invalidateStudents(queryClient);
      onSuccess?.();
    },
    onError() {
      onError?.();
    },
  });
}

export function useUpdateStudent(
  student_id: string,
  {
    onSuccess,
    onError,
  }: {
    onSuccess?: () => void;
    onError?: () => void;
  } = {}
) {
  const { data: student } = useUser(student_id);
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({
      name,
      address,
      email,
      gender,
      nik,
      phone,
      profile_image_uri,
      nationality,
      religion,

      study_program_id,
      grade,
    }: UpdateStudentForm) => {
      if (!student) throw new Error("User not found");

      const oldJSONText = (() => {
        try {
          return JSON.parse(student!.detail.json_text);
        } catch (error) {
          return {};
        }
      })();

      const payload = {
        nik: nik ?? "",
        name: name,
        detail: {
          json_text: JSON.stringify({
            ...oldJSONText,
            email: email,
            phone: phone ?? "",
            gender: gender,
            religion: religion,
            nationality: nationality,
            address: address,
          }),
          study_program_id: study_program_id,
          grade: grade,
        },
        profile_image_uri: profile_image_uri,
      };

      await UsersAPI.updateUserById(payload, student_id);
    },
    onSuccess() {
      invalidateStudents(queryClient);
      invalidateUser({ queryClient, user_id: student_id });
      onSuccess?.();
    },
    onError() {
      onError?.();
    },
  });
}

export function useDeleteStudent({
  onSuccess,
  onError,
}: {
  onSuccess?: () => void;
  onError?: () => void;
} = {}) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (student_id: string) => {
      const payload = {
        status: "deleted",
      };

      await UsersAPI.updateUserById(payload, student_id);
    },
    onSuccess() {
      invalidateStudents(queryClient);
      onSuccess?.();
    },
    onError() {
      onError?.();
    },
  });
}

export function useStudentsWithoutStudentGroup() {
  const { data: studentInStudengroups = [], isLoading: L1 } =
    useStudentInStudentGroups();
  const student_ids = studentInStudengroups.map(
    (student) => student.student_id
  );

  return useQuery({
    ...studentsQeuryOptions,
    select(students) {
      return students.filter((student) => !student_ids.includes(student.id));
    },
    enabled: !L1,
  });
}

export function useStudentsWithStudentGroup() {
  const { data: studentInStudengroups = [], isLoading: L1 } =
    useStudentInStudentGroups();
  const { data: studentGroups = [], isLoading: L2 } = useStudentGroups();
  return useQuery({
    ...studentsQeuryOptions,
    select(students) {
      return students.map((student) => {
        const studentGroupId = studentInStudengroups.find(
          (item) => item.student_id === student.id
        )?.student_group_id;
        const studentGroup = studentGroups.find(
          (item) => item.id === studentGroupId
        );
        return {
          ...student,
          student_group: studentGroup,
        };
      });
    },
    enabled: !L1 && !L2,
  });
}

export type StudentWithStudentGroup = NonNullable<
  ReturnType<typeof useStudentsWithStudentGroup>["data"]
>[number];

export function useStudentsWithStudentGroupAndAttendance({
  date_id,
}: {
  date_id: number | string;
}) {
  const { data: studentInStudengroups = [], isLoading: L1 } =
    useStudentInStudentGroups();
  const { data: studentGroups = [], isLoading: L2 } = useStudentGroups();
  const { data: studentAttendances = [], isLoading: L3 } =
    useStudentAttendances({ date_id });

  return useQuery({
    ...studentsQeuryOptions,
    select(students) {
      return students.map((student) => {
        const studentGroupId = studentInStudengroups.find(
          (item) => item.student_id === student.id
        )?.student_group_id;
        const studentGroup = studentGroups.find(
          (item) => item.id === studentGroupId
        );
        return {
          ...student,
          student_group: studentGroup,
          attendance: studentAttendances.find(
            (attendance) => attendance.student_id === student.id
          ),
        };
      });
    },
    enabled: !L1 && !L2 && !L3,
  });
}

export type StudentWithStudentGroupAndAttendance = NonNullable<
  ReturnType<typeof useStudentsWithStudentGroupAndAttendance>["data"]
>[number];

export function useStudentsWithStudentGroupAndClassAttendance({
  date_id,
  class_id,
}: {
  date_id: number | string;
  class_id: number | undefined | null;
}) {
  const { data: studentInStudengroups = [], isLoading: L1 } =
    useStudentInStudentGroups();
  const { data: studentGroups = [], isLoading: L2 } = useStudentGroups();
  const { data: studentClassAttendances = [], isLoading: L3 } =
    useStudentClassAttendancesByClassId({ date_id, class_id });
  const { data: class_, isLoading: L4 } = useClass(class_id);

  return useQuery({
    ...studentsQeuryOptions,
    select(students) {
      return students
        .map((student) => {
          const studentGroupId = studentInStudengroups.find(
            (item) => item.student_id === student.id
          )?.student_group_id;
          const studentGroup = studentGroups.find(
            (item) => item.id === studentGroupId
          );
          return {
            ...student,
            student_group: studentGroup,
          };
        })
        .filter((student) => {
          return (
            class_ && student.student_group?.id === class_?.student_group_id
          );
        })
        .map((student) => {
          return {
            ...student,
            class_attendance: studentClassAttendances.find(
              (item) =>
                item.date_id === Number(date_id) &&
                item.class_id === class_id &&
                item.student_id === student.id
            ),
            class_,
          };
        });
    },
    enabled: !L1 && !L2 && !L3 && !L4,
  });
}

export type StudentWithStudentGroupAndClassAttendance = NonNullable<
  ReturnType<typeof useStudentsWithStudentGroupAndClassAttendance>["data"]
>[number];

export function useStudentsWithStudyProgram() {
  const { data: studyPrograms = [], isLoading: L1 } = useStudyPrograms();
  return useQuery({
    ...studentsQeuryOptions,
    select(students) {
      return students.map((student) => {
        return {
          ...student,
          study_program: studyPrograms.find(
            (studyProgram) =>
              studyProgram.id === student.detail.study_program_id
          ),
        };
      });
    },
    enabled: !L1,
  });
}

export type StudentWithStudyProgram = NonNullable<
  ReturnType<typeof useStudentsWithStudyProgram>["data"]
>[number];

export function useStudentsByClassId(class_id: number | undefined) {
  const { data: class_, isLoading: L1 } = useClass(class_id);
  const { data: studentInStudentGroups = [], isLoading: L2 } =
    useStudentInStudentGroups();
  const filteredStudentInStudentGroups = studentInStudentGroups.filter(
    (item) => item.student_group_id === class_?.student_group_id
  );
  const filteredStudentIds = filteredStudentInStudentGroups.map(
    (item) => item.student_id
  );
  const { data: tasks = [] } = useTasks({
    class_id,
  });
  const { data: scores = [] } = useScoresByClassId(class_id);

  return useQuery({
    ...studentsQeuryOptions,
    select(students) {
      return students
        .filter((student) => filteredStudentIds.includes(student.id))
        .map((student) => ({
          ...student,
          tasks: tasks.map((task) => ({
            ...task,
            score: scores.find(
              (score) =>
                score.student_id === student.id && score.task_id === task.id
            ),
          })),
        }));
    },
    enabled: !!class_id && !L1 && !L2,
  });
}

export function useStudentsByClassIds(class_ids: number[]) {
  const { data: classes = [], isLoading: L1 } = useClasses();
  const { data: studentInStudentGroups = [], isLoading: L2 } =
    useStudentInStudentGroups();

  const filteredClasses = classes.filter((class_) =>
    class_ids.includes(class_.id)
  );
  const studentGroupIds = filteredClasses.map(
    (class_) => class_.student_group_id
  );

  const filteredStudentInStudentGroups = studentInStudentGroups.filter((item) =>
    studentGroupIds.includes(item.student_group_id)
  );
  const filteredStudentIds = filteredStudentInStudentGroups.map(
    (item) => item.student_id
  );

  return useQuery({
    ...studentsQeuryOptions,
    select(students) {
      return students
        .filter((student) => filteredStudentIds.includes(student.id))
        .map((student) => ({
          ...student,
        }));
    },
    enabled: !L1 && !L2,
  });
}
