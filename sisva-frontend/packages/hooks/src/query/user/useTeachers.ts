import UsersAPI from "@sisva/api/users";
import type { User } from "@sisva/types/apiTypes";
import { useQuery } from "@tanstack/react-query";

export const useTeachers = () => {
  return useQuery<User[]>({
    queryKey: ["teachers", "users"],
    queryFn: async () =>
      (await UsersAPI.getAllUsers("teacher")).data.data.filter(
        (student: User) => student.status === "active"
      ),
  });
};
