import AcademicAPI from "@sisva/api/academic";
import type { ExtracurricularMember } from "@sisva/types/apiTypes";
import type { QueryClient } from "@tanstack/react-query";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

export const useExtracurricularMembers = () => {
  return useQuery<ExtracurricularMember[]>({
    queryKey: ["extracurricular-members"],
    queryFn: async () => (await AcademicAPI.getAllExtraStudent()).data.data,
  });
};

export function invalidateExtracurricularMembers(queryClient: QueryClient) {
  return queryClient.invalidateQueries({
    queryKey: ["extracurricular-members"],
  });
}

export function useAddExtracurricularMember({
  onSuccess,
  onError,
}: {
  onSuccess?: () => void;
  onError?: () => void;
} = {}) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({
      extracurricular_id,
      student_id,
    }: {
      extracurricular_id: number;
      student_id: string;
    }) => {
      await AcademicAPI.createStudentInExtra(extracurricular_id, {
        student_id: student_id,
      });
    },
    onSuccess() {
      invalidateExtracurricularMembers(queryClient);
      onSuccess?.();
    },
    onError() {
      onError?.();
    },
  });
}

export function useEditExtracurricularMember({
  onSuccess,
  onError,
}: {
  onSuccess?: () => void;
  onError?: () => void;
} = {}) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({
      old_extracurricular_id,
      new_extracurricular_id,
      student_id,
    }: {
      old_extracurricular_id: number;
      new_extracurricular_id: number;
      student_id: string;
    }) => {
      await AcademicAPI.deleteStudentInExtra(old_extracurricular_id, {
        student_id,
      });
      await AcademicAPI.createStudentInExtra(new_extracurricular_id, {
        student_id,
      });
    },
    onSuccess() {
      invalidateExtracurricularMembers(queryClient);
      onSuccess?.();
    },
    onError() {
      onError?.();
    },
  });
}

export function useDeleteExtracurricularMember({
  onSuccess,
  onError,
}: {
  onSuccess?: () => void;
  onError?: () => void;
} = {}) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({
      extracurricular_id,
      student_id,
    }: {
      extracurricular_id: number;
      student_id: string;
    }) => {
      await AcademicAPI.deleteStudentInExtra(extracurricular_id, {
        student_id,
      });
    },
    onSuccess() {
      invalidateExtracurricularMembers(queryClient);
      onSuccess?.();
    },
    onError() {
      onError?.();
    },
  });
}
