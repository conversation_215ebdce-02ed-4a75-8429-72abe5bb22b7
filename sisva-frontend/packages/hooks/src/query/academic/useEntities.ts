import AcademicAPI, { type CreateEntityPayload } from "@sisva/api/academic";
import type { Entity } from "@sisva/types/apiTypes";
import type { QueryClient } from "@tanstack/react-query";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

export function useEntities() {
  return useQuery({
    queryKey: ["entities"],
    queryFn: async () => {
      return (await AcademicAPI.getEntities()).data.data ?? [];
    },
  });
}

export function invalidateEntities(queryClient: QueryClient) {
  queryClient.invalidateQueries({
    queryKey: ["entities"],
  });
}

export function useCreateEntity() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn(payload: CreateEntityPayload) {
      return AcademicAPI.createEntity(payload);
    },
    onSuccess() {
      invalidateEntities(queryClient);
    },
  });
}

export function useUpdateEntity() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn(payload: Entity) {
      return AcademicAPI.updateEntity(payload);
    },
    onSuccess() {
      invalidateEntities(queryClient);
    },
  });
}

export function useDeleteEntity() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn(entity_id: number) {
      return AcademicAPI.deleteEntity(entity_id);
    },
    onSuccess() {
      invalidateEntities(queryClient);
    },
  });
}
