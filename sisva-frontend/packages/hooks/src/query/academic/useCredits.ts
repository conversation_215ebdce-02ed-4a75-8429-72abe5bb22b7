import AcademicAPI from "@sisva/api/academic";
import type { Credit } from "@sisva/types/apiTypes";
import type { QueryClient } from "@tanstack/react-query";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

export const useCredit = <T = Credit>({
  select,
  enabled,
}: {
  select?: (credit: Credit) => T;
  enabled?: boolean;
} = {}) => {
  return useQuery<Credit, unknown, T>({
    queryKey: ["credit"],
    async queryFn() {
      return (await AcademicAPI.getCredit()).data.data;
    },
    select,
    enabled,
  });
};

export function invalidateCredit(queryClient: QueryClient) {
  queryClient.invalidateQueries({
    queryKey: ["credit"],
  });
}

export function useUpdateCredit({
  onSuccess,
  onError,
}: {
  onSuccess?: () => void;
  onError?: () => void;
} = {}) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (payload: { duration_minutes: number }) => {
      await AcademicAPI.createCredit(payload);
    },
    onSuccess() {
      invalidateCredit(queryClient);
      onSuccess?.();
    },
    onError() {
      onError?.();
    },
  });
}
