import AcademicAPI from "@sisva/api/academic";
import type { Curriculum } from "@sisva/types/apiTypes";
import type { QueryClient } from "@tanstack/react-query";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

export function useCurriculums<T = Curriculum[]>({
  select,
  enabled,
}: {
  select?: (curriulums: Curriculum[]) => T;
  enabled?: boolean;
} = {}) {
  return useQuery<Curriculum[], unknown, T>({
    queryKey: ["curriculums"],
    queryFn: async () => (await AcademicAPI.getAllCurriculum()).data.data,
    select,
    enabled,
  });
}

export function useCurriculum(curriculum_id: number | undefined) {
  return useCurriculums({
    select(curriulums) {
      return curriulums.find((curriculum) => curriculum.id === curriculum_id);
    },
    enabled: !!curriculum_id,
  });
}

export function invalidateCurriculums(queryClient: QueryClient) {
  queryClient.invalidateQueries({
    queryKey: ["curriculums"],
  });
}

export function useCreateCurriculum({
  onSuccess,
  onError,
}: {
  onSuccess?: () => void;
  onError?: () => void;
} = {}) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({
      name,
      description,
      passing_grade,
    }: {
      name: string;
      description?: string;
      passing_grade: number;
    }) => {
      await AcademicAPI.createCurriculum({ name, description, passing_grade });
    },
    onSuccess() {
      invalidateCurriculums(queryClient);
      onSuccess?.();
    },
    onError() {
      onError?.();
    },
  });
}

export function useUpdateCurriculum({
  onSuccess,
  onError,
}: {
  onSuccess?: () => void;
  onError?: () => void;
} = {}) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({
      curriculum_id,
      name,
      description,
      passing_grade,
    }: {
      curriculum_id: number;
      name: string;
      description?: string;
      passing_grade: number;
    }) => {
      await AcademicAPI.updateCurriculum(
        { name, description, passing_grade },
        curriculum_id
      );
    },
    onSuccess() {
      invalidateCurriculums(queryClient);
      onSuccess?.();
    },
    onError() {
      onError?.();
    },
  });
}

export function useDeleteCurriculum({
  onSuccess,
  onError,
}: {
  onSuccess?: () => void;
  onError?: () => void;
} = {}) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (curriculum_id: number) => {
      await AcademicAPI.deleteCurriculum(curriculum_id);
    },
    onSuccess() {
      invalidateCurriculums(queryClient);
      onSuccess?.();
    },
    onError() {
      onError?.();
    },
  });
}
