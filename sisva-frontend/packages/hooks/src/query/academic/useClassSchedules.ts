import AcademicAPI from "@sisva/api/academic";
import type { ClassSchedule } from "@sisva/types/apiTypes";
import {
  checkScheduleConflict,
  type ScheduleConflictError,
} from "@sisva/utils";
import type { QueryClient } from "@tanstack/react-query";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

import {
  useClass,
  useClassWithSubjectAndTeacherAndStudentGroup,
  useStudentGroupsClasses,
  useStudentsClasses,
  useTeachersClasses,
} from "./useClasses";
import { useSchoolSchedule } from "./useSchoolSchedules";
import { useStudentGroup } from "./useStudentGroups";

export const useClassSchedules = <T = ClassSchedule[]>(
  period_id: string | number | undefined | null,
  {
    select,
    enabled,
  }: {
    select?: (classSchedules: ClassSchedule[]) => T;
    enabled?: boolean;
  } = {
    enabled: true,
  }
) => {
  return useQuery<ClassSchedule[], unknown, T>({
    queryKey: ["class-schedules", period_id],
    queryFn: async () =>
      (
        await AcademicAPI.getAllClassSchedules({
          period_id,
        })
      ).data.data,
    enabled: !!period_id && enabled,
    select,
  });
};

export function useClassSchedule({
  period_id,
  class_schedule_id,
}: {
  period_id: number | undefined | null;
  class_schedule_id: number | undefined | null;
}) {
  return useClassSchedules(period_id, {
    select(classSchedules) {
      return classSchedules.find((item) => item.id === class_schedule_id);
    },
    enabled: !!class_schedule_id && !!period_id,
  });
}

export function useDeleteClassSchedules({
  onSuccess,
  onError,
}: {
  onSuccess?: () => void;
  onError?: () => void;
} = {}) {
  const queryClient = useQueryClient();
  return useMutation({
    async mutationFn(class_schedule_id: number) {
      return await AcademicAPI.deleteClassSchedule(class_schedule_id);
    },
    onSuccess() {
      onSuccess?.();
      invalidateClassSchedules(queryClient);
    },
    onError() {
      onError?.();
    },
  });
}

export function useCreateClassSchedule({
  class_id,
  school_schedule_id,
  onSuccess,
  onError,
}: {
  class_id?: number;
  school_schedule_id?: number;
  onSuccess?: () => void;
  onError?: (e: Error | ScheduleConflictError) => void;
} = {}) {
  const queryClient = useQueryClient();
  const { data: class_ } =
    useClassWithSubjectAndTeacherAndStudentGroup(class_id);
  const { data: schoolSchedule } = useSchoolSchedule({
    school_schedule_id,
    period_id: class_?.student_group?.period_id,
  });

  return useMutation({
    async mutationFn(payload: {
      class_id: number;
      school_schedule_id: number;
      start_time: string;
      end_time: string;
    }) {
      if (!class_ || !schoolSchedule)
        throw new Error("class or school schedule not found or still fetching");

      const period_id = class_.student_group?.period_id;
      if (!period_id) throw new Error("period_id undefined");

      const { error } = await checkScheduleConflict({
        schedule: {
          start_time: payload.start_time,
          end_time: payload.end_time,
          id: crypto.randomUUID(),
          student_group_id: class_.student_group_id,
          teacher_id: class_.teacher_id,
          period_id: period_id,
          day: schoolSchedule.day,
        },
      });

      if (error) throw error;
      return await AcademicAPI.createClassSchedule(payload);
    },
    onSuccess() {
      onSuccess?.();
      invalidateClassSchedules(queryClient);
    },
    onError(e) {
      onError?.(e);
    },
  });
}

export function useUpdateClassSchedule({
  class_id,
  school_schedule_id,
  onSuccess,
  onError,
}: {
  class_id?: number;
  school_schedule_id?: number;
  onSuccess?: () => void;
  onError?: (e: Error | ScheduleConflictError) => void;
} = {}) {
  const queryClient = useQueryClient();
  const { data: class_ } =
    useClassWithSubjectAndTeacherAndStudentGroup(class_id);
  const { data: schoolSchedule } = useSchoolSchedule({
    school_schedule_id,
    period_id: class_?.student_group?.period_id,
  });

  return useMutation({
    async mutationFn(payload: {
      class_schedule_id: number;
      class_id: number;
      school_schedule_id: number;
      start_time: string;
      end_time: string;
    }) {
      if (!class_ || !schoolSchedule)
        throw new Error("class or school schedule not found or still fetching");

      const period_id = class_.student_group?.period_id;
      if (!period_id) throw new Error("period_id undefined");

      const { error } = await checkScheduleConflict({
        schedule: {
          start_time: payload.start_time,
          end_time: payload.end_time,
          id: crypto.randomUUID(),
          student_group_id: class_.student_group_id,
          teacher_id: class_.teacher_id,
          period_id: period_id,
          day: schoolSchedule.day,
          class_schedule_id: payload.class_schedule_id,
        },
      });

      if (error) throw error;
      return await AcademicAPI.updateClassSchedule(
        payload.class_schedule_id,
        payload
      );
    },
    onSuccess() {
      onSuccess?.();
      invalidateClassSchedules(queryClient);
    },
    onError(e) {
      onError?.(e);
    },
  });
}

export const invalidateClassSchedules = (queryClient: QueryClient) => {
  queryClient.invalidateQueries({
    queryKey: ["class-schedules"],
  });
};

export const useStudentsClassSchedules = ({
  student_id,
  period_id,
}: {
  student_id: string | null | undefined;
  period_id: number | null | undefined;
}) => {
  const { data: studentsClasses = [], isLoading } =
    useStudentsClasses(student_id);

  return useClassSchedules(period_id, {
    select(classSchedules) {
      return classSchedules.filter((classSchedule) =>
        studentsClasses.some((class_) => class_.id === classSchedule.class_id)
      );
    },
    enabled: !isLoading && !!student_id && !!period_id,
  });
};

export const useTeachersClassSchedules = ({
  teacher_id,
  period_id,
}: {
  teacher_id: string | null | undefined;
  period_id: number | null | undefined;
}) => {
  const { data: teachersClasses = [], isLoading } =
    useTeachersClasses(teacher_id);

  return useClassSchedules(period_id, {
    select(classSchedules) {
      return classSchedules.filter((classSchedule) =>
        teachersClasses.some((class_) => class_.id === classSchedule.class_id)
      );
    },
    enabled: !isLoading && !!teacher_id && !!period_id,
  });
};

export const useStudentGroupsClassSchedules = ({
  student_group_id,
}: {
  student_group_id: number | null | undefined;
}) => {
  const { data: studentGroup, isLoading: L1 } =
    useStudentGroup(student_group_id);
  const { data: studentGroupsClasses = [], isLoading: L2 } =
    useStudentGroupsClasses(student_group_id);

  return useClassSchedules(studentGroup?.period_id, {
    select(classSchedules) {
      return classSchedules.filter((classSchedule) =>
        studentGroupsClasses.some(
          (class_) => class_.id === classSchedule.class_id
        )
      );
    },
    enabled: !L1 && !L2 && !!student_group_id,
  });
};

export function useClassSchedulesByClassId(class_id: number | undefined) {
  const { data: class_, isLoading: L1 } = useClass(class_id);
  const { data: studentGroup, isLoading: L2 } = useStudentGroup(
    class_?.student_group_id
  );
  return useClassSchedules(studentGroup?.period_id, {
    select(classSchedules) {
      return classSchedules.filter((schedule) => {
        return schedule.class_id === class_id;
      });
    },
    enabled: !L1 && !L2,
  });
}
