import AcademicAPI from "@sisva/api/academic";
import type { Subject } from "@sisva/types/apiTypes";
import type { QueryClient } from "@tanstack/react-query";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

import { useClass } from "./useClasses";

export const useSubjects = () => {
  return useQuery<Subject[]>({
    queryKey: ["subjects"],
    queryFn: async () => (await AcademicAPI.getAllSubject()).data.data,
  });
};

export const useSubject = (subject_id: number | string | undefined) => {
  return useQuery<Subject>({
    queryKey: ["subjects", { subject_id }],
    queryFn: async () =>
      (await AcademicAPI.getDetailSubject(subject_id)).data.data,
    enabled: !!subject_id,
  });
};

export function useSubjectByClassId(class_id: number | undefined) {
  const { data: class_ } = useClass(class_id);
  return useSubject(class_?.subject_id);
}

export function invalidateSubjects(queryClient: QueryClient) {
  queryClient.invalidateQueries({
    queryKey: ["subjects"],
  });
}

export function useCreateSubject({
  onSuccess,
  onError,
}: {
  onSuccess?: () => void;
  onError?: () => void;
} = {}) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({
      name,
      type,
      study_program_id,
      curriculum_id,
    }: {
      name: string;
      type: string;
      study_program_id: number;
      curriculum_id: number;
    }) => {
      await AcademicAPI.createSubject({
        name,
        type,
        study_program_id,
        curriculum_id,
      });
    },
    onSuccess() {
      invalidateSubjects(queryClient);
      onSuccess?.();
    },
    onError() {
      onError?.();
    },
  });
}

export function useUpdateSubject({
  onSuccess,
  onError,
}: {
  onSuccess?: () => void;
  onError?: () => void;
} = {}) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({
      subject_id,
      name,
      type,
      study_program_id,
      curriculum_id,
    }: {
      subject_id: number;
      name: string;
      type: string;
      study_program_id: number;
      curriculum_id: number;
    }) => {
      await AcademicAPI.updateSubject(
        {
          name,
          type,
          study_program_id,
          curriculum_id,
        },
        subject_id
      );
    },
    onSuccess() {
      invalidateSubjects(queryClient);
      onSuccess?.();
    },
    onError() {
      onError?.();
    },
  });
}

export function useDeleteSubject({
  onSuccess,
  onError,
}: {
  onSuccess?: () => void;
  onError?: () => void;
} = {}) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (subject_id: number) => {
      await AcademicAPI.deleteSubject(subject_id);
    },
    onSuccess() {
      invalidateSubjects(queryClient);
      onSuccess?.();
    },
    onError() {
      onError?.();
    },
  });
}
