import AcademicAPI from "@sisva/api/academic";
import type {
  StudentGroup,
  StudentInStudentGroup,
} from "@sisva/types/apiTypes";
import type { StudentGroupSchemaForm } from "@sisva/types/formTypes";
import { onlyUnique } from "@sisva/utils";
import type { QueryClient } from "@tanstack/react-query";
import {
  queryOptions,
  useMutation,
  useQuery,
  useQueryClient,
} from "@tanstack/react-query";

import { studentsQeuryOptions, useStudents } from "../user/useStudents";
import { useClass } from "./useClasses";

export function useStudentGroups<T = StudentGroup[]>({
  select,
  enabled,
}: {
  select?: (studentGroups: StudentGroup[]) => T;
  enabled?: boolean;
} = {}) {
  return useQuery<StudentGroup[], unknown, T>({
    queryKey: ["student-groups"],
    queryFn: async () => (await AcademicAPI.getAllStudentGroup()).data.data,
    select,
    enabled,
  });
}

export const useStudentGroup = (
  student_group_id: number | undefined | null
) => {
  return useStudentGroups({
    select(studentGroups) {
      return studentGroups.find(
        (studentGroup) => studentGroup.id === student_group_id
      );
    },
    enabled: !!student_group_id,
  });
};

export const useAddStudentToStudentGroup = (queryClient: QueryClient) => {
  return useMutation({
    mutationFn: async ({
      studentGroupId,
      studentId,
    }: {
      studentGroupId: number | number;
      studentId: string;
    }) => {
      await AcademicAPI.insertStudentToStudentGroup(studentGroupId, {
        student_id: studentId,
      });
    },
    onSuccess: () => {
      invalidateStudentInStudentGroups(queryClient);
    },
    onError: (error) => console.log(error),
  });
};

export const studentInStudentGroupsQueryOptions = queryOptions({
  queryKey: ["student-in-student-groups"],
  async queryFn() {
    return (await AcademicAPI.getStudentsInStudentGroup()).data.data;
  },
});

export const useStudentInStudentGroups = <T = StudentInStudentGroup[]>({
  select,
  enabled,
}: {
  select?: (studentInStudentGroups: StudentInStudentGroup[]) => T;
  enabled?: boolean;
} = {}) => {
  return useQuery({
    ...studentInStudentGroupsQueryOptions,
    select,
    enabled,
  });
};

export function useCreateStudentGroup({
  onSuccess,
  onError,
}: {
  onSuccess?: () => void;
  onError?: () => void;
} = {}) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({
      name,
      type,
      study_program_id,
      period_id,
      grade,
      homeroom_teacher_id,
    }: StudentGroupSchemaForm) => {
      await AcademicAPI.createStudentGroup({
        name,
        type,
        period_id,
        study_program_id,
        grade,
        detail: {
          homeroom_teacher_id,
        },
      });
    },
    onSuccess() {
      invalidateStudentInStudentGroups(queryClient);
      onSuccess?.();
    },
    onError() {
      onError?.();
    },
  });
}

export function useUpdateStudentGroup({
  onSuccess,
  onError,
}: {
  onSuccess?: () => void;
  onError?: () => void;
} = {}) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({
      name,
      type,
      study_program_id,
      period_id,
      grade,
      homeroom_teacher_id,
      student_group_id,
    }: StudentGroupSchemaForm & {
      student_group_id: number;
    }) => {
      await AcademicAPI.updateStudentGroup(student_group_id, {
        name,
        type,
        period_id,
        study_program_id,
        grade,
        detail: {
          homeroom_teacher_id,
        },
      });
    },
    onSuccess() {
      invalidateStudentGroups(queryClient);
      invalidateStudentInStudentGroups(queryClient);
      onSuccess?.();
    },
    onError() {
      onError?.();
    },
  });
}

export function useDeleteStudentGroup({
  onSuccess,
  onError,
}: {
  onSuccess?: () => void;
  onError?: () => void;
} = {}) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (student_group_id: number) => {
      await AcademicAPI.removeStudentGroup(student_group_id);
    },
    onSuccess() {
      invalidateStudentGroups(queryClient);
      invalidateStudentInStudentGroups(queryClient);
      onSuccess?.();
    },
    onError() {
      onError?.();
    },
  });
}

export const useUpdateStudentInStudentGroup = (queryClient: QueryClient) => {
  return useMutation({
    mutationFn: async ({
      oldStudentGroupId,
      newStudentGroupId,
      oldStudentId,
      studentId,
    }: {
      oldStudentGroupId: number | string;
      newStudentGroupId: number | string;
      oldStudentId: string;
      studentId: string;
    }) => {
      await AcademicAPI.removeStudentFromGroup(oldStudentGroupId, {
        student_id: oldStudentId,
      });
      await AcademicAPI.insertStudentToStudentGroup(newStudentGroupId, {
        student_id: studentId,
      });
    },
    onSuccess: () => {
      invalidateStudentInStudentGroups(queryClient);
    },
    onError: (error) => console.log(error),
  });
};

export function useUpdateStudentGroupsStudents({
  onSuccess,
  onError,
  student_group_id,
}: {
  onSuccess?: ({
    isError,
    deletedStudentCount,
    insertedStudentCount,
    studentToBeUpdatedCount,
  }: {
    isError: boolean;
    deletedStudentCount: number;
    insertedStudentCount: number;
    studentToBeUpdatedCount: number;
  }) => void;
  onError?: () => void;
  student_group_id?: number;
} = {}) {
  const queryClient = useQueryClient();
  const { data: students = [], isLoading: L1 } =
    useStudentGroupsStudents(student_group_id);
  return useMutation({
    mutationFn: async ({ student_ids }: { student_ids: string[] }) => {
      if (L1) throw new Error("students still loading");
      const student_ids_to_delete = students.map((student) => student.id);
      let isError = false;
      let deletedStudentCount = 0;
      let insertedStudentCount = 0;
      try {
        for (const student_id of student_ids_to_delete) {
          await AcademicAPI.removeStudentFromGroup(student_group_id, {
            student_id,
          });
          deletedStudentCount += 1;
        }
        for (const student_id of student_ids) {
          await AcademicAPI.insertStudentToStudentGroup(student_group_id, {
            student_id,
          });
          insertedStudentCount += 1;
        }
      } catch (error) {
        isError = true;
      }

      return {
        isError,
        deletedStudentCount,
        insertedStudentCount,
        studentToBeUpdatedCount: student_ids_to_delete
          .concat(student_ids)
          .filter(onlyUnique).length,
      };
    },
    onSuccess(data) {
      invalidateStudentInStudentGroups(queryClient);
      onSuccess?.(data);
    },
    onError() {
      onError?.();
    },
  });
}

export const invalidateStudentInStudentGroups = (queryClient: QueryClient) => {
  queryClient.invalidateQueries({
    queryKey: ["student-in-student-groups"],
  });
};

export const invalidateStudentGroups = (queryClient: QueryClient) => {
  queryClient.invalidateQueries({
    queryKey: ["student-groups"],
  });
};

export const useStudentsStudentGroup = (
  student_id: string | undefined | null
) => {
  const { data: studentGroups = [], isLoading: L1 } = useStudentGroups();
  return useStudentInStudentGroups({
    select: (studentInStudentGroups) => {
      const stundentInStudentGroup = studentInStudentGroups.find(
        (studentInStudentGroup) =>
          studentInStudentGroup.student_id === student_id
      );
      return studentGroups.find(
        (studentGroup) =>
          studentGroup.id === stundentInStudentGroup?.student_group_id
      );
    },
    enabled: !L1 && !!student_id,
  });
};

export function useTeachersStudentGroups(teacher_id: string | undefined) {
  return useStudentGroups({
    select(studentGroups) {
      return studentGroups.filter(
        (studentGroup) => studentGroup.detail.homeroom_teacher_id === teacher_id
      );
    },
    enabled: !!teacher_id,
  });
}

export function useStudentGroupsStudents(student_group_id: number | undefined) {
  const { data: studentInStudentGroups = [], isLoading: L1 } =
    useStudentInStudentGroups();
  const filteredStudentInStudentGroups = studentInStudentGroups.filter(
    (item) => item.student_group_id === student_group_id
  );
  const filteredStudentIds = filteredStudentInStudentGroups.map(
    (item) => item.student_id
  );
  return useQuery({
    ...studentsQeuryOptions,
    select(students) {
      return students.filter((student) =>
        filteredStudentIds.includes(student.id)
      );
    },
    enabled: !L1 && !!student_group_id,
  });
}

export function useStudentGroupsWithStudents() {
  const { data: students = [], isLoading: L1 } = useStudents();
  const { data: studentInStudentGroups = [], isLoading: L2 } =
    useStudentInStudentGroups();

  return useStudentGroups({
    select(studentGroups) {
      return studentGroups.map((studentGroup) => {
        const filteredStudentInStudentGroups = studentInStudentGroups.filter(
          (item) => item.student_group_id === studentGroup.id
        );
        const studentIds = filteredStudentInStudentGroups.map(
          (item) => item.student_id
        );
        const filteredStudents = students.filter((student) =>
          studentIds.includes(student.id)
        );
        return {
          ...studentGroup,
          students: filteredStudents,
        };
      });
    },
    enabled: !L1 && !L2,
  });
}

export type StudentGroupWithStudents = NonNullable<
  ReturnType<typeof useStudentGroupsWithStudents>["data"]
>[number];

export function useStudentGroupWithStudents(
  student_group_id: number | undefined
) {
  const { data: students = [], isLoading: L1 } = useStudents();
  const { data: studentInStudentGroups = [], isLoading: L2 } =
    useStudentInStudentGroups();

  return useStudentGroups({
    select(studentGroups) {
      return studentGroups
        .map((studentGroup) => {
          const filteredStudentInStudentGroups = studentInStudentGroups.filter(
            (item) => item.student_group_id === studentGroup.id
          );
          const studentIds = filteredStudentInStudentGroups.map(
            (item) => item.student_id
          );
          const filteredStudents = students.filter((student) =>
            studentIds.includes(student.id)
          );
          return {
            ...studentGroup,
            students: filteredStudents,
          };
        })
        .find((studentGroup) => studentGroup.id === student_group_id);
    },
    enabled: !L1 && !L2 && !!student_group_id,
  });
}

export function useStudentInStudentGroupsWithStudentAndStudentGroup() {
  const { data: students = [], isLoading: L1 } = useStudents();
  const { data: studentGroups = [], isLoading: L2 } = useStudentGroups();
  return useStudentInStudentGroups({
    select(studentInStudentGroups) {
      return studentInStudentGroups.map((studentInStudentGroup) => {
        const student = students.find(
          (student) => student.id === studentInStudentGroup.student_id
        );
        const studentGroup = studentGroups.find(
          (studentGroup) =>
            studentGroup.id === studentInStudentGroup.student_group_id
        );
        return {
          ...studentInStudentGroup,
          student,
          student_group: studentGroup,
        };
      });
    },
    enabled: !L1 && !L2,
  });
}

export type StudentInStudentGroupsWithStudentAndStudentGroup = NonNullable<
  ReturnType<typeof useStudentInStudentGroupsWithStudentAndStudentGroup>["data"]
>[number];

export function useDeleteStudentInStudentGroup({
  onSuccess,
  onError,
}: {
  onSuccess?: () => void;
  onError?: () => void;
} = {}) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({
      student_group_id,
      student_id,
    }: {
      student_group_id: number;
      student_id: string;
    }) => {
      AcademicAPI.removeStudentFromGroup(student_group_id, {
        student_id,
      });
    },
    onSuccess() {
      invalidateStudentInStudentGroups(queryClient);
      onSuccess?.();
    },
    onError() {
      onError?.();
    },
  });
}

export function useStudentGroupByClassId(class_id: number | undefined) {
  const { data: class_ } = useClass(class_id);
  return useStudentGroup(class_?.student_group_id);
}
