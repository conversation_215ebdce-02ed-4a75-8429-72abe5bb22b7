import AcademicAPI from "@sisva/api/academic";
import type { NonLearningSchedules } from "@sisva/types/apiTypes";
import {
  checkScheduleConflict,
  type ScheduleConflictError,
} from "@sisva/utils";
import type { QueryClient } from "@tanstack/react-query";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

import { useStudentGroup, useStudentsStudentGroup } from "./useStudentGroups";

export const useNonLearningSchedules = <T = NonLearningSchedules[]>(
  period_id: string | number | undefined | null,
  {
    select,
    enabled,
  }: {
    select?: (nonLearningSchedules: NonLearningSchedules[]) => T;
    enabled?: boolean;
  } = {
    enabled: true,
  }
) => {
  return useQuery<NonLearningSchedules[], unknown, T>({
    queryKey: ["non-learning-schedules", period_id],
    queryFn: async () =>
      (
        await AcademicAPI.getAllNonLearningSchedules({
          period_id,
        })
      ).data.data,
    enabled: !!period_id && enabled,
    select,
  });
};

export function useNonLearningSchedule({
  period_id,
  non_learning_schedule_id,
}: {
  period_id: number | undefined | null;
  non_learning_schedule_id: number | undefined | null;
}) {
  return useNonLearningSchedules(period_id, {
    select(nonLearningSchedules) {
      return nonLearningSchedules.find(
        (item) => item.id === non_learning_schedule_id
      );
    },
    enabled: !!non_learning_schedule_id && !!period_id,
  });
}

export function useCreateNonLearningSchedule({
  onSuccess,
  onError,
}: {
  onSuccess?: () => void;
  onError?: (e: Error | ScheduleConflictError) => void;
} = {}) {
  const queryClient = useQueryClient();
  return useMutation({
    async mutationFn(payload: {
      name: string;
      school_schedule_id: number;
      start_time: string;
      end_time: string;
    }) {
      const { error } = await checkScheduleConflict({
        schedule: {
          school_schedule_id: payload.school_schedule_id,
          start_time: payload.start_time,
          end_time: payload.end_time,
          id: crypto.randomUUID(),
        },
      });

      if (error) throw error;
      return await AcademicAPI.createNonLearningSchedule(payload);
    },
    onSuccess() {
      onSuccess?.();
      invalidateNonLearningSchedules(queryClient);
    },
    onError(e) {
      onError?.(e);
    },
  });
}

export function useUpdateNonLearningSchedule({
  onSuccess,
  onError,
}: {
  onSuccess?: () => void;
  onError?: (e: Error | ScheduleConflictError) => void;
} = {}) {
  const queryClient = useQueryClient();
  return useMutation({
    async mutationFn(payload: {
      non_learning_schedule_id: number;
      name: string;
      school_schedule_id: number;
      start_time: string;
      end_time: string;
    }) {
      const { error } = await checkScheduleConflict({
        schedule: {
          school_schedule_id: payload.school_schedule_id,
          start_time: payload.start_time,
          end_time: payload.end_time,
          id: crypto.randomUUID(),
          non_learning_schedule_id: payload.non_learning_schedule_id,
        },
      });

      if (error) throw error;
      return await AcademicAPI.updateNonLearningSchedule(
        payload.non_learning_schedule_id,
        payload
      );
    },
    onSuccess() {
      onSuccess?.();
      invalidateNonLearningSchedules(queryClient);
    },
    onError(e) {
      onError?.(e);
    },
  });
}

// rename this to useDeleteNonLearningSchedule later
export function useDeleteNonLearningSchedules({
  onSuccess,
  onError,
}: {
  onSuccess?: () => void;
  onError?: () => void;
} = {}) {
  const queryClient = useQueryClient();
  return useMutation({
    async mutationFn(non_learning_schedule_id: number) {
      return await AcademicAPI.deleteNonLearningSchedule(
        non_learning_schedule_id
      );
    },
    onSuccess() {
      onSuccess?.();
      invalidateNonLearningSchedules(queryClient);
    },
    onError() {
      onError?.();
    },
  });
}

export const invalidateNonLearningSchedules = (queryClient: QueryClient) => {
  queryClient.invalidateQueries({
    queryKey: ["non-learning-schedules"],
  });
};

export const useStudentsNonLearningSchedules = ({
  student_id,
  period_id,
}: {
  student_id: string | null | undefined;
  period_id: number | null | undefined;
}) => {
  const { data: studentsStudentGroup, isLoading } =
    useStudentsStudentGroup(student_id);

  return useNonLearningSchedules(period_id, {
    select(nonLearningSchedules) {
      return nonLearningSchedules.filter(
        (nonLearningSchedule) =>
          nonLearningSchedule.study_program_id ===
            studentsStudentGroup?.study_program_id &&
          nonLearningSchedule.grade === studentsStudentGroup?.grade
      );
    },
    enabled: !isLoading && !!student_id && !!period_id,
  });
};

export const useStudentGroupsNonLearningSchedules = ({
  student_group_id,
}: {
  student_group_id: number | null | undefined;
}) => {
  const { data: studentGroup, isLoading } = useStudentGroup(student_group_id);

  return useNonLearningSchedules(studentGroup?.period_id, {
    select(nonLearningSchedules) {
      return nonLearningSchedules.filter(
        (nonLearningSchedule) =>
          nonLearningSchedule.study_program_id ===
            studentGroup?.study_program_id &&
          nonLearningSchedule.grade === studentGroup?.grade
      );
    },
    enabled: !isLoading && !!student_group_id,
  });
};
