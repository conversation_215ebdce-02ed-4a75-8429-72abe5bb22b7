import AcademicAPI from "@sisva/api/academic";
import type { Announcement } from "@sisva/types/apiTypes";
import type { UserType } from "@sisva/types/types";
import type { QueryClient } from "@tanstack/react-query";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

export function useAnnouncements<T = Announcement[]>({
  select,
  enabled,
}: {
  select?: (annoucements: Announcement[]) => T;
  enabled?: boolean;
} = {}) {
  return useQuery<Announcement[], unknown, T>({
    queryKey: ["announcements"],
    queryFn: async () => {
      return (await AcademicAPI.getAllAnnouncements()).data.data;
    },
    select,
    enabled,
  });
}

export function useAnnouncement(announcement_id: number | undefined) {
  return useAnnouncements({
    select(annoucements) {
      return annoucements.find(
        (annoucement) => annoucement.id === announcement_id
      );
    },
    enabled: !!announcement_id,
  });
}

export function useStudentAnnouncements() {
  return useAnnouncements({
    select(annoucements) {
      return annoucements.filter((annoucement) =>
        annoucement.target_user_types.includes("student")
      );
    },
  });
}

export function useStaffAnnouncements() {
  return useAnnouncements({
    select(annoucements) {
      return annoucements.filter((annoucement) =>
        annoucement.target_user_types.includes("staff")
      );
    },
  });
}

export function invalidateAnnouncements(queryClient: QueryClient) {
  queryClient.invalidateQueries({
    queryKey: ["announcements"],
  });
}

export function useCreateAnnouncement({
  onSuccess,
  onError,
}: {
  onSuccess?: () => void;
  onError?: () => void;
} = {}) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({
      title,
      image_uri,
      text,
      target_user_types,
    }: {
      title: string;
      image_uri?: string;
      text: string;
      target_user_types: UserType[];
    }) => {
      await AcademicAPI.addAnnouncement({
        title,
        image_uri,
        text,
        target_user_types,
      });
    },
    onSuccess() {
      invalidateAnnouncements(queryClient);
      onSuccess?.();
    },
    onError() {
      onError?.();
    },
  });
}

export function useUpdateAnnouncement({
  onSuccess,
  onError,
}: {
  onSuccess?: () => void;
  onError?: () => void;
} = {}) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({
      id,
      title,
      image_uri,
      text,
      target_user_types,
    }: {
      id: number;
      title: string;
      image_uri?: string;
      text: string;
      target_user_types: UserType[];
    }) => {
      await AcademicAPI.updateAnnouncement(id, {
        title,
        image_uri,
        text,
        target_user_types,
      });
    },
    onSuccess() {
      invalidateAnnouncements(queryClient);
      onSuccess?.();
    },
    onError() {
      onError?.();
    },
  });
}

export function useDeleteAnnouncement({
  onSuccess,
  onError,
}: {
  onSuccess?: () => void;
  onError?: () => void;
} = {}) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (announcement_id: number) => {
      await AcademicAPI.deleteAnnouncement(announcement_id);
    },
    onSuccess() {
      invalidateAnnouncements(queryClient);
      onSuccess?.();
    },
    onError() {
      onError?.();
    },
  });
}
