import AcademicAPI from "@sisva/api/academic";
import type { Grade, Syllabus } from "@sisva/types/apiTypes";
import type { QueryClient } from "@tanstack/react-query";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

export function useSyllabuses<T = Syllabus[]>({
  select,
  enabled,
}: {
  select?: (syllabuses: Syllabus[]) => T;
  enabled?: boolean;
} = {}) {
  return useQuery<Syllabus[], unknown, T>({
    queryKey: ["syllabuses"],
    queryFn: async () => (await AcademicAPI.getAllSilabus()).data.data,
    select,
    enabled,
  });
}

export const useSyllabus = (syllabus_id: number | string | undefined) => {
  return useQuery<Syllabus>({
    queryKey: ["syllabuses", { syllabus_id }],
    queryFn: async () =>
      (await AcademicAPI.getDetailSilabus(syllabus_id)).data.data,
    enabled: !!syllabus_id,
  });
};

export function invalidateSyllabuses(queryClient: QueryClient) {
  queryClient.invalidateQueries({
    queryKey: ["syllabuses"],
  });
}

export function useCreateSyllabus({
  onSuccess,
  onError,
}: {
  onSuccess?: () => void;
  onError?: () => void;
} = {}) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({
      file_uri,
      subject_id,
      grade,
    }: {
      file_uri: string;
      subject_id: number;
      grade: Grade;
    }) => {
      await AcademicAPI.createSilabus({
        file_uri,
        subject_id,
        grade,
      });
    },
    onSuccess() {
      invalidateSyllabuses(queryClient);
      onSuccess?.();
    },
    onError() {
      onError?.();
    },
  });
}

export function useUpdateSyllabus({
  onSuccess,
  onError,
}: {
  onSuccess?: () => void;
  onError?: () => void;
} = {}) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({
      syllabus_id,
      file_uri,
      subject_id,
      grade,
    }: {
      syllabus_id: number;
      file_uri: string;
      subject_id: number;
      grade: Grade;
    }) => {
      await AcademicAPI.updateSilabus(
        {
          file_uri,
          subject_id,
          grade,
        },
        syllabus_id
      );
    },
    onSuccess() {
      invalidateSyllabuses(queryClient);
      onSuccess?.();
    },
    onError() {
      onError?.();
    },
  });
}

export function useDeleteSyllabus({
  onSuccess,
  onError,
}: {
  onSuccess?: () => void;
  onError?: () => void;
} = {}) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (syllabus_id: number) => {
      await AcademicAPI.deleteSilabus(syllabus_id);
    },
    onSuccess() {
      invalidateSyllabuses(queryClient);
      onSuccess?.();
    },
    onError() {
      onError?.();
    },
  });
}
