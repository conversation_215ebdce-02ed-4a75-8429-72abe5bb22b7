import AcademicAPI from "@sisva/api/academic";
import type { QueryClient } from "@tanstack/react-query";
import {
  queryOptions,
  useMutation,
  useQuery,
  useQueryClient,
} from "@tanstack/react-query";

import { useTeachers } from "../user/useTeachers";
import {
  useStudentGroup,
  useStudentGroups,
  useStudentGroupsWithStudents,
  useStudentsStudentGroup,
} from "./useStudentGroups";
import { useSubjects } from "./useSubjects";

export const classesQeuryOptions = queryOptions({
  queryKey: ["classes"],
  async queryFn() {
    return (await AcademicAPI.getAllClasses()).data.data;
  },
});

export function useClasses() {
  return useQuery({
    ...classesQeuryOptions,
  });
}

export const invalidateClasses = (queryClient: QueryClient) => {
  queryClient.invalidateQueries({ queryKey: ["classes"] });
};

export const useClass = (class_id: number | undefined | null) => {
  return useQuery({
    ...classesQeuryOptions,
    select: (classes) => classes.find((class_) => class_.id === class_id),
    enabled: !!class_id,
  });
};

export function useCreateClass({
  onSuccess,
  onError,
}: {
  onSuccess?: ({ data }: { data: number }) => void;
  onError?: () => void;
} = {}) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (payload: {
      name: string;
      student_group_id: number;
      subject_id: number;
      teacher_id: string;
    }) => {
      return (await AcademicAPI.createClass(payload)).data.data as number;
    },
    onSuccess(data) {
      invalidateClasses(queryClient);
      onSuccess?.({ data });
    },
    onError() {
      onError?.();
    },
  });
}

export function useUpdateClass({
  onSuccess,
  onError,
}: {
  onSuccess?: () => void;
  onError?: () => void;
} = {}) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (payload: {
      class_id: number;
      name: string;
      student_group_id: number;
      subject_id: number;
      teacher_id: number;
    }) => {
      await AcademicAPI.updateClass(payload.class_id, payload);
    },
    onSuccess() {
      invalidateClasses(queryClient);
      onSuccess?.();
    },
    onError() {
      onError?.();
    },
  });
}

export function useDeleteClass({
  onSuccess,
  onError,
}: {
  onSuccess?: () => void;
  onError?: () => void;
} = {}) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (class_id: number) => {
      await AcademicAPI.deleteClass(class_id);
    },
    onSuccess() {
      invalidateClasses(queryClient);
      onSuccess?.();
    },
    onError() {
      onError?.();
    },
  });
}

export const useStudentsClasses = (student_id: string | undefined | null) => {
  const { data: subjects = [], isLoading: L1 } = useSubjects();
  const { data: teachers = [], isLoading: L2 } = useTeachers();
  const { data: studentGroup, isLoading: L3 } =
    useStudentsStudentGroup(student_id);

  return useQuery({
    ...classesQeuryOptions,
    select: (classes) =>
      classes
        .filter((class_) => studentGroup?.id === class_.student_group_id)
        .map((class_) => {
          return {
            ...class_,
            subject: subjects.find(
              (subject) => subject.id === class_.subject_id
            ),
            teacher: teachers.find(
              (teacher) => teacher.id === class_.teacher_id
            ),
            student_group: studentGroup,
          };
        }),
    enabled: !!student_id && !L1 && !L2 && !L3,
  });
};

export const useTeachersClasses = (teacher_id: string | undefined | null) => {
  const { data: subjects = [], isLoading: L1 } = useSubjects();
  const { data: teachers = [], isLoading: L2 } = useTeachers();
  const { data: studentGroups = [], isLoading: L3 } = useStudentGroups();

  return useQuery({
    ...classesQeuryOptions,
    select: (classes) =>
      classes
        .filter((class_) => class_.teacher_id === teacher_id)
        .map((class_) => {
          return {
            ...class_,
            subject: subjects.find(
              (subject) => subject.id === class_.subject_id
            ),
            teacher: teachers.find(
              (teacher) => teacher.id === class_.teacher_id
            ),
            student_group: studentGroups.find(
              (studentGroup) => studentGroup.id === class_.student_group_id
            ),
          };
        }),
    enabled: !!teacher_id && !L1 && !L2 && !L3,
  });
};

export const useStudentGroupsClasses = (
  student_group_id: number | undefined | null
) => {
  const { data: subjects = [], isLoading: L1 } = useSubjects();
  const { data: teachers = [], isLoading: L2 } = useTeachers();
  const { data: studentGroup, isLoading: L3 } =
    useStudentGroup(student_group_id);

  return useQuery({
    ...classesQeuryOptions,
    select: (classes) =>
      classes
        .filter((class_) => studentGroup?.id === class_.student_group_id)
        .map((class_) => {
          return {
            ...class_,
            subject: subjects.find(
              (subject) => subject.id === class_.subject_id
            ),
            teacher: teachers.find(
              (teacher) => teacher.id === class_.teacher_id
            ),
            student_group: studentGroup,
          };
        }),
    enabled: !!student_group_id && !L1 && !L2 && !L3,
  });
};

export function useClassesWithSubjectAndTeacherAndStudentGroup() {
  const { data: subjects = [], isLoading: L1 } = useSubjects();
  const { data: teachers = [], isLoading: L2 } = useTeachers();
  const { data: studentGroups = [], isLoading: L3 } =
    useStudentGroupsWithStudents();

  return useQuery({
    ...classesQeuryOptions,
    select(classes) {
      return classes.map((class_) => {
        return {
          ...class_,
          subject: subjects.find((subject) => subject.id === class_.subject_id),
          teacher: teachers.find((teacher) => teacher.id === class_.teacher_id),
          student_group: studentGroups.find(
            (studentGroup) => studentGroup.id === class_.student_group_id
          ),
        };
      });
    },
    enabled: !L1 && !L2 && !L3,
  });
}

export type ClassWithSubjectAndTeacherAndStudentGroup = NonNullable<
  ReturnType<typeof useClassesWithSubjectAndTeacherAndStudentGroup>["data"]
>[number];

export function useClassWithSubjectAndTeacherAndStudentGroup(
  class_id: number | undefined
) {
  const { data: subjects = [], isLoading: L1 } = useSubjects();
  const { data: teachers = [], isLoading: L2 } = useTeachers();
  const { data: studentGroups = [], isLoading: L3 } = useStudentGroups();

  return useQuery({
    ...classesQeuryOptions,
    select(classes) {
      return classes
        .map((class_) => {
          return {
            ...class_,
            subject: subjects.find(
              (subject) => subject.id === class_.subject_id
            ),
            teacher: teachers.find(
              (teacher) => teacher.id === class_.teacher_id
            ),
            student_group: studentGroups.find(
              (studentGroup) => studentGroup.id === class_.student_group_id
            ),
          };
        })
        .find((class_) => class_.id === class_id);
    },
    enabled: !!class_id && !L1 && !L2 && !L3,
  });
}
