import AcademicAPI from "@sisva/api/academic";
import type { SchoolSchedules } from "@sisva/types/apiTypes";
import type { QueryClient } from "@tanstack/react-query";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

export function useSchoolSchedules<T = SchoolSchedules[]>(
  period_id: string | number | undefined | null,
  {
    select,
    enabled,
  }: {
    select?: (schoolSchedules: SchoolSchedules[]) => T;
    enabled?: boolean;
  } = {}
) {
  return useQuery<SchoolSchedules[], unknown, T>({
    queryKey: ["school-schedules", period_id],
    queryFn: async () =>
      (
        await AcademicAPI.getAllSchoolSchedules({
          period_id,
        })
      ).data.data,
    select,
    enabled: !!period_id && enabled,
  });
}

export function useSchoolSchedule({
  school_schedule_id,
  period_id,
}: {
  school_schedule_id: number | undefined | null;
  period_id: number | undefined | null;
}) {
  return useSchoolSchedules(period_id, {
    select: (schoolSchedules) => {
      return schoolSchedules.find((item) => item.id === school_schedule_id);
    },
    enabled: !!school_schedule_id && !!period_id,
  });
}

export function invalidateSchoolSchedule(queryClient: QueryClient) {
  queryClient.invalidateQueries({
    queryKey: ["school-schedules"],
  });
}

export function useCreateSchoolSchedule({
  onSuccess,
  onError,
}: {
  onSuccess?: () => void;
  onError?: () => void;
} = {}) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (payload: {
      period_id: number;
      study_program_id: number;
      grade: string;
      day: number;
      start_time: string;
      end_time: string;
    }) => {
      await AcademicAPI.createSchoolSchedule(payload);
    },
    onSuccess() {
      invalidateSchoolSchedule(queryClient);
      onSuccess?.();
    },
    onError() {
      onError?.();
    },
  });
}

export function useUpdateSchoolSchedule({
  onSuccess,
  onError,
}: {
  onSuccess?: () => void;
  onError?: () => void;
} = {}) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (payload: {
      school_schedule_id: number;
      period_id: number;
      study_program_id: number;
      grade: string;
      day: number;
      start_time: string;
      end_time: string;
    }) => {
      await AcademicAPI.editSchoolSchedule(payload, payload.school_schedule_id);
    },
    onSuccess() {
      invalidateSchoolSchedule(queryClient);
      onSuccess?.();
    },
    onError() {
      onError?.();
    },
  });
}

export function useDeleteSchoolSchedule({
  onSuccess,
  onError,
}: {
  onSuccess?: () => void;
  onError?: () => void;
} = {}) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (school_schedule_id: number) => {
      await AcademicAPI.deleteSchoolSchedule(school_schedule_id);
    },
    onSuccess() {
      invalidateSchoolSchedule(queryClient);
      onSuccess?.();
    },
    onError() {
      onError?.();
    },
  });
}
