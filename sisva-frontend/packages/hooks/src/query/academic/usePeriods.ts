import AcademicAPI from "@sisva/api/academic";
import AuthAPI from "@sisva/api/auth";
import { useCurrentUser } from "@sisva/providers";
import { useSchool } from "@sisva/providers";
import type { Grade, Period, PeriodCurriculum } from "@sisva/types/apiTypes";
import type { PeriodStatus } from "@sisva/types/types";
import type { QueryClient } from "@tanstack/react-query";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { sort } from "fast-sort";

import { useStudentsStudentGroup } from "./useStudentGroups";

export const usePeriods = <T = Period[]>({
  select,
  enabled,
}: {
  select?: (periods: Period[]) => T;
  enabled?: boolean;
} = {}) => {
  return useQuery<Period[], unknown, T>({
    queryKey: ["periods"],
    queryFn: async () => (await AcademicAPI.getAllPeriod()).data.data,
    select,
    enabled,
  });
};

export function usePeriod(period_id: number | undefined | null) {
  return usePeriods({
    select(periods) {
      return periods.find((period) => period.id === period_id);
    },
    enabled: !!period_id,
  });
}

export function usePeriodCurriculums<T = PeriodCurriculum[]>({
  select,
  enabled,
}: {
  select?: (periodCurriculums: PeriodCurriculum[]) => T;
  enabled?: boolean;
} = {}) {
  return useQuery<PeriodCurriculum[], unknown, T>({
    queryKey: ["period-curriculums"],
    queryFn: async () => (await AcademicAPI.getPeriodCurr()).data.data,
    select,
    enabled,
  });
}

export function invalidatePeriodCurriculums(queryClient: QueryClient) {
  queryClient.invalidateQueries({
    queryKey: ["period-curriculums"],
  });
}

export function usePeriodCurriculum({
  period_id,
  curriculum_id,
  study_program_id,
  grade,
}: {
  period_id: number | null | undefined;
  curriculum_id: number | null | undefined;
  study_program_id: number | null | undefined;
  grade: Grade | null | undefined;
}) {
  return usePeriodCurriculums({
    select(periodCurriculums) {
      return periodCurriculums.find(
        (periodCurriculum) =>
          periodCurriculum.period_id === period_id &&
          periodCurriculum.curriculum_id === curriculum_id &&
          periodCurriculum.study_program_id === study_program_id &&
          periodCurriculum.grade === grade
      );
    },
    enabled: !!period_id && !!curriculum_id && !!study_program_id && !!grade,
  });
}

export const useSelectedPeriod = () => {
  const currentUser = useCurrentUser();
  const { data: studentGroup, isLoading } = useStudentsStudentGroup(
    currentUser.id
  );
  return usePeriods({
    select(periods) {
      // if studentGroup exist, then currentUser is `student`, and we get selectedPeriod based on studentGroup
      if (studentGroup) {
        return periods.find((period) => period.id === studentGroup.period_id);
      }

      // else we get newest, active period, that has study_programs
      return sort(
        periods.filter(
          (period) =>
            period.status === "active" && period?.study_programs?.length
        )
      ).desc(["id"])[0];
    },
    enabled: !isLoading,
  });
};

export function invalidatePeriods(queryClient: QueryClient) {
  queryClient.invalidateQueries({
    queryKey: ["periods"],
  });
}

export function useCreatePeriod({
  onSuccess,
  onError,
}: {
  onSuccess?: () => void;
  onError?: () => void;
} = {}) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({
      name,
      start_time,
      end_time,
    }: {
      name: string;
      start_time: string;
      end_time: string;
    }) => {
      await AcademicAPI.createPeriod({
        name,
        start_time,
        end_time,
      });
    },
    onSuccess() {
      invalidatePeriods(queryClient);
      onSuccess?.();
    },
    onError() {
      onError?.();
    },
  });
}

export function useUpdatePeriod({
  onSuccess,
  onError,
}: {
  onSuccess?: () => void;
  onError?: (error: Error) => void;
} = {}) {
  const queryClient = useQueryClient();
  const currentUser = useCurrentUser();
  const school = useSchool();
  return useMutation({
    mutationFn: async ({
      period_id,
      name,
      start_time,
      end_time,
      status,
      password,
    }: {
      period_id: number;
      name: string;
      start_time: string;
      end_time: string;
      status: PeriodStatus;
      password: string;
    }) => {
      // if the status is not OK then it will throw error
      await AuthAPI.login({
        username: currentUser.username,
        password,
        school_id: school.id,
      });

      await AcademicAPI.updatePeriod(
        {
          name,
          start_time,
          end_time,
          status,
        },
        period_id
      );
    },
    onSuccess() {
      invalidatePeriods(queryClient);
      onSuccess?.();
    },
    onError(error) {
      onError?.(error);
    },
  });
}

export function useDeletePeriod({
  onSuccess,
  onError,
}: {
  onSuccess?: () => void;
  onError?: () => void;
} = {}) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (period_id: number) => {
      await AcademicAPI.deletePeriod(period_id);
    },
    onSuccess() {
      invalidatePeriods(queryClient);
      onSuccess?.();
    },
    onError() {
      onError?.();
    },
  });
}

export function useAddCurriculumInPeriod({
  onSuccess,
  onError,
}: {
  onSuccess?: () => void;
  onError?: () => void;
} = {}) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({
      curriculum_id,
      period_id,
      study_program_id,
      grade,
    }: {
      curriculum_id: number;
      period_id: number;
      study_program_id: number;
      grade: Grade;
    }) => {
      await AcademicAPI.addCurriculumInPeriod(period_id, {
        curriculum_id,
        study_program_id,
        grade,
      });
    },
    onSuccess() {
      invalidatePeriods(queryClient);
      invalidatePeriodCurriculums(queryClient);
      onSuccess?.();
    },
    onError() {
      onError?.();
    },
  });
}

export function useRemoveCurriculumInPeriod({
  onSuccess,
  onError,
}: {
  onSuccess?: () => void;
  onError?: () => void;
} = {}) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({
      curriculum_id,
      period_id,
      study_program_id,
      grade,
    }: {
      curriculum_id: number;
      period_id: number;
      study_program_id: number;
      grade: Grade;
    }) => {
      await AcademicAPI.deletePeriodCurr(period_id, {
        curriculum_id,
        study_program_id,
        grade,
      });
    },
    onSuccess() {
      invalidatePeriods(queryClient);
      invalidatePeriodCurriculums(queryClient);
      onSuccess?.();
    },
    onError() {
      onError?.();
    },
  });
}

export function useUpdateCurriculumInPeriod({
  onSuccess,
  onError,
}: {
  onSuccess?: () => void;
  onError?: () => void;
} = {}) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({
      curriculum_id,
      period_id,
      study_program_id,
      grade,
      new_curriculum_id,
      new_period_id,
      new_study_program_id,
      new_grade,
    }: {
      curriculum_id: number;
      period_id: number;
      study_program_id: number;
      grade: Grade;
      new_curriculum_id: number;
      new_period_id: number;
      new_study_program_id: number;
      new_grade: Grade;
    }) => {
      await AcademicAPI.deletePeriodCurr(period_id, {
        curriculum_id,
        study_program_id,
        grade,
      });
      await AcademicAPI.addCurriculumInPeriod(new_period_id, {
        curriculum_id: new_curriculum_id,
        study_program_id: new_study_program_id,
        grade: new_grade,
      });
    },
    onSuccess() {
      invalidatePeriods(queryClient);
      invalidatePeriodCurriculums(queryClient);
      onSuccess?.();
    },
    onError() {
      onError?.();
    },
  });
}
