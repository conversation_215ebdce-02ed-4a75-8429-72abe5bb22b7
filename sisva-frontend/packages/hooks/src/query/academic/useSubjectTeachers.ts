import AcademicAPI from "@sisva/api/academic";
import type { SubjectTeacher } from "@sisva/types/apiTypes";
import { useQuery } from "@tanstack/react-query";

export function useSubjectTeachers<T = SubjectTeacher[]>({
  select,
  enabled,
}: {
  select?: (data: SubjectTeacher[]) => T;
  enabled?: boolean;
} = {}) {
  return useQuery<SubjectTeacher[], unknown, T>({
    queryKey: ["subject_teachers"],
    queryFn: async () => (await AcademicAPI.getAllSubjectTeacher()).data.data,
    select,
    enabled,
  });
}

export function useTeachersSubjectTeachers(teacher_id: string | undefined) {
  return useSubjectTeachers({
    select(subjectTeachers) {
      return subjectTeachers.filter(
        (subjectTeacher) => subjectTeacher.teacher_id === teacher_id
      );
    },
    enabled: !!teacher_id,
  });
}
