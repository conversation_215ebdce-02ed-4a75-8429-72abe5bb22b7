import AcademicAPI from "@sisva/api/academic";
import type { Extracurricular } from "@sisva/types/apiTypes";
import type { QueryClient } from "@tanstack/react-query";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

export const useExtracurriculars = <T = Extracurricular[]>({
  select,
  enabled,
}: {
  select?: (extracurriculars: Extracurricular[]) => T;
  enabled?: boolean;
} = {}) => {
  return useQuery<Extracurricular[], unknown, T>({
    queryKey: ["extracurriculars"],
    queryFn: async () => (await AcademicAPI.getAllExtra()).data.data,
    select,
    enabled,
  });
};

export function useExtracurricular(extracurricular_id: number | undefined) {
  return useExtracurriculars({
    select: (extracurriculars) =>
      extracurriculars.find(
        (extracurricular) => extracurricular.id === extracurricular_id
      ),
    enabled: !!extracurricular_id,
  });
}

export function invalidateExtracurriculars(queryClient: QueryClient) {
  queryClient.invalidateQueries({
    queryKey: ["extracurriculars"],
  });
}

export function useCreateExtracurricular({
  onSuccess,
  onError,
}: {
  onSuccess?: () => void;
  onError?: () => void;
} = {}) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({
      name,
      teacher_id,
    }: {
      name: string;
      teacher_id: string;
    }) => {
      await AcademicAPI.createExtra({
        name,
        teacher_id,
      });
    },
    onSuccess() {
      invalidateExtracurriculars(queryClient);
      onSuccess?.();
    },
    onError() {
      onError?.();
    },
  });
}

export function useUpdateExtracurricular({
  onSuccess,
  onError,
}: {
  onSuccess?: () => void;
  onError?: () => void;
} = {}) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({
      extracurricular_id,
      name,
      teacher_id,
    }: {
      extracurricular_id: number;
      name: string;
      teacher_id: string;
    }) => {
      await AcademicAPI.updateExtra(extracurricular_id, {
        name,
        teacher_id,
      });
    },
    onSuccess() {
      invalidateExtracurriculars(queryClient);
      onSuccess?.();
    },
    onError() {
      onError?.();
    },
  });
}

export function useDeleteExtracurricular({
  onSuccess,
  onError,
}: {
  onSuccess?: () => void;
  onError?: () => void;
} = {}) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (extracurricular_id: number) => {
      await AcademicAPI.deleteExtra(extracurricular_id);
    },
    onSuccess() {
      invalidateExtracurriculars(queryClient);
      onSuccess?.();
    },
    onError() {
      onError?.();
    },
  });
}
