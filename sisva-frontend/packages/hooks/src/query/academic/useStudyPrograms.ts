import AcademicAPI from "@sisva/api/academic";
import type { StudyProgram } from "@sisva/types/apiTypes";
import type { Grade } from "@sisva/types/apiTypes";
import type { StudyProgramStatus } from "@sisva/types/types";
import type { QueryClient } from "@tanstack/react-query";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

export const useStudyPrograms = <T = StudyProgram[]>({
  select,
  enabled,
}: {
  select?: (studyPrograms: StudyProgram[]) => T;
  enabled?: boolean;
} = {}) => {
  return useQuery<StudyProgram[], unknown, T>({
    queryKey: ["study-programs"],
    queryFn: async () => (await AcademicAPI.getAllProdi()).data.data,
    select,
    enabled,
  });
};

export function useStudyProgram(study_program_id: number | undefined | null) {
  return useStudyPrograms({
    select: (studyPrograms) => {
      return studyPrograms.find(
        (studyProgram) => studyProgram.id === study_program_id
      );
    },
    enabled: !!study_program_id,
  });
}

export function invalidateStudyPrograms(queryClient: QueryClient) {
  queryClient.invalidateQueries({
    queryKey: ["study-programs"],
  });
}

export function useCreateStudyProgram({
  onSuccess,
  onError,
}: {
  onSuccess?: () => void;
  onError?: () => void;
} = {}) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({ name, code }: { name: string; code: string }) => {
      await AcademicAPI.createProdi({
        name,
        code,
        status: "active",
        grades: [],
      });
    },
    onSuccess() {
      invalidateStudyPrograms(queryClient);
      onSuccess?.();
    },
    onError() {
      onError?.();
    },
  });
}

export function useUpdateStudyProgram({
  onSuccess,
  onError,
}: {
  onSuccess?: () => void;
  onError?: () => void;
} = {}) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({
      id,
      name,
      code,
      status,
      grades,
    }: {
      id: number;
      name?: string;
      code?: string;
      status?: StudyProgramStatus;
      grades: Grade[];
    }) => {
      await AcademicAPI.updateProdi(
        {
          name,
          code,
          status,
          grades,
        },
        id
      );
    },
    onSuccess() {
      invalidateStudyPrograms(queryClient);
      onSuccess?.();
    },
    onError() {
      onError?.();
    },
  });
}

export function useDeleteStudyProgram({
  onSuccess,
  onError,
}: {
  onSuccess?: () => void;
  onError?: () => void;
} = {}) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (id: number) => {
      await AcademicAPI.deleteProdi(id);
    },
    onSuccess() {
      invalidateStudyPrograms(queryClient);
      onSuccess?.();
    },
    onError() {
      onError?.();
    },
  });
}
