module github.com/temui-sisva/ai-service

go 1.23

require (
	github.com/gorilla/mux v1.8.1
	github.com/joho/godotenv v1.5.1
	github.com/temui-sisva/go-lib/config v0.0.0-20250113164615-0e991db501ac
	github.com/temui-sisva/go-lib/context v0.0.0-20250113164615-0e991db501ac
	github.com/temui-sisva/go-lib/grpcclient/tenantconfiguration v0.0.0-20250113164615-0e991db501ac
	github.com/temui-sisva/go-lib/grpcclient/tenantuser v0.0.0-20250113164615-0e991db501ac
	google.golang.org/genai v1.6.0
	gopkg.in/yaml.v2 v2.4.0
)

require (
	cloud.google.com/go v0.116.0 // indirect
	cloud.google.com/go/auth v0.9.3 // indirect
	cloud.google.com/go/compute/metadata v0.5.0 // indirect
	github.com/eapache/go-resiliency v1.3.0 // indirect
	github.com/golang/groupcache v0.0.0-20210331224755-41bb18bfe9da // indirect
	github.com/google/go-cmp v0.6.0 // indirect
	github.com/google/s2a-go v0.1.8 // indirect
	github.com/googleapis/enterprise-certificate-proxy v0.3.4 // indirect
	github.com/gorilla/websocket v1.5.3 // indirect
	github.com/temui-sisva/go-lib/grpc v0.0.0-20230218164252-113e4b876da7 // indirect
	github.com/temui-sisva/grpc/common v0.0.0-20230216101249-c1df561dba3f // indirect
	github.com/temui-sisva/grpc/tenantconfiguration v0.0.0-20230218163901-85474745669a // indirect
	github.com/temui-sisva/grpc/tenantuser v0.0.0-20230312075501-78d02d612dfa // indirect
	go.opencensus.io v0.24.0 // indirect
	golang.org/x/crypto v0.27.0 // indirect
	golang.org/x/net v0.29.0 // indirect
	golang.org/x/sys v0.25.0 // indirect
	golang.org/x/text v0.18.0 // indirect
	google.golang.org/genproto/googleapis/rpc v0.0.0-20240903143218-8af14fe29dc1 // indirect
	google.golang.org/grpc v1.66.2 // indirect
	google.golang.org/protobuf v1.34.2 // indirect
)
