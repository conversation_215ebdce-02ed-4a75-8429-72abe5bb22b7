package model

// TeachingPlan represents an AI-generated teaching plan
type TeachingPlan struct {
	Description       string             `json:"description"`
	TeachingGoal      string             `json:"teachingGoal"`
	TeachingActivity  string             `json:"teachingActivity"`
	TeachingScoring   string             `json:"teachingScoring"`
	Tasks             []Task             `json:"tasks,omitempty"`
	TeachingMaterials []TeachingMaterial `json:"teachingMaterials,omitempty"`
}

// Task represents an AI-generated task
type Task struct {
	Name        string `json:"name"`
	Description string `json:"description"`
}

// TeachingMaterial represents AI-generated teaching material
type TeachingMaterial struct {
	Title   string `json:"title"`
	Content string `json:"content"`
}

// ExamQuestion represents an AI-generated exam question
type ExamQuestion struct {
	Text         string        `json:"text"`
	Type         string        `json:"type"` // "option", "text", or "scale"
	Weight       int           `json:"weight"`
	OptionDetail *OptionDetail `json:"option_detail,omitempty"`
	ScaleDetail  *ScaleDetail  `json:"scale_detail,omitempty"`
}

// OptionDetail contains details for option-type questions
type OptionDetail struct {
	Options []Option `json:"options"`
}

// Option represents an option for multiple-choice questions
type Option struct {
	Text    string `json:"text"`
	Correct bool   `json:"correct"`
}

// ScaleDetail contains details for scale-type questions
type ScaleDetail struct {
	MinLabel string `json:"min_label"`
	MaxLabel string `json:"max_label"`
}

// ExamQuestions represents a collection of AI-generated exam questions
type ExamQuestions struct {
	Questions []ExamQuestion `json:"questions"`
}

// TeachingPlanRequest represents a request to generate a teaching plan
type TeachingPlanRequest struct {
	Title          string `json:"title"`
	Specifications string `json:"specifications,omitempty"`
}

// TaskRequest represents a request to generate a task
type TaskRequest struct {
	Title          string `json:"title"`
	ClassName      string `json:"className,omitempty"`
	PlanTitle      string `json:"planTitle,omitempty"`
	SubjectName    string `json:"subjectName,omitempty"`
	Specifications string `json:"specifications,omitempty"`
}

// EditTaskRequest represents a request to edit a task using AI
type EditTaskRequest struct {
	CurrentTask  Task   `json:"currentTask"`
	Instructions string `json:"instructions"`
	ClassName    string `json:"className,omitempty"`
	PlanTitle    string `json:"planTitle,omitempty"`
	SubjectName  string `json:"subjectName,omitempty"`
}

// ExamQuestionsRequest represents a request to generate exam questions
type ExamQuestionsRequest struct {
	SubjectName         string `json:"subjectName"`
	QuestionCount       int    `json:"questionCount"`
	Name                string `json:"name,omitempty"`
	ClassName           string `json:"className,omitempty"`
	Description         string `json:"description,omitempty"`
	OptionQuestionCount int    `json:"optionQuestionCount,omitempty"`
	TextQuestionCount   int    `json:"textQuestionCount,omitempty"`
	ScaleQuestionCount  int    `json:"scaleQuestionCount,omitempty"`
}

// SingleExamQuestionRequest represents a request to generate a single exam question
type SingleExamQuestionRequest struct {
	QuestionType  string `json:"questionType"` // "option", "text", or "scale"
	SubjectName   string `json:"subjectName"`
	Name          string `json:"name,omitempty"`
	ClassName     string `json:"className,omitempty"`
	Description   string `json:"description,omitempty"`
	Customization string `json:"customization,omitempty"`
}

// TeachingMaterialRequest represents a request to generate teaching materials
type TeachingMaterialRequest struct {
	Title          string `json:"title"`
	Subject        string `json:"subject"`
	Specifications string `json:"specifications,omitempty"`
}

// EditTeachingPlanSectionRequest represents a request to edit a teaching plan section
type EditTeachingPlanSectionRequest struct {
	SectionType    string `json:"sectionType"` // "title", "description", "teachingGoal", "teachingActivity", "teachingScoring", "tasks", "teachingMaterials"
	CurrentContent string `json:"currentContent"`
	Instructions   string `json:"instructions"`
}

// GenerateTeachingPlanSectionRequest represents a request to generate a specific teaching plan section
type GenerateTeachingPlanSectionRequest struct {
	SectionType     string `json:"sectionType"` // "description", "teachingGoal", "teachingActivity", "teachingScoring"
	Title           string `json:"title,omitempty"`
	Subject         string `json:"subject,omitempty"`
	ClassName       string `json:"className,omitempty"`
	Specifications  string `json:"specifications,omitempty"`
}

// EditExamQuestionRequest represents a request to edit an exam question using AI
type EditExamQuestionRequest struct {
	CurrentQuestion ExamQuestion `json:"currentQuestion"`
	Instructions    string       `json:"instructions"`
	SubjectName     string       `json:"subjectName,omitempty"`
	ClassName       string       `json:"className,omitempty"`
	ExamName        string       `json:"examName,omitempty"`
	ExamDescription string       `json:"examDescription,omitempty"`
}
