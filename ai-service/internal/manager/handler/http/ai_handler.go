package http

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"net/http"
	"strings"
	"time"

	"github.com/gorilla/mux"
	"github.com/temui-sisva/ai-service/internal/manager/service"

	"github.com/temui-sisva/ai-service/internal/model"
	contextlib "github.com/temui-sisva/go-lib/context"
	tenantconfigurationclient "github.com/temui-sisva/go-lib/grpcclient/tenantconfiguration"
	tenantuserclient "github.com/temui-sisva/go-lib/grpcclient/tenantuser"
)

// AIHandler handles HTTP requests for AI operations
type AIHandler struct {
	aiManager             *service.AIManager
	tenantUserClientSvc   tenantuserclient.Service
	tenantConfigClientSvc tenantconfigurationclient.Service
}

// NewAIHandler creates a new AI handler
func NewAIHandler(
	aiManager *service.AIManager,
	tenantUserClientSvc tenantuserclient.Service,
	tenantConfigClientSvc tenantconfigurationclient.Service,
) *AIHandler {
	return &AIHandler{
		aiManager:             aiManager,
		tenantUserClientSvc:   tenantUserClientSvc,
		tenantConfigClientSvc: tenantConfigClientSvc,
	}
}

// RegisterRoutes registers routes for AI operations
func (h *AIHandler) RegisterRoutes(router *mux.Router) {
	// Subrouter for authenticated routes
	apiAIRouter := router.PathPrefix("/api/ai").Subrouter()
	apiAIRouter.Use(h.authMiddleware)

	apiAIRouter.HandleFunc("/teaching-plan", h.handleGenerateTeachingPlan).Methods(http.MethodPost, http.MethodOptions)
	apiAIRouter.HandleFunc("/task", h.handleGenerateTask).Methods(http.MethodPost, http.MethodOptions)
	apiAIRouter.HandleFunc("/edit-task", h.handleEditTask).Methods(http.MethodPost, http.MethodOptions)
	apiAIRouter.HandleFunc("/exam-questions", h.handleGenerateExamQuestions).Methods(http.MethodPost, http.MethodOptions)
	apiAIRouter.HandleFunc("/single-exam-question", h.handleGenerateSingleExamQuestion).Methods(http.MethodPost, http.MethodOptions)
	apiAIRouter.HandleFunc("/teaching-materials", h.handleGenerateTeachingMaterials).Methods(http.MethodPost, http.MethodOptions)
	apiAIRouter.HandleFunc("/edit-teaching-plan-section", h.handleEditTeachingPlanSection).Methods(http.MethodPost, http.MethodOptions)
	apiAIRouter.HandleFunc("/generate-teaching-plan-section", h.handleGenerateTeachingPlanSection).Methods(http.MethodPost, http.MethodOptions)
	apiAIRouter.HandleFunc("/edit-exam-question", h.handleEditExamQuestion).Methods(http.MethodPost, http.MethodOptions)
}

// authMiddleware handles token validation for requests
func (h *AIHandler) authMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Set CORS headers for preflight requests and actual requests
		setCORSHeaders(w)
		if r.Method == http.MethodOptions {
			w.WriteHeader(http.StatusOK)
			return
		}

		token := r.Header.Get("Authorization")
		userID := r.Header.Get("X-Sisva-UserID")
		schoolID := r.Header.Get("X-Sisva-SchoolID")

		if token == "" || userID == "" || schoolID == "" {
			log.Println("[AIHandler Auth] Missing Authorization, X-Sisva-UserID, or X-Sisva-SchoolID header")
			http.Error(w, "Unauthorized: Missing credentials", http.StatusUnauthorized)
			return
		}

		// Strip "Bearer " prefix if present
		rawToken := token
		if strings.HasPrefix(strings.ToLower(token), "bearer ") {
			rawToken = token[7:]
		}

		ctxWithSource := contextlib.SetSource(r.Context(), "ai.service.http.auth")
		tokenData, err := h.tenantUserClientSvc.ValidateToken(ctxWithSource, rawToken)
		if err != nil {
			log.Printf("[AIHandler Auth] Unauthorized error from ValidateToken. Err: %s\n", err.Error())
			http.Error(w, "Unauthorized: Invalid token", http.StatusUnauthorized)
			return
		}

		if userID != tokenData.UserID {
			log.Printf("[AIHandler Auth] Invalid UserID. Expected: %s, Got: %s\n", tokenData.UserID, userID)
			http.Error(w, "Unauthorized: UserID mismatch", http.StatusUnauthorized)
			return
		}

		if schoolID != tokenData.SchoolID {
			log.Printf("[AIHandler Auth] Invalid SchoolID. Expected: %s, Got: %s\n", tokenData.SchoolID, schoolID)
			http.Error(w, "Unauthorized: SchoolID mismatch", http.StatusUnauthorized)
			return
		}

		// If all checks pass, proceed to the next handler
		next.ServeHTTP(w, r)
	})
}

// handleGenerateTeachingPlan handles requests to generate a teaching plan
func (h *AIHandler) handleGenerateTeachingPlan(w http.ResponseWriter, r *http.Request) {
	// CORS is handled by the middleware now for actual requests.
	// OPTIONS requests are also handled by the middleware.

	// Parse request
	var req model.TeachingPlanRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	// Validate request
	if req.Title == "" {
		http.Error(w, "Title is required", http.StatusBadRequest)
		return
	}

	// Generate teaching plan
	teachingPlan, err := h.aiManager.GenerateTeachingPlan(r.Context(), req)
	if err != nil {
		log.Printf("Error generating teaching plan: %v", err)
		http.Error(w, "Failed to generate teaching plan", http.StatusInternalServerError)
		return
	}

	// Return response
	w.Header().Set("Content-Type", "application/json")
	if err := json.NewEncoder(w).Encode(teachingPlan); err != nil {
		log.Printf("Error encoding response: %v", err)
		http.Error(w, "Failed to encode response", http.StatusInternalServerError)
		return
	}
}

// handleGenerateTask handles requests to generate a task
func (h *AIHandler) handleGenerateTask(w http.ResponseWriter, r *http.Request) {
	// CORS and OPTIONS handled by middleware

	// Parse request
	var req model.TaskRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	// Validate request
	if req.Title == "" {
		http.Error(w, "Title is required", http.StatusBadRequest)
		return
	}

	// Generate task
	task, err := h.aiManager.GenerateTask(r.Context(), req)
	if err != nil {
		log.Printf("Error generating task: %v", err)
		http.Error(w, "Failed to generate task", http.StatusInternalServerError)
		return
	}

	// Return response
	w.Header().Set("Content-Type", "application/json")
	if err := json.NewEncoder(w).Encode(task); err != nil {
		log.Printf("Error encoding response: %v", err)
		http.Error(w, "Failed to encode response", http.StatusInternalServerError)
		return
	}
}

// handleEditTask handles requests to edit a task using AI
func (h *AIHandler) handleEditTask(w http.ResponseWriter, r *http.Request) {
	// CORS and OPTIONS handled by middleware

	// Parse request
	var req model.EditTaskRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	// Validate request
	if req.Instructions == "" {
		http.Error(w, "Instructions are required", http.StatusBadRequest)
		return
	}
	if req.CurrentTask.Name == "" {
		http.Error(w, "Current task name is required", http.StatusBadRequest)
		return
	}

	// Edit task
	editedTask, err := h.aiManager.EditTask(r.Context(), req)
	if err != nil {
		log.Printf("Error editing task: %v", err)
		http.Error(w, "Failed to edit task", http.StatusInternalServerError)
		return
	}

	// Return response
	w.Header().Set("Content-Type", "application/json")
	if err := json.NewEncoder(w).Encode(editedTask); err != nil {
		log.Printf("Error encoding response: %v", err)
		http.Error(w, "Failed to encode response", http.StatusInternalServerError)
		return
	}
}

// handleGenerateExamQuestions handles requests to generate exam questions
func (h *AIHandler) handleGenerateExamQuestions(w http.ResponseWriter, r *http.Request) {
	// CORS and OPTIONS handled by middleware

	// Set timeout context (3 minutes to allow for processing time)
	ctx, cancel := context.WithTimeout(r.Context(), 3*time.Minute)
	defer cancel()

	// Parse request
	var req model.ExamQuestionsRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	// Validate request
	if req.SubjectName == "" {
		http.Error(w, "Subject name is required", http.StatusBadRequest)
		return
	}

	// Validasi jumlah soal
	if req.QuestionCount <= 0 {
		http.Error(w, "Question count must be greater than 0", http.StatusBadRequest)
		return
	}

	// Batasi maksimal 50 soal untuk mencegah timeout
	maxQuestions := 50
	if req.QuestionCount > maxQuestions {
		http.Error(w, fmt.Sprintf("Maximum %d questions allowed per request", maxQuestions), http.StatusBadRequest)
		return
	}

	// Generate exam questions
	examQuestions, err := h.aiManager.GenerateExamQuestions(ctx, req)
	if err != nil {
		if errors.Is(err, context.DeadlineExceeded) {
			log.Printf("Request timeout: %v", err)
			http.Error(w, "Request timed out", http.StatusRequestTimeout)
		} else {
			log.Printf("Error generating exam questions: %v", err)
			http.Error(w, "Failed to generate exam questions", http.StatusInternalServerError)
		}
		return
	}

	// Validate generated questions count and trim if necessary
	if len(examQuestions.Questions) > req.QuestionCount {
		log.Printf("Warning: generated %d questions, expected %d. Trimming to requested count.",
			len(examQuestions.Questions), req.QuestionCount)
		examQuestions.Questions = examQuestions.Questions[:req.QuestionCount]
	}

	// Set appropriate headers for large responses
	w.Header().Set("Content-Type", "application/json")
	w.Header().Set("Transfer-Encoding", "chunked")

	// Write response directly without buffering
	encoder := json.NewEncoder(w)
	if err := encoder.Encode(examQuestions); err != nil {
		log.Printf("Error encoding response: %v", err)
		// Can't send HTTP error after writing headers, just log
		return
	}

	log.Printf("Successfully generated and sent %d exam questions for subject: %s",
		len(examQuestions.Questions), req.SubjectName)
}

// handleGenerateSingleExamQuestion handles requests to generate a single exam question
func (h *AIHandler) handleGenerateSingleExamQuestion(w http.ResponseWriter, r *http.Request) {
	// CORS and OPTIONS handled by middleware

	// Parse request
	var req model.SingleExamQuestionRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	// Validate request
	if req.QuestionType == "" {
		http.Error(w, "Question type is required", http.StatusBadRequest)
		return
	}
	if req.SubjectName == "" {
		http.Error(w, "Subject name is required", http.StatusBadRequest)
		return
	}

	// Generate single exam question
	examQuestion, err := h.aiManager.GenerateSingleExamQuestion(r.Context(), req)
	if err != nil {
		log.Printf("Error generating single exam question: %v", err)
		http.Error(w, "Failed to generate single exam question", http.StatusInternalServerError)
		return
	}

	// Return response
	w.Header().Set("Content-Type", "application/json")
	if err := json.NewEncoder(w).Encode(examQuestion); err != nil {
		log.Printf("Error encoding response: %v", err)
		http.Error(w, "Failed to encode response", http.StatusInternalServerError)
		return
	}
}

// handleGenerateTeachingMaterials handles requests to generate teaching materials
func (h *AIHandler) handleGenerateTeachingMaterials(w http.ResponseWriter, r *http.Request) {
	// CORS and OPTIONS handled by middleware

	// Parse request
	var req model.TeachingMaterialRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	// Validate request
	if req.Title == "" {
		http.Error(w, "Title is required", http.StatusBadRequest)
		return
	}
	if req.Subject == "" {
		http.Error(w, "Subject is required", http.StatusBadRequest)
		return
	}

	// Generate teaching materials
	teachingMaterial, err := h.aiManager.GenerateTeachingMaterials(r.Context(), req)
	if err != nil {
		log.Printf("Error generating teaching materials: %v", err)
		http.Error(w, "Failed to generate teaching materials", http.StatusInternalServerError)
		return
	}

	// Return response
	w.Header().Set("Content-Type", "application/json")
	if err := json.NewEncoder(w).Encode(teachingMaterial); err != nil {
		log.Printf("Error encoding response: %v", err)
		http.Error(w, "Failed to encode response", http.StatusInternalServerError)
		return
	}
}

// handleEditTeachingPlanSection handles requests to edit a teaching plan section
func (h *AIHandler) handleEditTeachingPlanSection(w http.ResponseWriter, r *http.Request) {
	// CORS and OPTIONS handled by middleware

	// Parse request
	var req model.EditTeachingPlanSectionRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	// Validate request
	if req.SectionType == "" {
		http.Error(w, "Section type is required", http.StatusBadRequest)
		return
	}
	if req.CurrentContent == "" {
		http.Error(w, "Current content is required", http.StatusBadRequest)
		return
	}
	if req.Instructions == "" {
		http.Error(w, "Instructions are required", http.StatusBadRequest)
		return
	}

	// Edit teaching plan section
	editedContent, err := h.aiManager.EditTeachingPlanSection(r.Context(), req)
	if err != nil {
		log.Printf("Error editing teaching plan section: %v", err)
		http.Error(w, "Failed to edit teaching plan section", http.StatusInternalServerError)
		return
	}

	// Return response
	w.Header().Set("Content-Type", "text/plain")
	w.WriteHeader(http.StatusOK)
	_, _ = w.Write([]byte(editedContent))
}

// handleGenerateTeachingPlanSection handles requests to generate a specific teaching plan section
func (h *AIHandler) handleGenerateTeachingPlanSection(w http.ResponseWriter, r *http.Request) {
	// CORS and OPTIONS handled by middleware

	// Parse request
	var req model.GenerateTeachingPlanSectionRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	// Validate request
	if req.SectionType == "" {
		http.Error(w, "Section type is required", http.StatusBadRequest)
		return
	}

	// Validate section type
	validSectionTypes := map[string]bool{
		"description":      true,
		"teachingGoal":     true,
		"teachingActivity": true,
		"teachingScoring":  true,
	}
	if !validSectionTypes[req.SectionType] {
		http.Error(w, "Invalid section type. Must be one of: description, teachingGoal, teachingActivity, teachingScoring", http.StatusBadRequest)
		return
	}

	// Generate teaching plan section
	generatedContent, err := h.aiManager.GenerateTeachingPlanSection(r.Context(), req)
	if err != nil {
		log.Printf("Error generating teaching plan section: %v", err)
		http.Error(w, "Failed to generate teaching plan section", http.StatusInternalServerError)
		return
	}

	// Return response
	w.Header().Set("Content-Type", "text/plain")
	w.WriteHeader(http.StatusOK)
	_, _ = w.Write([]byte(generatedContent))
}

// handleEditExamQuestion handles requests to edit an exam question
func (h *AIHandler) handleEditExamQuestion(w http.ResponseWriter, r *http.Request) {
	// CORS and OPTIONS handled by middleware

	// Parse request
	var req model.EditExamQuestionRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	// Validate request
	if req.Instructions == "" {
		http.Error(w, "Instructions are required", http.StatusBadRequest)
		return
	}
	if req.CurrentQuestion.Text == "" || req.CurrentQuestion.Type == "" {
		http.Error(w, "Current question text and type are required", http.StatusBadRequest)
		return
	}

	// Edit exam question
	editedQuestion, err := h.aiManager.EditExamQuestion(r.Context(), req)
	if err != nil {
		log.Printf("Error editing exam question: %v", err)
		http.Error(w, "Failed to edit exam question", http.StatusInternalServerError)
		return
	}

	// Return response
	w.Header().Set("Content-Type", "application/json")
	if err := json.NewEncoder(w).Encode(editedQuestion); err != nil {
		log.Printf("Error encoding response: %v", err)
		http.Error(w, "Failed to encode response", http.StatusInternalServerError)
		return
	}
}

// setCORSHeaders sets CORS headers for cross-origin requests
func setCORSHeaders(w http.ResponseWriter) {
	w.Header().Set("Access-Control-Allow-Origin", "*")
	w.Header().Set("Access-Control-Allow-Methods", "GET, POST, OPTIONS")
	w.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization, X-Sisva-Source, X-Sisva-UserID, X-Sisva-SchoolID")
}
