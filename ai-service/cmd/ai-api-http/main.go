package main

import (
	"context"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"strconv"

	"github.com/gorilla/mux"
	// "github.com/joho/godotenv" // No longer needed
	handler "github.com/temui-sisva/ai-service/internal/manager/handler/http"
	"github.com/temui-sisva/ai-service/internal/manager/service"
	configlib "github.com/temui-sisva/go-lib/config"
	tenantconfigurationclient "github.com/temui-sisva/go-lib/grpcclient/tenantconfiguration"
	tenantconfigurationclientservice "github.com/temui-sisva/go-lib/grpcclient/tenantconfiguration/service"
	tenantuserclient "github.com/temui-sisva/go-lib/grpcclient/tenantuser"
	tenantuserclientservice "github.com/temui-sisva/go-lib/grpcclient/tenantuser/service"
	"gopkg.in/yaml.v2"
)

// Config holds the application configuration
type Config struct {
	Server                    ServerConfig                    `yaml:"server"`
	TenantConfigurationClient TenantConfigurationClientConfig `yaml:"tenant_configuration_client"`
	TenantUserClient          TenantUserClientConfig          `yaml:"tenant_user_client"`
	Gemini                    GeminiConfig                    `yaml:"gemini"`
}

// GeminiConfig holds Gemini specific configuration
type GeminiConfig struct {
	APIKey string `yaml:"api_key"`
}

// ServerConfig holds server specific configuration
type ServerConfig struct {
	Port int `yaml:"port"`
}

// TenantConfigurationClientConfig holds configuration for the tenant configuration gRPC client
type TenantConfigurationClientConfig struct {
	Address           string                                                 `yaml:"address"`
	ConnectionTimeout configlib.Duration                                     `yaml:"connection_timeout"`
	RetryInterval     configlib.Duration                                     `yaml:"retry_interval"`
	ScopeSettings     map[string]TenantConfigurationClientScopeSettingConfig `yaml:"scope_settings"`
}

// TenantConfigurationClientScopeSettingConfig holds scope specific settings for tenant configuration client
type TenantConfigurationClientScopeSettingConfig struct {
	BreakerErrorThreshold   int                `yaml:"breaker_error_threshold"`
	BreakerSuccessThreshold int                `yaml:"breaker_success_threshold"`
	BreakerTimeout          configlib.Duration `yaml:"breaker_timeout"`
}

// TenantUserClientConfig holds configuration for the tenant user gRPC client
type TenantUserClientConfig struct {
	Address           string                                  `yaml:"address"`
	ConnectionTimeout configlib.Duration                      `yaml:"connection_timeout"`
	RetryInterval     configlib.Duration                      `yaml:"retry_interval"`
	ScopeSettings     map[string]TenantUserClientScopeSetting `yaml:"scope_settings"`
}

// TenantUserClientScopeSetting holds scope specific settings for tenant user client
type TenantUserClientScopeSetting struct {
	BreakerErrorThreshold   int                `yaml:"breaker_error_threshold"`
	BreakerSuccessThreshold int                `yaml:"breaker_success_threshold"`
	BreakerTimeout          configlib.Duration `yaml:"breaker_timeout"`
}

func main() {
	// .env loading removed, API key will come from config.development.yaml

	// Load configuration
	var cfg Config
	configFile, err := os.ReadFile("files/etc/ai-api-http/config.development.yaml")
	if err != nil {
		log.Fatalf("Failed to read config file: %v", err)
	}
	if err := yaml.Unmarshal(configFile, &cfg); err != nil {
		log.Fatalf("Failed to unmarshal config: %v", err)
	}

	// Initialize tenant configuration client service
	var tenantConfigurationClientSvc tenantconfigurationclient.Service
	{
		options := make([]tenantconfigurationclientservice.Option, 0)
		options = append(options, tenantconfigurationclientservice.WithGRPCConfig(tenantconfigurationclientservice.GRPCConfig{
			ConnectionTimeout: time.Duration(cfg.TenantConfigurationClient.ConnectionTimeout),
			RetryInterval:     time.Duration(cfg.TenantConfigurationClient.RetryInterval),
		}))

		for scope, scopeCfg := range cfg.TenantConfigurationClient.ScopeSettings {
			options = append(options, tenantconfigurationclientservice.WithScopeSetting(scope, tenantconfigurationclientservice.ScopeSetting{
				BreakerErrorThreshold:   scopeCfg.BreakerErrorThreshold,
				BreakerSuccessThreshold: scopeCfg.BreakerSuccessThreshold,
				BreakerTimeout:          time.Duration(scopeCfg.BreakerTimeout),
			}))
		}
		tenantConfigurationClientSvc, err = tenantconfigurationclientservice.New(cfg.TenantConfigurationClient.Address, options...)
		if err != nil {
			log.Fatalf("Failed to initialize tenant configuration client service: %s\n", err.Error())
		}
	}

	// Initialize tenant user client service
	var tenantUserClientSvc tenantuserclient.Service
	{
		options := make([]tenantuserclientservice.Option, 0)
		options = append(options, tenantuserclientservice.WithGRPCConfig(tenantuserclientservice.GRPCConfig{
			ConnectionTimeout: time.Duration(cfg.TenantUserClient.ConnectionTimeout),
			RetryInterval:     time.Duration(cfg.TenantUserClient.RetryInterval),
		}))

		for scope, scopeCfg := range cfg.TenantUserClient.ScopeSettings {
			options = append(options, tenantuserclientservice.WithScopeSetting(scope, tenantuserclientservice.ScopeSetting{
				BreakerErrorThreshold:   scopeCfg.BreakerErrorThreshold,
				BreakerSuccessThreshold: scopeCfg.BreakerSuccessThreshold,
				BreakerTimeout:          time.Duration(scopeCfg.BreakerTimeout),
			}))
		}
		tenantUserClientSvc, err = tenantuserclientservice.New(cfg.TenantUserClient.Address, options...)
		if err != nil {
			log.Fatalf("Failed to initialize tenant user client service: %s\n", err.Error())
		}
	}

	// Create AI manager
	aiManager, err := service.NewAIManager(cfg.Gemini.APIKey)
	if err != nil {
		log.Fatalf("Failed to create AI manager: %v", err)
	}

	// Create HTTP handler
	aiHandler := handler.NewAIHandler(aiManager, tenantUserClientSvc, tenantConfigurationClientSvc)

	// Create router
	router := mux.NewRouter()

	// Register routes
	aiHandler.RegisterRoutes(router)

	// Add middleware
	router.Use(loggingMiddleware)

	// Get port from config or use default from .env or hardcoded
	port := cfg.Server.Port
	if port == 0 {
		envPort := os.Getenv("PORT")
		if envPort != "" {
			portInt, err := strconv.Atoi(envPort)
			if err == nil {
				port = portInt
			}
		}
		if port == 0 {
			port = 8080 // Default if not in config or .env
		}
	}

	// Create server
	server := &http.Server{
		Addr:         ":" + strconv.Itoa(port),
		Handler:      router,
		ReadTimeout:  60 * time.Second, // Consider making these configurable
		WriteTimeout: 60 * time.Second,
		IdleTimeout:  60 * time.Second,
	}

	// Start server in a goroutine
	go func() {
		log.Printf("Server starting on port %d...", port)
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("Failed to start server: %v", err)
		}
	}()

	// Wait for interrupt signal to gracefully shut down the server
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	log.Println("Server is shutting down...")

	// Create a deadline to wait for current operations to complete
	ctx, cancel := context.WithTimeout(context.Background(), 15*time.Second)
	defer cancel()

	// Shut down server
	if err := server.Shutdown(ctx); err != nil {
		log.Fatalf("Server forced to shutdown: %v", err)
	}

	log.Println("Server exited properly")
}

// loggingMiddleware logs HTTP requests
func loggingMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		start := time.Now()
		next.ServeHTTP(w, r)
		log.Printf(
			"%s %s %s",
			r.Method,
			r.RequestURI,
			time.Since(start),
		)
	})
}
