# AI Service

Repo Backend untuk AI Sisva (Butuh Go 1.23 dan Google GenAI library). 

## 📋 Todo List

1. 📕 **Implementasi Built-in Vision File Processing** dari <PERSON> untuk generate konten berdasarkan PDF (Contoh: <PERSON>ring<PERSON> bahan ajar)
2. 🔤 **Autofill di masing-masing komponen** tanpa perlu mengetik manual
3. 🔐 **Authorization** sesuai dengan format backend lain yang sudah ada
4. 💰 **Sistem rate limit berdasarkan kredit per akun**

## 🧪 Testing


Bisa ditest menggunakan postman dengan json berikut :
```json
{
	"info": {
		"_postman_id": "205f48d7-2928-4d7c-829a-91077f991b99",
		"name": "AI Service - Teaching Platform",
		"description": "Collection untuk testing AI Service backend yang menangani teaching plan, tasks, exam questions, dan teaching materials",
		"schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json",
		"_exporter_id": "37341456"
	},
	"item": [
		{
			"name": "Generate Teaching Plan",
			"request": {
				"method": "POST",
				"header": [
					{
						"key": "Content-Type",
						"value": "application/json"
					}
				],
				"body": {
					"mode": "raw",
					"raw": "{\n  \"title\": \"Pembelajaran Matematika Dasar\",\n  \"specifications\": \"Rencana pembelajaran untuk siswa kelas 3 SD tentang operasi penjumlahan dan pengurangan dengan fokus pada pemahaman konsep dasar\"\n}"
				},
				"url": {
					"raw": "{{base_url}}/api/ai/teaching-plan",
					"host": [
						"{{base_url}}"
					],
					"path": [
						"api",
						"ai",
						"teaching-plan"
					]
				},
				"description": "Endpoint untuk generate teaching plan menggunakan AI"
			},
			"response": []
		},
		{
			"name": "Generate Task",
			"request": {
				"method": "POST",
				"header": [
					{
						"key": "Content-Type",
						"value": "application/json"
					}
				],
				"body": {
					"mode": "raw",
					"raw": "{\n  \"title\": \"Latihan Penjumlahan\",\n  \"className\": \"3A\",\n  \"planTitle\": \"Pembelajaran Matematika Dasar\",\n  \"subjectName\": \"Matematika\"\n}"
				},
				"url": {
					"raw": "{{base_url}}/api/ai/task",
					"host": [
						"{{base_url}}"
					],
					"path": [
						"api",
						"ai",
						"task"
					]
				},
				"description": "Endpoint untuk generate task/tugas menggunakan AI"
			},
			"response": []
		},
		{
			"name": "Generate Exam Questions",
			"request": {
				"method": "POST",
				"header": [
					{
						"key": "Content-Type",
						"value": "application/json"
					}
				],
				"body": {
					"mode": "raw",
					"raw": "{\n  \"subjectName\": \"Matematika\",\n  \"questionCount\": 55,\n  \"name\": \"Ujian Tengah Semester\",\n  \"className\": \"3A\",\n  \"description\": \"Ujian materi Integral dan Turunan untuk kelas 12 SMA\",\n  \"optionQuestionCount\": 40,\n  \"textQuestionCount\": 10,\n  \"scaleQuestionCount\": 5\n}"
				},
				"url": {
					"raw": "{{base_url}}/api/ai/exam-questions",
					"host": [
						"{{base_url}}"
					],
					"path": [
						"api",
						"ai",
						"exam-questions"
					]
				},
				"description": "Endpoint untuk generate multiple exam questions sekaligus"
			},
			"response": []
		},
		{
			"name": "Generate Single Exam Question - Multiple Choice",
			"request": {
				"method": "POST",
				"header": [
					{
						"key": "Content-Type",
						"value": "application/json"
					}
				],
				"body": {
					"mode": "raw",
					"raw": "{\n  \"questionType\": \"option\",\n  \"subjectName\": \"Matematika\",\n  \"name\": \"Soal Penjumlahan\",\n  \"className\": \"3A\",\n  \"description\": \"Soal pilihan ganda tentang penjumlahan bilangan\",\n  \"customization\": \"Buatkan soal dengan angka 1-20 dan berikan 4 pilihan jawaban\"\n}"
				},
				"url": {
					"raw": "{{base_url}}/api/ai/single-exam-question",
					"host": [
						"{{base_url}}"
					],
					"path": [
						"api",
						"ai",
						"single-exam-question"
					]
				},
				"description": "Generate single exam question tipe multiple choice"
			},
			"response": []
		},
		{
			"name": "Generate Single Exam Question - Text",
			"request": {
				"method": "POST",
				"header": [
					{
						"key": "Content-Type",
						"value": "application/json"
					}
				],
				"body": {
					"mode": "raw",
					"raw": "{\n  \"questionType\": \"text\",\n  \"subjectName\": \"Bahasa Indonesia\",\n  \"name\": \"Soal Essay\",\n  \"className\": \"4B\",\n  \"description\": \"Soal essay tentang pemahaman cerita\",\n  \"customization\": \"Buatkan soal yang mengharuskan siswa menjelaskan pesan moral dari sebuah cerita\"\n}"
				},
				"url": {
					"raw": "{{base_url}}/api/ai/single-exam-question",
					"host": [
						"{{base_url}}"
					],
					"path": [
						"api",
						"ai",
						"single-exam-question"
					]
				},
				"description": "Generate single exam question tipe text/essay"
			},
			"response": []
		},
		{
			"name": "Generate Single Exam Question - Scale",
			"request": {
				"method": "POST",
				"header": [
					{
						"key": "Content-Type",
						"value": "application/json"
					}
				],
				"body": {
					"mode": "raw",
					"raw": "{\n  \"questionType\": \"scale\",\n  \"subjectName\": \"IPA\",\n  \"name\": \"Penilaian Praktikum\",\n  \"className\": \"5A\",\n  \"description\": \"Penilaian skala untuk praktikum sains\",\n  \"customization\": \"Buatkan soal penilaian dengan skala 1-5 untuk mengukur pemahaman konsep\"\n}"
				},
				"url": {
					"raw": "{{base_url}}/api/ai/single-exam-question",
					"host": [
						"{{base_url}}"
					],
					"path": [
						"api",
						"ai",
						"single-exam-question"
					]
				},
				"description": "Generate single exam question tipe scale/rating"
			},
			"response": []
		},
		{
			"name": "Generate Teaching Materials",
			"request": {
				"method": "POST",
				"header": [
					{
						"key": "Content-Type",
						"value": "application/json"
					}
				],
				"body": {
					"mode": "raw",
					"raw": "{\n  \"title\": \"Sejarah Bab 1 Perjuangan Kemerdekaan Indonesia\",\n  \"subject\": \"Sejarah Kelas 5 SD\",\n  \"specifications\": \"Materi pembelajaran detail mengenai sejarah kemerdekaan indonesia secara lengkap\"\n}"
				},
				"url": {
					"raw": "{{base_url}}/api/ai/teaching-materials",
					"host": [
						"{{base_url}}"
					],
					"path": [
						"api",
						"ai",
						"teaching-materials"
					]
				},
				"description": "Generate teaching materials menggunakan AI"
			},
			"response": []
		},
		{
			"name": "Edit Teaching Plan Section - Description",
			"request": {
				"method": "POST",
				"header": [
					{
						"key": "Content-Type",
						"value": "application/json"
					}
				],
				"body": {
					"mode": "raw",
					"raw": "{\n  \"sectionType\": \"description\",\n  \"currentContent\": \"Pembelajaran matematika dasar untuk siswa kelas 3\",\n  \"instructions\": \"Perbaiki deskripsi ini agar lebih detail dan mencakup tujuan pembelajaran yang jelas serta metode yang akan digunakan\"\n}"
				},
				"url": {
					"raw": "{{base_url}}/api/ai/edit-teaching-plan-section",
					"host": [
						"{{base_url}}"
					],
					"path": [
						"api",
						"ai",
						"edit-teaching-plan-section"
					]
				},
				"description": "Edit bagian description dari teaching plan"
			},
			"response": []
		},
		{
			"name": "Edit Teaching Plan Section - Teaching Goal",
			"request": {
				"method": "POST",
				"header": [
					{
						"key": "Content-Type",
						"value": "application/json"
					}
				],
				"body": {
					"mode": "raw",
					"raw": "{\n  \"sectionType\": \"teachingGoal\",\n  \"currentContent\": \"Siswa dapat melakukan penjumlahan\",\n  \"instructions\": \"Buat tujuan pembelajaran yang lebih spesifik, terukur, dan sesuai dengan standar kurikulum untuk kelas 3 SD\"\n}"
				},
				"url": {
					"raw": "{{base_url}}/api/ai/edit-teaching-plan-section",
					"host": [
						"{{base_url}}"
					],
					"path": [
						"api",
						"ai",
						"edit-teaching-plan-section"
					]
				},
				"description": "Edit bagian teaching goal dari teaching plan"
			},
			"response": []
		},
		{
			"name": "Edit Teaching Plan Section - Teaching Activity",
			"request": {
				"method": "POST",
				"header": [
					{
						"key": "Content-Type",
						"value": "application/json"
					}
				],
				"body": {
					"mode": "raw",
					"raw": "{\n  \"sectionType\": \"teachingActivity\",\n  \"currentContent\": \"Kegiatan belajar penjumlahan dengan latihan soal\",\n  \"instructions\": \"Tambahkan variasi aktivitas yang lebih menarik seperti permainan edukatif, diskusi kelompok, dan penggunaan media pembelajaran\"\n}"
				},
				"url": {
					"raw": "{{base_url}}/api/ai/edit-teaching-plan-section",
					"host": [
						"{{base_url}}"
					],
					"path": [
						"api",
						"ai",
						"edit-teaching-plan-section"
					]
				},
				"description": "Edit bagian teaching activity dari teaching plan"
			},
			"response": []
		},
		{
			"name": "Edit Teaching Plan Section - Teaching Scoring",
			"request": {
				"method": "POST",
				"header": [
					{
						"key": "Content-Type",
						"value": "application/json"
					}
				],
				"body": {
					"mode": "raw",
					"raw": "{\n  \"sectionType\": \"teachingScoring\",\n  \"currentContent\": \"Penilaian berdasarkan hasil ujian\",\n  \"instructions\": \"Buat sistem penilaian yang lebih komprehensif mencakup penilaian proses, hasil, dan aspek afektif dengan rubrik yang jelas\"\n}"
				},
				"url": {
					"raw": "{{base_url}}/api/ai/edit-teaching-plan-section",
					"host": [
						"{{base_url}}"
					],
					"path": [
						"api",
						"ai",
						"edit-teaching-plan-section"
					]
				},
				"description": "Edit bagian teaching scoring dari teaching plan"
			},
			"response": []
		}
	],
	"event": [
		{
			"listen": "prerequest",
			"script": {
				"type": "text/javascript",
				"exec": [
					"// Set base URL jika belum ada",
					"if (!pm.variables.get('base_url')) {",
					"    pm.variables.set('base_url', 'http://localhost:8080');",
					"}"
				]
			}
		},
		{
			"listen": "test",
			"script": {
				"type": "text/javascript",
				"exec": [
					"// Test untuk semua response",
					"pm.test('Status code should be 200', function () {",
					"    pm.response.to.have.status(200);",
					"});",
					"",
					"pm.test('Response should be JSON', function () {",
					"    pm.response.to.be.json;",
					"});",
					"",
					"pm.test('Response time should be less than 30 seconds', function () {",
					"    pm.expect(pm.response.responseTime).to.be.below(30000);",
					"});"
				]
			}
		}
	],
	"variable": [
		{
			"key": "base_url",
			"value": "http://localhost:8080"
		}
	]
}
```


